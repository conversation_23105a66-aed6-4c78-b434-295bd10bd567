{
  "App": {
    "SelfUrl": "http://localhost:6889",
    "CorsOrigins": "http://localhost:6889,http://localhost:6789,http://localhost:4200,http://localhost:4300"
  },
  "Service": {
    "Name": "PortalApiGateway",
    "Title": "PortalApiGateway",
    "BaseUrl": "portal-api-gateway",
    "AuthApiName": "PortalApiGateway"
  },
  "RemoteServices": {
    "Default": {
      "BaseUrl": "http://localhost:6889/"
    }
  },
  "MicroserviceSwagger": {
    "BaseUrl": "http://localhost",
    "IsEnable":  1
  },
  "AuthServer": {
    "Authority": "http://localhost:6868",
    "RequireHttpsMetadata": "false",
    "ApiName": "einvoice",
    "SwaggerClientId": "einvoiceportal_swagger",
    "SwaggerClientSecret": "Vnis@12A"
  },
  "IsPortalApiGateway": 1,
  "AllowedHosts": "*",
  "Logging": {
    "Directory": "/home/<USER>/logs/portal-api-gateway", // thư mục lưu file, nếu để trống thì sẽ lưu mặc định vào thư mục góc của service
    "OutPutTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] [{ServerName}] [{UserName}] [{CorrelationId}] [{ProcessId}] [{ThreadId}] [{ClassName}]  {Message:lj}{NewLine}{Exception}", // Pattern cấu trúc log
    "FileSizeLimitBytes": 104857600, // 100MB = 100 * 1024 * 1024
    "FlushToDiskInterval": "00:00:03", // tối đa 3 giây sẽ phải đầy vào disk
    "RollingInterval": "Day", // 1 day sẽ rolling file
    "RollOnFileSizeLimit": true, // rolling một file mới khi đạt giớ hạn kích thước
    "RetainedFileCountLimit": 31, // Giữ lại tối đa 31 file trong một ngày
    "Formatter": "txt" // Định dạng ghi log (txt | json), nếu để trống sẽ ghi log mặc định dạng "txt"
  }
}