using System;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Serilog;
using Serilog.Events;

namespace PortalApiGateway
{
    public class Program
    {
        public static int Main(string[] args)
        {
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Debug()
                .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
                .MinimumLevel.Override("Microsoft.EntityFrameworkCore", LogEventLevel.Warning)
                .Enrich.WithProperty("Application", "Portal Api Gateway")
                .Enrich.FromLogContext()
                .WriteTo.File($"Logs/{DateTime.Now:yyyy-MM-dd}-log.txt")
                .CreateLogger();

            try
            {
                Log.Information("Starting Api Gateway.");
                CreateHostBuilder(args).Build().Run();
                Console.WriteLine("Starting Api Gateway.");
                return 0;
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Api Gateway terminated unexpectedly!");
                Console.WriteLine("Error: Starting Api Gateway.");
                return 1;
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        internal static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args).ConfigureAppConfiguration((hostingContext, config) =>
            {
                config
                    .SetBasePath(hostingContext.HostingEnvironment.ContentRootPath)
                    .AddJsonFile($"appsettings.json", optional: true, reloadOnChange: true)
                    .AddJsonFile($"appsettings.{hostingContext.HostingEnvironment.EnvironmentName}.json", true, true)
                    .AddJsonFile("api-gateway-configuration.json", optional: true, reloadOnChange: true)
                    .AddJsonFile($"api-gateway-configuration.{hostingContext.HostingEnvironment.EnvironmentName}.json", true, true)
                    .AddEnvironmentVariables();
            })
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>();
                })
                .UseAutofac()
                .UseSerilog();
    }
}
