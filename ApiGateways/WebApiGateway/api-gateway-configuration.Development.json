{
  "Routes": [
    {
      "DownstreamPathTemplate": "/api/abp/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6789
        }
      ],
      "UpstreamPathTemplate": "/api/abp/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/account/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 44368
        }
      ],
      "UpstreamPathTemplate": "/api/account/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/identity/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 44368
        }
      ],
      "UpstreamPathTemplate": "/api/identity/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/auth/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 44368
        }
      ],
      "UpstreamPathTemplate": "/api/auth/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/catalog/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6001
        }
      ],
      "UpstreamPathTemplate": "/api/catalog/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/export/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6002
        }
      ],
      "UpstreamPathTemplate": "/api/export/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/exportpdf/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6003
        }
      ],
      "UpstreamPathTemplate": "/api/exportpdf/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/invoice01/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6004
        }
      ],
      "UpstreamPathTemplate": "/api/invoice01/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/invoice02/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6005
        }
      ],
      "UpstreamPathTemplate": "/api/invoice02/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/invoice03/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6006
        }
      ],
      "UpstreamPathTemplate": "/api/invoice03/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/invoice04/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6007
        }
      ],
      "UpstreamPathTemplate": "/api/invoice04/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/license/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6008
        }
      ],
      "UpstreamPathTemplate": "/api/license/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/mediafile/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6009
        }
      ],
      "UpstreamPathTemplate": "/api/mediafile/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/portal/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6010
        }
      ],
      "UpstreamPathTemplate": "/api/portal/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/registrationInvoice/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6011
        }
      ],
      "UpstreamPathTemplate": "/api/registrationInvoice/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/report/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6012
        }
      ],
      "UpstreamPathTemplate": "/api/report/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/sendmail/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6013
        }
      ],
      "UpstreamPathTemplate": "/api/sendmail/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    //{ //ky tich hop tvan vnis
    //  "DownstreamPathTemplate": "/api/sign/{everything}",
    //  "DownstreamScheme": "http",
    //  "DownstreamHostAndPorts": [
    //    {
    //      "Host": "localhost",
    //      "Port": 6014
    //    }
    //  ],
    //  "UpstreamPathTemplate": "/api/sign/{everything}",
    //  "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ]
    //},
    {
      "DownstreamPathTemplate": "/api/system/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6015
        }
      ],
      "UpstreamPathTemplate": "/api/system/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/tvan/info/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6016
        }
      ],
      "UpstreamPathTemplate": "/api/tvan/info/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/tvaninvoice/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6017
        }
      ],
      "UpstreamPathTemplate": "/api/tvaninvoice/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/01gtkt",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6020
        }
      ],
      "UpstreamPathTemplate": "/api/01gtkt",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/01gtkt/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6020
        }
      ],
      "UpstreamPathTemplate": "/api/01gtkt/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/02gttt",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6022
        }
      ],
      "UpstreamPathTemplate": "/api/02gttt",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/02gttt/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6022
        }
      ],
      "UpstreamPathTemplate": "/api/02gttt/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/03xknb",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6021
        }
      ],
      "UpstreamPathTemplate": "/api/03xknb",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/03xknb/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6021
        }
      ],
      "UpstreamPathTemplate": "/api/03xknb/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/nuocsachhanoi/invoice01/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6026
        }
      ],
      "UpstreamPathTemplate": "/api/nuocsachhanoi/invoice01/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/input/catalog/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6031
        }
      ],
      "UpstreamPathTemplate": "/api/input/catalog/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/input/invoice01/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6032
        }
      ],
      "UpstreamPathTemplate": "/api/input/invoice01/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/input/exportpdf/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6033
        }
      ],
      "UpstreamPathTemplate": "/api/input/exportpdf/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/input/01gtkt",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6034
        }
      ],
      "UpstreamPathTemplate": "/api/input/01gtkt",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/services/app/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6035
        }
      ],
      "UpstreamPathTemplate": "/api/services/app/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/Dashboard/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6036
        }
      ],
      "UpstreamPathTemplate": "/api/Dashboard/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/signalr/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6060
        }
      ],
      "UpstreamPathTemplate": "/api/signalr/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/batch-signer/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6066
        }
      ],
      "UpstreamPathTemplate": "/api/batch-signer/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/ticket/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6050
        }
      ],
      "UpstreamPathTemplate": "/api/ticket/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    ////ky tich hop tvan minvoice
    {
      "DownstreamPathTemplate": "/api/sign/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6054
        }
      ],
      "UpstreamPathTemplate": "/api/sign/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/purchaseinvoice/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6023
        }
      ],
      "UpstreamPathTemplate": "/api/purchaseinvoice/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/purchaseinvoicemediafile/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6024
        }
      ],
      "UpstreamPathTemplate": "/api/purchaseinvoicemediafile/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    },
    {
      "DownstreamPathTemplate": "/api/invoiceapi02/{everything}",
      "DownstreamScheme": "http",
      "DownstreamHostAndPorts": [
        {
          "Host": "localhost",
          "Port": 6038
        }
      ],
      "UpstreamPathTemplate": "/api/invoiceapi02/{everything}",
      "UpstreamHttpMethod": [ "Put", "Delete", "Get", "Post" ],
      "UpstreamHeaderTransform": {
        "X-Forwarded-For": "{RemoteIpAddress}"
      }
    }
  ],
  "GlobalConfiguration": {
    "BaseUrl": "https://localhost:6789"
  }
}