using System;
using System.Threading.Tasks;
using Xunit;
using Moq;
using Vcb.Catalog.Application.CustomerVCB;
using Vcb.Catalog.Application.CustomerVCB.Dto;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using Core.Domain.Repositories;
using Core.Shared.Factory;
using Core.Application.Services;
using Core;
using Core.Application.Dtos;

public class CustomerVCBServiceTests
{
    private readonly Mock<IAppFactory> _appFactoryMock;
    private readonly Mock<IRepository<CustomerVCBEntity, long>> _repoMock;
    private readonly CustomerVCBService _service;

    public CustomerVCBServiceTests()
    {
        _appFactoryMock = new Mock<IAppFactory>();
        _repoMock = new Mock<IRepository<CustomerVCBEntity, long>>();
        // Setup tenant and user
        var tenant = new Mock<ITenant>();
        tenant.Setup(t => t.Id).Returns(new TenantId(1));
        var user = new Mock<IUser>();
        user.Setup(u => u.Id).Returns(new UserId(2));
        var uow = new Mock<IUnitOfWork>();
        _appFactoryMock.Setup(f => f.CurrentTenant).Returns(tenant.Object);
        _appFactoryMock.Setup(f => f.CurrentUser).Returns(user.Object);
        _appFactoryMock.Setup(f => f.CurrentUnitOfWork).Returns(uow.Object);

        // Use partial mock for base.CreateAsync
        _service = new CustomerVCBService(_appFactoryMock.Object, _repoMock.Object);
    }

    [Fact]
    public async Task CreateAsync_ShouldThrow_WhenDuplicatedCIFAndSTK_EmptyStk()
    {
        var input = new CustomerVCBDto { Cif = "CIF001", Stk = "" };
        _repoMock.Setup(r => r.AnyAsync(It.IsAny<Func<CustomerVCBEntity, bool>>()))
            .ReturnsAsync(true);

        var ex = await Assert.ThrowsAsync<UserFriendlyException>(() => _service.CreateAsync(input));
        Assert.Contains("DuplicatedCIFAndSTK", ex.Message);
    }

    [Fact]
    public async Task CreateAsync_ShouldThrow_WhenDuplicatedCIFAndSTK_WithStk()
    {
        var input = new CustomerVCBDto { Cif = "CIF001", Stk = "STK001" };
        _repoMock.Setup(r => r.AnyAsync(It.IsAny<Func<CustomerVCBEntity, bool>>()))
            .ReturnsAsync(true);

        var ex = await Assert.ThrowsAsync<UserFriendlyException>(() => _service.CreateAsync(input));
        Assert.Contains("DuplicatedCIFAndSTK", ex.Message);
    }

    [Fact]
    public async Task CreateAsync_ShouldSetFieldsAndCallBase_WhenNoDuplicate()
    {
        var input = new CustomerVCBDto { Cif = "CIF002", Stk = "STK002" };
        _repoMock.Setup(r => r.AnyAsync(It.IsAny<Func<CustomerVCBEntity, bool>>()))
            .ReturnsAsync(false);

        var expectedDto = new CustomerVCBDto();
        var serviceMock = new Mock<CustomerVCBService>(_appFactoryMock.Object, _repoMock.Object) { CallBase = true };
        serviceMock.Setup(s => s.CreateAsync(It.IsAny<CustomerVCBDto>()))
            .CallBase();
        serviceMock.Protected()
            .Setup<Task<CustomerVCBDto>>("CreateAsync", ItExpr.IsAny<CustomerVCBDto>())
            .ReturnsAsync(expectedDto);

        var result = await serviceMock.Object.CreateAsync(input);

        Assert.Equal(expectedDto, result);
        Assert.Equal(CustomerVCBStatus.WaitApprove, input.Status);
        Assert.NotNull(input.CreationTime);
        Assert.Equal(2, input.CreatorId);
        Assert.Equal(1, input.TenantId);
    }
}
