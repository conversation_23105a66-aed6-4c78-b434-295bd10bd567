using System;
using System.Threading.Tasks;
using Xunit;
using Moq;
using Vcb.Catalog.Application.CustomerVCB;
using Vcb.Catalog.Application.CustomerVCB.Dto;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using Core.Domain.Repositories;
using Core.Shared.Factory;
using Core.Application.Services;
using Core;
using Core.Application.Dtos;
using Core.Shared.Constants;
using System.Linq.Expressions;
using Microsoft.Extensions.Localization;
using Core.Localization.Resources.AbpLocalization;

namespace Vcb.Catalog.Application.Tests.CustomerVCB
{
    public class CustomerVCBServiceTests
    {
        private readonly Mock<IAppFactory> _appFactoryMock;
        private readonly Mock<IRepository<CustomerVCBEntity, long>> _repoMock;
        private readonly Mock<ITenant> _tenantMock;
        private readonly Mock<IUser> _userMock;
        private readonly Mock<IUnitOfWork> _uowMock;
        private readonly Mock<IStringLocalizer<CoreLocalizationResource>> _localizerMock;
        private readonly CustomerVCBService _service;
        private readonly Guid _testTenantId = Guid.NewGuid();
        private readonly Guid _testUserId = Guid.NewGuid();

        public CustomerVCBServiceTests()
        {
            _appFactoryMock = new Mock<IAppFactory>();
            _repoMock = new Mock<IRepository<CustomerVCBEntity, long>>();
            _tenantMock = new Mock<ITenant>();
            _userMock = new Mock<IUser>();
            _uowMock = new Mock<IUnitOfWork>();
            _localizerMock = new Mock<IStringLocalizer<CoreLocalizationResource>>();

            // Setup tenant and user
            _tenantMock.Setup(t => t.Id).Returns(_testTenantId);
            _userMock.Setup(u => u.Id).Returns(_testUserId);

            _appFactoryMock.Setup(f => f.CurrentTenant).Returns(_tenantMock.Object);
            _appFactoryMock.Setup(f => f.CurrentUser).Returns(_userMock.Object);
            _appFactoryMock.Setup(f => f.CurrentUnitOfWork).Returns(_uowMock.Object);

            _service = new CustomerVCBService(_appFactoryMock.Object, _repoMock.Object);
        }

        #region CreateAsync Tests

        [Fact]
        public async Task CreateAsync_WithValidInput_ShouldCreateSuccessfully()
        {
            // Arrange
            var input = new CustomerVCBDto
            {
                Cif = "CIF001",
                Stk = "STK001",
                CustomerName = "Test Customer",
                Address = "Test Address",
                CustomerType = CustomerVCBType.Normal
            };

            var expectedResult = new CustomerVCBDto
            {
                Id = 1,
                Cif = input.Cif,
                Stk = input.Stk,
                CustomerName = input.CustomerName,
                Address = input.Address,
                Status = CustomerVCBStatus.WaitApprove,
                CreatorId = _testUserId,
                TenantId = _testTenantId
            };

            _repoMock.Setup(r => r.AnyAsync(It.IsAny<Expression<Func<CustomerVCBEntity, bool>>>()))
                .ReturnsAsync(false);

            // Mock base.CreateAsync behavior
            var serviceMock = new Mock<CustomerVCBService>(_appFactoryMock.Object, _repoMock.Object) { CallBase = true };
            serviceMock.Protected()
                .Setup<Task<CustomerVCBDto>>("CreateAsync", ItExpr.IsAny<CustomerVCBDto>())
                .ReturnsAsync(expectedResult);

            // Act
            var result = await serviceMock.Object.CreateAsync(input);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(CustomerVCBStatus.WaitApprove, input.Status);
            Assert.Equal(_testUserId, input.CreatorId);
            Assert.Equal(_testTenantId, input.TenantId);
            Assert.True(input.CreationTime > DateTime.MinValue);

            _repoMock.Verify(r => r.AnyAsync(It.IsAny<Expression<Func<CustomerVCBEntity, bool>>>()), Times.Once);
        }

        [Fact]
        public async Task CreateAsync_WithNullInput_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            await Assert.ThrowsAsync<ArgumentNullException>(() => _service.CreateAsync(null));
        }

        [Fact]
        public async Task CreateAsync_WithDuplicatedCifAndEmptyStk_ShouldThrowUserFriendlyException()
        {
            // Arrange
            var input = new CustomerVCBDto
            {
                Cif = "CIF001",
                Stk = "",
                CustomerName = "Test Customer",
                Address = "Test Address"
            };

            _repoMock.Setup(r => r.AnyAsync(It.IsAny<Expression<Func<CustomerVCBEntity, bool>>>()))
                .ReturnsAsync(true);

            // Act & Assert
            var ex = await Assert.ThrowsAsync<UserFriendlyException>(() => _service.CreateAsync(input));
            Assert.Contains("DuplicatedCIFAndSTK", ex.Message);

            _repoMock.Verify(r => r.AnyAsync(It.Is<Expression<Func<CustomerVCBEntity, bool>>>(
                expr => expr.ToString().Contains("Cif") && expr.ToString().Contains("Stk"))), Times.Once);
        }

        [Fact]
        public async Task CreateAsync_WithDuplicatedCifAndStk_ShouldThrowUserFriendlyException()
        {
            // Arrange
            var input = new CustomerVCBDto
            {
                Cif = "CIF001",
                Stk = "STK001",
                CustomerName = "Test Customer",
                Address = "Test Address"
            };

            _repoMock.Setup(r => r.AnyAsync(It.IsAny<Expression<Func<CustomerVCBEntity, bool>>>()))
                .ReturnsAsync(true);

            // Act & Assert
            var ex = await Assert.ThrowsAsync<UserFriendlyException>(() => _service.CreateAsync(input));
            Assert.Contains("DuplicatedCIFAndSTK", ex.Message);

            _repoMock.Verify(r => r.AnyAsync(It.Is<Expression<Func<CustomerVCBEntity, bool>>>(
                expr => expr.ToString().Contains("Cif") && expr.ToString().Contains("Stk"))), Times.Once);
        }

        [Fact]
        public async Task CreateAsync_WithNullStk_ShouldCheckForDuplicateCorrectly()
        {
            // Arrange
            var input = new CustomerVCBDto
            {
                Cif = "CIF001",
                Stk = null,
                CustomerName = "Test Customer",
                Address = "Test Address"
            };

            var expectedResult = new CustomerVCBDto { Id = 1 };

            _repoMock.Setup(r => r.AnyAsync(It.IsAny<Expression<Func<CustomerVCBEntity, bool>>>()))
                .ReturnsAsync(false);

            var serviceMock = new Mock<CustomerVCBService>(_appFactoryMock.Object, _repoMock.Object) { CallBase = true };
            serviceMock.Protected()
                .Setup<Task<CustomerVCBDto>>("CreateAsync", ItExpr.IsAny<CustomerVCBDto>())
                .ReturnsAsync(expectedResult);

            // Act
            var result = await serviceMock.Object.CreateAsync(input);

            // Assert
            Assert.NotNull(result);
            _repoMock.Verify(r => r.AnyAsync(It.IsAny<Expression<Func<CustomerVCBEntity, bool>>>()), Times.Once);
        }

        [Fact]
        public async Task CreateAsync_ShouldSetCorrectAuditFields()
        {
            // Arrange
            var input = new CustomerVCBDto
            {
                Cif = "CIF001",
                Stk = "STK001",
                CustomerName = "Test Customer",
                Address = "Test Address"
            };

            var beforeCreationTime = DateTime.Now.AddSeconds(-1);

            _repoMock.Setup(r => r.AnyAsync(It.IsAny<Expression<Func<CustomerVCBEntity, bool>>>()))
                .ReturnsAsync(false);

            var serviceMock = new Mock<CustomerVCBService>(_appFactoryMock.Object, _repoMock.Object) { CallBase = true };
            serviceMock.Protected()
                .Setup<Task<CustomerVCBDto>>("CreateAsync", ItExpr.IsAny<CustomerVCBDto>())
                .ReturnsAsync(new CustomerVCBDto { Id = 1 });

            // Act
            await serviceMock.Object.CreateAsync(input);

            // Assert
            Assert.Equal(CustomerVCBStatus.WaitApprove, input.Status);
            Assert.Equal(_testUserId, input.CreatorId);
            Assert.Equal(_testTenantId, input.TenantId);
            Assert.True(input.CreationTime > beforeCreationTime);
            Assert.True(input.CreationTime <= DateTime.Now.AddSeconds(1));
        }

        [Fact]
        public async Task CreateAsync_WhenRepositoryThrowsException_ShouldPropagateException()
        {
            // Arrange
            var input = new CustomerVCBDto
            {
                Cif = "CIF001",
                Stk = "STK001",
                CustomerName = "Test Customer",
                Address = "Test Address"
            };

            _repoMock.Setup(r => r.AnyAsync(It.IsAny<Expression<Func<CustomerVCBEntity, bool>>>()))
                .ThrowsAsync(new InvalidOperationException("Database error"));

            // Act & Assert
            var ex = await Assert.ThrowsAsync<InvalidOperationException>(() => _service.CreateAsync(input));
            Assert.Equal("Database error", ex.Message);
        }

        [Fact]
        public async Task CreateAsync_ShouldCallCurrentTenantAndCurrentUser()
        {
            // Arrange
            var input = new CustomerVCBDto
            {
                Cif = "CIF001",
                Stk = "STK001",
                CustomerName = "Test Customer",
                Address = "Test Address"
            };

            _repoMock.Setup(r => r.AnyAsync(It.IsAny<Expression<Func<CustomerVCBEntity, bool>>>()))
                .ReturnsAsync(false);

            var serviceMock = new Mock<CustomerVCBService>(_appFactoryMock.Object, _repoMock.Object) { CallBase = true };
            serviceMock.Protected()
                .Setup<Task<CustomerVCBDto>>("CreateAsync", ItExpr.IsAny<CustomerVCBDto>())
                .ReturnsAsync(new CustomerVCBDto { Id = 1 });

            // Act
            await serviceMock.Object.CreateAsync(input);

            // Assert
            _appFactoryMock.Verify(f => f.CurrentTenant, Times.AtLeastOnce);
            _appFactoryMock.Verify(f => f.CurrentUser, Times.AtLeastOnce);
        }

        #endregion

        #region CreateAsync - Dependency Verification Tests

        [Fact]
        public async Task CreateAsync_ShouldCallCurrentTenantAndCurrentUser()
        {
            // Arrange
            var input = new CustomerVCBDto
            {
                Cif = "CIF001",
                Stk = "STK001",
                CustomerName = "Test Customer",
                Address = "Test Address"
            };

            _repoMock.Setup(r => r.AnyAsync(It.IsAny<Expression<Func<CustomerVCBEntity, bool>>>()))
                .ReturnsAsync(false);

            var serviceMock = new Mock<CustomerVCBService>(_appFactoryMock.Object, _repoMock.Object) { CallBase = true };
            serviceMock.Protected()
                .Setup<Task<CustomerVCBDto>>("CreateAsync", ItExpr.IsAny<CustomerVCBDto>())
                .ReturnsAsync(new CustomerVCBDto { Id = 1 });

            // Act
            await serviceMock.Object.CreateAsync(input);

            // Assert
            _appFactoryMock.Verify(f => f.CurrentTenant, Times.AtLeastOnce);
            _appFactoryMock.Verify(f => f.CurrentUser, Times.AtLeastOnce);
        }

        [Fact]
        public async Task CreateAsync_ShouldSetCorrectAuditFields()
        {
            // Arrange
            var input = new CustomerVCBDto
            {
                Cif = "CIF001",
                Stk = "STK001",
                CustomerName = "Test Customer",
                Address = "Test Address"
            };

            var beforeCreationTime = DateTime.Now.AddSeconds(-1);

            _repoMock.Setup(r => r.AnyAsync(It.IsAny<Expression<Func<CustomerVCBEntity, bool>>>()))
                .ReturnsAsync(false);

            var serviceMock = new Mock<CustomerVCBService>(_appFactoryMock.Object, _repoMock.Object) { CallBase = true };
            serviceMock.Protected()
                .Setup<Task<CustomerVCBDto>>("CreateAsync", ItExpr.IsAny<CustomerVCBDto>())
                .ReturnsAsync(new CustomerVCBDto { Id = 1 });

            // Act
            await serviceMock.Object.CreateAsync(input);

            // Assert
            Assert.Equal(CustomerVCBStatus.WaitApprove, input.Status);
            Assert.Equal(_testUserId, input.CreatorId);
            Assert.Equal(_testTenantId, input.TenantId);
            Assert.True(input.CreationTime > beforeCreationTime);
            Assert.True(input.CreationTime <= DateTime.Now.AddSeconds(1));
        }

        [Fact]
        public async Task CreateAsync_ShouldReturnResultFromBaseCreateAsync()
        {
            // Arrange
            var input = new CustomerVCBDto
            {
                Cif = "CIF001",
                Stk = "STK001",
                CustomerName = "Test Customer",
                Address = "Test Address"
            };

            var expectedResult = new CustomerVCBDto
            {
                Id = 123,
                Cif = input.Cif,
                Stk = input.Stk,
                CustomerName = input.CustomerName,
                Address = input.Address,
                Status = CustomerVCBStatus.WaitApprove
            };

            _repoMock.Setup(r => r.AnyAsync(It.IsAny<Expression<Func<CustomerVCBEntity, bool>>>()))
                .ReturnsAsync(false);

            var serviceMock = new Mock<CustomerVCBService>(_appFactoryMock.Object, _repoMock.Object) { CallBase = true };
            serviceMock.Protected()
                .Setup<Task<CustomerVCBDto>>("CreateAsync", ItExpr.IsAny<CustomerVCBDto>())
                .ReturnsAsync(expectedResult);

            // Act
            var result = await serviceMock.Object.CreateAsync(input);

            // Assert
            Assert.Equal(expectedResult, result);
            Assert.Equal(123, result.Id);
        }

        #endregion
    }
}
