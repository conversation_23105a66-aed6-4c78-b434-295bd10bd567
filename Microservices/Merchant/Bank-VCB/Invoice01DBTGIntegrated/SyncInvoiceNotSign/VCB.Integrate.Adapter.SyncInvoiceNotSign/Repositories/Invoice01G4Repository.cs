using Core.Data;
using Core.Domain.Repositories;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Uow;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using VCB.Integrate.Adapter.SyncInvoiceNotSign.Interfaces;
using VCB.Integrate.Adapter.SyncInvoiceNotSign.Services;
using VnisCore.Core.MongoDB.Base;
using VnisCore.Core.MongoDB.Entities.Invoices.Invoice01;
using VnisCore.Core.MongoDB.Entities.Invoices.Invoice01.InvoiceNotSigned;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;

namespace VCB.Integrate.Adapter.SyncInvoiceNotSign.Repositories
{

    public class Invoice01G4Repository : IInvoice01Repository<MongoInvoice01HeaderG4NotSigned, MongoG4Invoice01Detail>
    {
        private readonly IAppFactory _appFactory;
        private readonly IRepository<Invoice01HeaderEntity, long> _repoInvoice01Header;
        private readonly IRepository<Invoice01DetailEntity, long> _repoInvoice01Detail;
        private readonly IRepository<Invoice01TransactionDetailEntity, long> _repoInvoice01TransactionDetail;
        private readonly IRepository<Invoice01TaxBreakdownEntity, long> _repoInvoice01TaxBreakdown;
        private readonly IRepository<Invoice01ReferenceEntity, long> _repoInvoiceReference;
        private readonly IInvoice01ExtraPropertyService _invoice01ExtraPropertyService;
        private readonly IRepository<Invoice01HeaderExtraPropertyEntity, long> _repoInvoice01HeaderExtraProperty;
        private readonly IRepository<Invoice01DetailExtraPropertyEntity, long> _repoInvoice01DetailExtraProperty;

        public Invoice01G4Repository(
            IAppFactory appFactory,
            IRepository<Invoice01HeaderEntity, long> repoInvoice01Header,
            IRepository<Invoice01DetailEntity, long> repoInvoice01Detail,
            IRepository<Invoice01TransactionDetailEntity, long> repoInvoice01TransactionDetail,
            IRepository<Invoice01TaxBreakdownEntity, long> repoInvoice01TaxBreakdown,
            IRepository<Invoice01ReferenceEntity, long> repoInvoiceReference,
            IInvoice01ExtraPropertyService invoice01ExtraPropertyService,
            IRepository<Invoice01HeaderExtraPropertyEntity, long> repoInvoice01HeaderExtraProperty,
            IRepository<Invoice01DetailExtraPropertyEntity, long> repoInvoice01DetailExtraProperty

        )
        {
            _appFactory = appFactory;
            _repoInvoice01Header = repoInvoice01Header;
            _repoInvoice01Detail = repoInvoice01Detail;
            _repoInvoice01TransactionDetail = repoInvoice01TransactionDetail;
            _repoInvoice01TaxBreakdown = repoInvoice01TaxBreakdown;
            _repoInvoiceReference = repoInvoiceReference;
            _invoice01ExtraPropertyService = invoice01ExtraPropertyService;
            _repoInvoice01HeaderExtraProperty = repoInvoice01HeaderExtraProperty;
            _repoInvoice01DetailExtraProperty = repoInvoice01DetailExtraProperty;
        }

        public async Task CreateBatchAsync(List<MongoInvoice01HeaderG4NotSigned> invoiceNotSigns)
        {
            using var transaction = _appFactory.UnitOfWorkManager.Begin(new AbpUnitOfWorkOptions
            {
                IsolationLevel = IsolationLevel.ReadCommitted, // INSERT NO LOCK
                IsTransactional = true
            }, true);

            var headers = new List<Invoice01HeaderEntity>();
            var details = new List<Invoice01DetailEntity>();
            var taxBreakdowns = new List<Invoice01TaxBreakdownEntity>();
            var transactionDetails = new List<Invoice01TransactionDetailEntity>();
            var invoiceReferences = new List<Invoice01ReferenceEntity>();
            var headerExtraProperties = new List<Invoice01HeaderExtraPropertyEntity>();
            var detailExtraProperties = new List<Invoice01DetailExtraPropertyEntity>();

            foreach (var model in invoiceNotSigns)
            {
                var header = MappingToHeaderEntity(model);
                headers.Add(header);

                var lstDetail = MappingToDetailEntity(model);
                details.AddRange(lstDetail);

                var lstTaxBreakdown = MappingToTaxBreakdownEntity(model.InvoiceTaxBreakdown);
                taxBreakdowns.AddRange(lstTaxBreakdown);

                var invoiceTransactionDetails = MappingToTransactionDetailEntity(model.InvoiceTransactionDetails);
                transactionDetails.AddRange(invoiceTransactionDetails);

                if (model.InvoiceReference != null)
                {
                    invoiceReferences.Add(new Invoice01ReferenceEntity
                    {
                        InvoiceHeaderId = model.InvoiceReference.InvoiceHeaderId,
                        InvoiceReferenceId = model.InvoiceReference.InvoiceReferenceId,
                        TemplateNoReference = model.InvoiceReference.TemplateNoReference,
                        SerialNoReference = model.InvoiceReference.SerialNoReference,
                        InvoiceNoReference = model.InvoiceReference.InvoiceNoReference,
                        NumberReference = model.InvoiceReference.NumberReference,
                        InvoiceDateReference = model.InvoiceReference.InvoiceDateReference.ToLocalTime(),
                        InvoiceStatus = model.InvoiceReference.InvoiceStatus,
                        Partition = model.InvoiceReference.Partition,
                        Note = model.InvoiceReference.Note,
                        TenantId = model.InvoiceReference.TenantId,
                        CreationTime = model.InvoiceReference.CreationTime,
                        CreatorId = model.InvoiceReference.CreatorId,
                    });

                    //thêm reference từ hóa đơn hiện tại sang hóa đơn lquan
                    invoiceReferences.Add(new Invoice01ReferenceEntity
                    {
                        InvoiceHeaderId = model.InvoiceReference.InvoiceReferenceId,
                        InvoiceReferenceId = model.InvoiceReference.InvoiceHeaderId,
                        TemplateNoReference = model.TemplateNo,
                        SerialNoReference = model.SerialNo,
                        InvoiceNoReference = model.InvoiceNo,
                        NumberReference = model.Number.Value,
                        InvoiceDateReference = model.InvoiceDate.ToLocalTime(),
                        InvoiceStatus = GetInvoiceReferenceStatus(model.InvoiceStatus),
                        Partition = model.InvoiceReference.Partition, //TODO: thuvt check lại giá trị này ở hóa đơn có lquan
                        Note = model.InvoiceReference.Note,
                        TenantId = model.InvoiceReference.TenantId,
                        CreationTime = DateTime.Now,
                        CreatorId = model.InvoiceReference.CreatorId,
                    });
                }


                // TODO: headerExtraProperties
                var inputHeaderExtraProperty = new Tuple<ExtraPropertyDictionary, long, DateTime>(model.ExtraProperties, model.InvoiceHeaderId, model.InvoiceDate);
                var headerExtraPropertity = _invoice01ExtraPropertyService
                    .MappingToInvoice01HeaderExtraPropertyEntities(inputHeaderExtraProperty);
                if (headerExtraPropertity.Any())
                {
                    headerExtraProperties.AddRange(headerExtraPropertity);
                }

                // TODO: detailExtraProperties
                var inputDetailExtraProperties = model.InvoiceDetail
                    .Select(p =>
                        new Tuple<ExtraPropertyDictionary, long, DateTime>(p.ExtraProperties, p.InvoiceDetailId, model.InvoiceDate)
                    )
                    .ToList();
                var detailExtraPropertity = _invoice01ExtraPropertyService
                    .MappingToInvoice01DetailExtraPropertyEntities(inputDetailExtraProperties);
                if (detailExtraPropertity.Any())
                {
                    detailExtraProperties.AddRange(detailExtraPropertity);
                }
            }

            await _repoInvoice01Header.InsertManyAsync(headers);

            await _repoInvoice01Detail.InsertManyAsync(details);

            if (taxBreakdowns.Any())
            {
                await _repoInvoice01TaxBreakdown.InsertManyAsync(taxBreakdowns);
            }

            if (transactionDetails.Any())
            {
                await _repoInvoice01TransactionDetail.InsertManyAsync(transactionDetails);
            }

            if (invoiceReferences.Any())
            {
                await _repoInvoiceReference.InsertManyAsync(invoiceReferences);
            }

            if (headerExtraProperties.Any())
            {
                await _repoInvoice01HeaderExtraProperty.InsertManyAsync(headerExtraProperties);
            }

            if (detailExtraProperties.Any())
            {
                await _repoInvoice01DetailExtraProperty.InsertManyAsync(detailExtraProperties);
            }


            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            await transaction.CompleteAsync();
        }

        private int GetInvoiceReferenceStatus(short invoiceStatus)
        {
            switch (invoiceStatus)
            {
                case (short)InvoiceStatus.DieuChinhDinhDanh:
                    return InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode();
                case (short)InvoiceStatus.DieuChinhTangGiam:
                    return InvoiceStatus.BiDieuChinhTangGiam.GetHashCode();
                case (short)InvoiceStatus.ThayThe:
                    return InvoiceStatus.BiThayThe.GetHashCode();
                default:
                    return InvoiceStatus.BiDieuChinhTangGiam.GetHashCode();
            }
        }

        private Invoice01HeaderEntity MappingToHeaderEntity(MongoInvoice01HeaderG4NotSigned model)
        {
            var result = new Invoice01HeaderEntity();

            //Header
            result.Id = model.InvoiceHeaderId;
            result.TenantId = model.TenantId;
            result.CreationTime = model.CreationTime.ToLocalTime();
            result.CreatorId = model.CreatorId;
            result.Source = model.Source;
            result.BatchId = model.BatchId;
            result.ErpId = model.ErpId;
            result.CreatorErp = model.CreatorErp;
            result.TransactionId = model.TransactionId;
            result.InvoiceTemplateId = model.InvoiceTemplateId;
            result.TemplateNo = model.TemplateNo;
            result.SerialNo = model.SerialNo;
            result.Note = model.Note;
            result.InvoiceDate = model.InvoiceDate.ToLocalTime();
            result.InvoiceStatus = model.InvoiceStatus;
            result.SignStatus = model.SignStatus;
            result.ApproveStatus = model.ApproveStatus;
            result.ApproveCancelStatus = model.ApproveCancelStatus;
            result.ApproveDeleteStatus = model.ApproveDeleteStatus;
            result.RegistrationHeaderId = model.RegistrationHeaderId;
            result.RegistrationDetailId = model.RegistrationDetailId;
            result.Number = model.Number;
            result.InvoiceNo = model.InvoiceNo;
            result.SellerId = model.SellerId;
            result.SellerCode = model.SellerCode;
            result.SellerTaxCode = model.SellerTaxCode;
            result.SellerAddressLine = model.SellerAddressLine;
            result.SellerCountryCode = model.SellerCountryCode;
            result.SellerDistrictName = model.SellerDistrictName;
            result.SellerCityName = model.SellerCityName;
            result.SellerPhoneNumber = model.SellerPhoneNumber;
            result.SellerFaxNumber = model.SellerFaxNumber;
            result.SellerEmail = model.SellerEmail;
            result.SellerBankName = model.SellerBankName;
            result.SellerBankAccount = model.SellerBankAccount;
            result.SellerLegalName = model.SellerLegalName;
            result.SellerFullName = model.SellerFullName;
            result.RoundingCurrency = model.RoundingCurrency;
            result.FromCurrency = model.FromCurrency;
            result.CurrencyConversion = model.CurrencyConversion;
            result.ToCurrency = model.ToCurrency;
            result.ExchangeRate = model.ExchangeRate;
            result.PaymentMethod = model.PaymentMethod;
            result.PaymentDate = model.PaymentDate.Value.ToLocalTime();
            result.PaymentAmountWords = model.PaymentAmountWords;
            result.PaymentAmountWordsEn = model.PaymentAmountWordsEn;
            result.TotalAmount = model.TotalAmount;
            result.TotalPaymentAmount = model.TotalPaymentAmount;
            result.FullNameCreator = model.FullNameCreator;
            result.UserNameCreator = model.UserNameCreator;
            result.BuyerId = model.BuyerId;
            result.BuyerType = model.BuyerType;
            result.BuyerCode = model.BuyerCode;
            result.BuyerFullName = model.BuyerFullName;
            result.BuyerLegalName = model.BuyerLegalName;
            result.BuyerTaxCode = model.BuyerTaxCode;
            result.BuyerAddressLine = model.BuyerAddressLine;
            result.BuyerDistrictName = model.BuyerDistrictName;
            result.BuyerCityName = model.BuyerCityName;
            result.BuyerCountryCode = model.BuyerCountryCode;
            result.BuyerPhoneNumber = model.BuyerPhoneNumber;
            result.BuyerFaxNumber = model.BuyerFaxNumber;
            result.BuyerEmail = model.BuyerEmail;
            result.BuyerBankName = model.BuyerBankName;
            result.BuyerBankAccount = model.BuyerBankAccount;
            result.TotalDiscountAmountBeforeTax = model.TotalDiscountAmountBeforeTax;
            result.TotalDiscountAmountAfterTax = model.TotalDiscountAmountAfterTax;
            result.TotalDiscountPercentAfterTax = model.TotalDiscountPercentAfterTax;
            result.TotalVatAmount = model.TotalVatAmount;
            result.IsActive = model.IsActive;
            result.InvoiceDateYear = model.InvoiceDateYear;
            result.InvoiceDateQuater = model.InvoiceDateQuater;
            result.InvoiceDateMonth = model.InvoiceDateMonth;
            result.InvoiceDateWeek = model.InvoiceDateWeek;
            result.InvoiceDateNumber = model.InvoiceDateNumber;
            result.IsOpened = model.IsOpened;
            result.IsViewed = model.IsViewed;
            result.Partition = model.Partition;
            result.BudgetUnitCode = model.BudgetUnitCode;
            result.BuyerIDNumber = model.BuyerIDNumber;
            result.BuyerPassportNumber = model.BuyerPassportNumber;

            return result;
        }

        private List<Invoice01DetailEntity> MappingToDetailEntity(MongoInvoice01HeaderG4NotSigned header)
        {
            var modelDetails = header.InvoiceDetail;
            var details = new List<Invoice01DetailEntity>();
            var iDetail = 0;

            //Details
            foreach (var detail in modelDetails)
            {
                string productName = detail.ProductName.Trim();
                string subProductName = "";
                // Nếu hóa đơn là hóa đơn gốc thì thực hiện cập nhật lại productname
                if (header.InvoiceStatus == InvoiceStatus.Goc.GetHashCode())
                {
                    subProductName = $" tháng {header.InvoiceDate.ToLocalTime():MM/yyyy} đính kèm bảng kê số {header.InvoiceNo} ngày {header.InvoiceDate.ToLocalTime():dd/MM/yyyy}";
                }
                else
                {
                    var date = header.InvoiceDate.ToLocalTime();
                    if (header.InvoiceReference != null)
                    {
                        date = header.InvoiceReference.InvoiceDateReference.ToLocalTime();
                    }
                    subProductName = $" tháng {date:MM/yyyy}";
                }

                if (productName.Contains("{0}"))
                {
                    productName = string.Format(productName, subProductName);
                }
                else
                {
                    productName = detail.ProductName.Trim() + subProductName;
                }

                // VCB không quản lý Unit, Product, ProductType nên hard code
                details.Add(new Invoice01DetailEntity
                {
                    Id = detail.InvoiceDetailId,
                    Index = detail.Index,
                    TenantId = detail.TenantId,
                    InvoiceHeaderId = detail.InvoiceHeaderId,
                    Amount = detail.Amount,
                    DiscountAmountBeforeTax = detail.DiscountAmountBeforeTax,
                    DiscountPercentBeforeTax = detail.DiscountPercentBeforeTax,
                    Note = detail.Note,
                    PaymentAmount = detail.PaymentAmount,
                    ProductName = productName,
                    Quantity = detail.Quantity.GetValueOrDefault(),
                    UnitName = detail.UnitName,
                    UnitPrice = detail.UnitPrice.GetValueOrDefault(),
                    VatAmount = detail.VatAmount,
                    VatPercent = detail.VatPercent,
                    VatPercentDisplay = detail.VatPercentDisplay,
                    Partition = detail.Partition,
                    IsPromotion = detail.IsPromotion,
                    ProductType = detail.ProductType,
                    ProductId = detail.ProductId,
                    ProductCode = detail.ProductCode,
                    UnitId = detail.UnitId,
                    RoundingUnit = detail.RoundingUnit,
                    HideQuantity = detail.HideQuantity,
                    HideUnit = detail.HideUnit,
                    HideUnitPrice = detail.HideUnitPrice,

                    ExtraProperties = detail.ExtraProperties
                });

                iDetail++;
            }

            return details;
        }

        private List<Invoice01TaxBreakdownEntity> MappingToTaxBreakdownEntity(List<MongoBaseInvoice01TaxBreakdown> models)
        {
            var taxBreakdowns = new List<Invoice01TaxBreakdownEntity>();

            //TaxBreakdowns
            foreach (var item in models)
            {
                taxBreakdowns.Add(new Invoice01TaxBreakdownEntity
                {
                    InvoiceHeaderId = item.InvoiceHeaderId,
                    Name = item.Name,
                    TenantId = item.TenantId,
                    VatAmount = item.VatAmount,
                    VatAmountBackUp = item.VatAmountBackUp,
                    VatPercent = item.VatPercent,
                    VatPercentDisplay = item.VatPercentDisplay
                });
            }

            return taxBreakdowns;
        }

        private List<Invoice01TransactionDetailEntity> MappingToTransactionDetailEntity(List<MongoInvoice01TransactionDetail> invoiceTransactionDetails)
        {
            var transactionDetailEntities = new List<Invoice01TransactionDetailEntity>();

            //TaxBreakdowns
            foreach (var item in invoiceTransactionDetails)
            {
                transactionDetailEntities.Add(new Invoice01TransactionDetailEntity
                {
                    Index = item.Index,
                    InvoiceHeaderId = item.InvoiceHeaderId,
                    ProductType = item.ProductType,
                    Note = item.Note,
                    Amount = item.Amount,
                    PaymentAmount = item.PaymentAmount,
                    VatPercent = item.VatPercent,
                    VatAmount = item.VatAmount,
                    VatPercentDisplay = item.VatPercentDisplay,
                    GLNo = item.GLNo,

                    RefNo = item.RefNo,
                    CreatorErp = item.CreatorErp,
                    TellSeq = item.TellSeq,
                    Quantity = item.Quantity,
                    UnitPrice = item.UnitPrice,

                    BuyerBankAccount = item.BuyerBankAccount,
                    ProductName = item.ProductName,
                    UnitName = item.UnitName,
                    ExchangeRate = item.ExchangeRate,
                    ToCurrency = item.ToCurrency,
                    TransactionDate = item.TransactionDate,
                    Pctime = item.Pctime,
                    TenantId = item.TenantId,

                    RoundingUnit = item.RoundingUnit,
                    ProductId = item.ProductId,
                    ProductCode = item.ProductCode,
                    UnitId = item.UnitId,

                    Partition = item.Partition,
                });
            }

            return transactionDetailEntities;
        }
    }
}
