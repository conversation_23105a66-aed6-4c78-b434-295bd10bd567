{
  "ConnectionStrings": {
    "Default": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=INVOICE)))';User Id=VCB_INV_AUTH_STG;Password=VCB_INV_AUTH_STG",
    "VnisCoreOracle": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=INVOICE)))';User Id=VCB_INV_CORE_STG;Password=VCB_INV_CORE_STG",
    "VcbIntegratedOracle": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=INVOICE)))';User Id=VCB_INV_DBTG_STG;Password=VCB_INV_DBTG_STG",
    "VCB_INVOICE_AUDIT_LOGGING": "*******************************************************************************",
    "VCB_INVOICE_CORE": "*************************************************************"
  },
  "TimePeriod": 1, //second
  "IndexSlotAdapter": {
    "G123": 0,
    "G4": 0,
    "G5": 0
  },
  "Logging": {
    "Directory": "/home/<USER>/logs/sync-invoice-not-sign", // thư mục lưu file, nếu để trống thì sẽ lưu mặc định vào thư mục góc của service
    "OutPutTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] [{ServerName}] [{UserName}] [{CorrelationId}] [{ProcessId}] [{ThreadId}] [{ClassName}]  {Message:lj}{NewLine}{Exception}", // Pattern cấu trúc log
    "FileSizeLimitBytes": 104857600, // 100MB = 100 * 1024 * 1024
    "FlushToDiskInterval": "00:00:03", // tối đa 3 giây sẽ phải đầy vào disk
    "RollingInterval": "Day", // 1 day sẽ rolling file
    "RollOnFileSizeLimit": true, // rolling một file mới khi đạt giớ hạn kích thước
    "RetainedFileCountLimit": 31, // Giữ lại tối đa 31 file trong một ngày
    "Formatter": "txt" // Định dạng ghi log (txt | json), nếu để trống sẽ ghi log mặc định dạng "txt"
  }
}