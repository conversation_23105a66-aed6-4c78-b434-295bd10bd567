using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using VnisCore.RegistrationInvoice.Application.InvoiceTemplate.Interfaces;

namespace VnisCore.RegistrationInvoice.Application.InvoiceTemplate.Abstractions
{
    public class PreviewTemplateFactory : IPreviewTemplateFactory
    {
        private readonly IEnumerable<IPreviewTemplateService> _previewServices;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;

        public PreviewTemplateFactory(IEnumerable<IPreviewTemplateService> previewServices,
            IStringLocalizer<CoreLocalizationResource> localizer)
        {
            _previewServices = previewServices;
            _localizer = localizer;
        }

        public IPreviewTemplateService GetService(int index)
        {
            var name = $"PreviewTemplateInvoice{index:00}Service";
            var implement = _previewServices.FirstOrDefault(x => x.GetType().Name == name);
            if (implement == null)
                throw new Exception(_localizer["Vnis.BE.FileTemplate.NotFound"]);

            return implement;
        }
    }
}
