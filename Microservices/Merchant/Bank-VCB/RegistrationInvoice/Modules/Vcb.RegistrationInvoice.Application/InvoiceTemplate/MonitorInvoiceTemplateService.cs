using Core.Application.Services;
using Core.Domain.Repositories;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities;
using VnisCore.RegistrationInvoice.Application.InvoiceTemplate.Dto;
using VnisCore.RegistrationInvoice.Application.InvoiceTemplate.Interfaces;

namespace VnisCore.RegistrationInvoice.Application.InvoiceTemplate
{
    public class MonitorInvoiceTemplateService : CrudAppService<MonitorInvoiceTemplateEntity, MonitorInvoiceTemplateDto, long, InvoiceTemplatePagedRequestDto,
           MonitorInvoiceTemplateDto, MonitorInvoiceTemplateDto>,
        IMonitorInvoiceTemplateService
    {
        private readonly IRepository<MonitorInvoiceTemplateEntity, long> _monitorInvoiceTemplateRepository;
        public MonitorInvoiceTemplateService(IRepository<MonitorInvoiceTemplateEntity, long> monitorInvoiceTemplateRepository)
            : base(monitorInvoiceTemplateRepository)
        {
            _monitorInvoiceTemplateRepository = monitorInvoiceTemplateRepository;
        }

        public override Task<MonitorInvoiceTemplateDto> CreateAsync(MonitorInvoiceTemplateDto input)
        {
            return base.CreateAsync(input);
        }
    }
}