@using VnisCore.RegistrationInvoice.Application.NewRegistration.Dto
@using Core.Shared.Constants;
@using System;
@using System.Collections.Generic;
@using Core.Shared.Extensions
@using VnisCore.Core.Oracle.Domain.Entities.RegisterInvoice
@using VnisCore.RegistrationInvoice.Application.NewRegistration.Models;
@model PdfRegistrationModel
@{
    int itemsPerPageTable5 = 10;
    var DetailTable5 = Model.RegistrationDetails;
    List<ICollection<PdfRegistrationDetailModel>> pagesTable5 = new List<ICollection<PdfRegistrationDetailModel>>();
    int i;
    for (i = 0; i < (DetailTable5.Count() / itemsPerPageTable5) + 1; i++)
    {
        List<PdfRegistrationDetailModel> chunk = new List<PdfRegistrationDetailModel>();
        for (int j = i * itemsPerPageTable5; j < DetailTable5.Count() && j < itemsPerPageTable5 * (i + 1); j++)
        {
            chunk.Add(DetailTable5.ElementAt((j)));
        }
        pagesTable5.Add(chunk);
    }

    var invoiceServiceProviders = Model.RegistrationDetailExtensions.Where(x => x.Type == (short)NewRegistrationDetailExtensionType.InvoiceServiceProvider);
    var transmissionProviders = Model.RegistrationDetailExtensions.Where(x => x.Type == (short)NewRegistrationDetailExtensionType.TransmissionProvider);
    var dependentAccountingUnits = Model.RegistrationDetailExtensions.Where(x => x.Type == (short)NewRegistrationDetailExtensionType.DependentAccountingUnit);
    var pauseInvoiceUsages = Model.RegistrationDetailExtensions.Where(x => x.Type == (short)NewRegistrationDetailExtensionType.PauseInvoiceUsage);
    var registerInvoiceIntegrations = Model.RegistrationDetailExtensions.Where(x => x.Type == (short)NewRegistrationDetailExtensionType.RegisterInvoiceIntegration);
    var legalRepresentativeSex = EnumExtension.TryToEnum<SexConstants>(Convert.ToInt16(Model.LegalRepresentativeSex)).ToDisplayName();
}



<!DOCTYPE html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!--CSS-->
    <style>

        body, p, h1, h2, h3, h4, h5, h6, a, ul, li, table, tr, td {
            margin: 0;
            padding: 0;
            vertical-align: top;
            ;
        }

        .clear-both {
            clear: both;
        }
        /* Căn trái chữ */
        .cantrai {
            text-align: left;
        }

        /* Căn phải chữ */
        .canphai {
            text-align: right;
        }

        /* Căn giữa chữ */
        .cangiua {
            text-align: center;
        }

        /* Cho chữ chạy từ trái sang */
        .chaytrai {
            float: left;
        }

        /* Cho chữ chạy từ phải sang */
        .chayphai {
            float: right;
        }

        /* Viết hoa */
        .viethoa {
            text-transform: uppercase;
        }

        /* Chữ màu đỏ */
        .chudo {
            color: red;
        }

        /* Chữ màu xanh */
        .chuxanh {
            color: blue;
        }

        /* Chữ màu đen */
        .chuden {
            color: black
        }

        /* Chữ in nghiêng */
        .innghieng {
            font-style: italic;
        }

        /* Chữ in đậm */
        .indam {
            font-weight: bold;
        }

        /* Tiêu đề size 22*/
        .tieude-22 {
            font-size: 22px;
        }

        /* Tiêu đề size 24*/
        .tieude-24 {
            font-size: 24px;
        }

        /* Tiêu đề size 26*/
        .tieude-26 {
            font-size: 26px;
        }

        /* Tiêu đề size 20*/
        .tieude-20 {
            font-size: 20px;
        }

        .cachdong50 {
            padding-top: 50px;
        }
        /* tự đẩy xuống dưới 25px nếu dùng class này*/
        .cachdong25 {
            padding-top: 25px;
        }
        /* tự đẩy xuống dưới 5px nếu dùng class này*/
        .cachdong5 {
            padding-top: 5px;
        }
        /* tự đẩy xuống dưới 10px nếu dùng class này*/
        .cachdong10 {
            padding-top: 10px;
        }
        /* tự đẩy xuống dưới 20px nếu dùng class này*/
        .cachdong20 {
            padding-top: 20px;
        }

        hr.kengang {
            border: 0;
            clear: both;
            display: block;
            width: 98%;
            background-color: black;
            height: 1px;
        }

        /* font chữ chung; font-size chung; màu chữ chung của cả hóa đơn*/
        body {
            /*Font chữ*/
            font-family: Times, "Times New Roman", serif;
            /* size*/
            font-size: 16px;
            word-spacing: 1px;
            line-height: 26px;
            /* Màu chữ*/
            color: black;
        }
        /*Khung viền của hóa đơn, nếu hóa đơn bị nhảy trang,
                                                                        thử chỉnh lại phần height của chỗ này*/
        .khunghoadon {
            width: 1000px;
            height: 1414px;
            margin: 0 auto;
            padding: 0;
            position: relative;
        }

            .khunghoadon::before {
                content: '';
                background: url('');
                background-repeat: no-repeat;
                background-position: top left;
                background-size: 100%;
                width: 100%;
                height: 100%;
                opacity: 1;
                position: absolute;
            }

        .hoadon {
            width: 96%;
            margin: 0 auto;
            padding-top: 10px;
        }

            /*Logo chìm của hóa đơn*/
            .hoadon::before {
                content: '';
                background: url('');
                background-repeat: no-repeat;
                background-position: center 500px;
                background-size: 50%;
                width: 100%;
                height: 100%;
                opacity: 0.1;
                position: absolute;
            }

        .dongngang {
            width: 98%;
            margin: 0 auto;
        }

        .logo {
            width: 18%;
            padding-left: 15px;
        }

            .logo img {
                width: 140px;
            }

        .thongtinbenban {
            width: 83%;
            padding-left: 20px;
        }

        .cot1benban {
            width: 90px;
        }

        .mstbenban {
            letter-spacing: 5px;
        }

        .qrcode {
            width: 14%;
        }

            .qrcode img {
                width: 130px;
            }

        .tenhoadon {
            text-align: center;
            width: 50%;
            padding-left: 50px;
            padding-top: 10px;
        }

        .boso {
            width: 25%;
            float: right;
            padding-top: 10px;
        }

        .thongtinbenmua {
            padding-left: 20px;
        }

        .cot1benmua {
            width: 150px;
        }

        .bangchitiet {
            width: 98%;
            margin-left: 10px;
            border-collapse: collapse;
            border: 1px solid black;
        }

            .bangchitiet td, th {
                border: 1px solid black;
            }

        .nguoimuaban {
            width: 30%;
        }

        .nguoichuyendoi {
            width: 350px;
        }

        .khungky {
            color: green;
            font-size: 12px;
            line-height: 17px;
            border: 2px solid green;
            padding: 8px;
            margin-right: 10px;
        }

        .chantrang {
            position: fixed;
            width: 100%;
            bottom: 110px;
            font-size: 13px;
            line-height: 17px;
        }

        .cachtrai10 {
            padding-left: 10px;
        }

        .cachtrai50 {
            padding-left: 50px;
        }

        .cachtrai5 {
            padding-left: 5px;
        }
    </style>
</head>
<body>
    <div class="khunghoadon">
        <div class="hoadon">
            <div class="dongngang cachdong20">
                <div class="logo chaytrai">
                    <img src="">
                </div>
                <div class="tenhoadon chaytrai" style="padding-top: 50px">
                    <p><span class="indam viethoa tieude-22">CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM</span></p>
                    <p><span class="indam" style="font-size: 18px;padding-left: 10px 0px">Độc lập - Tự do - Hạnh phúc</span></p>
                    <div class="dongngang cangiua" style="width: 30%">
                        <hr class="kengang">
                    </div>
                    <p style="font-size: 18px;" class="indam">
                        <span>TỜ KHAI</span><br />
                        <span>Đăng ký/thay đổi thông tin sử dụng hóa đơn điện tử</span>
                    </p>
                </div>
                <div class="boso chayphai">
                    <p class="indam" style="font-size: 19px">
                        <span>Mẫu số</span><span>: </span>
                        <span>01ĐKTĐ/HĐĐT</span>
                    </p>
                </div>
            </div>
            <div class="clear-both"></div>
            <div class="dongngang" style="width: 10%">
                <hr class="kengang">
            </div>
            <div class="clear-both"></div>

            <div class="dongngang" style="width: 15%; margin: 0 auto;">
                <p style="float: left;">
                    @if (Model.Type.GetHashCode() == 1)
                    {
                    <p><input type="checkbox" checked><span>Đăng ký mới</span></p>
                    <p><input type="checkbox" name=""><span>Thay đổi thông tin</span></p>
                    }
                    else
                    {
                    <p><input type="checkbox" name=""><span>Đăng ký mới</span></p>
                    <p><input type="checkbox" name="" checked><span>Thay đổi thông tin</span></p>
                    }

                </p>
            </div>
            <div class="clear-both"></div>
            <div class="dongngang">
                <table class="bangchitiet">
                    <tr>
                        <td class="cachtrai10">Tên người nộp thuế:</td>
                        <td class="cachtrai10" colspan="2">@Model.FullName</td>
                    </tr>
                    <tr>
                        <td class="cachtrai10">Mã số thuế:</td>
                        <td class="cachtrai10" colspan="2">@Model.TaxCode</td>
                    </tr>
                    <tr>
                        <td class="cachtrai10">Cơ quan thuế quản lý:</td>
                        <td class="cachtrai10" colspan="2">@Model.TaxDepartment</td>
                    </tr>
                    <tr>
                        <td class="cachtrai10">Tên người đại diện pháp luật:</td>
                        <td class="cachtrai10" colspan="2">@Model.LegalRepresentativeName</td>
                    </tr>
                    <tr>
                        <td class="cachtrai10">Quốc tịch:</td>
                        <td class="cachtrai10" width="40%">@Model.LegalRepresentativeNationality</td>
                        <td width="40%">
                            <span>Số điện thoại người đại diện pháp luật:</span>
                            <span class="cachtrai10">@Model.LegalRepresentativePhone</span>
                        </td>
                    </tr>
                    <tr>
                        <td class="cachtrai10">Căn cước công dân (Số CC/CCCD/số định danh):</td>
                        <td class="cachtrai10" width="40%">@Model.LegalRepresentativeIDNumber</td>
                        <td width="40%">
                            <span>Số hộ chiếu:</span>
                            <span class="cachtrai10">@Model.LegalRepresentativePassportNumber</span>
                        </td>
                    </tr>
                    <tr>
                        <td class="cachtrai10">Ngày tháng năm sinh:</td>
                        <td class="cachtrai10" width="40%">@Model.LegalRepresentativeBirthday.ToString("dd/MM/yyyy")</td>
                        <td width="40%">
                            <span>Giới tính:</span>
                            <span class="cachtrai10">
                                <span class="cachtrai10">
                                    @legalRepresentativeSex
                                </span>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td class="cachtrai10">Người liên hệ:</td>
                        <td class="cachtrai10" width="40%">@Model.ContactName</td>
                        <td width="40%">
                            <span class="cachtrai10">Điện thoại liên hệ:</span>
                            <span class="cachtrai10">@Model.ContactPhone</span>
                        </td>
                    </tr>
                    <tr>
                        <td class="cachtrai10">Địa chỉ liên hệ:</td>
                        <td width="40%" class="cachtrai10">@Model.ContactAddress</td>
                        <td width="40%">
                            <span class="cachtrai10">Thư điện tử:</span>
                            <span class="cachtrai10">@Model.ContactEmail</span>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3" class="cachtrai10">Theo Nghị định số 70/2025/NĐ-CP ngày 20 tháng 3 năm 2025 của Chính phủ, chúng tôi/tôi thuộc đối tượng sử dụng chứng từ điện tử. Chúng tôi/tôi đăng ký/thay đổi thông tin đã đăng ký với cơ quan thuế về việc sử dụng chứng từ điện tử như sau:</td>
                    </tr>
                    <tr>
                        <td class="cachtrai10 indam" colspan="3">1. Hình thức hóa đơn:</td>
                    </tr>
                    <tr>
                        <td colspan="3" class="cachtrai50">
                            @if (Model.InvoiceHasCode)
                            {
                                <input type="checkbox" name="" checked>

                                <span> Có mã của cơ quan thuế</span>

                                <br />
                                <input type="checkbox" name=""> <span> Không có mã của cơ quan thuế</span>
                            }
                            else
                            {
                                <input type="checkbox" name="">

                                <span> Có mã của cơ quan thuế</span>

                                <br />
                                <input type="checkbox" name="" checked> <span> Không có mã của cơ quan thuế</span>
                            }
                        </td>
                    </tr>
                    <tr>
                        <td class="cachtrai10 indam" colspan="3">2. Hình thức gửi dữ liệu hóa đơn điện tử:</td>
                    </tr>
                    <tr>
                        <td class="cachtrai10" colspan="3">
                            <span>
                                @if (Model.SendInvoiceType.GetHashCode() == 1 || Model.SendInvoiceType.GetHashCode() == 2 || Model.SendInvoiceType.GetHashCode() == 5)
                                {
                                    <span>a. <input type="checkbox" name="" checked></span>
                                }
                                else
                                {
                                    <span> a. <input type="checkbox" name=""> </span>
                                }

                                <span> Trường hợp sử dụng hóa đơn điện tử có mã trên Cổng thông tin điện tử của Tổng cục Thuế hoặc tổ chức cung cấp dịch vụ hóa đơn điện tử được Tổng cục Thuế ủy thác:</span>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3" class="cachtrai50">
                            @if (Model.SendInvoiceType.GetHashCode() == 1)
                            {
                                <input type="checkbox" name="" checked>
                            }
                            else
                            {
                                <input type="checkbox" name="">
                            }
                            <span> Doanh nghiệp nhỏ và vừa, hợp tác xã, hộ, cá nhân kinh doanh tại địa bàn có điều kiện kinh tế xã hội khó khăn, địa bàn có điều kiện kinh tế xã hội đặc biệt khó khăn (điểm a khoản 1 Điều 14 của Nghị định).<br /></span>

                            @if (Model.SendInvoiceType.GetHashCode() == 2)
                            {
                                <input type="checkbox" name="" checked>
                            }
                            else
                            {
                                <input type="checkbox" name="">
                            }
                            <span> Doanh nghiệp nhỏ và vừa khác theo đề nghị của Ủy ban nhân dân tỉnh, thành phố trực thuộc trung ương gửi Bộ Tài chính trừ doanh nghiệp hoạt động tại các khu kinh tế, khu công nghiệp, khu công nghệ cao (điểm b khoản 1 Điều 14 của Nghị định).<br /></span>
                            @if (Model.SendInvoiceType.GetHashCode() == 5)
                            {
                                <input type="checkbox" name="" checked>
                            }
                            else
                            {
                                <input type="checkbox" name="">
                            }
                            <span>Cơ quan thuế hoặc cơ quan được giao nhiệm vụ tổ chức, xử lý tài sản công theo quy định pháp luật về quản lý, sử dụng tài sản công (khoản 11 điều 1 Nghị định 70)</span>
                        </td>
                    </tr>
                    <tr>
                        <td class="cachtrai10" colspan="3">
                            <span>
                                @if (Model.SendInvoiceType.GetHashCode() == 3 || Model.SendInvoiceType.GetHashCode() == 4 || Model.SendInvoiceType.GetHashCode() == 6)
                                {
                                    <span>b. <input type="checkbox" name="" checked> </span>
                                }
                                else
                                {
                                    <span>b. <input type="checkbox" name=""></span>
                                }
                                <span> Trường hợp sử dụng hóa đơn điện tử không có mã của cơ quan thuế:</span>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3" class="cachtrai50">
                            @if (Model.SendInvoiceType.GetHashCode() == 3)
                            {
                                <input type="checkbox" name="" checked>
                            }
                            else
                            {
                                <input type="checkbox" name="">
                            }
                            <span>Chuyển dữ liệu hóa đơn điện tử trực tiếp đến cơ quan thuế (điểm b1, khoản 3, Điều 22 của Nghị định 70).<br /></span>
                            @if (Model.SendInvoiceType.GetHashCode() == 4)
                            {
                                <input type="checkbox" name="" checked>
                            }
                            else
                            {
                                <input type="checkbox" name="">
                            }
                            <span>Thông qua tổ chức cung cấp dịch vụ hóa đơn điện tử (điểm b2, khoản 3, Điều 22 của Nghị Định 123 (đã được sửa đổi, bổ sung theo quy định tại Nghị định 70)).<br /></span>

                            @* 
                            @if (Model.SendInvoiceType.GetHashCode() == 6)
                            {
                                <input type="checkbox" name="" checked>
                            }
                            else
                            {
                                <input type="checkbox" name="">
                            }
                            <span>Nhà cung cấp ở nước ngoài không có cơ sở thường trú tại Việt Nam có hoạt động kinh doanh thương mại điện tử, kinh doanh trên nền tảng số và các dịch vụ khác đăng ký tự nguyện sử dụng hóa đơn điện tử thông qua Cổng thông tin điện tử dành cho nhà cung cấp ở nước ngoài không có cơ sở thường trú tại Việt Nam của Tổng cục Thuế (khoản 1 Điều 15 Nghị định 70)</span>
                            *@
                        </td>
                    </tr>
                    <tr>
                        <td class="cachtrai10 indam" colspan="3">3. Phương thức chuyển dữ liệu của hóa đơn điện tử:</td>
                    </tr>
                    <tr>
                        <td colspan="3" class="cachtrai50">
                            @if (Model.SendInvoiceMethod.GetHashCode() == 1)
                            {
                                <input type="checkbox" name="" checked>
                                <span>Chuyển đẩy đủ nội dung từng hóa đơn. <br /></span>

                                <input type="checkbox" name="">
                                <span>Chuyển theo bảng tổng hợp dữ liệu hoá đơn điện tử(điều a1. khoản 3, Điều 22 của Nghị định)</span>
                            }
                            else
                            {
                                <input type="checkbox" name="">

                                <span>Chuyển đẩy đủ nội dung từng hóa đơn. <br /></span>
                                <input type="checkbox" name="" checked> <span>Chuyển theo bảng tổng hợp dữ liệu hoá đơn điện tử(điều a1, khoản 3, Điều 22 của Nghị định)</span>
                            }
                        </td>
                    </tr>
                    <tr>
                        <td class="cachtrai10 indam" colspan="3">4. Loại hóa đơn sử dụng:</td>
                    </tr>
                    <tr>
                        <td colspan="3" class="cachtrai50">
                            @{
                                int loai1 = 0;
                                int loai2 = 0;
                                int loai3 = 0;
                                int loai4 = 0;
                                int loai5 = 0;
                                int loai6 = 0;
                                int loai7 = 0;
                                int loai8 = 0;
                                int loai9 = 0;

                                foreach (int j in Model.InvoiceTypes)
                                {
                                    switch (j)
                                    {
                                        case 1:
                                            loai1++;
                                            break;

                                        case 2:
                                            loai2++;
                                            break;

                                        case 3:
                                            loai3++;
                                            break;

                                        case 4:
                                            loai4++;
                                            break;

                                        case 5:
                                            loai5++;
                                            break;

                                        case 6:
                                            loai6++;
                                            break;

                                        case 7:
                                            loai7++;
                                            break;

                                        case 8:
                                            loai8++;
                                            break;

                                        case 9:
                                            loai9++;
                                            break;

                                    }
                                }
                            }

                            @if (loai1 != 0)
                            {
                                <input type="checkbox" name="" checked>

                                <span>Hóa đơn GTGT<br /></span>
                            }
                            else
                            {
                                <input type="checkbox" name="">

                                <span>Hóa đơn GTGT<br /></span>
                            }


                            @if (loai2 != 0)
                            {
                                <input type="checkbox" name="" checked>

                                <span>Hóa đơn bán hàng<br /></span>
                            }
                            else
                            {
                                <input type="checkbox" name="">

                                <span>Hóa đơn bán hàng<br /></span>
                            }


                            @if (loai3 != 0)
                            {
                                <input type="checkbox" name="" checked>

                                <span>Hóa đơn bán tài sản công<br /></span>
                            }
                            else
                            {
                                <input type="checkbox" name="">

                                <span>Hóa đơn bán tài sản công<br /></span>
                            }


                            @if (loai4 != 0)
                            {
                                <input type="checkbox" name="" checked> <span>Hóa đơn bán hàng dự trữ quốc gia<br /></span>
                            }
                            else
                            {
                                <input type="checkbox" name=""> <span>Hóa đơn bán hàng dự trữ quốc gia<br /></span>
                            }

                            @if (loai5 != 0)
                            {
                                <input type="checkbox" name="" checked> <span> Các loại hóa đơn khác<br /></span>
                            }
                            else
                            {
                                <input type="checkbox" name=""> <span>Các loại hóa đơn khác<br /></span>
                            }


                            @if (loai6 != 0)
                            {
                                <input type="checkbox" name="" checked> <span>Các chứng từ được in, phát hành, sử dụng và quản lý như hóa đơn<br /></span>
                            }
                            else
                            {
                                <input type="checkbox" name=""> <span>Các chứng từ được in, phát hành, sử dụng và quản lý như hóa đơn<br /></span>
                            }

                            @if (loai7 != 0)
                            {
                                <input type="checkbox" name="" checked> <span>Hóa đơn GTGT tích hợp biên lai thu thuế, phí, lệ phí<br /></span>
                            }
                            else
                            {
                                <input type="checkbox" name=""> <span>Hóa đơn GTGT tích hợp biên lai thu thuế, phí, lệ phí<br /></span>
                            }


                            @if (loai8 != 0)
                            {
                                <input type="checkbox" name="" checked> <span>Hóa đơn bán hàng tích hợp biên lai thu thuế, phí, lệ phí<br /></span>
                            }
                            else
                            {
                                <input type="checkbox" name=""> <span>Hóa đơn bán hàng tích hợp biên lai thu thuế, phí, lệ phí<br /></span>
                            }

                            @if (loai9 != 0)
                            {
                                <input type="checkbox" name="" checked> <span>Hóa đơn thương mại<br /></span>
                            }
                            else
                            {
                                <input type="checkbox" name=""> <span>Hóa đơn thương mại<br /></span>
                            }

                        </td>
                    </tr>
                </table>
            </div>
            <div class="clear-both"></div>
        </div>
    </div>

    <p style="page-break-before: always"></p>



    <div class="khunghoadon">
        <div class="hoadon">
            <div class="clear-both"></div>
            <div class="dongngang">
                @for (int pageIndexTable5 = 0; pageIndexTable5 < pagesTable5.Count(); pageIndexTable5++)
                {
                    <table class="bangchitiet">
                        <tr>
                            <td class="cachtrai10 indam" colspan="7">5. Danh sách chứng thư số sử dụng:</td>
                        </tr>
                        <tr>
                            <th rowspan="2" valign="middle">STT</th>
                            <th rowspan="2" valign="middle">Tên tổ chức cơ quan chứng thực/cấp/công nhận chữ ký số, chữ ký điện tử</th>
                            <th rowspan="2" valign="middle">Số sê-ri chứng thư</th>
                            <th colspan="2" valign="middle">Thời hạn sử dụng chứng thư số</th>
                            <th rowspan="2" valign="middle">Hình thức đăng ký (Thêm mới, gia hạn, ngừng sử dụng)</th>
                        </tr>
                        <tr>
                            <th>Từ ngày</th>
                            <th>Đến ngày</th>
                        </tr>
                        @{
                            foreach (var detail in pagesTable5[pageIndexTable5])
                            {
                                <tr>
                                    <td class="cangiua">@detail.Index</td>
                                    <td class="cachtrai5">@detail.SubjectName</td>
                                    <td>@detail.SerialNumber</td>
                                    <td>@(((DateTime)detail.StartDate))</td>
                                    <td>@(((DateTime)detail.EndDate))</td>
                                    <td class="cachtrai5">
                                        @if (detail.RegisterType.ToString() == "AddNew")
                                        {
                                            <span>Thêm mới</span>
                                        }
                                        else if (detail.RegisterType.ToString() == "Extend")
                                        {
                                            <span>Gia hạn</span>
                                        }
                                        else if (detail.RegisterType.ToString() == "OutOfService")
                                        {
                                            <span>Ngừng sử dụng</span>
                                        }
                                    </td>

                                </tr>
                            }
                        }
                    </table>
                }

                <table class="bangchitiet" style="border-top: hidden;">
                    <tr>
                        <td class="cachtrai10 indam" colspan="8">6. Tổ chức cung cấp dịch vụ:</td>
                    </tr>
                    <tr valign="middle">
                        <th valign="middle">STT</th>
                        <th valign="middle">Tên tổ chức cung cấp dịch vụ</th>
                        <th valign="middle">Mã số thuế</th>
                        <th valign="middle">Thời gian sử dụng(Từ ngày)</th>
                        <th valign="middle">Thời gian sử dụng(Đến ngày)</th>
                        <th valign="middle">Ghi chú</th>
                    </tr>
                    @{
                        foreach (var detail in invoiceServiceProviders)
                        {
                            <tr>
                                <td>@detail.Index</td>
                                <td>@detail.SubjectName</td>
                                <td>@detail.TaxCode</td>
                                <td>@detail.StartDate</td>
                                <td>@detail.EndDate</td>
                                <td>@detail.Note</td>
                            </tr>
                        }
                    }
                </table>

                <table class="bangchitiet" style="border-top: hidden;">
                    <tr>
                        <td class="cachtrai10 indam" colspan="8">7. Thông tin đơn vị truyền nhận:</td>
                    </tr>
                    <tr valign="middle">
                        <th valign="middle">STT</th>
                        <th valign="middle">Tên đơn vị truyền nhận</th>
                        <th valign="middle">Mã số thuế</th>
                        <th valign="middle">Thời gian sử dụng(Từ ngày)</th>
                        <th valign="middle">Thời gian sử dụng(Đến ngày)</th>
                        <th valign="middle">Ghi chú</th>
                    </tr>
                    @{
                        foreach (var detail in transmissionProviders)
                        {
                            <tr>
                                <td>@detail.Index</td>
                                <td>@detail.SubjectName</td>
                                <td>@detail.TaxCode</td>
                                <td>@detail.StartDate</td>
                                <td>@detail.EndDate</td>
                                <td>@detail.Note</td>
                            </tr>
                        }
                    }
                </table>

                <table class="bangchitiet" style="border-top: hidden;">
                    <tr>
                        <td class="cachtrai10 indam" colspan="8">8. Thông tin đơn vị hạch toán phụ thuộc cần cấp quyền tra cứu hóa đơn:</td>
                    </tr>
                    <tr valign="middle">
                        <th valign="middle">STT</th>
                        <th valign="middle">Tên tổ chức cung cấp dịch vụ</th>
                        <th valign="middle">Mã số thuế</th>
                        <th valign="middle">Cấp quyền(Từ ngày)</th>
                        <th valign="middle">Cấp quyền(Đến ngày)</th>
                        <th valign="middle">Ghi chú</th>
                    </tr>
                    @{
                        foreach (var detail in dependentAccountingUnits)
                        {
                            <tr>
                                <td>@detail.Index</td>
                                <td>@detail.SubjectName</td>
                                <td>@detail.TaxCode</td>
                                <td>@detail.StartDate</td>
                                <td>@detail.EndDate</td>
                                <td>@detail.Note</td>
                            </tr>
                        }
                    }
                </table>

                <table class="bangchitiet" style="border-top: hidden;">
                    <tr>
                        <td class="cachtrai10 indam" colspan="8">9. Đề nghị tạm ngừng sử dụng hóa đơn điện tử:</td>
                    </tr>
                    <tr valign="middle">
                        <th valign="middle">STT</th>
                        <th valign="middle">Tạm ngừng(Từ ngày)</th>
                        <th valign="middle">Tạm ngừng(Đến ngày)</th>
                        <th valign="middle">Tên tổ chức cung cấp dịch vụ</th>
                        <th valign="middle">Mã số thuế</th>
                        <th valign="middle">Số seri chứng thư</th>
                        <th valign="middle">Ghi chú</th>
                    </tr>
                    @{
                        foreach (var detail in pauseInvoiceUsages)
                        {
                            <tr>
                                <td>@detail.Index</td>
                                <td>@detail.StartDate</td>
                                <td>@detail.EndDate</td>
                                <td>@detail.SubjectName</td>
                                <td>@detail.TaxCode</td>
                                <td>@detail.SerialNumber</td>
                                <td>@detail.Note</td>
                            </tr>
                        }
                    }
                </table>

                <table class="bangchitiet" style="border-top: hidden;">
                    <tr>
                        <td class="cachtrai10 indam" colspan="8">10. Đăng ký tích hợp hóa đơn điện tử với chứng từ điện tử:</td>
                    </tr>
                    <tr valign="middle">
                        <th valign="middle">STT</th>
                        <th valign="middle">Tên loại hóa đơn</th>
                        <th valign="middle">Ký hiệu mẫu hóa đơn tích hợp</th>
                        <th valign="middle">Ký hiệu hóa đơn tích hợp</th>
                        <th valign="middle">Tên tổ chức cung cấp dịch vụ</th>
                        <th valign="middle">Mã số thuế</th>
                        <th valign="middle">Mục đích tích hợp</th>
                        <th valign="middle">Thời hạn tích hợp(Từ ngày)</th>
                        <th valign="middle">Thời hạn tích hợp(Đến ngày)</th>
                        <th valign="middle">Ghi chú</th>
                    </tr>
                    @{
                        foreach (var detail in registerInvoiceIntegrations)
                        {
                            <tr>
                                <td>@detail.Index</td>
                                <td>@detail.IntegrationInvoiceTypeName</td>
                                <td>@detail.IntegrationSerialNo</td>
                                <td>@detail.SubjectName</td>
                                <td>@detail.TaxCode</td>
                                <td>@detail.IntegrationPurpose</td>
                                <td>@detail.StartDate</td>
                                <td>@detail.EndDate</td>
                                <td>@detail.Note</td>
                            </tr>
                        }
                    }

                    <tr>
                        <td colspan="9" style="padding: 30px 0px;border-right: hidden;border-left: hidden;" class="cachtrai50">Chúng tôi cam kết hoàn toàn chịu trách nhiệm trước pháp luật về tính chính xác, trung thực của nội dung nêu trên và thực hiện theo đúng quy định của pháp luật./.</td>
                    </tr>
                    <tr class="canphai" style="padding-right: 200px; border: hidden;">
                        <td colspan="6" style="padding-right: 200px; border: hidden;"></td>
                        <td colspan="3" class="cangiua" style="border: hidden;">
                            <span>@Model.PlaceName, ngày @(((DateTime)Model.RegistrationDate).ToString("dd")) tháng @(((DateTime)Model.RegistrationDate).ToString("MM")) năm @(((DateTime)Model.RegistrationDate).ToString("yyyy"))</span><br />
                            <span class="indam">NGƯỜI NỘP THUẾ</span><br />
                            <span class="innghieng">(Chữ ký số người nộp thuế)</span><br /><br />
                            @if (Model.SignedTime != null)
                            {
                                <p class="khungky">
                                    <img id="logoTick" width="30px" src="data:image/png;base64,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">
                                    <u>
                                        <b>
                                            <span>Được ký bởi:</span>
                                            <span style="text-transform: uppercase;">@Model.FullName</span><br />Ngày ký :  @((Model.SignedTime))
                                        </b>
                                    </u>
                                </p>
                            }
                        </td>
                    </tr>
                </table>
            </div>

            <div class="clear-both"></div>
        </div>
    </div>



</body>
    </html>