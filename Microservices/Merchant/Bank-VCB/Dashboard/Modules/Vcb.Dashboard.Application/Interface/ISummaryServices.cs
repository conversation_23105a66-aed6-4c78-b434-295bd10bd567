using Core.Shared.Constants;
using Core.Shared.Messages;
using System;
using System.Threading.Tasks;

namespace VnisCore.Dashboard.Application.Interface
{
    public interface ISummaryServices
    {
        Task SummaryAsync(
            Guid tenantId,
            InvoiceAction action, 
            InvoiceStatus invoiceStatus, 
            ApproveStatus approveStatus, 
            InvoiceSummaryStatisticResponseModel model);
    }
}
