using Core.DependencyInjection;
using Core.EventBus.Distributed;
using Core.Shared.Factory;
using Core.Shared.MessageEventsData.StatisticSummary;
using System.Threading.Tasks;
using VnisCore.Dashboard.Application.Interface;

namespace VnisCore.Dashboard.Application.RabbitMqEventBus.StatisticSummary.ReceivedEventHandler
{
    public class StatisticSummaryApiReceivedEventHandler : IDistributedEventHandler<StatisticSummaryApiEventResultData>, ITransientDependency
    {
        private readonly IAppFactory _appFactory;
        public StatisticSummaryApiReceivedEventHandler(IAppFactory appFactory)
        {
            _appFactory = appFactory;
        }

        public async Task HandleEventAsync(StatisticSummaryApiEventResultData eventData)
        {
            var service = _appFactory.GetServiceDependency<ISummaryServices>();
            await service.SummaryAsync(eventData.TenantId, eventData.Action, eventData.InvoiceStatus, eventData.ApproveStatus,
                eventData.InvoiceSummaryStatistic);
            //await _distributedEventBus.PublishAsync(eventData);
        }
    }
}
