using System;
using System.Collections.Generic;

namespace VnisCore.Dashboard.Application.Models.Responses
{
    public class GetNotificationTemplateResponseModel
    {
        /// <summary>
        /// đã dkph
        /// </summary>
        public decimal TotalRegister { get; set; }

        /// <summary>
        /// <PERSON><PERSON> sử dụng
        /// </summary>
        public decimal TotalUsed { get; set; }

        /// <summary>
        /// Chưa sử dụng
        /// </summary>
        public decimal UnUsed { get; set; }

        public List<SummaryByTemplateModel> SummaryTemplates { get; set; }
    }

    public class SummaryByTemplateModel
    {
        public long Id { get; set; }
        public short TemplateNo { get; set; }
        public string SerialNo { get; set; }
        public int Remain { get; set; }
        public int StartNumber { get; set; }
        public int EndNumber { get; set; }
        public int CurrentNumber { get; set; }
    }
}
