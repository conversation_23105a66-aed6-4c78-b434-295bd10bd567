using Core.DependencyInjection;
using Core.EventBus.Distributed;
using Core.Shared.Factory;
using System.Threading.Tasks;
using VnisCore.Invoice01.Application.Factories.Services;
using VnisCore.Invoice01.Application.Invoice01DetailField.RabbitMqEventBus.MessageEventData;

namespace VnisCore.Invoice01.Application.Invoice01DetailField.RabbitMqEventBus.SubscribeEventHandler
{
    public class SyncDetailFieldPublishEventHandler : IDistributedEventHandler<SyncInvoice01DetailFieldEventSendData>, ITransientDependency
    {
        private readonly IAppFactory _appFactory;

        public SyncDetailFieldPublishEventHandler(
            IAppFactory appFactory)
        {
            _appFactory = appFactory;
        }

        public async Task HandleEventAsync(SyncInvoice01DetailFieldEventSendData eventData)
        {
            var syncDataService = _appFactory.GetServiceDependency<ISyncInvoice01DetailFieldService>();
            await syncDataService.SyncDataInheritAsync(eventData, eventData.IsDelete);
        }
    }
}
