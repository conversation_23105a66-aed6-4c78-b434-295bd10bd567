using System;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;

namespace Vcb.Invoice01.Application.Factories.Models
{
    public class Invoice01TransactionDetailModel
    {
        public long Id { get; set; }

        public long InvoiceHeaderId { get; set; }

        public int Index { get; set; }

        /// <summary>
        /// Ghi chú
        /// </summary>
        public string Note { get; set; }

        /// <summary>
        /// code bản ghi hàng hóa
        /// </summary>
        public string ProductCode { get; set; }

        /// <summary>
        /// Mã hàng hóa
        /// </summary>
        public long? ProductId { get; set; }

        /// <summary>
        /// Tên hàng hóa
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// Tính chất hàng hóa (Không phải nhóm hàng hóa)
        /// 1 - Hàng hóa, dịch vụ
        /// 2 - <PERSON><PERSON><PERSON><PERSON>n mại
        /// 3 - <PERSON><PERSON><PERSON> khấu thương mại (Trong trường hợp muốn thể hiện thông tin chiết khấu theo dòng)
        /// 4 - <PERSON>hi chú/diễn giải
        /// </summary>
        public short ProductType { get; set; }

        public long? UnitId { get; set; }

        /// <summary>
        /// Tên Đơn vị tính
        /// </summary>
        public string UnitName { get; set; }

        /// <summary>
        /// Đơn giá hàng hóa
        /// </summary>
        public decimal UnitPrice { get; set; }

        public int RoundingUnit { get; set; }

        /// <summary>
        /// Số lượng
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// Tổng tiền trước thuế / trước chiết khấu
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// Tổng tiền sau thuế
        /// </summary>
        public decimal PaymentAmount { get; set; }

        /// <summary>
        /// Phần trăm thuế
        /// </summary>
        public decimal VatPercent { get; set; }

        /// <summary>
        /// Giá trị hiển thị phần trăm thuế
        /// </summary>
        public string VatPercentDisplay { get; set; }

        /// <summary>
        /// Tổng tiền thuế
        /// </summary>
        public decimal VatAmount { get; set; }

        public Guid TenantId { get; set; }

        /// <summary>
        /// GL ghi nhận phí
        /// </summary>
        public string GLNo { get; set; }

        /// <summary>
        /// VD: Số REF của món liên quan TF
        /// </summary>
        public string RefNo { get; set; }

        /// <summary>
        /// Mã thanh toán viên
        /// - Hóa đơn G4 gốc: 
        /// + DBTG có dữ liệu: lấy giá trị theo DBTG
        /// + DBTG không có dữ liệu: bỏ trống
        /// - Hóa đơn điều chỉnh thủ công: 
        /// + Nếu người dùng chỉnh sửa dữ liệu trên màn hình hệ thống: lấy giá trị người dùng nhập
        /// + Được phép bỏ trống
        /// - Ràng buộc chỉ lưu vào DBCore nếu người dùng điều chỉnh đồng thời 2 trường Mã TTV và Số chứng từ
        /// </summary>
        public string CreatorErp { get; set; }

        /// <summary>
        /// Số chứng từ giao dịch
        /// - Hóa đơn G4 gốc: 
        /// + DBTG có dữ liệu -> lấy giá trị theo DBTG
        /// + DBTG không có dữ liệu -> bỏ trống
        /// - Hóa đơn điều chỉnh thủ công: 
        /// + Nếu người dùng chỉnh sửa dữ liệu trên màn hình hệ thống: lấy giá trị người dùng nhập
        /// + Được phép bỏ trống
        /// - Ràng buộc chỉ lưu vào DBCore nếu người dùng điều chỉnh đồng thời 2 trường Mã TTV và Số chứng từ"
        /// </summary>
        public string TellSeq { get; set; }

        public decimal ExchangeRate { get; set; }

        public string BuyerBankAccount { get; set; }

        public string Pctime { get; set; }

        public DateTime TransactionDate { get; set; }


        public static explicit operator Invoice01TransactionDetailModel(Invoice01TransactionDetailEntity entity)
        {
            if (entity == null)
                return null;

            return new Invoice01TransactionDetailModel
            {
                Id = entity.Id,
                Index = entity.Index,
                Note = entity.Note,
                ProductCode = entity.ProductCode,
                ProductId = entity.ProductId,
                ProductName = entity.ProductName,
                UnitId = entity.UnitId,
                UnitName = entity.UnitName,
                UnitPrice = entity.UnitPrice,
                RoundingUnit = entity.RoundingUnit,
                Quantity = entity.Quantity,
                Amount = entity.Amount,
                PaymentAmount = entity.PaymentAmount,
                VatPercent = entity.VatPercent,
                VatPercentDisplay = entity.VatPercentDisplay,
                VatAmount = entity.VatAmount,
                TenantId = entity.TenantId,
                ProductType = (short)entity.ProductType,
            };
        }
    }
}
