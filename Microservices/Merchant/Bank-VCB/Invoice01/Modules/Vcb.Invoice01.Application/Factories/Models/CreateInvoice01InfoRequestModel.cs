using Core.Shared.Attributes;
using System.ComponentModel.DataAnnotations;

namespace VnisCore.Invoice01.Application.Factories.Models
{
    public class CreateInvoice01InfoRequestModel
    {
        /// <summary>
        /// Mẫu số của hóa đơn 
        /// </summary>
        [Required(ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01InfoRequestModel.TemplateNo.Required")]
        //[TemplateNo(ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01InfoRequestModel.TemplateNo.WrongFormat")]
        public short TemplateNo { get; set; }

        /// <summary>
        /// Ký hiệu hóa đơn 
        /// </summary>
        [Required(ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01InfoRequestModel.SerialNo.Required")]
        [SerialNo(ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01InfoRequestModel.SerialNo.WrongFormat")]
        public string SerialNo { get; set; }
    }
}
