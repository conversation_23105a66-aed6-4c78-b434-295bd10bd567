using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.BaseEntities;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;

namespace VnisCore.Invoice01.Application.Factories.Repositories
{
    public interface IInvoiceReferenceRepository<T> where T : BaseInvoiceReference
    {
        /// <summary>
        /// lấy thông tin invoice reference của hóa đơn theo invoiceCode
        /// </summary>
        /// <param name="invoiceId"></param>
        /// <returns></returns>
        Task<T> GetByCodeInvoiceHeaderAsync(long invoiceId);

        /// <summary>
        /// lấy thông tin invoice reference của nhiều hóa đơn theo invoiceCode
        /// </summary>
        /// <param name="invoiceIds"></param>
        /// <returns></returns>
        Task<List<T>> GetByCodeInvoiceHeadersAsync(List<long> invoiceIds);

        /// <summary>
        /// lấy thông tin invoice reference theo invoiceRefenceCode
        /// </summary>
        /// <param name="invoiceId"></param>
        /// <returns></returns>
        Task<T> GetByInvoiceReferenceCodeAsync(long invoiceId);

        Task<List<long>> GetInvoiceReferenceIdsByRootInvoiceIdAsync(long invoiceId);

        /// <summary>
        /// lấy thông tin invoice id theo invoiceRefenceCode
        /// </summary>
        /// <param name="invoiceId"></param>
        /// <returns></returns>
        Task<List<T>> GetByInvoiceReferenceAsync(long invoiceId);

        Task<Invoice01ReferenceEntity> GetInvoiceReferenceRawAsync(long IdInvoice);

        Task<Invoice01ReferenceEntity> GetInvoiceReferenceByReferenceIdRawAsync(long IdInvoice);

        Task UpdateInvoiceDateReferenceInvoiceReferenceRawAsync(Invoice01ReferenceEntity invoice01ReferenceEntity);
    }
}
