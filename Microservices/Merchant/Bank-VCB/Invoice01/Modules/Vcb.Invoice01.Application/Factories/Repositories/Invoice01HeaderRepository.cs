using Core.Application.Dtos;
using Core.Domain.Repositories;
using Core.ObjectMapping;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Dapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Invoice01.Application.Factories.Services;
using VnisCore.Invoice01.Application.Invoice01.Dto;
using VnisCore.Invoice01.Application.Invoice01.Models.Requests.Queries;

namespace VnisCore.Invoice01.Application.Factories.Repositories
{
    public class Invoice01HeaderRepository : IInvoice01HeaderRepository
    {
        private readonly IRepository<Invoice01HeaderEntity, long> _repoInvoice01Header;
        private readonly IInvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity> _invoiceService;
        private readonly IAppFactory _appFactory;
        protected IObjectMapper _objectMapper { get; }

        public Invoice01HeaderRepository(
            IRepository<Invoice01HeaderEntity, long> repoInvoice01Header,
            IInvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity> invoiceService,
            IAppFactory appFactory,
            IObjectMapper objectMapper
            )
        {
            _repoInvoice01Header = repoInvoice01Header;
            _invoiceService = invoiceService;
            _appFactory = appFactory;
            _objectMapper = objectMapper;
        }

        public async Task<PagedResultDto<Invoice01HeaderEntity>> PagingAsync(Guid tenantId, Guid userId, PagingInvoice01Request model)
        {
            var query = await Filter(tenantId, userId, model);

            var totalItems = query.Count();

            query = query.OrderByDescending(x => x.InvoiceDate).ThenByDescending(x => x.Number).ThenByDescending(x => x.CreationTime);

            var items = query.Skip(model.SkipCount).Take(model.MaxResultCount).ToList();

            return new PagedResultDto<Invoice01HeaderEntity>
            {
                TotalCount = totalItems,
                Items = items,
            };
        }

        private async Task<IQueryable<Invoice01HeaderEntity>> Filter(Guid tenantId, Guid userId, PagingInvoice01Request model)
        {
            IQueryable<Invoice01HeaderEntity> items = _repoInvoice01Header.Where(x => x.TenantId == tenantId);

            var allReadTemplates = await _invoiceService.GetReadInvoiceTemplates(tenantId, userId, VnisType._01GTKT);

            if (model.TemplateNo.HasValue)
            {
                var allReadTemplateIds = allReadTemplates.Where(x => x.TemplateNo == model.TemplateNo).Select(x => x.Id).ToList();
                if (allReadTemplateIds != null && allReadTemplateIds.Any())
                    items.Where(x => allReadTemplateIds.Contains(x.InvoiceTemplateId));
            }

            //lấy hóa đơn có số hoặc không số
            if (model.IsNullInvoice)
                items = items.Where(x => !x.Number.HasValue);
            else
                items = items.Where(x => x.Number.HasValue);

            if (!string.IsNullOrEmpty(model.Keyword))
            {
                var q = model.Keyword.Trim();
                int.TryParse(model.Keyword, out int outInvoiceNo);
                var invoiceStatus = EnumExtension.TryToEnum<InvoiceStatus>(model.Keyword);
                var signStatus = EnumExtension.TryToEnum<SignStatus>(model.Keyword);

                items = items.Where(x =>
                     (x.FullNameCreator != null && x.FullNameCreator.Contains(model.Keyword))
                    || (x.UserNameCreator != null && x.UserNameCreator.Contains(model.Keyword))
                    || (x.BuyerEmail != null && x.BuyerEmail.Contains(model.Keyword))
                    || (x.BuyerFullName != null && x.BuyerFullName.Contains(model.Keyword))
                    || (x.Number == outInvoiceNo)
                    || (x.PaymentMethod != null && x.PaymentMethod.Contains(model.Keyword))
                    || (x.BuyerPhoneNumber != null && x.BuyerPhoneNumber.Contains(model.Keyword))
                    || (x.SerialNo != null && x.SerialNo.Contains(model.Keyword))
                    //|| (x.TemplateNo.Contains(query.Q))
                    || (x.ErpId != null && x.ErpId.Contains(model.Keyword))
                    || (x.TransactionId != null && x.TransactionId.Contains(model.Keyword))
                    || (x.CreatorErp != null && x.CreatorErp.Contains(model.Keyword))
                    || (x.BuyerCode != null && x.BuyerCode.Contains(model.Keyword))
                    || (x.InvoiceStatus == invoiceStatus.GetHashCode())
                    || (x.SignStatus == signStatus.GetHashCode())
                    );
            }

            if (model.TemplateNo.HasValue)
            {
                items = items.Where(x => x.TemplateNo == model.TemplateNo);
            }

            if (model.CreateFromDate.HasValue)
            {
                var createFromDate = model.CreateFromDate.Value.Date.ToUniversalTime();
                items = items.Where(x => x.InvoiceDate >= createFromDate);
            }

            if (model.CreateToDate.HasValue)
            {
                var createToDate = model.CreateToDate.Value.Date.AddDays(1);
                items = items.Where(x => x.InvoiceDate < createToDate);
            }

            if (model.CancelFromDate.HasValue)
                items = items.Where(x => x.LastModificationTime.HasValue && x.LastModificationTime.Value.Date >= model.CancelFromDate.Value.Date
                                           && (x.InvoiceStatus == InvoiceStatus.XoaBo.GetHashCode() || x.InvoiceStatus == InvoiceStatus.XoaHuy.GetHashCode()));

            if (model.CancelToDate.HasValue)
            {
                var cancelToDate = model.CancelToDate.Value.Date.AddDays(1);
                items = items.Where(x => x.LastModificationTime.HasValue && x.LastModificationTime.Value.Date < cancelToDate
                                            && (x.InvoiceStatus == InvoiceStatus.XoaBo.GetHashCode() || x.InvoiceStatus == InvoiceStatus.XoaHuy.GetHashCode()));
            }

            //if (query.IssuedAt.HasValue)
            //{
            //    var issuedAt = query.IssuedAt.Value.Date;
            //    items = items.Where(x => x.IssuedAt != null && x.IssuedAt.Value.Date == issuedAt
            //                                );
            //}

            if (!string.IsNullOrEmpty(model.Customers))
            {
                items = items.Where(x => (x.BuyerCode != null && x.BuyerCode.Contains(model.Customers))
                  || (x.BuyerLegalName != null && x.BuyerLegalName.Contains(model.Customers))
                  || (x.BuyerTaxCode != null && x.BuyerTaxCode.Contains(model.Customers))
                  || (x.BuyerEmail != null && x.BuyerEmail.Contains(model.Customers)));
            }

            if (!string.IsNullOrEmpty(model.Customer))
            {
                items = items.Where(x =>
                    (x.BuyerFullName != null && x.BuyerFullName.Contains(model.Customer))
                    || (x.BuyerEmail != null && x.BuyerEmail.Contains(model.Customer))
                    || (x.BuyerPhoneNumber != null && x.BuyerPhoneNumber.Contains(model.Customer)));
            }

            if (model.InvoiceTemplateIds != null && model.InvoiceTemplateIds.Any())
                items = items.Where(x => model.InvoiceTemplateIds.Contains(x.InvoiceTemplateId));

            if (!string.IsNullOrEmpty(model.InvoiceNo))
            {
                int.TryParse(model.InvoiceNo, out int outInvoiceNo);
                items = items.Where(x => x.Number != null && x.Number == outInvoiceNo);
            }

            if (model.InvoiceStatuses != null && model.InvoiceStatuses.Any())
                items = items.Where(x => model.InvoiceStatuses.Contains(x.InvoiceStatus));

            if (model.SignStatuses != null && model.SignStatuses.Any())
                items = items.Where(x => model.SignStatuses.Contains(x.SignStatus));

            if (model.ApproveStatuses != null && model.ApproveStatuses.Any())
                items = items.Where(x => model.ApproveStatuses.Contains(x.ApproveStatus));

            if (!string.IsNullOrEmpty(model.TransactionId))
                items = items.Where(x => x.TransactionId == model.TransactionId);

            if (!string.IsNullOrEmpty(model.ErpId))
                items = items.Where(x => x.ErpId == model.ErpId);

            if (model.FromNumber.HasValue)
                items = items.Where(x => x.Number >= model.FromNumber);

            if (model.ToNumber.HasValue)
                items = items.Where(x => x.Number <= model.ToNumber);

            if (!string.IsNullOrEmpty(model.UserNameCreator))
                items = items.Where(x => x.UserNameCreator.Contains(model.UserNameCreator));

            if (!string.IsNullOrEmpty(model.UserNameCreatorErp))
                items = items.Where(x => x.CreatorErp.Contains(model.UserNameCreatorErp));

            //if (model.Codes != null)
            //    items = items.Where(x => model.Codes.Contains(x.Code));

            return items;
        }

        public async Task<Invoice01HeaderEntity> GetInvoiceHeaderRawAsync(Guid tenantId, long IdRootInvoice)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var query = @"SELECT * FROM ""Invoice01Header"" WHERE ""TenantId"" = :TenantId AND ""Id"" = :Id";
            var dicParameter = new Dictionary<string, object>
            {
                { ":TenantId", rawTenantId },
                { ":Id", IdRootInvoice },
            };
            var result = await _appFactory.VnisCoreOracle.Connection.QuerySingleOrDefaultAsync<Invoice01HeaderDto>(query, new DynamicParameters(dicParameter));
            var invoice01 = _objectMapper.Map<Invoice01HeaderDto, Invoice01HeaderEntity>(result);
            return invoice01;
        }

        public async Task<List<Invoice01HeaderEntity>> GetInvoiceHeadersRawAsync(Guid tenantId, List<long> IdRootInvoices)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);

            var query = @"SELECT * FROM ""Invoice01Header"" WHERE ""TenantId"" = :TenantId AND ""Id"" IN :Ids";
            var dicParameter = new Dictionary<string, object>
            {
                { ":TenantId", rawTenantId },
                { ":Ids", IdRootInvoices },
            };

            var result = await _appFactory.VnisCoreOracle.Connection.QueryAsync<Invoice01HeaderDto>(query, new DynamicParameters(dicParameter));
            var headers = result.Select(x => _objectMapper.Map<Invoice01HeaderDto, Invoice01HeaderEntity>(x)).ToList();
            return headers;
        }

        public async Task<List<Invoice01HeaderDto>> GetInvoiceHeadersForCancelInvoiceRawAsync(Guid tenantId, List<long> IdRootInvoices)
        {
            var query = $@" SELECT ""Id"",
                                    ""ErpId"",
                                    ""TenantId"",
                                    ""TemplateNo"",
                                    ""SerialNo"",
                                    ""InvoiceTemplateId"",
                                    ""Number"",
                                    ""InvoiceNo"",
                                    ""InvoiceDate"",
                                    ""InvoiceStatus"",
                                    ""SignStatus"",
                                    ""ApproveCancelStatus"",
                                    ""TotalAmount"",
                                    ""TotalPaymentAmount"",
                                    ""TotalVatAmount""
                            FROM ""Invoice01Header"" 
                            WHERE ""Id"" IN :Ids 
                                AND ""TenantId"" = :TenantId ";
            var dicParameter = new Dictionary<string, object>
            {
                {":Ids", IdRootInvoices },
                {":TenantId", OracleExtension.ConvertGuidToRaw(tenantId) }
            };
            return (await _appFactory.VnisCoreOracle.Connection.QueryAsync<Invoice01HeaderDto>(query, new DynamicParameters(dicParameter))).ToList();
        }

        public async Task<List<Invoice01HeaderDto>> GetInvoiceHeadersForCancelDeleteInvoiceRawAsync(Guid tenantId, List<string> erpIds)
        {
            var query = $@" SELECT ""Id"",
                                    ""CreatorId"",
                                    ""FullNameCreator"",
                                    ""UserNameCreator"",
                                    ""Source"",
                                    ""ErpId"",
                                    ""TenantId"",
                                    ""TemplateNo"",
                                    ""SerialNo"",
                                    ""InvoiceTemplateId"",
                                    ""Number"",
                                    ""InvoiceNo"",
                                    ""InvoiceDate"",
                                    ""InvoiceStatus"",
                                    ""SignStatus"",
                                    ""ApproveCancelStatus"",
                                    ""TotalAmount"",
                                    ""TotalPaymentAmount"",
                                    ""TotalVatAmount""
                            FROM ""Invoice01Header"" 
                            WHERE ""ErpId"" IN :ErpIds 
                            AND ""TenantId"" = :TenantId
                            AND ""InvoiceStatus"" not in :ListInvoiceStatus ";
            var dicParameter = new Dictionary<string, object>
            {
                {":ErpIds",  erpIds},
                { ":TenantId", OracleExtension.ConvertGuidToRaw(tenantId) },
                { ":ListInvoiceStatus", new List<int> {
                        InvoiceStatus.XoaBo.GetHashCode(),
                        InvoiceStatus.XoaHuy.GetHashCode(),
                        InvoiceStatus.BiDieuChinhTangGiam.GetHashCode(),
                        InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode(),
                        InvoiceStatus.BiThayThe.GetHashCode(),
                    }
                },
            };

            return (await _appFactory.VnisCoreOracle.Connection.QueryAsync<Invoice01HeaderDto>(query, new DynamicParameters(dicParameter))).ToList();
        }
    }
}
