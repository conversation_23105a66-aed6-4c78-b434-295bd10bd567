using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.MessageEventsData.SyncDataTenant.Dto;
using Dapper;
using System;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Invoice01.Application.Invoice01HeaderField.Dto;
using VnisCore.Invoice01.Application.Invoice01HeaderField.Models.Requests;

namespace VnisCore.Invoice01.Application.Factories.Services
{
    public interface ISyncInvoice01HeaderFieldService
    {
        Task SyncDataInheritAsync(SyncInvoiceHeaderFieldRequestModel input, bool isDelete);
    }

    public class SyncInvoice01HeaderFieldService : ISyncInvoice01HeaderFieldService
    {
        private readonly IAppFactory _appFactory;

        public SyncInvoice01HeaderFieldService(IAppFactory appFactory)
        {
            _appFactory = appFactory;
        }

        public async Task SyncDataInheritAsync(SyncInvoiceHeaderFieldRequestModel input, bool isDelete)
        {
            var queryTenants = @$"SELECT * FROM ""VnisTenants"" WHERE ""IsDeleted"" = 0 AND ""ParentId"" = '{ OracleExtension.ConvertGuidToRaw(input.TenantId) }'";
            var inheritTenants = await _appFactory.AuthDatabase.Connection.QueryAsync<VnisTenantDto>(queryTenants);

            if (!inheritTenants.Any())
                return;

            var tenantIds = inheritTenants.Select(x => OracleExtension.ConvertGuidToRaw(x.Id)).ToList();

            string tenantArr = "";
            foreach (var tenant in tenantIds)
                tenantArr += "'" + tenant + "',";
            tenantArr = tenantArr.Remove(tenantArr.Length - 1);

            var query = @$" SELECT * FROM ""Invoice01HeaderField"" WHERE ""TenantId"" IN ({tenantArr}) AND ""IsDeleted"" = 0";

            var existsHeaderFields = await _appFactory.VnisCoreOracle.Connection.QueryAsync<Invoice01HeaderFieldDto>(query);
            var indexHeaderExtra = existsHeaderFields.GroupBy(x => x.TenantId).ToDictionary(x => x.Key, x => x.ToList());

            StringBuilder rawSql = new StringBuilder($@" BEGIN");

            foreach (var tenant in inheritTenants)
            {
                if (!indexHeaderExtra.ContainsKey(tenant.Id))
                {
                    if (!isDelete)
                    {
                        input.Id = await GetSEQsNextVal(SequenceName.Invoice01HeaderField);
                        rawSql.Append(GenerateDrawInsertHeaderField(tenant.Id, input.UserId, input));
                    }
                }
                else
                {
                    var exists = indexHeaderExtra[tenant.Id];

                    var exist = exists.FirstOrDefault(x => x.FieldName == input.FieldNameRef || x.FieldName == input.FieldName);

                    if (isDelete)
                    {
                        if (exist != null)
                        {
                            input.Id = exist.Id;
                            rawSql.Append(GenerateDrawDeleteHeaderField(input.UserId, input));
                        }    
                    }
                    else
                    {
                        if (exist == null)
                        {
                            input.Id = await GetSEQsNextVal(SequenceName.Invoice01HeaderField);
                            rawSql.Append(GenerateDrawInsertHeaderField(tenant.Id, input.UserId, input));
                        }
                        else
                        {
                            input.Id = exist.Id;
                            rawSql.Append(GenerateDrawUpdateHeaderField(input.UserId, input));
                        }
                    }
                }
            }
            rawSql.Append(" END;");

            await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(rawSql.ToString());
        }

        private string GenerateDrawInsertHeaderField(Guid tenantId, Guid userId, SyncInvoiceHeaderFieldRequestModel input)
        {
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);
            var rawTenantd = OracleExtension.ConvertGuidToRaw(tenantId);

            StringBuilder rawSql = new StringBuilder();

            rawSql.Append($@" INSERT INTO ""Invoice01HeaderField"" (
                                ""Id"",
                                ""CreationTime"",
                                ""CreatorId"",
                                ""TenantId"",
                                ""FieldName"",
                                ""DisplayName"",
                                ""Metadata"",
                                ""IsActive""
                            )
                            VALUES (
                                { input.Id },
                                '{ DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',
                                '{ rawUserId }',
                                '{ rawTenantd }',
                                N'{ input.FieldName }',
                                N'{ input.DisplayName }',
                                '{ input.Metadata }',
                                0
                            );
            ");

            return rawSql.ToString();
        }

        private string GenerateDrawUpdateHeaderField(Guid userId, SyncInvoiceHeaderFieldRequestModel input)
        {
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);

            StringBuilder rawSql = new StringBuilder();

            rawSql.Append($@" UPDATE ""Invoice01HeaderField"" SET
                                ""FieldName"" = '{ input.FieldName }',
                                ""DisplayName"" = N'{ input.DisplayName }',
                                ""LastModificationTime"" = '{ DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }',
                                ""LastModifierId"" = '{ rawUserId }'
                              WHERE ""Id"" = { input.Id };
            ");

            return rawSql.ToString();
        }

        private string GenerateDrawDeleteHeaderField(Guid userId, SyncInvoiceHeaderFieldRequestModel input)
        {
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);

            StringBuilder rawSql = new StringBuilder();

            rawSql.Append($@" UPDATE ""Invoice01HeaderField"" SET
                                ""DeleterId"" = '{ rawUserId }',
                                ""IsDeleted"" = 1,
                                ""LastModificationTime"" = '{ DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")) }'
                              WHERE ""Id"" = { input.Id };
            ");

            return rawSql.ToString();
        }

        private async Task<long> GetSEQsNextVal(string sequenceName)
        {
            var sql = $@"   SELECT ""{sequenceName}"".NEXTVAL FROM dual";

            var result = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<long>(sql);

            return result;
        }
    }
}
