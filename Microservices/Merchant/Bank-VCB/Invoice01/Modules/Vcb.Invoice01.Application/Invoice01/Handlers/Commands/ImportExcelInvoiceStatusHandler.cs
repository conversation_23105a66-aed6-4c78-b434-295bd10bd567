using Core;
using Core.EventBus.Distributed;
using Core.Localization.Resources.AbpLocalization;
using Core.MultiTenancy;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Interfaces;
using Core.Shared.Invoice.Interfaces;
using Core.Shared.Validations;
using Core.Users;
using MediatR;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Vcb.Invoice01.Application.Invoice01.Dto;
using Vcb.Invoice01.Application.Invoice01.Services;
using Vcb.Invoice01.Application.Invoice01.ValidationRules.ImportExcelModify;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Core.Oracle.Domain.Entities.InvoiceTemplate;
using VnisCore.Invoice01.Application.Factories.Services;
using VnisCore.Invoice01.Application.Invoice01.Dto;
using VnisCore.Invoice01.Application.Invoice01.Models.Requests.Commands;
using VnisCore.Invoice01.Application.Invoice01.Models.Responses.Commands;
using VnisCore.Invoice01.Application.Invoice01.RabbitMqEventBus.CancelInvoice01.MessageEventData;

namespace Vcb.Invoice01.Application.Invoice01.Handlers.Commands
{
    public class ImportExcelInvoiceStatusHandler : IRequestHandler<ImportExcelInvoiceStatusModel, ImportExcelInvoiceStatusResponseModel>
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IDistributedEventBus _distributedEventBus;
        private readonly IAppFactory _appFactory;
        private readonly ICommonInvoice01Service _commonInvoice01Service;
        private readonly IValidatorFactory _validatorFactory;
        private readonly IBaseInvoiceHeaderService<Invoice01HeaderEntity> _baseInvoiceHeaderService;
        private readonly IBaseInvoiceDetailService<Invoice01DetailEntity> _baseInvoiceDetailService;
        private readonly IBaseInvoiceTaxBreakdownService<Invoice01TaxBreakdownEntity> _baseInvoiceTaxBreakdownService;
        private readonly IBaseInvoiceHeaderExtraPropertyService<Invoice01HeaderExtraPropertyEntity> _baseInvoiceHeaderExtraPropertyService;
        private readonly IBaseInvoiceDetailExtraPropertyService<Invoice01DetailExtraPropertyEntity> _baseInvoiceDetailExtraPropertyService;
        private readonly IBaseErpIdService _baseErpIdService;
        private readonly IBaseReadMoneyService _baseReadMoneyService;
        private readonly IBaseCurrencyService _baseCurrencyService;
        private readonly ISerialNoTransferService _serialNoTransferService;
        private readonly IBaseInvoiceTemplateService _baseInvoiceTemplateService;
        private readonly IBaseInvoiceReferenceService<Invoice01ReferenceEntity> _baseInvoiceReferenceService;
        private readonly IImportExcelModifyValidationRule _importExcelModifyValidationRule;
        private readonly ICurrentTenant _currentTenant;
        private readonly ICurrentUser _currentUser;
        private readonly DateTime _invoiceDate;
        private readonly ApproveStatus _approveStatus;
        private readonly ApproveStatus _approveCancelStatus;
        private readonly ApproveStatus _approveDeleteStatus;

        private readonly Random _random;

        private List<long> CancelInvoiceIds = new();
        private List<long> AdjustmentDetailInvoiceIds = new();
        private int StartRowNumber = 2;
        private List<Invoice01HeaderEntity> InvoiceHeaderEntities = new();
        private List<Invoice01DetailEntity> InvoiceDetailEntities = new();
        private List<Invoice01TaxBreakdownEntity> InvoiceTaxBreakdownEntities = new();
        private List<CurrencyEntity> CurrencyEntities = new();
        public List<Invoice01HeaderExtraPropertyEntity> InvoiceHeaderExtraPropertyEntities = new();
        private List<Invoice01DetailExtraPropertyEntity> InvoiceDetailExtraPropertyEntities = new();
        private List<InvoiceTemplateEntity> InvoiceTemplateEntities = new();
        private List<Invoice01ReferenceEntity> InvoiceReferences = new();
        private List<Invoice01HeaderEntity> InvoiceHeaderRelations = new();
        private List<Invoice01DetailEntity> InvoiceDetailRelations = new();
        private string PcTime;
        private string TellSeq;
        private InvoiceSource Source;
        private DateTime FromDate;
        private DateTime ToDate;

        public ImportExcelInvoiceStatusHandler(
            IServiceProvider serviceProvider,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IDistributedEventBus distributedEventBus,
            IAppFactory appFactory,
            ICommonInvoice01Service commonInvoice01Service,
            IValidatorFactory validatorFactory,
            IBaseInvoiceHeaderService<Invoice01HeaderEntity> baseInvoiceHeaderService,
            IBaseInvoiceDetailService<Invoice01DetailEntity> baseInvoiceDetailService,
            IBaseInvoiceTaxBreakdownService<Invoice01TaxBreakdownEntity> baseInvoiceTaxBreakdownService,
            IBaseInvoiceHeaderExtraPropertyService<Invoice01HeaderExtraPropertyEntity> baseInvoiceHeaderExtraPropertyService,
            IBaseInvoiceDetailExtraPropertyService<Invoice01DetailExtraPropertyEntity> baseInvoiceDetailExtraPropertyService,
            IBaseErpIdService baseErpIdService,
            IBaseReadMoneyService baseReadMoneyService,
            IBaseCurrencyService baseCurrencyService,
            ISerialNoTransferService serialNoTransferService,
            IBaseInvoiceTemplateService baseInvoiceTemplateService,
            IBaseInvoiceReferenceService<Invoice01ReferenceEntity> baseInvoiceReferenceService,
            IImportExcelModifyValidationRule importExcelModifyValidationRule,
            IBaseApproveStatusService baseApproveStatusService

        )
        {
            _serviceProvider = serviceProvider;
            _localizer = localizer;
            _distributedEventBus = distributedEventBus;
            _appFactory = appFactory;
            _commonInvoice01Service = commonInvoice01Service;
            _validatorFactory = validatorFactory;
            _baseInvoiceHeaderService = baseInvoiceHeaderService;
            _baseInvoiceDetailService = baseInvoiceDetailService;
            _baseInvoiceTaxBreakdownService = baseInvoiceTaxBreakdownService;
            _baseInvoiceHeaderExtraPropertyService = baseInvoiceHeaderExtraPropertyService;
            _baseInvoiceDetailExtraPropertyService = baseInvoiceDetailExtraPropertyService;
            _baseErpIdService = baseErpIdService;
            _baseReadMoneyService = baseReadMoneyService;
            _baseCurrencyService = baseCurrencyService;
            _serialNoTransferService = serialNoTransferService;
            _baseInvoiceTemplateService = baseInvoiceTemplateService;
            _baseInvoiceReferenceService = baseInvoiceReferenceService;
            _importExcelModifyValidationRule = importExcelModifyValidationRule;
            _currentTenant = appFactory.CurrentTenant;
            _currentUser = appFactory.CurrentUser;
            _invoiceDate = DateTime.Now.Date;
            _approveStatus = baseApproveStatusService.GetApproveStatus();
            _approveCancelStatus = baseApproveStatusService.GetApproveCancelStatus();
            _approveDeleteStatus = baseApproveStatusService.GetApproveDeleteStatus();
            _random = new Random();

        }
        public async Task<ImportExcelInvoiceStatusResponseModel> Handle(ImportExcelInvoiceStatusModel request, CancellationToken cancellationToken)
        {
            try
            {
                // TODO: SetFromDate, SetToDate
                SetFromDateSetToDate(request);

                // TODO: Kiểm tra số dòng import
                // TODO: Kiểm trả dòng hiện tại có vượt quá LimiteRow không?
                if (request.Invoices.Count > StaticData.ImportExcel.LimitedRow)
                {
                    throw new UserFriendlyException(_localizer["Vnis.BE.Invoice.ImportExcel.File.InValidMaxRow", new string[] { StaticData.ImportExcel.LimitedRow.ToString() }]);
                }

                // TODO: Kiểm tra dữ liệu của request
                var importExcelInvoiceStatusModelValidationResult = await ValidateImportExcelInvoiceStatusModelAsync(request);
                if (importExcelInvoiceStatusModelValidationResult != null)
                {
                    return importExcelInvoiceStatusModelValidationResult;
                }

                // TODO: Xử lý các hóa đơn xóa hủy
                if (CancelInvoiceIds.Any())
                {
                    await HandleCancelInvoices();
                }

                // TODO: Xử lý các hóa đơn điều chỉnh tăng giảm.
                if (AdjustmentDetailInvoiceIds.Any())
                {
                    await HandleAdjustmentDetailInvoices();
                }

                return new ImportExcelInvoiceStatusResponseModel(new List<InvoiceStatusErrorMessageModel>());
            }
            catch (Exception ex)
            {
                Log.Error(ex.Message, ex.InnerException);
                throw new UserFriendlyException(ex.Message);
            }

        }

        /// <summary>
        /// Set FromDate, ToDate dùng cho truy vấn dữ liệu
        /// </summary>
        /// <param name="request"></param>
        private void SetFromDateSetToDate(ImportExcelInvoiceStatusModel request)
        {
            try
            {
                var invoices = request.Invoices.OrderBy(p => p._InvoiceDate);
                FromDate = invoices.First()._InvoiceDate;
                ToDate = invoices.Last()._InvoiceDate;
            }
            catch (Exception ex)
            {
                Log.Error(ex.ToString());
                throw new Exception("Ngày hóa đơn phải có định dạng dd/MM/yyyy. Vui lòng kiểm tra lại!");
            }
        }

        /// <summary>
        /// Kiểm tra dữ liệu từ request
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        private async Task<ImportExcelInvoiceStatusResponseModel> ValidateImportExcelInvoiceStatusModelAsync(ImportExcelInvoiceStatusModel request)
        {
            // TODO: Kiểm tra Duplicate dữ liệu
            var duplicationInvoiceStatusModels = DuplicateInvoiceStatusModels(request);
            if (duplicationInvoiceStatusModels.Any())
            {
                return new ImportExcelInvoiceStatusResponseModel(duplicationInvoiceStatusModels);
            }

            // TODO: Kiểm tra Duplicate trạng thái của dữ liệu
            if (IsDuplicateInvoiceStatus(request))
            {
                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.DataFileImportDifference"]);
            }

            // TODO: Truy vẫn dữ liệu.
            await QueryInvoiceStatusDataAsync(request);

            // TODO: Kiểm tra tính hợp lệ của dữ liệu
            var invoiceStatusModelValidationResults = ValidateInvoiceStatusModels(request);
            if (invoiceStatusModelValidationResults.Any())
            {
                return new ImportExcelInvoiceStatusResponseModel(invoiceStatusModelValidationResults);
            }

            return null;
        }

        private List<InvoiceStatusErrorMessageModel> DuplicateInvoiceStatusModels(ImportExcelInvoiceStatusModel request)
        {
            var results = new List<InvoiceStatusErrorMessageModel>();
            var errorMessage = _localizer["Vnis.BE.VCB.Invoice01.UpdateStatusInvoice.DuplicateInvoice"];

            var invoiceStatusModels = request.Invoices;
            var duplicateInvoiceStatusModels = invoiceStatusModels
                .GroupBy(p => new
                {
                    p.TemplateNo,
                    p.SerialNo,
                    p.InvoiceNo,
                    p.InvoiceDate
                })
                .Where(p => p.Count() > 1)
                .Select(p => p.First())
                .ToList();

            if (!duplicateInvoiceStatusModels.Any())
            {
                return new List<InvoiceStatusErrorMessageModel>();
            }

            return duplicateInvoiceStatusModels
                .Select(p => new InvoiceStatusErrorMessageModel
                {
                    TemplateNo = p.TemplateNo,
                    SerialNo = p.SerialNo,
                    InvoiceNo = p.InvoiceNo,
                    CurrentStatus = p.CurrentStatus,
                    NewStatus = p.NewStatus,
                    InvoiceDate = p._InvoiceDate,
                    ErrorMessage = errorMessage
                })
                .ToList();


        }

        private bool IsDuplicateInvoiceStatus(ImportExcelInvoiceStatusModel request)
        {
            var invoiceStatusModels = request.Invoices;
            if (invoiceStatusModels
                .GroupBy(p => p.CurrentStatus)
                .Count() > 1)
            {
                return true;
            }

            if (invoiceStatusModels
                .GroupBy(p => p.NewStatus)
                .Count() > 1)
            {
                return true;
            }

            return false;
        }

        private async Task QueryInvoiceStatusDataAsync(ImportExcelInvoiceStatusModel request)
        {

            await GetInvoiceHeaderEntitiesAsync(request.Invoices);

            var adjustmentDetailInvoices = request.Invoices
                .Where(p => p.NewStatus == (short)InvoiceStatus.DieuChinhTangGiam)
                .GroupBy(p => new
                {
                    p.TemplateNo,
                    p.SerialNo,
                    p.InvoiceNo,
                    InvoiceDate = p._InvoiceDate
                })
                .Select(p => new
                {
                    p.Key.TemplateNo,
                    p.Key.SerialNo,
                    p.Key.InvoiceNo,
                    p.Key.InvoiceDate
                })
                .ToList();

            // TODO: Nếu không có hd điều chỉnh tăng giảm thì không load dữ liệu khác nữa.
            if (!adjustmentDetailInvoices.Any())
            {
                return;
            }

            var invoiceHeaderIds = InvoiceHeaderEntities
                .Join(adjustmentDetailInvoices,
                    entity => new
                    {
                        entity.TemplateNo,
                        entity.SerialNo,
                        entity.InvoiceNo,
                        entity.InvoiceDate
                    },
                    invoice => new
                    {
                        invoice.TemplateNo,
                        invoice.SerialNo,
                        invoice.InvoiceNo,
                        invoice.InvoiceDate
                    },
                    (entity, invoice) => entity.Id

                )
                .ToList();

            await GetInvoiceDetailEntitiesAsync(invoiceHeaderIds);
            await GetInvoiceTaxBreakdownEntitiesAsync(invoiceHeaderIds);
            await GetInvoiceHeaderRelationsAsync(invoiceHeaderIds);
            await GetInvoiceDetailRelationsAsync(invoiceHeaderIds);
            await GetInvoiceHeaderExtraPropertyEntities(invoiceHeaderIds);
            await GetInvoiceDetailExtraPropertyEntities(invoiceHeaderIds);
            await GetCurrencyEntitiesAsync();
            await GetInvoiceTemplateEntitiesAsync();
        }


        private List<InvoiceStatusErrorMessageModel> ValidateInvoiceStatusModels(ImportExcelInvoiceStatusModel request)
        {
            var results = new List<InvoiceStatusErrorMessageModel>();

            var invoiceStatusModels = request.Invoices;
            foreach (var invoiceStatusModel in invoiceStatusModels)
            {
                var invoiceStatusModelResult = ValidateInvoiceStatusModel(invoiceStatusModel);
                if (invoiceStatusModelResult != null)
                {
                    results.Add(invoiceStatusModelResult);
                }

                if (!results.Any())
                {
                    // Nếu danh sách dữ liệu hợp lệ thì thêm Id vào CancelInvoiceIds, AdjustmentDetailInvoiceIds
                    AddInvoiceHeaderId(invoiceStatusModel);
                }
            }

            return results;

        }

        private InvoiceStatusErrorMessageModel ValidateInvoiceStatusModel(InvoiceStatusModel invoiceStatusModel)
        {
            // TODO: Kiểm tra trạng thái
            var statusValidationResult = ValidateStatus(invoiceStatusModel);
            if (statusValidationResult != null)
            {
                return statusValidationResult;
            }

            // TODO: Kiểm tra dữ liệu có tồn tại SerialNo của G4. 
            var existedG4SerialNo = ExistedG4SerialNo(invoiceStatusModel);
            if (existedG4SerialNo != null)
            {
                return existedG4SerialNo;
            }

            // TODO: Kiểm tra dữ liệu với InvoiceHeaderEntities
            var originalInvoiceResult = ValidateOriginalInvoiceHeader(invoiceStatusModel);
            if (originalInvoiceResult != null)
            {
                return originalInvoiceResult;
            }

            // TODO: Kiểm tra dữ liệu với InvoiceRelationsEntities
            return ValidateInvoiceRelations(invoiceStatusModel);

        }

        private InvoiceStatusErrorMessageModel ValidateStatus(InvoiceStatusModel invoiceStatusModel)
        {
            var errorMessages = new List<string>();

            var currentStatus = invoiceStatusModel.CurrentStatus;
            if (currentStatus != InvoiceStatus.Goc.GetHashCode()
                && currentStatus != InvoiceStatus.ThayThe.GetHashCode()
                && currentStatus != InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()
                && currentStatus != InvoiceStatus.DieuChinhTangGiam.GetHashCode()
                && currentStatus != InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()
            )
            {
                errorMessages.Add("Tình trạng cũ của hóa đơn không phải active");
            }

            var newStatus = invoiceStatusModel.NewStatus;
            if (newStatus != InvoiceStatus.DieuChinhTangGiam.GetHashCode()
                && newStatus != InvoiceStatus.XoaHuy.GetHashCode()
            )
            {
                errorMessages.Add("Tình trạng mới của hóa đơn không phải điều chỉnh/hủy");
            }

            if (!errorMessages.Any())
            {
                return null;
            }

            return new InvoiceStatusErrorMessageModel
            {
                TemplateNo = invoiceStatusModel.TemplateNo,
                SerialNo = invoiceStatusModel.SerialNo,
                InvoiceNo = invoiceStatusModel.InvoiceNo,
                CurrentStatus = invoiceStatusModel.CurrentStatus,
                NewStatus = invoiceStatusModel.NewStatus,
                InvoiceDate = invoiceStatusModel._InvoiceDate,
                ErrorMessage = String.Join(", ", errorMessages.Select(p => p).ToList())
            };
        }

        private InvoiceStatusErrorMessageModel ExistedG4SerialNo(InvoiceStatusModel invoiceStatusModel)
        {
            var errorMessage = _localizer["Vnis.BE.VCB.Invoice01.UpdateStatusInvoice.SerialNoTransfer.SerialTransfer.InvalidInvoice"];

            var g4Serials = _commonInvoice01Service.CreateG4Serial();
            Log.Information($"-> g4Serials = {JsonConvert.SerializeObject(g4Serials)}");

            var subSerial = invoiceStatusModel.SerialNo.Substring(invoiceStatusModel.SerialNo.Length - 3, 2);
            Log.Information($"-> subSerial = {subSerial}");

            if (g4Serials.All(p => p != subSerial))
            {
                return null;
            }

            return new InvoiceStatusErrorMessageModel
            {
                TemplateNo = invoiceStatusModel.TemplateNo,
                SerialNo = invoiceStatusModel.SerialNo,
                InvoiceNo = invoiceStatusModel.InvoiceNo,
                CurrentStatus = invoiceStatusModel.CurrentStatus,
                NewStatus = invoiceStatusModel.NewStatus,
                InvoiceDate = invoiceStatusModel._InvoiceDate,
                ErrorMessage = errorMessage
            };
        }

        private InvoiceStatusErrorMessageModel ValidateOriginalInvoiceHeader(InvoiceStatusModel invoiceStatusModel)
        {
            var invoiceHeaderEntity = GetInvoiceHeaderEntity(invoiceStatusModel);

            // TODO: Không tìm thấy Entity
            if (invoiceHeaderEntity == null)
            {
                return new InvoiceStatusErrorMessageModel
                {
                    TemplateNo = invoiceStatusModel.TemplateNo,
                    SerialNo = invoiceStatusModel.SerialNo,
                    InvoiceNo = invoiceStatusModel.InvoiceNo,
                    CurrentStatus = invoiceStatusModel.CurrentStatus,
                    NewStatus = invoiceStatusModel.NewStatus,
                    InvoiceDate = invoiceStatusModel._InvoiceDate,
                    ErrorMessage = _localizer["Vnis.BE.Invoice01.OriginalInvoiceNotFound"]
                };

            }

            // TODO: Entity khác trạng thái trong file import
            if (invoiceHeaderEntity.InvoiceStatus != invoiceStatusModel.CurrentStatus)
            {
                return new InvoiceStatusErrorMessageModel
                {
                    TemplateNo = invoiceStatusModel.TemplateNo,
                    SerialNo = invoiceStatusModel.SerialNo,
                    InvoiceNo = invoiceStatusModel.InvoiceNo,
                    CurrentStatus = invoiceStatusModel.CurrentStatus,
                    NewStatus = invoiceStatusModel.NewStatus,
                    InvoiceDate = invoiceStatusModel._InvoiceDate,
                    ErrorMessage = _localizer["Vnis.BE.Invoice01.InvalidOldStautus"]
                };
            }

            // TODO: Validate Hóa đơn điều chỉnh định danh
            return ValidateOriginalAdjustmentDetailInvoiceHeader(invoiceStatusModel, invoiceHeaderEntity);

        }

        private InvoiceStatusErrorMessageModel ValidateOriginalAdjustmentDetailInvoiceHeader(InvoiceStatusModel invoiceStatusModel, Invoice01HeaderEntity invoiceHeaderEntity)
        {
            if (invoiceStatusModel.NewStatus != (short)InvoiceStatus.DieuChinhTangGiam)
            {
                return null;
            }

            // TODO: Entity có trạng thái xóa hủy
            if (invoiceHeaderEntity.InvoiceStatus == (short)InvoiceStatus.XoaHuy)
            {
                return new InvoiceStatusErrorMessageModel
                {
                    TemplateNo = invoiceStatusModel.TemplateNo,
                    SerialNo = invoiceStatusModel.SerialNo,
                    InvoiceNo = invoiceStatusModel.InvoiceNo,
                    CurrentStatus = invoiceStatusModel.CurrentStatus,
                    NewStatus = invoiceStatusModel.NewStatus,
                    InvoiceDate = invoiceStatusModel._InvoiceDate,
                    ErrorMessage = _localizer["Vnis.BE.Invoice.Validator.ApproveCancelStatus.Invalid"]
                };
            }

            // TODO: Entity có trạng thái xóa bỏ
            if (invoiceHeaderEntity.InvoiceStatus == (short)InvoiceStatus.XoaBo)
            {
                return new InvoiceStatusErrorMessageModel
                {
                    TemplateNo = invoiceStatusModel.TemplateNo,
                    SerialNo = invoiceStatusModel.SerialNo,
                    InvoiceNo = invoiceStatusModel.InvoiceNo,
                    CurrentStatus = invoiceStatusModel.CurrentStatus,
                    NewStatus = invoiceStatusModel.NewStatus,
                    InvoiceDate = invoiceStatusModel._InvoiceDate,
                    ErrorMessage = _localizer["Vnis.BE.Invoice.Validator.ApproveDeleteStatus.Invalid"]
                };
            }

            // TODO: Entity có trạng thái khác đã duyệt hoặc khác trạng thái không cần duyệt
            if (invoiceHeaderEntity.ApproveStatus != (short)ApproveStatus.DaDuyet
                && invoiceHeaderEntity.ApproveStatus != (short)ApproveStatus.KhongQuyTrinhDuyet
            )
            {
                return new InvoiceStatusErrorMessageModel
                {
                    TemplateNo = invoiceStatusModel.TemplateNo,
                    SerialNo = invoiceStatusModel.SerialNo,
                    InvoiceNo = invoiceStatusModel.InvoiceNo,
                    CurrentStatus = invoiceStatusModel.CurrentStatus,
                    NewStatus = invoiceStatusModel.NewStatus,
                    InvoiceDate = invoiceStatusModel._InvoiceDate,
                    ErrorMessage = _localizer["Vnis.BE.Invoice.Validator.ApproveStatus.Invalid"]
                };
            }

            // TODO: Entity có trạng thái khác gốc
            if (invoiceHeaderEntity.InvoiceStatus != (short)InvoiceStatus.Goc)
            {
                return new InvoiceStatusErrorMessageModel
                {
                    TemplateNo = invoiceStatusModel.TemplateNo,
                    SerialNo = invoiceStatusModel.SerialNo,
                    InvoiceNo = invoiceStatusModel.InvoiceNo,
                    CurrentStatus = invoiceStatusModel.CurrentStatus,
                    NewStatus = invoiceStatusModel.NewStatus,
                    InvoiceDate = invoiceStatusModel._InvoiceDate,
                    ErrorMessage = _localizer["Vnis.BE.Invoice.Validator.Adjustment.InvoiceStatus.Invalid"]
                };
            }

            // TODO: Entity có trạng thái khác đã ký
            if (invoiceHeaderEntity.SignStatus != (short)SignStatus.DaKy)
            {
                return new InvoiceStatusErrorMessageModel
                {
                    TemplateNo = invoiceStatusModel.TemplateNo,
                    SerialNo = invoiceStatusModel.SerialNo,
                    InvoiceNo = invoiceStatusModel.InvoiceNo,
                    CurrentStatus = invoiceStatusModel.CurrentStatus,
                    NewStatus = invoiceStatusModel.NewStatus,
                    InvoiceDate = invoiceStatusModel._InvoiceDate,
                    ErrorMessage = _localizer["Vnis.BE.Invoice.Validator.SignStatus.Invalid"]
                };
            }

            return null;
        }

        private InvoiceStatusErrorMessageModel ValidateInvoiceRelations(InvoiceStatusModel invoiceStatusModel)
        {
            if (invoiceStatusModel.NewStatus != (short)InvoiceStatus.DieuChinhTangGiam)
            {
                return null;
            }

            var invoiceHeaderRelationsResult = ValidateInvoiceHeaderRelations(invoiceStatusModel);
            if (invoiceHeaderRelationsResult != null)
            {
                return invoiceHeaderRelationsResult;
            }

            return ValidateInvoiceDetailRelations(invoiceStatusModel);
        }



        private InvoiceStatusErrorMessageModel ValidateInvoiceHeaderRelations(InvoiceStatusModel invoiceStatusModel)
        {
            var invoiceHeaderEntity = GetInvoiceHeaderEntity(invoiceStatusModel);
            var invoiceHeaderRelations = GetInvoiceHeaderRelations(invoiceHeaderEntity.Id);

            foreach (var invoiceHeaderRelation in invoiceHeaderRelations)
            {
                if (invoiceHeaderRelation.SignStatus != (short)SignStatus.DaKy)
                {
                    return new InvoiceStatusErrorMessageModel
                    {
                        TemplateNo = invoiceStatusModel.TemplateNo,
                        SerialNo = invoiceStatusModel.SerialNo,
                        InvoiceNo = invoiceStatusModel.InvoiceNo,
                        CurrentStatus = invoiceStatusModel.CurrentStatus,
                        NewStatus = invoiceStatusModel.NewStatus,
                        InvoiceDate = invoiceStatusModel._InvoiceDate,
                        ErrorMessage = $"Lập hóa đơn không thành công do có hóa đơn điều chỉnh/thay thế số {invoiceHeaderRelation.InvoiceNo} ký hiệu {invoiceHeaderRelation.SerialNo} ngày {invoiceHeaderRelation.InvoiceDate.ToString("dd/MM/yyyy")} chưa được ký hoặc xóa hủy"
                    };
                }
            }

            return null;

        }

        private InvoiceStatusErrorMessageModel ValidateInvoiceDetailRelations(InvoiceStatusModel invoiceStatusModel)
        {

            // TODO: HeaderOriginal, DetailOriginal
            var invoiceHeaderEntity = GetInvoiceHeaderEntity(invoiceStatusModel);
            var invoiceDetailEntities = GetInvoiceDetailEntities(new List<long> { invoiceHeaderEntity.Id })
                .OrderBy(p => p.Index)
                .ToList();

            // TODO:  HeaderRelations, DetailRelations, chỉ lấy DieuChinhTangGiam
            var invoiceHeaderRelations = GetInvoiceHeaderRelations(invoiceHeaderEntity.Id)
                .Where(p => p.InvoiceStatus == (short)InvoiceStatus.DieuChinhTangGiam)
                .ToList();
            if (!invoiceHeaderRelations.Any())
            {
                return null;
            }

            var invoiceHeaderRelationIds = invoiceHeaderRelations.Select(p => p.Id).ToList();
            var invoiceDetailRelations = GetInvoiceDetailRelations(invoiceHeaderRelationIds);

            var sumValues = invoiceDetailRelations
                .GroupBy(p => p.Index)
                .Select(p => new
                {
                    Index = p.Key,
                    Quantity = p.Sum(s => s.Quantity),
                    Amount = p.Sum(s => s.Amount),
                    VatAmount = p.Sum(s => s.VatAmount),
                    UnitPrice = p.Sum(s => s.UnitPrice),
                    PaymentAmount = p.Sum(s => s.PaymentAmount)
                })
                .ToList();
            foreach (var invoiceDetailEntity in invoiceDetailEntities)
            {
                var sumValue = sumValues.FirstOrDefault(p => p.Index == invoiceDetailEntity.Index);
                if (sumValue == null)
                {
                    return new InvoiceStatusErrorMessageModel
                    {
                        TemplateNo = invoiceStatusModel.TemplateNo,
                        SerialNo = invoiceStatusModel.SerialNo,
                        InvoiceNo = invoiceStatusModel.InvoiceNo,
                        CurrentStatus = invoiceStatusModel.CurrentStatus,
                        NewStatus = invoiceStatusModel.NewStatus,
                        InvoiceDate = invoiceStatusModel._InvoiceDate,
                        ErrorMessage = "Không tìm thấy invoice detail relation hợp lệ"
                    };
                }

                // TODO: Tổng giá trị của hđ liên quan +  (giá trị hóa đơn gốc + giá trị hóa đơn sẽ tạo = 0 )
                if (sumValue.Quantity < 0
                    || sumValue.Amount < 0
                    || sumValue.VatAmount < 0
                    || sumValue.UnitPrice < 0
                    || sumValue.PaymentAmount < 0
                )
                {
                    return new InvoiceStatusErrorMessageModel
                    {
                        TemplateNo = invoiceStatusModel.TemplateNo,
                        SerialNo = invoiceStatusModel.SerialNo,
                        InvoiceNo = invoiceStatusModel.InvoiceNo,
                        CurrentStatus = invoiceStatusModel.CurrentStatus,
                        NewStatus = invoiceStatusModel.NewStatus,
                        InvoiceDate = invoiceStatusModel._InvoiceDate,
                        ErrorMessage = _localizer["Vnis.BE.Invoice01.CreateAdjustDetail.CannotAdjustmentGreaterThanRootTotalPaymentAmount"]
                    };
                }
            }

            return null;
        }



        private void SetSource(InvoiceSource invoiceSource)
        {
            Source = invoiceSource == InvoiceSource.ShareAdapterG1
                || invoiceSource == InvoiceSource.ShareAdapterG2
                || invoiceSource == InvoiceSource.ShareAdapterG3
                || invoiceSource == InvoiceSource.ShareAdapterG4
                || invoiceSource == InvoiceSource.ShareAdapterG5
                || invoiceSource == InvoiceSource.ShareAdapterG6
            ? invoiceSource
            : InvoiceSource.Excel;
        }

        private InvoiceSource GetSource() => Source;

        private async Task<List<Invoice01HeaderEntity>> GetInvoiceHeaderEntitiesAsync(List<InvoiceStatusModel> invoiceStatusModels)
        {
            if (!InvoiceHeaderEntities.Any())
            {
                var currentTenantId = _currentTenant.GetId();

                var parameters = invoiceStatusModels
                    .GroupBy(p => new
                    {
                        p.TemplateNo,
                        p.SerialNo,
                        p.InvoiceNo,
                        InvoiceDate = p._InvoiceDate
                    })
                .Select(p => (
                    currentTenantId,
                    p.Key.TemplateNo,
                    p.Key.SerialNo,
                    p.Key.InvoiceNo,
                    p.Key.InvoiceDate
                ))
                .ToList();

                InvoiceHeaderEntities = await _baseInvoiceHeaderService.GetByInfoAsync(parameters);
            }

            return InvoiceHeaderEntities;
        }

        private Invoice01HeaderEntity GetInvoiceHeaderEntity(InvoiceStatusModel invoiceStatusModel)
            => InvoiceHeaderEntities
                .FirstOrDefault(x => x.TemplateNo == invoiceStatusModel.TemplateNo
                    && x.SerialNo == invoiceStatusModel.SerialNo
                    && x.InvoiceNo == invoiceStatusModel.InvoiceNo
                    && x.InvoiceDate == invoiceStatusModel._InvoiceDate
                );

        private void AddInvoiceHeaderId(InvoiceStatusModel invoiceStatusModel)
        {
            var invoiceHeaderEntity = GetInvoiceHeaderEntity(invoiceStatusModel);

            if (invoiceStatusModel.NewStatus == InvoiceStatus.XoaHuy.GetHashCode())
            {
                CancelInvoiceIds.Add(invoiceHeaderEntity.Id);
            }

            if (invoiceStatusModel.NewStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode())
            {
                AdjustmentDetailInvoiceIds.Add(invoiceHeaderEntity.Id);
            }
        }

        private async Task HandleCancelInvoices()
        {
            Log.Information($"-> CancelInvoiceIds = {JsonConvert.SerializeObject(CancelInvoiceIds)}");
            var cancelInvoiceHeaderDto = CreateCancelInvoices();
            await ValidateCancelInvoicesAsync(cancelInvoiceHeaderDto);
            await PublishCancleInvoicesAsync(cancelInvoiceHeaderDto);
        }
        private CancelInvoice01HeaderDto CreateCancelInvoices()
            => new CancelInvoice01HeaderDto { Ids = CancelInvoiceIds };

        private async Task ValidateCancelInvoicesAsync(CancelInvoice01HeaderDto cancelInvoiceHeaderDto)
        {
            var validator = _validatorFactory.GetValidator<CancelInvoice01HeaderDto>();
            var validationResult = await validator.HandleAsync(new CancelInvoice01HeaderDto { Ids = CancelInvoiceIds });
            if (!validationResult.Success)
            {
                throw new Exception(validationResult.Message);
            }
        }

        private async Task PublishCancleInvoicesAsync(CancelInvoice01HeaderDto cancelInvoiceHeaderDto)
        {
            var currentTenant = _appFactory.CurrentTenant;
            var currentUser = _appFactory.CurrentUser;
            var data = new CancelInvoice01HeaderEventSendData(cancelInvoiceHeaderDto);
            data.TenantId = currentTenant.GetId();
            data.UserId = currentUser.GetId();
            data.UserName = currentUser.UserName;

            await _distributedEventBus.PublishAsync(data);
        }

        private async Task HandleAdjustmentDetailInvoices()
        {
            Log.Information($"-> AdjustmentDetailInvoiceIds = {JsonConvert.SerializeObject(AdjustmentDetailInvoiceIds)}");

            // TODO: Tạo ModifyInvoiceHeaderDtos
            var modifyInvoice01HeaderDtos = CreateModifyInvoiceHeaderDtos(InvoiceHeaderEntities)
                .ToList();

            // TODO: Validate
            var validationResults = await _importExcelModifyValidationRule.ValidatorInvoiceStatusAsync(modifyInvoice01HeaderDtos);
            if (validationResults.Any())
            {
                throw new UserFriendlyException(validationResults.First().Message);
            }

            // TODO: Publish
            var importExcelFactoryService = new ImportExcelModifyFactoryService(_serviceProvider);
            var modifyPublish = importExcelFactoryService.CrateImportExcelAbstractPublish();
            await modifyPublish.PublishAsync(modifyInvoice01HeaderDtos);
        }
        private List<Invoice01HeaderEntity> GetInvoiceHeaderEntities(List<long> invoiceHeaderIds)
            => InvoiceHeaderEntities.Where(p => invoiceHeaderIds.Contains(p.Id)).ToList();

        private async Task<List<Invoice01DetailEntity>> GetInvoiceDetailEntitiesAsync(List<long> invoiceHeaderIds)
        {
            if (!InvoiceDetailEntities.Any())
            {
                InvoiceDetailEntities = await _baseInvoiceDetailService
                    .GetListAsync(invoiceHeaderIds, FromDate, ToDate);
            }

            return InvoiceDetailEntities;
        }

        private List<Invoice01DetailEntity> GetInvoiceDetailEntities(List<long> invoiceHeaderIds)
        {
            var invoiceDetailEntities = InvoiceDetailEntities
                .Where(p => invoiceHeaderIds.Contains(p.InvoiceHeaderId))
                .ToList();
            if (!invoiceDetailEntities.Any())
            {
                return new List<Invoice01DetailEntity>();
            }

            return invoiceDetailEntities;
        }

        private async Task<List<Invoice01TaxBreakdownEntity>> GetInvoiceTaxBreakdownEntitiesAsync(List<long> invoiceHeaderIds)
        {
            if (!InvoiceTaxBreakdownEntities.Any())
            {
                InvoiceTaxBreakdownEntities = await _baseInvoiceTaxBreakdownService
                    .GetListAsync(invoiceHeaderIds, FromDate, ToDate);
            }

            return InvoiceTaxBreakdownEntities;
        }

        private IEnumerable<ModifyInvoice01HeaderDto> CreateModifyInvoiceHeaderDtos(List<Invoice01HeaderEntity> invoiceHeaderEntities)
        {
            foreach (var invoiceHeaderEntity in invoiceHeaderEntities)
            {
                yield return CreateModifyInvoiceHeaderDto(invoiceHeaderEntity);
            }
        }

        private ModifyInvoice01HeaderDto CreateModifyInvoiceHeaderDto(Invoice01HeaderEntity invoiceHeaderEntity)
        {
            // TODO: Set Source
            SetSource((InvoiceSource)invoiceHeaderEntity.Source);

            // TODO: Create HeaderExtras
            var invoiceHeaderExtraPropertyEntities = InvoiceHeaderExtraPropertyEntities
                .Where(p => p.Invoice01HeaderId == invoiceHeaderEntity.Id)
                .ToList();
            var invoiceHeaderExtras = CreateModifyInvoiceHeaderExtras(invoiceHeaderExtraPropertyEntities);

            // TODO: Create Detail
            var invoiceDetailEntities = InvoiceDetailEntities
                .Where(p => p.InvoiceHeaderId == invoiceHeaderEntity.Id)
                .ToList();
            var invoiceDetails = CreateModifyInvoiceDetailDtos(invoiceDetailEntities)
                .ToList();

            // TODO: Create TaxBreakdown
            var invoiceTaxBreakdwonEntities = InvoiceTaxBreakdownEntities
                .Where(p => p.InvoiceHeaderId == invoiceHeaderEntity.Id)
                .ToList();
            var invoiceTaxBreakdowns = CreateModifyInvoiceTaxBreakdownDtos(invoiceTaxBreakdwonEntities)
                .ToList();


            var totalAmount = invoiceDetails.Sum(p => p.Amount);
            var totalVatAmount = invoiceDetails.Sum(p => p.VatAmount);
            var totalPaymentAmount = invoiceDetails.Sum(p => p.PaymentAmount);
            var toCurrency = GetCurrencyEntity(invoiceHeaderEntity.ToCurrency);
            var paymentAmountWordsVi = _baseReadMoneyService.ReadMoneyVi(_currentTenant.GetId(), toCurrency, totalPaymentAmount, InvoiceStatus.DieuChinhTangGiam);
            var paymentAmountWordsEn = _baseReadMoneyService.ReadMoneyEn(_currentTenant.Id.Value, toCurrency, totalPaymentAmount, InvoiceStatus.DieuChinhTangGiam);

            var firstInvoiceDetail = invoiceDetails.First();

            var erpId = firstInvoiceDetail.ErpId;
            var serialNo = CreateSerialNo(invoiceHeaderEntity.SerialNo, firstInvoiceDetail.RowNumber);
            var invoiceTemplateId = GetInvoiceTemplateId(invoiceHeaderEntity.TemplateNo, serialNo);
            return new ModifyInvoice01HeaderDto
            {
                InvoiceType = ModifyInvoiceType.AdjustmentDetail,

                TenantId = _currentTenant.GetId(),
                CreatorId = _currentUser.GetId(),
                Resource = GetSource(),
                ErpId = erpId,
                CreatorErp = invoiceHeaderEntity.CreatorErp,
                TransactionId = invoiceHeaderEntity.TransactionId,
                InvoiceTemplateId = invoiceTemplateId,
                TemplateNo = invoiceHeaderEntity.TemplateNo,
                SerialNo = serialNo,
                Note = invoiceHeaderEntity.Note,
                InvoiceDate = _invoiceDate,
                InvoiceStatus = InvoiceStatus.DieuChinhTangGiam,
                SignStatus = SignStatus.ChoKy,
                ApproveStatus = _approveStatus,
                ApproveCancelStatus = _approveCancelStatus,
                ApproveDeleteStatus = _approveDeleteStatus,

                BuyerCode = invoiceHeaderEntity.BuyerCode,
                BuyerFullName = invoiceHeaderEntity.BuyerFullName,
                BuyerTaxCode = invoiceHeaderEntity.BuyerTaxCode,
                BuyerAddressLine = invoiceHeaderEntity.BuyerAddressLine,
                BuyerEmail = invoiceHeaderEntity.BuyerEmail,
                BuyerBankName = invoiceHeaderEntity.BuyerBankName,
                BuyerBankAccount = invoiceHeaderEntity.BuyerBankAccount,
                BuyerType = invoiceHeaderEntity.BuyerType,

                FromCurrency = invoiceHeaderEntity.FromCurrency,
                ToCurrency = invoiceHeaderEntity.ToCurrency,
                RoundingCurrency = invoiceHeaderEntity.RoundingCurrency,
                CurrencyConversion = invoiceHeaderEntity.CurrencyConversion,
                ExchangeRate = invoiceHeaderEntity.ExchangeRate,

                DepartmentCode = invoiceHeaderEntity.DepartmentCode,
                PaymentMethod = invoiceHeaderEntity.PaymentMethod,

                SellerId = invoiceHeaderEntity.SellerId,
                SellerCode = invoiceHeaderEntity.SellerCode,
                SellerTaxCode = invoiceHeaderEntity.SellerTaxCode,
                SellerAddressLine = invoiceHeaderEntity.SellerAddressLine,
                SellerCountryCode = invoiceHeaderEntity.SellerCountryCode,
                SellerDistrictName = invoiceHeaderEntity.SellerDistrictName,
                SellerCityName = invoiceHeaderEntity.SellerCityName,
                SellerPhoneNumber = invoiceHeaderEntity.SellerPhoneNumber,
                SellerFaxNumber = invoiceHeaderEntity.SellerFaxNumber,
                SellerEmail = invoiceHeaderEntity.SellerEmail,
                SellerBankName = invoiceHeaderEntity.SellerBankName,
                SellerBankAccount = invoiceHeaderEntity.SellerBankAccount,
                SellerLegalName = invoiceHeaderEntity.SellerLegalName,
                SellerFullName = invoiceHeaderEntity.SellerFullName,

                TotalAmount = totalAmount,
                TotalVatAmount = totalVatAmount,
                TotalPaymentAmount = totalPaymentAmount,
                PaymentAmountWords = paymentAmountWordsVi,
                PaymentAmountWordsEn = paymentAmountWordsEn,

                InvoiceReferenceId = invoiceHeaderEntity.Id,
                TemplateNoReference = invoiceHeaderEntity.TemplateNo,
                SerialNoReference = invoiceHeaderEntity.SerialNo,
                InvoiceNoReference = invoiceHeaderEntity.InvoiceNo,
                NumberReference = invoiceHeaderEntity.Number,
                InvoiceDateReference = invoiceHeaderEntity.InvoiceDate,
                NoteReference = invoiceHeaderEntity.Note,

                InvoiceDetails = invoiceDetails,
                InvoiceTaxBreakdowns = invoiceTaxBreakdowns,
                InvoiceHeaderExtras = invoiceHeaderExtras
            };
        }


        private IEnumerable<ModifyInvoice01DetailDto> CreateModifyInvoiceDetailDtos(List<Invoice01DetailEntity> invoiceDetailEntities)
        {
            foreach (var invoiceDetailEntity in invoiceDetailEntities)
            {
                yield return CreateModifyInvoiceDetailDto(invoiceDetailEntity);
            }
        }

        private ModifyInvoice01DetailDto CreateModifyInvoiceDetailDto(Invoice01DetailEntity invoiceDetailEntity)
        {

            var productCode = StringExtension.RandomByDateTime();

            // TODO: Get ErpId
            var erpId = GetErpId(Source);

            // TODO: Create Detail Extras
            var invoiceDetailExtraPropertyEntities = InvoiceDetailExtraPropertyEntities
                .Where(p => p.Invoice01DetailId == invoiceDetailEntity.Id)
                .ToList();
            var invoiceDetailExtras = CreateModifyInvoiceDetailExtras(invoiceDetailExtraPropertyEntities);

            return new ModifyInvoice01DetailDto
            {
                Index = invoiceDetailEntity.Index,
                Amount = -invoiceDetailEntity.Amount,
                VatAmount = -invoiceDetailEntity.VatAmount,
                VatPercent = invoiceDetailEntity.VatPercent,
                VatPercentDisplay = invoiceDetailEntity.VatPercentDisplay,
                Quantity = -invoiceDetailEntity.Quantity,
                PaymentAmount = -invoiceDetailEntity.PaymentAmount,
                Note = invoiceDetailEntity.Note,
                ErpId = erpId,
                RowNumber = StartRowNumber++,
                ProductId = invoiceDetailEntity.ProductId,
                ProductName = invoiceDetailEntity.ProductName,
                ProductCode = productCode,
                ProductType = invoiceDetailEntity.ProductType,
                UnitId = invoiceDetailEntity.UnitId,
                UnitName = invoiceDetailEntity.UnitName,
                UnitPrice = -invoiceDetailEntity.UnitPrice,
                RoundingUnit = invoiceDetailEntity.RoundingUnit,
                HideQuantity = invoiceDetailEntity.HideQuantity,
                HideUnitPrice = invoiceDetailEntity.HideUnitPrice,
                InvoiceDetailExtras = invoiceDetailExtras,
            };
        }

        private List<BaseImportInvoice01ExtraDto> CreateModifyInvoiceDetailExtras(List<Invoice01DetailExtraPropertyEntity> invoiceDetailExtraPropertyEntities)
        {
            var results = invoiceDetailExtraPropertyEntities
                .Select(p => new BaseImportInvoice01ExtraDto
                {
                    FieldName = p.FieldName,
                    FieldValue = p.FieldValue
                })
                .ToList();

            return results;
        }

        private IEnumerable<ModifyInvoice01TaxBreakdownDto> CreateModifyInvoiceTaxBreakdownDtos(List<Invoice01TaxBreakdownEntity> invoiceTaxBreakdownEntities)
        {
            foreach (var invoiceTaxBreakdownEntity in invoiceTaxBreakdownEntities)
            {
                yield return CreateModifyInvoiceTaxBreakdownDto(invoiceTaxBreakdownEntity);
            }
        }

        private ModifyInvoice01TaxBreakdownDto CreateModifyInvoiceTaxBreakdownDto(Invoice01TaxBreakdownEntity invoiceTaxBreakdownEntity)
        {
            return new ModifyInvoice01TaxBreakdownDto
            {
                Name = invoiceTaxBreakdownEntity.Name,
                VatAmount = -invoiceTaxBreakdownEntity.VatAmount,
                VatPercent = invoiceTaxBreakdownEntity.VatPercent,
                VatPercentDisplay = invoiceTaxBreakdownEntity.VatPercentDisplay,
                VatAmountBackUp = -invoiceTaxBreakdownEntity.VatAmountBackUp
            };
        }

        private List<ModifyInvoice01ExtraDto> CreateModifyInvoiceHeaderExtras(List<Invoice01HeaderExtraPropertyEntity> invoice01HeaderExtraPropertyEntities)
        {
            var results = invoice01HeaderExtraPropertyEntities
                .Select(p => new ModifyInvoice01ExtraDto
                {
                    FieldName = p.FieldName,
                    FieldValue = p.FieldValue
                })
                .ToList();

            // TODO: PCTime
            ProcessPcTime(results);

            // TODO: TellSeq
            ProcessTellSeq(results);

            return results;
        }

        private void ProcessPcTime(List<ModifyInvoice01ExtraDto> modifyInvoice01ExtraDtos)
        {
            PcTime = _random.Next(300000, 999999).ToString();

            var modifyInvoice01ExtraDto = modifyInvoice01ExtraDtos
                .FirstOrDefault(p => p.FieldName == MetadataExtraKey._PCTIME);
            if (modifyInvoice01ExtraDto == null)
            {
                modifyInvoice01ExtraDtos.Add(new ModifyInvoice01ExtraDto
                {
                    FieldName = MetadataExtraKey._PCTIME,
                    FieldValue = PcTime
                });

                return;
            }

            modifyInvoice01ExtraDto.FieldValue = PcTime;
        }

        private void ProcessTellSeq(List<ModifyInvoice01ExtraDto> modifyInvoice01ExtraDtos)
        {
            var modifyInvoice01ExtraDto = modifyInvoice01ExtraDtos
                .FirstOrDefault(p => p.FieldName == MetadataExtraKey._TELLSEQ);
            if (modifyInvoice01ExtraDto != null)
            {
                TellSeq = String.Empty;
                return;
            }

            TellSeq = modifyInvoice01ExtraDto.FieldValue;
        }


        /// <summary>
        /// Lấy danh sách Currency
        /// </summary>
        private async Task<List<CurrencyEntity>> GetCurrencyEntitiesAsync()
        {
            if (!CurrencyEntities.Any())
            {
                CurrencyEntities = await _baseCurrencyService.GetByTenantIdFromCacheAsync(_currentTenant.GetId());
            }

            return CurrencyEntities;
        }

        private CurrencyEntity GetCurrencyEntity(string currencyCode)
        {
            var result = CurrencyEntities
                .FirstOrDefault(p => p.CurrencyCode == currencyCode);
            if (result == null)
            {
                throw new Exception($"Không tìm thấy đơn vị tiền tệ có CurrencyCode = {currencyCode}");
            }

            return result;
        }

        private string GetErpId(InvoiceSource invoiceSource)
        {
            if (invoiceSource == InvoiceSource.ShareAdapterG4 || invoiceSource == InvoiceSource.ShareAdapterG5)
            {
                // sinh them Gx name cho giong G4 Adapter
                return Guid.NewGuid().ToString();
            }

            return _baseErpIdService.Create(
                PcTime,
                _invoiceDate,
                _currentTenant.TenantCode,
                _currentUser.CashierCode,
                TellSeq
            );
        }

        private async Task<List<Invoice01HeaderExtraPropertyEntity>> GetInvoiceHeaderExtraPropertyEntities(List<long> invoiceHeaderIds)
        {
            if (!InvoiceHeaderExtraPropertyEntities.Any())
            {
                InvoiceHeaderExtraPropertyEntities = await _baseInvoiceHeaderExtraPropertyService
                    .GetListAsync(invoiceHeaderIds, FromDate, ToDate);
            }

            return InvoiceHeaderExtraPropertyEntities;
        }

        private async Task<List<Invoice01DetailExtraPropertyEntity>> GetInvoiceDetailExtraPropertyEntities(List<long> invoiceHeaderIds)
        {
            if (!InvoiceDetailExtraPropertyEntities.Any())
            {
                var invoiceDetailIds = InvoiceDetailEntities
                    .Where(p => invoiceHeaderIds.Contains(p.InvoiceHeaderId))
                    .Select(p => p.Id).ToList();

                InvoiceDetailExtraPropertyEntities = await _baseInvoiceDetailExtraPropertyService
                    .GetListAsync(invoiceDetailIds, FromDate, ToDate);
            }

            return InvoiceDetailExtraPropertyEntities;
        }

        private string CreateSerialNo(string serialNo, int rowNumber)
        {
            // TODO: SerialNoTransfer
            var (errorMessage, serialNoTransfer) = _serialNoTransferService.Transfer(serialNo, rowNumber);
            if (!string.IsNullOrWhiteSpace(errorMessage))
            {
                throw new Exception(errorMessage);
            }

            return serialNoTransfer;
        }

        private long GetInvoiceTemplateId(short templateNo, string serialNo)
        {
            var templateEntity = InvoiceTemplateEntities
                .FirstOrDefault(p => p.TemplateNo == templateNo
                    && p.SerialNo == serialNo
                );
            if (templateEntity == null)
            {
                throw new Exception($"Không tìm thấy InvoiceTemplate hợp lệ với templateNo = {templateNo}, serialNo = {serialNo}");
            }

            return templateEntity.Id;
        }

        public async Task<List<InvoiceTemplateEntity>> GetInvoiceTemplateEntitiesAsync()
        {
            if (!InvoiceTemplateEntities.Any())
            {
                InvoiceTemplateEntities = await _baseInvoiceTemplateService.GetByTenantIdFromCacheAsync(_currentTenant.GetId());
            }

            return InvoiceTemplateEntities;
        }

        private async Task<List<Invoice01ReferenceEntity>> GetInvoiceReferences(List<long> invoiceReferenceIds)
        {
            if (!InvoiceReferences.Any())
            {

                InvoiceReferences = await _baseInvoiceReferenceService.GetListByInvoiceReferenceIds(invoiceReferenceIds);
            }

            return InvoiceReferences;

        }

        private async Task<List<Invoice01HeaderEntity>> GetInvoiceHeaderRelationsAsync(List<long> invoiceReferenceIds)
        {
            if (!InvoiceHeaderRelations.Any())
            {

                var invoiceReferences = await GetInvoiceReferences(invoiceReferenceIds);
                if (!invoiceReferences.Any())
                {
                    return InvoiceHeaderRelations;
                }

                var headerIds = invoiceReferences
                    .Select(p => p.InvoiceHeaderId)
                    .ToList();

                var invoiceHeaderRelations = await _baseInvoiceHeaderService
                    .GetListAsync(headerIds, FromDate, DateTime.Now); // ToDate = DateTime.Now vì sẽ có trường hợp hđ liên quan tạo khác ngày với hóa đơn gốc

                InvoiceHeaderRelations = invoiceHeaderRelations
                    .Where(p => p.InvoiceStatus != (short)InvoiceStatus.XoaBo && p.InvoiceStatus != (short)InvoiceStatus.XoaHuy)
                    .ToList();
            }

            return InvoiceHeaderRelations;
        }

        private List<Invoice01HeaderEntity> GetInvoiceHeaderRelations(long invoiceReferenceId)
        {
            var invoiceReferences = InvoiceReferences
                .Where(p => p.InvoiceReferenceId == invoiceReferenceId)
                .ToList();

            if (!invoiceReferences.Any())
            {
                return new List<Invoice01HeaderEntity>();
            }

            var invoiceHeaderIds = invoiceReferences
                .Select(p => p.InvoiceHeaderId)
                .ToList();

            var invoiceHeaderRelation = InvoiceHeaderRelations
                .Where(p => invoiceHeaderIds.Contains(p.Id))
                .ToList();

            if (!invoiceHeaderRelation.Any())
            {
                return new List<Invoice01HeaderEntity>();
            }

            return invoiceHeaderRelation;

        }

        private async Task<List<Invoice01DetailEntity>> GetInvoiceDetailRelationsAsync(List<long> invoiceReferenceIds)
        {
            if (!InvoiceDetailRelations.Any())
            {
                var invoiceReferences = await GetInvoiceReferences(invoiceReferenceIds);
                if (!invoiceReferences.Any())
                {
                    return InvoiceDetailRelations;
                }

                var headerIds = invoiceReferences
                    .Select(p => p.InvoiceHeaderId)
                    .ToList();
                InvoiceDetailRelations = await _baseInvoiceDetailService
                    .GetListAsync(headerIds, FromDate, DateTime.Now); // ToDate = DateTime.Now vì sẽ có trường hợp hđ liên quan tạo khác ngày với hóa đơn gốc
            }

            return InvoiceDetailRelations;
        }

        private List<Invoice01DetailEntity> GetInvoiceDetailRelations(List<long> headerIds)
        {
            var invoiceDetailRelations = InvoiceDetailRelations
                .Where(p => headerIds.Contains(p.InvoiceHeaderId))
                .ToList();
            if (!invoiceDetailRelations.Any())
            {
                return new List<Invoice01DetailEntity>();
            }

            return invoiceDetailRelations;
        }
    }
}
