using Core.Shared.Extensions;
using Core.Shared.Models;
using Dapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Vcb.Invoice01.Application.Invoice01.Handlers.IQueries;

namespace Vcb.Invoice01.Application.Invoice01.Handlers.Queries
{
    public class GetInfosBeforePublishCreateInvoice01QueryHandler : IGetInfosBeforePublishCreateInvoice01QueryHandler
    {
        public QueryDataModel GenerateDrawInfosBeforePublishCreateInvoice01Query(Guid tenantId, List<string> productCodes, List<string> unitNames)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var dicParameter = new Dictionary<string, object>
            {
                { ":TenantId", rawTenantId },
                { ":IsDeleted", 0 },
            };


            // TODO: productCodes
            var andProductCode = new StringBuilder();
            andProductCode.Append($@" AND ""ProductCode"" IN ('') ");

            if (productCodes != null && productCodes.Any())
            {
                var paramProductCodes = productCodes.Where(p => !string.IsNullOrWhiteSpace(p));
                if (paramProductCodes.Any())
                {
                    andProductCode.Append($@" AND ""ProductCode"" IN :ProductCodes ");
                    dicParameter.Add(":ProductCodes", paramProductCodes);
                }
            }

            // TODO: unitNames
            var andUnitName = new StringBuilder();
            andUnitName.Append($@" AND ""NormalizedName"" IN ('') ");
            if (unitNames != null && unitNames.Any())
            {
                var paramUnitNames = unitNames.Where(p => !string.IsNullOrWhiteSpace(p)).Select(x => x.ToUpper());
                if (paramUnitNames.Any())
                {
                    andUnitName.Append($@" AND ""NormalizedName"" IN :NormalizedNames ");
                    dicParameter.Add(":NormalizedNames", paramUnitNames);
                }
            }

            var sql = new StringBuilder();
            sql.Append($@"  WITH Products AS (                                                                     
                                  SELECT ""Id"", ""ProductTypeId"", ""ProductCode""                                    
                                  FROM ""Product""                                                                     
                                  WHERE                                                                                
                                      ""TenantId"" = :TenantId                                                   
                                      {andProductCode}                                                                 
                                      AND ""IsDeleted"" = :IsDeleted                                                            
                              ),                                                                                       
                              ProductTypes AS(                                                                         
                                SELECT pt.""Id"", pt.""HideQuantity"", pt.""HideUnit"", pt.""HideUnitPrice""                     
                                FROM ""ProductType"" pt
                                JOIN Products p
                                ON pt.""Id"" = p.""ProductTypeId""
                                WHERE pt.""IsDeleted"" = :IsDeleted                                                            
                              ),                                                                                         
                              Units AS(                                                                                
                                  SELECT ""Id"", ""Name"", ""Rounding""                                                
                                  FROM ""Unit""                                                                        
                                  WHERE                                                                                
                                      ""TenantId"" = :TenantId                                                   
                                      {andUnitName}                                                                    
                                      AND ""IsDeleted"" = :IsDeleted                                                            
                              ),                                                                                       
                              HeaderFields AS(                                                                         
                                  SELECT ""Id"", ""FieldName""                                                         
                                  FROM ""Invoice01HeaderField""                                                        
                                  WHERE ""TenantId"" = :TenantId AND ""IsDeleted"" = :IsDeleted                                                
                              ),                                                                                       
                              DetailFields AS(                                                                         
                                  SELECT ""Id"", ""FieldName""                                                         
                                  FROM ""Invoice01DetailField""                                                        
                                  WHERE ""TenantId"" = :TenantId AND ""IsDeleted"" = :IsDeleted                                                
                              )                                                                                        
                              SELECT 1 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM Products    
                              UNION ALL                                                                                                    
                              SELECT 2 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM ProductTypes
                              UNION ALL                                                                                                    
                              SELECT 3 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM Units       
                              UNION ALL                                                                                                    
                              SELECT 4 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM HeaderFields
                              UNION ALL                                                                                                    
                              SELECT 5 ""Type"", ( JSON_ARRAYAGG( JSON_OBJECT(* RETURNING CLOB) RETURNING CLOB)) ""Data"" FROM DetailFields
            ");

            return new QueryDataModel
            {
                Query = sql.ToString(),
                Parameters = new DynamicParameters(dicParameter)
            };
        }
    }
}
