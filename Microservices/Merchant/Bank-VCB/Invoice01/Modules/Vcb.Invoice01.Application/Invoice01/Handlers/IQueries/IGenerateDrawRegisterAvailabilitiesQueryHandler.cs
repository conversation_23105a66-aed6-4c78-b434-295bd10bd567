using Core.Shared.Models;
using System;

namespace Vcb.Invoice01.Application.Invoice01.Handlers.IQueries
{
    public interface IGenerateDrawRegisterAvailabilitiesQueryHandler
    {
        /// <summary>
        /// Tạo câu query lấy danh sách đăng ký khả dụng
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="userId"></param>
        /// <param name="templateNo"></param>
        /// <returns></returns>
        QueryDataModel GenerateDrawRegisterAvailabilitiesQuery(Guid tenantId, Guid userId, short templateNo);
    }
}
