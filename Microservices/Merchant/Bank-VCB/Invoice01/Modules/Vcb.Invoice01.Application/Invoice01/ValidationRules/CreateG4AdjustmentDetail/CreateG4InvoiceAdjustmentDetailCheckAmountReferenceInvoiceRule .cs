using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Dapper;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using Serilog;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Vcb.Invoice01.Application.Invoice01.Models.Requests.Commands;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Invoice01.Application.Factories.Repositories;
using VnisCore.Invoice01.Application.Invoice01.Dto;

namespace Vcb.Invoice01.Application.Invoice01.ValidationRules.CreateG4AdjustmentDetail
{
    /// <summary>
    /// kiểm tra thông tin tiền và đơn giá, số lượng của hóa đơn có bị điều chỉnh giảm qua các lần tổng cộng lại < 0 hay không
    /// </summary>
    public class CreateG4InvoiceAdjustmentDetailCheckAmountReferenceInvoiceRule : IValidationRuleAsync<CreateG4Invoice01AdjustmentDetailRequest, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IValidationContext _validationContext;
        private readonly IAppFactory _appFactory;

        public CreateG4InvoiceAdjustmentDetailCheckAmountReferenceInvoiceRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IValidationContext validationContext,
            IAppFactory appFactory)
        {
            _localizer = localizer;
            _validationContext = validationContext;
            _appFactory = appFactory;
        }

        public async Task<ValidationResult> HandleAsync(CreateG4Invoice01AdjustmentDetailRequest input)
        {

            var rootInvoice = _validationContext.GetItem<Invoice01HeaderEntity>("InvoiceReference");

            if (input.Source == InvoiceSource.ShareAdapterG4Excel)
            {
                return new ValidationResult(true);
            }

            // kiểm tra xem hóa đơn GỐC đã bị thay thế/điều chỉnh bởi hóa đơn khác chưa
            //tìm kiếm trong bảng InvoiceReference đã có bản ghi nào có InvoiceReferenceCode = Code hóa đơn không
            //vì khi tạo hóa đơn thay thế/điều chỉnh đã insert sẵn 1 bản ghi vào bảng InvoiceRefererence có InvoiceReferenceCode = Code hóa đơn gốc (kể cả dcdd k sinh số)
            var repoInvoiceRefernce = _appFactory.GetServiceDependency<IInvoiceReferenceRepository<Invoice01ReferenceEntity>>();
            var invoiceReference = await repoInvoiceRefernce.GetByInvoiceReferenceCodeAsync(rootInvoice.Id);

            var invoiceTransactionDetailSummaries = new List<Invoice01DetailDto>();

            var invoiceAdjAutoDetailSummaries = new List<Invoice01DetailDto>();

            // Tính toán tổng tiền hóa đơn
            if (invoiceReference == null)
            {
                if (input.TotalPaymentAmount + rootInvoice.TotalPaymentAmount < 0)
                    return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.CreateAdjustDetail.CannotAdjustmentGreaterThanRootTotalPaymentAmount"]);

                if (input.TotalAmount + rootInvoice.TotalAmount < 0)
                    return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.CreateAdjustDetail.CannotAdjustmentGreaterThanRootTotalAmount"]);

                if (input.TotalVatAmount + rootInvoice.TotalVatAmount < 0)
                    return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.CreateAdjustDetail.CannotAdjustmentGreaterThanRootTotalVatAmount"]);

                var invoiceTransactionDetailReferences = await GetInvoiceTransactionDetailReferencesAsync(new List<long> { rootInvoice.Id });

                invoiceTransactionDetailSummaries.AddRange(invoiceTransactionDetailReferences);
            }
            else
            {
                decimal totalPaymentAmountAdj = 0;
                decimal totalAmountAdj = 0;
                decimal totalVatAmountAdj = 0;

                var invoiceReferences = await repoInvoiceRefernce.GetByInvoiceReferenceAsync(rootInvoice.Id);
                var idInvoiceHeaders = invoiceReferences.Select(x => x.InvoiceHeaderId).ToList();
                idInvoiceHeaders.Add(rootInvoice.Id);

                var invoiceHeaderReferences = await GetInvoiceHeaderReferencesAsync(idInvoiceHeaders);

                //bỏ qua các hóa đơn xóa hủy/xóa bỏ
                invoiceHeaderReferences = invoiceHeaderReferences.Where(x => x.InvoiceStatus != InvoiceStatus.XoaHuy.GetHashCode()
                                                                             && x.InvoiceStatus != InvoiceStatus.XoaBo.GetHashCode())
                                                                 .ToList();

                idInvoiceHeaders = invoiceHeaderReferences.Select(x => x.Id).ToList();

                var invoiceTransactionDetailReferences = await GetInvoiceTransactionDetailReferencesAsync(idInvoiceHeaders);

                foreach (var invoiceHeaderReference in invoiceHeaderReferences)
                {
                    // Hóa đơn đã điều chỉnh mà chưa ký hay chưa xóa hủy không được lập hóa đơn điều chỉnh khác
                    // không phân biệt hóa đơn điều chỉnh định danh hay tăng giảm
                    if (invoiceHeaderReference.SignStatus != SignStatus.DaKy.GetHashCode() && invoiceHeaderReference.InvoiceStatus != InvoiceStatus.XoaHuy.GetHashCode())
                        return new ValidationResult(false, $"Lập hóa đơn không thành công do có hóa đơn điều chỉnh số {invoiceHeaderReference.InvoiceNo} ký hiệu {invoiceHeaderReference.SerialNo} ngày {invoiceHeaderReference.InvoiceDate.ToString("dd/MM/yyyy")} chưa được ký hoặc xóa hủy");

                    // nếu hóa đơn điều chỉnh bị xóa bỏ thì tiền đc trở về 0
                    if (invoiceHeaderReference.InvoiceStatus == InvoiceStatus.XoaHuy.GetHashCode())
                        continue;

                    totalPaymentAmountAdj += invoiceHeaderReference.TotalPaymentAmount;
                    totalAmountAdj += invoiceHeaderReference.TotalAmount;
                    totalVatAmountAdj += invoiceHeaderReference.TotalVatAmount;
                }

                if (input.TotalPaymentAmount < 0 && input.TotalPaymentAmount + totalPaymentAmountAdj < 0)
                    return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.CreateAdjustDetail.CannotAdjustmentGreaterThanRootTotalPaymentAmount"]);

                if (input.TotalAmount < 0 && input.TotalAmount + totalAmountAdj < 0)
                    return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.CreateAdjustDetail.CannotAdjustmentGreaterThanRootTotalAmount"]);

                if (input.TotalVatAmount < 0 && input.TotalVatAmount + totalVatAmountAdj < 0)
                    return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.CreateAdjustDetail.CannotAdjustmentGreaterThanRootTotalVatAmount"]);

                //group các detail của các hóa đơn

                invoiceTransactionDetailSummaries.AddRange(invoiceTransactionDetailReferences);
                invoiceAdjAutoDetailSummaries = await GetInvoiceDetailAdjustmentDetailAutoReferencesAsync(idInvoiceHeaders);
            }

            if (input.InvoiceTransactionDetails != null && input.InvoiceTransactionDetails.Any())
            {
                invoiceTransactionDetailSummaries.AddRange(input.InvoiceTransactionDetails.Select(x => new Invoice01DetailDto
                {
                    Index = x.Index,
                    Amount = x.Amount,
                    PaymentAmount = x.PaymentAmount,
                    VatAmount = x.VatAmount,
                    VatPercent = x.VatPercent,
                    Quantity = x.Quantity,
                    UnitPrice = x.UnitPrice
                }));
            }

            #region So sánh thông tin trong transaction detail
            var lastTotalInvoiceTransactionDetails = new List<Invoice01DetailDto>();

            //tính tổng các phần tổng tiền ở transaction detail
            var groupInvoiceTransactionDetailByIndexs = invoiceTransactionDetailSummaries.GroupBy(x => x.Index);

            foreach (var groupInvoiceTransactionDetailByIndex in groupInvoiceTransactionDetailByIndexs)
            {
                var invoiceDetailLast = groupInvoiceTransactionDetailByIndex.OrderBy(x => x.InvoiceHeaderId).LastOrDefault();

                invoiceDetailLast.Index = groupInvoiceTransactionDetailByIndex.Key;
                invoiceDetailLast.Amount = groupInvoiceTransactionDetailByIndex.Sum(x => x.Amount);
                invoiceDetailLast.PaymentAmount = groupInvoiceTransactionDetailByIndex.Sum(x => x.PaymentAmount);
                invoiceDetailLast.VatAmount = groupInvoiceTransactionDetailByIndex.Sum(x => x.VatAmount);
                invoiceDetailLast.UnitPrice = groupInvoiceTransactionDetailByIndex.Sum(x => x.UnitPrice);
                invoiceDetailLast.Quantity = groupInvoiceTransactionDetailByIndex.Sum(x => x.Quantity); //do k cho điều chỉnh số lượng nên bảng kê phần này của các hóa đơn điều chỉnh = 0, hóa đơn gốc là 1 => sum lại = 1

                lastTotalInvoiceTransactionDetails.Add(invoiceDetailLast);
            }

            foreach (var item in lastTotalInvoiceTransactionDetails)
            {
                if (item.Amount < 0)
                    return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.CreateAdjustDetail.CannotAdjustmentGreaterThanRootAmount", new string[] { item.Index.ToString() }]);

                if (item.PaymentAmount < 0)
                    return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.CreateAdjustDetail.CannotAdjustmentGreaterThanRootPaymentAmount", new string[] { item.Index.ToString() }]);

                if (item.VatAmount < 0)
                    return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.CreateAdjustDetail.CannotAdjustmentGreaterThanRootDetailVatAmount", new string[] { item.Index.ToString() }]);

                if (item.UnitPrice < 0)
                    return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.CreateAdjustDetail.CannotAdjustmentGreaterThanRootUnitPrice", new string[] { item.Index.ToString() }]);

                //if (item.Quantity < 0)
                //    return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.CreateAdjustDetail.CannotAdjustmentGreaterThanRootQuantity", new string[] { item.Index.ToString() }]);
            }
            #endregion

            // Nếu hóa đơn có điều chỉnh tự động thì cần so sánh thêm thông tin
            if (invoiceAdjAutoDetailSummaries.Count > 0)
            {
                Log.Information($"detailAutos: {JsonConvert.SerializeObject(invoiceAdjAutoDetailSummaries)}");

                // TODO: Bỏ TransactionDetails của hóa đơn import thay đổi trạng thái hóa đơn vì
                // lastTotalInvoiceTransactionDetails đã có.
                var headerIds = invoiceAdjAutoDetailSummaries.Select(p => p.InvoiceHeaderId).Distinct();

                #region So sánh thông tin trong detail
                var grpDetail = lastTotalInvoiceTransactionDetails
                    .Where(p => !headerIds.Contains(p.InvoiceHeaderId))
                    .GroupBy(x => new { x.ProductName, x.VatPercent }).Select(x => new Invoice01DetailDto
                    {
                        ProductName = x.Key.ProductName,
                        VatPercent = x.Key.VatPercent,
                        PaymentAmount = x.Sum(y => y.PaymentAmount),
                        Amount = x.Sum(y => y.Amount),
                    });
                Log.Information($"grpDetail: {JsonConvert.SerializeObject(grpDetail)}");

                // Tính toán theo tổng tiền trong invoice detail
                // Validate thông tin invoice detail hóa đơn điều chỉnh với hóa đơn gốc
                foreach (var detail in grpDetail)
                {
                    var checkDetail = invoiceAdjAutoDetailSummaries
                        .Where(x => x.VatPercent == detail.VatPercent && x.ProductName.StartsWith(detail.ProductName));

                    var totalPaymentAmount = checkDetail.Sum(x => x.PaymentAmount);
                    var totalAmount = checkDetail.Sum(x => x.Amount);

                    if (totalPaymentAmount + detail.PaymentAmount < 0)
                    {
                        return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.CreateAdjustDetail.CannotAdjustmentGreaterThanRootTotalPaymentAmount"]);
                    }

                    if (totalAmount + detail.Amount < 0)
                    {
                        return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.CreateAdjustDetail.CannotAdjustmentGreaterThanRootTotalAmount"]);
                    }
                }
                #endregion
            }

            return new ValidationResult(true);
        }

        private async Task<List<Invoice01DetailDto>> GetInvoiceDetailAdjustmentDetailAutoReferencesAsync(List<long> idInvoiceHeaders)
        {
            var queryId = @$"
                    SELECT 
	                    id.*
                    FROM 
	                    ""{DatabaseExtension<Invoice01HeaderEntity>.GetTableName()}"" ih 
	                    JOIN ""{DatabaseExtension<Invoice01DetailEntity>.GetTableName()}"" id ON ih.""Id"" = id.""InvoiceHeaderId""
                    WHERE 
	                    ih.""Id"" IN :InvoiceHeaderIds
	                    AND ih.""InvoiceStatus"" = {InvoiceStatus.DieuChinhTangGiam.GetHashCode()} AND ih.""Source"" = {InvoiceSource.ShareAdapterG4.GetHashCode()}
                ";

            var result = await _appFactory.VnisCoreOracle.Connection.QueryAsync<Invoice01DetailDto>(queryId, new { InvoiceHeaderIds = idInvoiceHeaders });

            return result.ToList();
        }

        private async Task<List<Invoice01DetailDto>> GetInvoiceTransactionDetailReferencesAsync(List<long> idInvoiceHeaders)
        {
            var queryId = @$" SELECT * FROM ""{DatabaseExtension<Invoice01TransactionDetailEntity>.GetTableName()}"" WHERE ""InvoiceHeaderId"" in :InvoiceHeaderIds";


            var result = await _appFactory.VnisCoreOracle.Connection.QueryAsync<Invoice01DetailDto>(queryId, new { InvoiceHeaderIds = idInvoiceHeaders });

            return result.ToList();
        }

        private async Task<List<Invoice01HeaderDto>> GetInvoiceHeaderReferencesAsync(List<long> idInvoiceHeaders)
        {
            var queryId = @$" SELECT * FROM ""Invoice01Header"" WHERE ""Id"" in :Ids";

            var result = await _appFactory.VnisCoreOracle.Connection.QueryAsync<Invoice01HeaderDto>(queryId, new { Ids = idInvoiceHeaders });

            return result.ToList();
        }
    }
}
