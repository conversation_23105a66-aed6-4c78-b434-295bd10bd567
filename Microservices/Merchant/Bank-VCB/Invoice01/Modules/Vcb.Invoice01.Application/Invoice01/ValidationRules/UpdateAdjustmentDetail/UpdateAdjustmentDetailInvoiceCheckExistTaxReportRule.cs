using Core.Shared.Validations;
using System.Threading.Tasks;
using Core.Domain.Repositories;
using VnisCore.Invoice01.Application.Invoice01.Dto;
using Core.Shared.Factory;
using VnisCore.Core.Oracle.Domain.Entities.Reports;
using Core.Shared.Constants;
using Core;
using VnisCore.Invoice01.Application.Invoice01.Models.Requests.Commands;
using Core.Localization.Resources.AbpLocalization;
using Microsoft.Extensions.Localization;

namespace VnisCore.Invoice01.Application.Invoice01.ValidationRule.UpdateAdjustmentDetail
{
    /// <summary>
    /// kiểm tra đã có báo cáo trong kỳ đang tạo hóa đơn mà chờ ký không
    /// nếu có => không cho tạo
    /// </summary>
    public class UpdateAdjustmentDetailInvoiceCheckExistTaxReportRule : IValidationRuleAsync<UpdateAdjustmentDetailInvoice01Request, ValidationResult>
    {
        private readonly IAppFactory _appFactory;
        private readonly IRepository<TaxReport01HeaderEntity, long> _repoTaxReport01Header;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;

        public UpdateAdjustmentDetailInvoiceCheckExistTaxReportRule(
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IRepository<TaxReport01HeaderEntity, long> repoTaxReport01Header)
        {
            _appFactory = appFactory;
            _repoTaxReport01Header = repoTaxReport01Header;
            _localizer = localizer;
        }

        public async Task<ValidationResult> HandleAsync(UpdateAdjustmentDetailInvoice01Request input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;

            if (await _repoTaxReport01Header.AnyAsync(x => x.TenantId == tenantId && x.ReportMonth == input.InvoiceDate.Month && x.ReportYear == input.InvoiceDate.Year && (x.ApproveStatus == (short)ApproveStatus.DaDuyet.GetHashCode() || x.SignStatus == SignStatus.DaKy.GetHashCode())))
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.CannotUpdateWhenTaxReportApproved"]);

            return new ValidationResult(true);
        }
    }
}
