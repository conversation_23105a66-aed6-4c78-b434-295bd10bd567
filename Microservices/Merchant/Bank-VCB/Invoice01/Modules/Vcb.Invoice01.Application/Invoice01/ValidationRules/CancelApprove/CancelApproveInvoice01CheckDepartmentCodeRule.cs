using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Dapper;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Invoice01.Application.Factories.Repositories;
using VnisCore.Invoice01.Application.Invoice01.Dto;

namespace VnisCore.Invoice01.Application.Invoice01.ValidationRules.CancelApprove
{
    public class CancelApproveInvoice01CheckDepartmentCodeRule : IValidationRuleAsync<CancelApproveInvoice01HeaderDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IInvoiceHeaderRepository<Invoice01HeaderEntity> _repoInvoice01Header;

        public CancelApproveInvoice01CheckDepartmentCodeRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IInvoiceHeaderRepository<Invoice01HeaderEntity> repoInvoice01Header
            )
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _repoInvoice01Header = repoInvoice01Header;
        }

        public async Task<ValidationResult> HandleAsync(CancelApproveInvoice01HeaderDto input)
        {
            var invoices = await _repoInvoice01Header.GetByIdsAsync(input.Ids.ToList());

            //var departmentCode = _appFactory.CurrentUser.DepartmentCode;
            var userId = _appFactory.CurrentUser.Id.Value;
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var queryDepartmentCode = @$" SELECT ""DepartmentCode"" FROM ""VnisUsers"" 
                                        WHERE ""Id"" = '{OracleExtension.ConvertGuidToRaw(userId)}' AND ""TenantId"" =  '{OracleExtension.ConvertGuidToRaw(tenantId)}' AND ""IsDeleted"" = 0";
            var departmentCode = await _appFactory.AuthDatabase.Connection.QueryFirstOrDefaultAsync<string>(queryDepartmentCode);

            foreach (var invoice in invoices)
            {
                if (invoice.Source == InvoiceSource.Form.GetHashCode() || invoice.Source == InvoiceSource.Excel.GetHashCode())
                {
                    if (!string.IsNullOrEmpty(invoice.DepartmentCode) && invoice.DepartmentCode != departmentCode)
                        return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.Cancel.AccountNotPermissionForCancelInvoice"]);
                }
            }

            return new ValidationResult(true);
        }
    }
}
