using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Invoice01.Application.Invoice01.Dto;

namespace VnisCore.Invoice01.Application.Invoice01.ValidationRule.UpdateReplace
{
    public class UpdateReplaceInvoicePreProcess : IValidationRuleAsync<UpdateReplaceInvoice01HeaderDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IValidationContext _validationContext;
        private readonly IRepository<Invoice01HeaderEntity> _repoInvoice01Header;

        public UpdateReplaceInvoicePreProcess(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IValidationContext validationContext,
            IRepository<Invoice01HeaderEntity> repoInvoice01Header)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _validationContext = validationContext;
            _repoInvoice01Header = repoInvoice01Header;
        }

        public async Task<ValidationResult> HandleAsync(UpdateReplaceInvoice01HeaderDto input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;

            //var tenantId = Utilities.TenantId;
            //var userId = Utilities.CreatorId;

            //_validationContext.GetOrAddItem("TenantId", () =>
            //{
            //    return tenantId;
            //});

            //_validationContext.GetOrAddItem("UserId", () =>
            //{
            //    return userId;
            //});

            var invoice = await _repoInvoice01Header.FirstOrDefaultAsync(x => x.TenantId == tenantId && x.Id == input.Id);
            if (invoice == null)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.InvoiceNotFound"]);

            _validationContext.GetOrAddItem("Invoice", () =>
            {
                return invoice;
            });

            return new ValidationResult(true);
        }
    }
}
