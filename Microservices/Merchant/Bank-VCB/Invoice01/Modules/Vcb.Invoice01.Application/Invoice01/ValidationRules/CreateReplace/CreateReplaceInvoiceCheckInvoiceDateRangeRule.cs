using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Core.Oracle.Domain.Entities.InvoiceTemplate;
using VnisCore.Invoice01.Application.Factories.Models;
using VnisCore.Invoice01.Application.Factories.Services;
using VnisCore.Invoice01.Application.Invoice01.Dto;

namespace VnisCore.Invoice01.Application.Invoice01.ValidationRule.CreateReplace
{
    /// <summary>
    /// check ngày hóa đơn
    /// </summary>
    public class CreateReplaceInvoiceCheckInvoiceDateRangeRule : IValidationRuleAsync<CreateReplaceInvoice01Dto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IInvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity> _invoiceService;
        private readonly IValidationContext _validationContext;
        private readonly ICommonInvoice01Service _commonInvoice01Service;

        public CreateReplaceInvoiceCheckInvoiceDateRangeRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IValidationContext validationContext,
            IInvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity> invoiceService,
            ICommonInvoice01Service commonInvoice01Service

        )
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _invoiceService = invoiceService;
            _validationContext = validationContext;
            _commonInvoice01Service = commonInvoice01Service;
        }

        public async Task<ValidationResult> HandleAsync(CreateReplaceInvoice01Dto input)
        {
            if (!_commonInvoice01Service.CheckInvoiceDateAndSerialNo(input.InvoiceDate, input.SerialNo))
            {
                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice.InvoiceDateRange.InvalidInvoiceDateAndSerialNo"]);
            }

            var tenantId = _appFactory.CurrentTenant.Id.Value;

            var template = _validationContext.GetItem<InvoiceTemplateEntity>("Template");

            InvoiceDateRangeModel range = new InvoiceDateRangeModel();

            if (template.SourceType == (short)InvoiceTemplateSource.FormExcel)
            {
                range = new InvoiceDateRangeModel
                {
                    Max = DateTime.Now.Date,
                    Min = DateTime.Now.Date
                };
            }
            else
            {
                range = await _invoiceService.InvoiceDateRangeAsync(tenantId, input.TemplateNo, input.SerialNo, null);
            }

            if (range == null)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice.InvoiceDateRange.TemplateOutOfInvoiceNo"]);

            if (range.Min.Date > DateTime.Now.Date)
                return input.Source != InvoiceSource.Excel
                    ? new ValidationResult(false, _localizer["Vnis.BE.Invoice01.InvoiceDateMin", new[] { range.Min.ToString("dd/MM/yyyy") }])
                    : new ValidationResult(false, _localizer["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.InvoiceDateMin",
                        new[] {
                            input.TemplateNo.ToString(),
                            input.SerialNo,
                            input.InvoiceNo,
                            range.Min.ToString("dd/MM/yyyy")
                        }]);

            if (input.InvoiceDate > range.Max || input.InvoiceDate < range.Min)
                return input.Source != InvoiceSource.Excel
                    ? new ValidationResult(false, _localizer["Vnis.BE.Invoice01.InvoiceDateRange", new[] { range.Min.ToString("dd/MM/yyyy"), range.Max.ToString("dd/MM/yyyy") }])
                    : new ValidationResult(false, _localizer["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.InvoiceDateRange",
                        new[] {
                            input.TemplateNo.ToString(),
                            input.SerialNo,
                            input.InvoiceNo,
                            range.Min.ToString("dd/MM/yyyy"),
                            range.Max.ToString("dd/MM/yyyy")
                        }]);

            return new ValidationResult(true);
        }
    }
}
