using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using Core.Shared.Services;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Vcb.Invoice01.Application.Invoice01.Dto;

namespace Vcb.Invoice01.Application.Invoice01.ValidationRules.CreateG4Replace
{
    /// <summary>
    /// Validate thuế suất đã tồn tại
    /// </summary>
    public class CreateG4ReplaceInvoiceCheckExistTaxRule : IValidationRuleAsync<CreateG4ReplaceInvoice01Dto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IValidationContext _validationContext;
        private readonly ITaxService _taxService;

        public CreateG4ReplaceInvoiceCheckExistTaxRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IValidationContext validationContext,
            ITaxService taxService)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _validationContext = validationContext;
            _taxService = taxService;
        }

        public async Task<ValidationResult> HandleAsync(CreateG4ReplaceInvoice01Dto input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;
            //var tenantId = _validationContext.GetItem<Guid>("TenantId");

            var taxes = await _validationContext.UpdateOrAddItemAsync<Dictionary<decimal, Tuple<string, string>>>("Taxes", async () =>
            {
                return await _taxService.GetTaxesAsync(tenantId);
            });

            if (taxes.Count == 0)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.TaxConfigNotExist"]);

            return new ValidationResult(true);
        }
    }
}
