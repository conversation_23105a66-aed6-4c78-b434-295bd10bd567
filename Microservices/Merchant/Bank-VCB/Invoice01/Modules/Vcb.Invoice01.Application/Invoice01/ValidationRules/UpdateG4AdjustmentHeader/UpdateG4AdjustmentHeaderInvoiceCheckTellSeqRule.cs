using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.Services;
using Core.Shared.Validations;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Vcb.Invoice01.Application.Invoice01.Dto;
using VnisCore.Invoice01.Application.Factories.Interfaces;
using VnisCore.Invoice01.Application.Factories.Services;
using VnisCore.Invoice01.Application.Invoice01.Models;

namespace Vcb.Invoice01.Application.Invoice01.ValidationRules.UpdateG4AdjustmentHeader
{
    public class UpdateG4AdjustmentHeaderInvoiceCheckTellSeqRule : IValidationRuleAsync<UpdateG4AdjustmentHeaderInvoice01Dto, ValidationResult>
    {
        private readonly IAppFactory _appFactory;
        private readonly IEnumerable<ICheckInvoiceHeaderExtraService> _checkInvoiceHeaderExtraServices;

        public UpdateG4AdjustmentHeaderInvoiceCheckTellSeqRule(
            IAppFactory appFactory,
            IEnumerable<ICheckInvoiceHeaderExtraService> checkInvoiceHeaderExtraServices
            )
        {
            _appFactory = appFactory;
            _checkInvoiceHeaderExtraServices = checkInvoiceHeaderExtraServices;
        }

        public async Task<ValidationResult> HandleAsync(UpdateG4AdjustmentHeaderInvoice01Dto input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var service = _checkInvoiceHeaderExtraServices.First(x => x.GetType().Name == typeof(Invoice01CheckHeaderExtraValueService).Name);

            var repoSetting = _appFactory.GetServiceDependency<ISettingService>();
            var setting = await repoSetting.GetByCodeAsync(tenantId, SettingKey.ValidateInvoiceConfigName.ToString());

            if (setting != null && !string.IsNullOrEmpty(setting.Value))
            {
                var serviceName = $"Invoice01{setting.Value}CheckHeaderExtraValueService";
                service = _checkInvoiceHeaderExtraServices.FirstOrDefault(x => x.GetType().Name == serviceName);
            }

            var headerExtras = input.InvoiceHeaderExtras.Select(x => new CheckHeaderExtraModel
            {
                FieldName = x.FieldName,
                FieldValue = x.FieldValue
            }).ToList();

            return await service.ValidateAsync(headerExtras);
        }
    }
}
