using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using Core.Shared.Invoice.Interfaces;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.InvoiceTemplate;
using VnisCore.Invoice01.Application.Invoice01.Dto;

namespace VnisCore.Invoice01.Application.Invoice01.ValidationRule.CreateReplace
{
    public class CreateReplaceInvoiceCheckMonitorInvoiceTemplateRule : IValidationRuleAsync<CreateReplaceInvoice01Dto, ValidationResult>
    {
        private readonly IAppFactory _appFactory;
        private readonly IValidationContext _validationContext;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IMonitorInvoiceTemplateService _monitorInvoiceTemplateService;

        public CreateReplaceInvoiceCheckMonitorInvoiceTemplateRule(
            IAppFactory appFactory,
            IValidationContext validationContext,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IMonitorInvoiceTemplateService monitorInvoiceTemplateService
        )
        {
            _appFactory = appFactory;
            _validationContext = validationContext;
            _localizer = localizer;
            _monitorInvoiceTemplateService = monitorInvoiceTemplateService;
        }
        public async Task<ValidationResult> HandleAsync(CreateReplaceInvoice01Dto input)
        {
            try
            {
                var invoiceTemplate = GetInvoiceTemplate();
                var monitorInvoiceTemplateModel = await _monitorInvoiceTemplateService.GetMonitorInvoiceTemplateAsync(invoiceTemplate.Id);

                if (monitorInvoiceTemplateModel.CurrentNumber == monitorInvoiceTemplateModel.EndNumber)
                {
                    Log.Error($"-> Dải hóa đơn {invoiceTemplate.TemplateNo}{invoiceTemplate.SerialNo}, TenantId = {invoiceTemplate.TenantId} đã hết số lượng đăng ký");
                    return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.ValidationRule.CurrentNumberRule.Invalid", new string[] { (invoiceTemplate.TemplateNo + invoiceTemplate.SerialNo) }]);
                }
                return new ValidationResult(true, string.Empty);

            }
            catch (Exception ex)
            {
                return new ValidationResult(false, ex.Message);
            }
        }

        private InvoiceTemplateEntity GetInvoiceTemplate()
        {
            var invoiceTemplate = _validationContext.GetItem<InvoiceTemplateEntity>("Template");
            Log.Information($"Template.Id = {invoiceTemplate.Id}");
            return invoiceTemplate;
        }
    }
}
