using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Extensions;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System.Threading.Tasks;
using VnisCore.Invoice01.Application.Invoice01.Dto;

namespace VnisCore.Invoice01.Application.Invoice01.ValidationRule.UpdateAdjustmentHeader
{
    public class UpdateAdjustmentHeaderInvoiceCheckBuyerCodeRule : IValidationRuleAsync<UpdateAdjustmentHeaderInvoice01Dto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        public UpdateAdjustmentHeaderInvoiceCheckBuyerCodeRule(
            IStringLocalizer<CoreLocalizationResource> localizer

        )
        {
            _localizer = localizer;
        }

        public async Task<ValidationResult> HandleAsync(UpdateAdjustmentHeaderInvoice01Dto input)
        {
            var buyerCode = input.BuyerCode.DeleteZeroAtFirstChar();
            if (string.IsNullOrWhiteSpace(buyerCode))
            {
                var errorMessage = _localizer["Vnis.BE.Invoice01.CreateInvoice01HeaderDto.BuyerCode.Invalid", new string[] { input.BuyerCode }];
                return new ValidationResult(false, errorMessage);
            }
            input.BuyerCode = buyerCode;

            return new ValidationResult(true);
        }
    }
}
