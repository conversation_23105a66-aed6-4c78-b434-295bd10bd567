using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Invoice01.Application.Factories.Services;
using VnisCore.Invoice01.Application.Invoice01.Dto;

namespace VnisCore.Invoice01.Application.Invoice01.ValidationRule.Create
{
    public class CreateInvoiceCheckExistSerialRule : IValidationRuleAsync<CreateInvoice01HeaderDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly ICommonInvoice01Service _commonInvoice01Service;


        public CreateInvoiceCheckExistSerialRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            ICommonInvoice01Service commonInvoice01Service
        )
        {
            _localizer = localizer;
            _commonInvoice01Service = commonInvoice01Service;
        }

        public async Task<ValidationResult> HandleAsync(CreateInvoice01HeaderDto input)
        {
            var serialNo = input.SerialNo;
            if (string.IsNullOrWhiteSpace(serialNo))
            {
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.ValidationRule.SerialRule.NotEmpty"]);
            }

            var invoiceDate = input.InvoiceDate;
            var now = DateTime.Now;
            var currentDay = now.Day;
            var currentMonth = now.Month;
            var backDateDay = invoiceDate.Day;
            var backDateMonth = invoiceDate.Month;

            if (currentMonth - backDateMonth > 1)
            {
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.ValidationRule.SerialRule.InValidBackDate"]);
            }

            var isSameDay = currentDay == backDateDay;
            var isSameMonth = currentMonth == backDateMonth;
            var isOneTax = input.InvoiceTaxBreakdowns.Count == 1;

            // TODO
            if (isSameDay && isSameMonth)
            {
                // TODO: check tax
                if (isOneTax)
                {
                    if (InvoiceTemplateBackDateCodeConstant.ListNhieuThueTaoSua.Any(p => serialNo.Contains(p)))
                    {
                        return new ValidationResult(false, _localizer["Vnis.FE.Invoice01.Create.Lb.Error.Taxes"]);
                    }
                }
                else
                {
                    if (InvoiceTemplateBackDateCodeConstant.ListMotThueTaoSua.Any(p => serialNo.Contains(p)))
                    {
                        return new ValidationResult(false, _localizer["Vnis.FE.Invoice01.Create.Lb.Error.Tax"]);
                    }
                }

                if (!serialNo.IsTAxTBx() && !serialNo.IsTCxTDx())
                {
                    return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.ValidationRule.SerialRule.InValidSerial", new string[] { serialNo }]);
                }

                return new ValidationResult(true);

            }

            // TODO: BackDate

            var validationResult = await _commonInvoice01Service.ValidationSerialOfBackDate(serialNo, isOneTax, isSameMonth, invoiceDate);

            return validationResult;

        }
    }
}
