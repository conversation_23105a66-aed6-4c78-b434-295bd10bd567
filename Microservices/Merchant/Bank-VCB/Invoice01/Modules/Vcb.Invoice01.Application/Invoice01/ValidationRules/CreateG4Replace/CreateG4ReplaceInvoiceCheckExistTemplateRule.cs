using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System.Threading.Tasks;
using Vcb.Invoice01.Application.Invoice01.Dto;
using VnisCore.Core.Oracle.Domain.Entities.InvoiceTemplate;

namespace Vcb.Invoice01.Application.Invoice01.ValidationRules.CreateG4Replace
{
    /// <summary>
    /// kiểm tra mẫu hóa đơn
    /// </summary>
    public class CreateG4ReplaceInvoiceCheckExistTemplateRule : IValidationRuleAsync<CreateG4ReplaceInvoice01Dto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IValidationContext _validationContext;
        private readonly IRepository<InvoiceTemplateEntity, long> _repoTemplate;

        public CreateG4ReplaceInvoiceCheckExistTemplateRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IRepository<InvoiceTemplateEntity, long> repoTemplate,
            IValidationContext validationContext)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _repoTemplate = repoTemplate;
            _validationContext = validationContext;
        }

        public async Task<ValidationResult> HandleAsync(CreateG4ReplaceInvoice01Dto input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;
            //var tenantId = _validationContext.GetItem<Guid>("TenantId");

            var template = await _validationContext.UpdateOrAddItemAsync("Template", async () =>
            {
                return await _repoTemplate.FirstOrDefaultAsync(x => x.TenantId == tenantId && x.TemplateNo == input.TemplateNo && x.SerialNo == input.SerialNo && !x.IsDeleted);
            });

            if (template == null)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.TemplateIncorrect"]);

            if (template.SourceType == 4)
                return new ValidationResult(false, "Không thể lập hóa đơn thay thế cho hóa đơn lùi ngày");

            return new ValidationResult(true);
        }
    }
}
