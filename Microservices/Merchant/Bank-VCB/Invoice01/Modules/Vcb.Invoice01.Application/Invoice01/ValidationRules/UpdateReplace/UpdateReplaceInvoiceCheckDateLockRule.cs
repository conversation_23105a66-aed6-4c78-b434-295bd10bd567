using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.Services;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Invoice01.Application.Factories.Services;
using VnisCore.Invoice01.Application.Invoice01.Dto;

namespace VnisCore.Invoice01.Application.Invoice01.ValidationRule.UpdateReplace
{
    /// <summary>
    /// Nếu đã đến ngày khóa tạo hóa đơn của tháng trước thì sẽ không cho tạo hóa đơn của tháng trước nữa
    /// </summary>
    public class UpdateReplaceInvoiceCheckDateLockRule : IValidationRuleAsync<UpdateReplaceInvoice01HeaderDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IValidationContext _validationContext;
        private readonly IInvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity> _invoiceService;
        private readonly ISettingService _settingService;

        public UpdateReplaceInvoiceCheckDateLockRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IValidationContext validationContext,
            ISettingService settingService,
            IInvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity> invoiceService)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _validationContext = validationContext;
            _invoiceService = invoiceService;
            _settingService = settingService;
        }

        public async Task<ValidationResult> HandleAsync(UpdateReplaceInvoice01HeaderDto input)
        {
            // lấy cấu hình ngày khóa không cho tạo hóa đơn lùi ngày của tháng trước
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var daySettingLock = await _settingService.GetByCodeAsync(tenantId, SettingKey.LockBackDatePreviousMonth.ToString());
            var dayLock = 1;
            if (int.TryParse(daySettingLock?.Value, out int value))
            {
                dayLock = value;
            }

            var dateNow = DateTime.Now.Date;                                            // ngày hiện tại
            var firstDateThisMonth = new DateTime(dateNow.Year, dateNow.Month, 1);      // ngày đầu tháng T
            var firstDatePrevMonth = firstDateThisMonth.AddMonths(-1);                  // ngày đầu tháng T-1
            var dateLock = new DateTime(dateNow.Year, dateNow.Month, dayLock);   // ngày cấu hình khóa để tạo báo cáo

            if (input.InvoiceDate < firstDatePrevMonth || input.InvoiceDate > dateNow)
                return new ValidationResult(false, "Không được tạo hóa đơn có ngày nhỏ hơn tháng trước hoặc ngày tương lai!");

            if (dateNow >= dateLock && input.InvoiceDate < firstDateThisMonth)
                return new ValidationResult(false, "Không được tạo hóa đơn tháng trước vì đã lập báo cáo");

            return new ValidationResult(true);
        }
    }
}
