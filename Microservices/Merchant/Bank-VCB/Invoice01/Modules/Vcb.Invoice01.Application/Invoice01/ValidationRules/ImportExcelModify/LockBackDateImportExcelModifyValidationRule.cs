using Core.DependencyInjection;
using System;
using System.Linq;
using Vcb.Invoice01.Application.Invoice01.Dto;
using Vcb.Invoice01.Application.Invoice01.ValidationRules.BaseValidationRules;

namespace Vcb.Invoice01.Application.Invoice01.ValidationRules.ImportExcelModify
{
    public class LockBackDateImportExcelModifyValidationRule : LockBackDateBaseValidationRule<ModifyInvoice01HeaderDto>, ILockBackDateImportExcelModifyValidationRule, IScopedDependency
    {
        public LockBackDateImportExcelModifyValidationRule(IServiceProvider serviceProvider)
            : base(serviceProvider)
        {
        }

        protected override string GetMessageInValidDate(ModifyInvoice01HeaderDto input) => _localizer["Vnis.BE.Invoice.ImportExcel.Validator.LockBackDate.Date.Invalid", new string[]
        {
            input.InvoiceDetails.First().RowNumber.ToString()
        }];

        protected override string GetMessageExisted(ModifyInvoice01HeaderDto input) => _localizer["Vnis.BE.Invoice.ImportExcel.Validator.LockBackDate.Existed", new string[]
        {
            input.InvoiceDetails.First().RowNumber.ToString()
        }];
    }
}
