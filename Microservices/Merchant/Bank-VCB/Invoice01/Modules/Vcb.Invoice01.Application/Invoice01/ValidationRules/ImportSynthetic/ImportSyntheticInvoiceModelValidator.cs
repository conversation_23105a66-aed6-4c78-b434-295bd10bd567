using Core.ExceptionHandling.Localization;
using Core.Shared.Extensions;
using FluentValidation;
using Microsoft.Extensions.Localization;
using System;
using System.Globalization;
using VnisCore.Invoice01.Application.Invoice01.Models;
using Core;
using VnisCore.Invoice01.Application.Factories.Services;

namespace Vcb.Invoice01.Application.Invoice01.ValidationRules.ImportSynthetic
{
    public class ImportSyntheticInvoiceModelValidator : AbstractValidator<ImportSyntheticInvoiceModel>
    {
        public ImportSyntheticInvoiceModelValidator(
             IStringLocalizer<AbpExceptionHandlingResource> localizierException,
             ICommonInvoice01Service commonInvoice01Service
        )
        {
            RuleFor(rule => rule.GroupId)
                .NotEmpty()
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.GroupId.Required", new string[] { "A", p.RowNumber.ToString() }]);

            RuleFor(rule => rule._InvoiceType)
                .NotEmpty()
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.InvoiceType.Required", new string[] { "B", p.RowNumber.ToString() }]);

            RuleFor(rule => rule.InvoiceType)
                .Must((p, pp) => pp >= 1 && pp <= 3)
                .When(p => !string.IsNullOrWhiteSpace(p._InvoiceType), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.InvoiceType.WrongFormat", new string[] { "B", p.RowNumber.ToString() }]);

            RuleFor(rule => rule._TemplateNo)
                .NotEmpty()
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.TemplateNo.Required", new string[] { "C", p.RowNumber.ToString() }]);

            RuleFor(rule => rule.TemplateNo)
                .Must((p, pp) => pp.ToString().IsTemplateNo() && p.SerialNo.IsSerialNo())
                .When(p => !string.IsNullOrWhiteSpace(p._TemplateNo) || !string.IsNullOrWhiteSpace(p.SerialNo), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.TemplateNo.WrongFormat", new string[] { "C", p.RowNumber.ToString() }]);

            //RuleFor(rule => rule.SerialNo)
            //.NotEmpty()
            //.When(p => true, ApplyConditionTo.CurrentValidator)
            //.WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.SerialNo.Required", new string[] { "C", p.RowNumber.ToString() }])

            //.Must((p, pp) => pp.IsSerialNo())
            //.When(p => !string.IsNullOrWhiteSpace(p.SerialNo), ApplyConditionTo.CurrentValidator)
            //.Must(p => string.IsNullOrWhiteSpace(p) || (!string.IsNullOrWhiteSpace(p) && p.IsSerialNo()))
            //.WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.SerialNo.WrongFormat", new string[] { "C", p.RowNumber.ToString() }]);

            //.Must((p, pp) => pp.IsTKxTLx())
            //.When(p => !string.IsNullOrWhiteSpace(p.SerialNo), ApplyConditionTo.CurrentValidator)
            //.WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.WrongFormat", new string[] { "C", p.RowNumber.ToString() }]);


                //.Must((p, pp) => pp.IsTKxTLx())
                //.When(p => !string.IsNullOrWhiteSpace(p.SerialNo), ApplyConditionTo.CurrentValidator)
                //.WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.WrongFormat", new string[] { "C", p.RowNumber.ToString() }]);


            RuleFor(rule => rule.PCTime)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.PCTime.Required", new string[] { "C", p.RowNumber.ToString() }])

                .Must((p, pp) => pp.IsHHMMSS())
                .When(p => !string.IsNullOrWhiteSpace(p.PCTime), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.PCTime.WrongFormat", new string[] { "C", p.RowNumber.ToString() }]);

            RuleFor(rule => rule.InvoiceDate)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.InvoiceDate.Required", new string[] { "D", p.RowNumber.ToString() }])

                .Must((p, pp) => DateTime.TryParseExact(pp, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime invoiceDate))
                .When(p => !string.IsNullOrWhiteSpace(p.InvoiceDate), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.WrongFormat", new string[] { "E", p.RowNumber.ToString() }])
                
                .Must((p, pp) => commonInvoice01Service.CheckInvoiceDateAndSerialNo(p.GetInvoiceDate(), p.SerialNo))
                .When(p => !string.IsNullOrWhiteSpace(p.InvoiceDate) && !string.IsNullOrEmpty(p.SerialNo), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.WrongFormat", new string[] { "E", p.RowNumber.ToString() }])

                .Must((p, pp) => DateTime.Now.Date >= p.GetInvoiceDate().Date)
                .When(p => !string.IsNullOrWhiteSpace(p.InvoiceDate), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.InvoiceDate.GreaterThanToDay", new string[] { "D", p.RowNumber.ToString() }]);

            RuleFor(rule => rule.OldInvoiceNo)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.OldInvoiceNo.Required", new string[] { "E", p.RowNumber.ToString() }])

                .Must((p, pp) => pp.IsInvoiceNo())
                .When(p => !string.IsNullOrWhiteSpace(p.OldInvoiceNo), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.OldInvoiceNo.WrongFormat", new string[] { "E", p.RowNumber.ToString() }]);

            RuleFor(rule => rule._OldTemplateNo)
                .NotEmpty()
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.OldTemplateNo.Required", new string[] { "F", p.RowNumber.ToString() }]);

            RuleFor(rule => rule.OldTemplateNo)
                .Must((p, pp) => pp.ToString().IsTemplateNo() && p.OldSerialNo.IsSerialNo())
                .When(p => !string.IsNullOrWhiteSpace(p._OldTemplateNo) || !string.IsNullOrWhiteSpace(p.OldSerialNo), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.OldTemplateNo.WrongFormat", new string[] { "F", p.RowNumber.ToString() }]);

            RuleFor(rule => rule.OldSerialNo)
                .Must((p, pp) => pp.IsTHxTIx())
                .When(p => !string.IsNullOrWhiteSpace(p.SerialNo) && !string.IsNullOrWhiteSpace(p.OldSerialNo) && p.SerialNo.IsTHxTIx(), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.SerialNo.IsTHxTIX", new string[] { p.OldInvoiceNo, p._OldTemplateNo, p.OldSerialNo }])

                .Must((p, pp) => !pp.IsTHxTIx())
                .When(p => !string.IsNullOrWhiteSpace(p.SerialNo) && !string.IsNullOrWhiteSpace(p.OldSerialNo) && !p.SerialNo.IsTHxTIx(), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.SerialNo.NotIsTHxTIX", new string[] { p.OldInvoiceNo, p._OldTemplateNo, p.OldSerialNo }]);


            RuleFor(rule => rule.OldInvoiceDate)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.OldInvoiceDate.Required", new string[] { "G", p.RowNumber.ToString() }])

                .Must((p, pp) => DateTime.TryParseExact(pp, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime invoiceDate))
                .When(p => !string.IsNullOrWhiteSpace(p.OldInvoiceDate), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.OldInvoiceDate.WrongFormat", new string[] { "G", p.RowNumber.ToString() }]);

            RuleFor(rule => rule.CreatorErp)
                //.NotEmpty()
                //.When(p => true, ApplyConditionTo.CurrentValidator)
                //.WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.Required", new string[] { "H", p.RowNumber.ToString() }])

                .Must((p, pp) => pp.IsCreatorErp())
                .When(p => !string.IsNullOrWhiteSpace(p.CreatorErp), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.CreatorErp.WrongFormat", new string[] { "H", p.RowNumber.ToString() }]);

            RuleFor(rule => rule.TellSeq)
                //.NotEmpty()
                //.When(p => true, ApplyConditionTo.CurrentValidator)
                //.WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.Required", new string[] { "I", p.RowNumber.ToString() }])

                .Must((p, pp) => pp.CheckTellSeqVCB())
                .When(p => !string.IsNullOrWhiteSpace(p.TellSeq), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.TellSeq.WrongFormat", new string[] { "I", p.RowNumber.ToString() }]);

            RuleFor(rule => rule.PaymentMethod)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.PaymentMethod.Required", new string[] { "J", p.RowNumber.ToString() }])

                .Must((p, pp) => pp == "1" || pp == "2")
                .When(p => !string.IsNullOrWhiteSpace(p.PaymentMethod), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.PaymentMethod.WrongFormat", new string[] { "J", p.RowNumber.ToString() }]);

            RuleFor(rule => rule.Currency)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.Currency.Required", new string[] { "K", p.RowNumber.ToString() }])

                .Must((p, pp) => pp.Length <= 3)
                .When(p => !string.IsNullOrWhiteSpace(p.Currency), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.Currency.WrongFormat", new string[] { "K", p.RowNumber.ToString() }]);

            RuleFor(rule => rule._ExchangeRate)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.ExchangeRate.Required", new string[] { "L", p.RowNumber.ToString() }])

                .Must((p, pp) => decimal.TryParse(pp, out var rs))
                .When(p => !string.IsNullOrWhiteSpace(p._ExchangeRate), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.ExchangeRate.WrongFormat", new string[] { "L", p.RowNumber.ToString() }]);

            RuleFor(rule => rule.ExchangeRate)
                .ScalePrecision(2, 7)
                .When(p => !string.IsNullOrWhiteSpace(p._ExchangeRate), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.ExchangeRate.ScalePrecision", new string[] { "L", p.RowNumber.ToString() }]);


            RuleFor(rule => rule.BuyerCode)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerCode.Required", new string[] { "N", p.RowNumber.ToString() }])

                .Must((p, pp) => pp.IsCustomerCode())
                .When(p => !string.IsNullOrWhiteSpace(p.BuyerCode), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.BuyerCode.WrongFormat", new string[] { "N", p.RowNumber.ToString() }]);

            RuleFor(rule => rule.BuyerFullName)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerFullName.Required", new string[] { "O", p.RowNumber.ToString() }])

                .MaximumLength(400)
                .When(p => !string.IsNullOrWhiteSpace(p.BuyerFullName), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerFullName.Maxlength", new string[] { "O", p.RowNumber.ToString() }]);

            RuleFor(rule => rule.BuyerTaxCode)
                .MaximumLength(14)
                .When(p => !string.IsNullOrWhiteSpace(p.BuyerTaxCode), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerTaxCode.Maxlength", new string[] { "P", p.RowNumber.ToString() }]);


            RuleFor(rule => rule.BuyerAddressLine)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerAddressLine.Required", new string[] { "Q", p.RowNumber.ToString() }])

                .MaximumLength(400)
                .When(p => !string.IsNullOrWhiteSpace(p.BuyerAddressLine), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerAddressLine.Maxlength", new string[] { "P", p.RowNumber.ToString() }]);

            RuleFor(rule => rule.BuyerBankAccount)
                .MaximumLength(30)
                .When(p => !string.IsNullOrWhiteSpace(p.BuyerBankAccount), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerBankAccount.Maxlength", new string[] { "R", p.RowNumber.ToString() }]);

            RuleFor(rule => rule.BuyerBankName)
                .MaximumLength(400)
                .When(p => !string.IsNullOrWhiteSpace(p.BuyerBankName), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerBankName.Maxlength", new string[] { "S", p.RowNumber.ToString() }]);

            RuleFor(rule => rule.BuyerEmail)
                .MaximumLength(50)
                .When(p => !string.IsNullOrWhiteSpace(p.BuyerEmail), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerEmail.Maxlength", new string[] { "T", p.RowNumber.ToString() }])

                .Must((p, pp) => pp.IsEmail())
                .When(p => !string.IsNullOrWhiteSpace(p.BuyerEmail), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerEmail.WrongFormat", new string[] { "T", p.RowNumber.ToString() }]);

            RuleFor(rule => rule.BuyerType)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerType.Required", new string[] { "U", p.RowNumber.ToString() }])

                .Must((p, pp) => pp == "I" || pp == "C")
                .When(p => !string.IsNullOrWhiteSpace(p.BuyerType), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerType.WrongFormat", new string[] { "U", p.RowNumber.ToString() }]);

            //RuleFor(rule => rule.TotalAmount)
            //    .NotEmpty()
            //    .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.Required", new string[] { "W", p.RowNumber.ToString() }]);

            //RuleFor(rule => rule.TotalVatAmount)
            //    .NotEmpty()
            //    .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.Required", new string[] { "X", p.RowNumber.ToString() }]);

            //RuleFor(rule => rule.TotalPaymentAmount)
            //    .NotEmpty()
            //    .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.Required", new string[] { "Y", p.RowNumber.ToString() }]);

            RuleFor(rule => rule._ProductIndex)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.ProductIndex.Required", new string[] { "V", p.RowNumber.ToString() }])

                .Must((p, pp) => pp.Length <= 4)
                .When(p => !string.IsNullOrWhiteSpace(p._ProductIndex), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.ProductIndex.MaximumLength", new string[] { "V", p.RowNumber.ToString() }])

                .Must((p, pp) => int.TryParse(pp, out var rs))
                .When(p => !string.IsNullOrWhiteSpace(p._ProductIndex), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.ProductIndex.WrongFormat", new string[] { "V", p.RowNumber.ToString() }]);


            RuleFor(rule => rule.ProductIndex)
                .Must((p, pp) => pp == 0)
                .When(p => !string.IsNullOrWhiteSpace(p._ProductIndex) && p._ProductIndex == "0", ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.ProductIndex.WrongFormat", new string[] { "V", p.RowNumber.ToString() }]);


            RuleFor(rule => rule.ProductName)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.ProductName.Required", new string[] { "W", p.RowNumber.ToString() }])

                .MaximumLength(500)
                .When(p => !string.IsNullOrWhiteSpace(p.ProductName), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.ProductName.MaximumLength", new string[] { "W", p.RowNumber.ToString() }]);


            RuleFor(rule => rule.UnitName)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.UnitName.Required", new string[] { "X", p.RowNumber.ToString() }]);

            RuleFor(rule => rule._Quantity)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.Quantity.Required", new string[] { "Y", p.RowNumber.ToString() }])

                .Must((p, pp) => pp.DecimalFormatUnitedKingdom() != null)
                .When(p => !string.IsNullOrWhiteSpace(p._Quantity), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.Quantity.WrongFormat", new string[] { "Y", p.RowNumber.ToString() }]);


            RuleFor(rule => rule.Quantity)
                .Must((p, pp) => pp.Value >= 0)
                .When(p => !string.IsNullOrWhiteSpace(p._Quantity) && p.Quantity.HasValue && p._InvoiceType == "3", ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.Quantity.Positive", new string[] { "Y", p.RowNumber.ToString() }]);

            RuleFor(rule => rule._UnitPrice)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.UnitPrice.Required", new string[] { "Z", p.RowNumber.ToString() }])

                .Must((p, pp) => decimal.TryParse(pp, out var rs))
                .When(p => !string.IsNullOrWhiteSpace(p._UnitPrice), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.UnitPrice.WrongFormat", new string[] { "Z", p.RowNumber.ToString() }]);


            RuleFor(rule => rule.UnitPrice)
                .Must((p, pp) => pp.Value >= 0)
                .When(p => !string.IsNullOrWhiteSpace(p._UnitPrice) && p.UnitPrice.HasValue && p._InvoiceType == "3", ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.UnitPrice.Positive", new string[] { "Z", p.RowNumber.ToString() }]);


            RuleFor(rule => rule._Amount)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.Amount.Required", new string[] { "AA", p.RowNumber.ToString() }])

                .Must((p, pp) => decimal.TryParse(pp, out var rs))
                .When(p => !string.IsNullOrWhiteSpace(p._Amount), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.Amount.WrongFormat", new string[] { "AA", p.RowNumber.ToString() }]);

            RuleFor(rule => rule.Amount)
                .Must((p, pp) => pp.HasValue && pp.Value >= 0)
                .When(p => !string.IsNullOrWhiteSpace(p._Amount) && p.Amount.HasValue && p._InvoiceType == "3", ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.Amount.Positive", new string[] { "AA", p.RowNumber.ToString() }]);

            RuleFor(rule => rule._VatPercent)
               .NotEmpty()
               .When(p => true, ApplyConditionTo.CurrentValidator)
               .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.VatPercent.Required", new string[] { "AB", p.RowNumber.ToString() }])

               .Must((p, pp) => decimal.TryParse(pp, out var rs))
               .When(p => !string.IsNullOrWhiteSpace(p._VatPercent), ApplyConditionTo.CurrentValidator)
               .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.VatPercent.WrongFormat", new string[] { "AB", p.RowNumber.ToString() }]);

            //RuleFor(rule => rule.VatPercent)
            //    .NotEmpty()
            //    .When(p => true, ApplyConditionTo.CurrentValidator)
            //    .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.VatPercent.Required", new string[] { "AB", p.RowNumber.ToString() }]);


            RuleFor(rule => rule._VatAmount)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.VatAmount.Required", new string[] { "AC", p.RowNumber.ToString() }])

                .Must((p, pp) => decimal.TryParse(pp, out var rs))
                .When(p => !string.IsNullOrWhiteSpace(p._VatAmount), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.VatAmount.WrongFormat", new string[] { "AC", p.RowNumber.ToString() }]);

            RuleFor(rule => rule.VatAmount)
                .Must((p, pp) => pp.Value >= 0)
                .When(p => !string.IsNullOrWhiteSpace(p._VatAmount) && p.VatAmount.HasValue && p._InvoiceType == "3", ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.VatAmount.Positive", new string[] { "AC", p.RowNumber.ToString() }]);

            RuleFor(rule => rule._PaymentAmount)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.PaymentAmount.Required", new string[] { "AD", p.RowNumber.ToString() }])

                .Must((p, pp) => decimal.TryParse(pp, out var rs))
                .When(p => !string.IsNullOrWhiteSpace(p._PaymentAmount), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.PaymentAmount.WrongFormat", new string[] { "AD", p.RowNumber.ToString() }]);

            RuleFor(rule => rule.PaymentAmount)
                .Must((p, pp) => pp.Value >= 0)
                .When(p => !string.IsNullOrWhiteSpace(p._PaymentAmount) && p.PaymentAmount.HasValue && p._InvoiceType == "3", ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.PaymentAmount.Positive", new string[] { "AD", p.RowNumber.ToString() }]);

        }

    }
}
