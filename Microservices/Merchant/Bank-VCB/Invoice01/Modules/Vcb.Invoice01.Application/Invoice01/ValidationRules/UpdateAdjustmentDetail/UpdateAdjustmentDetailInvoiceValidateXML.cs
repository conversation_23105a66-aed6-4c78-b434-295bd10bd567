using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using Core.Shared.Helper;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Diagnostics;
using System.Reflection;
using System.Threading.Tasks;
using System.Xml;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Invoice01.Application.Invoice01.Models.Requests.Commands;

namespace VnisCore.Invoice01.Application.Invoice01.ValidationRule.UpdateAdjustmentDetail
{
    public class UpdateAdjustmentDetailInvoiceValidateXML : IValidationRuleAsync<UpdateAdjustmentDetailInvoice01Request, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IValidationContext _validationContext;
        private readonly IAppFactory _appFactory;
        private readonly IRepository<Invoice01HeaderEntity> _repoInvoice01Header;

        public UpdateAdjustmentDetailInvoiceValidateXML(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IValidationContext validationContext,
            IRepository<Invoice01HeaderEntity> repoInvoice01Header,
            IAppFactory appFactory)
        {
            _localizer = localizer;
            _validationContext = validationContext;
            _repoInvoice01Header = repoInvoice01Header;
            _appFactory = appFactory;
        }

        public async Task<ValidationResult> HandleAsync(UpdateAdjustmentDetailInvoice01Request input)
        {
            // Validate thông tin hóa đơn
            Type type = input.GetType();
            PropertyInfo[] properties = type.GetProperties();
            foreach (PropertyInfo property in properties)
            {
                if (property.PropertyType == typeof(string))
                {
                    string value = (string)property.GetValue(input);
                    if (!string.IsNullOrEmpty(value) && !StringHelper.ContainsIllegalCharacters(value))
                        return new ValidationResult(false, $"{property.Name}: {value}");
                }
            }

            // Validate InvoiceDetails
            if (input.InvoiceDetails != null)
            {
                foreach (var item in input.InvoiceDetails)
                {
                    type = item.GetType();
                    properties = type.GetProperties();
                    foreach (PropertyInfo property in properties)
                    {
                        if (property.PropertyType == typeof(string))
                        {
                            string value = (string)property.GetValue(item);
                            if (!string.IsNullOrEmpty(value) && !StringHelper.ContainsIllegalCharacters(value))
                                return new ValidationResult(false, $"InvoiceDetails - {property.Name}: {value}");
                        }
                    }

                    // InvoiceDetailExtras
                    if (item.InvoiceDetailExtras != null)
                    {
                        foreach (var invoiceDetailExtras in item.InvoiceDetailExtras)
                        {
                            type = invoiceDetailExtras.GetType();
                            properties = type.GetProperties();
                            foreach (PropertyInfo property in properties)
                            {
                                if (property.PropertyType == typeof(string))
                                {
                                    string value = (string)property.GetValue(invoiceDetailExtras);
                                    if (!string.IsNullOrEmpty(value) && !StringHelper.ContainsIllegalCharacters(value))
                                        return new ValidationResult(false, $"InvoiceDetails -  InvoiceDetailExtras - {property.Name}: {value}");
                                }
                            }
                        }
                    }
                }
            }

            // Validate InvoiceHeaderExtras
            if (input.InvoiceHeaderExtras != null)
            {
                foreach (var item in input.InvoiceHeaderExtras)
                {
                    type = item.GetType();
                    properties = type.GetProperties();
                    foreach (PropertyInfo property in properties)
                    {
                        if (property.PropertyType == typeof(string))
                        {
                            string value = (string)property.GetValue(item);
                            if (!string.IsNullOrEmpty(value) && !StringHelper.ContainsIllegalCharacters(value))
                                return new ValidationResult(false, $"InvoiceHeaderExtras - {property.Name}: {value}");
                        }
                    }
                }
            }

            return new ValidationResult(true);
        }
    }
}
