using Core.Domain.Repositories;
using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Core.Oracle.Domain.Entities.InvoiceTemplate;
using VnisCore.Invoice01.Application.Factories.Models;
using VnisCore.Invoice01.Application.Factories.Services;
using VnisCore.Invoice01.Application.Invoice01.Dto;

namespace VnisCore.Invoice01.Application.Invoice01.ValidationRule.UpdateReplace
{
    /// <summary>
    /// chekc ngày hóa đơn 
    /// </summary>
    public class UpdateReplaceInvoiceCheckInvoiceDateRangeRule : IValidationRuleAsync<UpdateReplaceInvoice01HeaderDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IValidationContext _validationContext;
        private readonly IInvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity> _invoiceService;
        private readonly IRepository<InvoiceTemplateEntity, long> _repoTemplate;
        private readonly ICommonInvoice01Service _commonInvoice01Service;

        public UpdateReplaceInvoiceCheckInvoiceDateRangeRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IValidationContext validationContext,
            IRepository<InvoiceTemplateEntity, long> repoTemplate,
            IInvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity> invoiceService,
            ICommonInvoice01Service commonInvoice01Service
        )
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _validationContext = validationContext;
            _invoiceService = invoiceService;
            _repoTemplate = repoTemplate;
            _commonInvoice01Service = commonInvoice01Service;
        }

        public async Task<ValidationResult> HandleAsync(UpdateReplaceInvoice01HeaderDto input)
        {
            if (!_commonInvoice01Service.CheckInvoiceDateAndSerialNo(input.InvoiceDate, input.SerialNo))
            {
                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice.InvoiceDateRange.InvalidInvoiceDateAndSerialNo"]);
            }

            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;
            //var tenantId = _validationContext.GetItem<Guid>("TenantId");
            var invoice = _validationContext.GetItem<Invoice01HeaderEntity>("Invoice");
            var template = await _repoTemplate.FirstOrDefaultAsync(x => x.TenantId == tenantId && x.TemplateNo == input.TemplateNo && x.SerialNo == input.SerialNo && !x.IsDeleted);

            //- Mặc định hiển thị ngày tạo hóa đơn hoặc ngày sửa hóa đơn gần nhất
            //-Cho phép cập nhật Ngày hóa đơn:
            //Cận dưới: Ngày tạo hoá đơn
            //Cận trên: Ngày hiện tại
            InvoiceDateRangeModel range = new InvoiceDateRangeModel();
            if (template.SourceType == InvoiceTemplateSource.FormExcel.GetHashCode())
            {
                range.Min = invoice.InvoiceDate;
            }

            range = await _invoiceService.InvoiceDateRangeAsync(tenantId, invoice.TemplateNo, invoice.SerialNo, invoice.InvoiceNo);
            if (range.Min.Date > DateTime.Now.Date)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.InvoiceDateMin", new[] { range.Min.ToString("dd/MM/yyyy") }]);

            if (input.InvoiceDate > range.Max || input.InvoiceDate < range.Min)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.InvoiceDateRange", new[] { range.Min.ToString("dd/MM/yyyy"), range.Max.ToString("dd/MM/yyyy") }]);

            return new ValidationResult(true);
        }
    }
}
