using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.Services;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Invoice01.Application.Factories.Interfaces;
using VnisCore.Invoice01.Application.Factories.Services;
using Vcb.Invoice01.Application.Invoice01.Dto;
using VnisCore.Invoice01.Application.Invoice01.Models;

namespace Vcb.Invoice01.Application.Invoice01.ValidationRules.CreateG4AdjustmentHeader
{
    public class CreateG4InvoiceAdjustmentHeaderCheckTellSeqRule : IValidationRuleAsync<CreateG4AdjustmentHeaderInvoice01HeaderDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IEnumerable<ICheckInvoiceHeaderExtraService> _checkInvoiceHeaderExtraServices;

        public CreateG4InvoiceAdjustmentHeaderCheckTellSeqRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IEnumerable<ICheckInvoiceHeaderExtraService> checkInvoiceHeaderExtraServices
            )
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _checkInvoiceHeaderExtraServices = checkInvoiceHeaderExtraServices;
        }

        public async Task<ValidationResult> HandleAsync(CreateG4AdjustmentHeaderInvoice01HeaderDto input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var service = _checkInvoiceHeaderExtraServices.First(x => x.GetType().Name == typeof(Invoice01CheckHeaderExtraValueService).Name);

            var repoSetting = _appFactory.GetServiceDependency<ISettingService>();
            var setting = await repoSetting.GetByCodeAsync(tenantId, SettingKey.ValidateInvoiceConfigName.ToString());

            if (setting != null && !string.IsNullOrEmpty(setting.Value))
            {
                var serviceName = $"Invoice01{setting.Value}CheckHeaderExtraValueService";
                service = _checkInvoiceHeaderExtraServices.FirstOrDefault(x => x.GetType().Name == serviceName);
            }

            var headerExtras = input.InvoiceHeaderExtras.Select(x => new CheckHeaderExtraModel
            {
                FieldName = x.FieldName,
                FieldValue = x.FieldValue
            }).ToList();

            return await service.ValidateAsync(headerExtras);
        }
    }
}
