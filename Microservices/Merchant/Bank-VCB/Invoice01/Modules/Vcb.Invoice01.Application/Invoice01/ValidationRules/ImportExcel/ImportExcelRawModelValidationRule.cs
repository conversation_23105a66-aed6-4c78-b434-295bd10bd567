using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Extensions;
using Core.Shared.ValidationRules.ImportExcel;
using FluentValidation;
using Microsoft.Extensions.Localization;
using Vcb.Invoice01.Application.Invoice01.Models;

namespace Vcb.Invoice01.Application.Invoice01.ValidationRules.ImportExcel
{
    public class ImportExcelRawModelValidationRule<TRawModel> : ImportExcelRawBaseModelValidationRule<TRawModel>
        where TRawModel : ImportExcelRawModel
    {
        public ImportExcelRawModelValidationRule()
            : base()
        {

        }
        public ImportExcelRawModelValidationRule(
            IStringLocalizer<CoreLocalizationResource> _localizier
            )
            : base(_localizier)
        {
            // ProductName
            RuleFor(p => p.ProductName)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => _localizier["Vnis.BE.VCB.Invoice01.ProductNameNotNull", new string[] { p.RowNumber.ToString() }])
                .MaximumLength(500)
                .When(p => !string.IsNullOrWhiteSpace(p.ProductName), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => _localizier["Vnis.BE.VCB.Invoice01.ProductNameMax500", new string[] { p.RowNumber.ToString() }]);

            // UnitName
            RuleFor(p => p.UnitName)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => _localizier["Vnis.BE.VCB.Invoice01.UnitNotNull", new string[] { p.RowNumber.ToString() }])
                .MaximumLength(50)
                .When(p => !string.IsNullOrWhiteSpace(p.UnitName), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => _localizier["Vnis.BE.VCB.Invoice01.UnitMax50", new string[] { p.RowNumber.ToString() }]);

            // Quantity
            RuleFor(p => p._Quantity)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => _localizier["Vnis.BE.VCB.Invoice01.QuantityIsNull", new string[] { p.RowNumber.ToString() }])
                .Must((p, pp) => pp.DecimalFormatUnitedKingdom() != null)
                .When(p => !string.IsNullOrWhiteSpace(p._Quantity), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => _localizier["Vnis.BE.VCB.Invoice01.QuantityWrongFormat", new string[] { p.RowNumber.ToString() }]);

            // UnitPrice
            RuleFor(p => p._UnitPrice)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => _localizier["Vnis.BE.VCB.Invoice01.UnitPriceIsNull", new string[] { p.RowNumber.ToString() }])
                .Must((p, pp) => pp.DecimalFormatUnitedKingdom() != null)
                .When(p => !string.IsNullOrWhiteSpace(p._UnitPrice), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => _localizier["Vnis.BE.VCB.Invoice01.UnitPriceWrongFormat", new string[] { p.RowNumber.ToString() }]);

            // Amount
            RuleFor(p => p._Amount)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => _localizier["Vnis.BE.VCB.Invoice01.AmountIsNull", new string[] { p.RowNumber.ToString() }])
                .Must((p, pp) => pp.DecimalFormatUnitedKingdom() != null)
                .When(p => !string.IsNullOrWhiteSpace(p._Amount), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => _localizier["Vnis.BE.VCB.Invoice01.AmountWrongFormat", new string[] { p.RowNumber.ToString() }]);

            // VatPercent
            RuleFor(p => p._VatPercent)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => _localizier["Vnis.BE.VCB.Invoice01.VatPercentIsNull", new string[] { p.RowNumber.ToString() }])
                .Must((p, pp) => pp.DecimalFormatUnitedKingdom() != null)
                .When(p => !string.IsNullOrWhiteSpace(p._VatPercent), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => _localizier["Vnis.BE.VCB.Invoice01.VatPercentWrongFormat", new string[] { p.RowNumber.ToString() }]);

            // VatAmount
            RuleFor(p => p._VatAmount)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => _localizier["Vnis.BE.VCB.Invoice01.VatAmountIsNull", new string[] { p.RowNumber.ToString() }])
                .Must((p, pp) => pp.DecimalFormatUnitedKingdom() != null)
                .When(p => !string.IsNullOrWhiteSpace(p._VatAmount), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => _localizier["Vnis.BE.VCB.Invoice01.VatAmountWrongFormat", new string[] { p.RowNumber.ToString() }]);

            // PaymentAmount
            RuleFor(p => p._PaymentAmount)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => _localizier["Vnis.BE.VCB.Invoice01.PaymentAmountIsNull", new string[] { p.RowNumber.ToString() }])
                .Must((p, pp) => pp.DecimalFormatUnitedKingdom() != null)
                .When(p => !string.IsNullOrWhiteSpace(p._PaymentAmount), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => _localizier["Vnis.BE.VCB.Invoice01.PaymentAmountWrongFormat", new string[] { p.RowNumber.ToString() }]);

            // DetailNote
            RuleFor(p => p.DetailNote)
                .MaximumLength(2000)
                .WithMessage(p => _localizier["Vnis.BE.VCB.Invoice01.DetailNoteMax2000", new string[] { p.RowNumber.ToString() }]);

            #region Detail
            #endregion Detail
        }
    }
}
