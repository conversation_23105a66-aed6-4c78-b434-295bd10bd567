using Core.EventBus;

namespace VnisCore.Invoice01.Application.Invoice01.RabbitMqEventBus.ImportInvoice01.MessageEventData
{
    [EventName("invoice01.importinvoice01result")]
    public class ImportInvoice01EventResultData
    {
        public ImportInvoice01EventResultData()
        {

        }

        public ImportInvoice01EventResultData(long id)
        {
            InvoiceId = id;
        }

        public long InvoiceId { get; set; }
    }
}