using Core.EventBus;
using VnisCore.Invoice01.Application.Invoice01.Dto;

namespace VnisCore.Invoice01.Application.Invoice01.RabbitMqEventBus.DeleteInvoice01.MessageEventData
{
    [EventName("invoice01.deleteinvoice01result")]
    public class DeleteInvoice01HeaderEventResultData : DeleteInvoice01HeaderDto
    {
        public DeleteInvoice01HeaderEventResultData()
        {

        }

        public DeleteInvoice01HeaderEventResultData(DeleteInvoice01HeaderDto item)
        {
            Ids = Ids;
            ErrorMessages = item.ErrorMessages;
        }
    }
}