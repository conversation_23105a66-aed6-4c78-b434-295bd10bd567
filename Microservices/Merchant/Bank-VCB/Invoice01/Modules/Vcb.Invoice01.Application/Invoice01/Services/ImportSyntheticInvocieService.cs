using Core;
using Core.ExceptionHandling.Localization;
using Core.Localization.Resources.AbpLocalization;
using Core.MultiTenancy;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Users;
using FluentValidation.Results;
using IdentityServer4.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using OfficeOpenXml;
using Serilog;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Vcb.Invoice01.Application.Invoice01.ValidationRules.ImportSynthetic;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Invoice01.Application.Factories.Services;
using VnisCore.Invoice01.Application.Invoice01.Models;
using VnisCore.Invoice01.Application.Invoice01.Models.Requests.Commands;
using VnisCore.Invoice01.Application.Invoice01.Services;
using static CoreDbtg.Shared.Constants.DbtgSharedConst;

namespace Vcb.Invoice01.Application.Invoice01.Services
{
    public class ImportSyntheticInvocieService : IImportSyntheticInvoiceService
    {
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly IStringLocalizer<AbpExceptionHandlingResource> _localizierException;
        private readonly IInvoice01Service _invoice01Service;
        private readonly IImportReplaceInvoiceService _importReplaceInvoiceService;
        private readonly IImportAdjustmentHeaderInvoiceService _importAdjustmentHeaderInvoiceService;
        private readonly IImportAdjustmentDetailInvoiceService _importAdjustmentDetailInvoiceService;
        private readonly IImportSyntheticCommonService _importSyntheticCommonService;
        private readonly ICurrentTenant _currentTenant;
        private readonly ICurrentUser _currentUser;
        private List<long> _headerIds;
        private List<Invoice01HeaderEntity> _invoice01HeaderEntities;
        private List<Invoice01DetailEntity> _invoice01DetailEntities;
        private readonly ICommonInvoice01Service _commonInvoice01Service;
        private readonly IServiceProvider _serviceProvider;
        private readonly IInvoice01LockService _invoice01LockService;

        private List<long> _listReplaceId = new List<long>();
        private long _ticks = DateTime.Now.Ticks;

        public ImportSyntheticInvocieService(
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IStringLocalizer<AbpExceptionHandlingResource> localizierException,
            IInvoice01Service invoice01Service,
            IImportReplaceInvoiceService importReplaceInvoiceService,
            IImportAdjustmentHeaderInvoiceService importAdjustmentHeaderInvoiceService,
            IImportAdjustmentDetailInvoiceService importAdjustmentDetailInvoiceService,
            IImportSyntheticCommonService importSyntheticCommonService,
            ICommonInvoice01Service commonInvoice01Service,
            IServiceProvider serviceProvider,
             IInvoice01LockService invoice01LockService
        )
        {
            _appFactory = appFactory;
            _localizier = localizier;
            _localizierException = localizierException;
            _invoice01Service = invoice01Service;
            _importReplaceInvoiceService = importReplaceInvoiceService;
            _importAdjustmentHeaderInvoiceService = importAdjustmentHeaderInvoiceService;
            _importAdjustmentDetailInvoiceService = importAdjustmentDetailInvoiceService;
            _importSyntheticCommonService = importSyntheticCommonService;

            _currentTenant = _appFactory.CurrentTenant;
            _currentUser = _appFactory.CurrentUser;
            _headerIds = new List<long>();
            _invoice01HeaderEntities = new List<Invoice01HeaderEntity>();
            _invoice01DetailEntities = new List<Invoice01DetailEntity>();
            _serviceProvider = serviceProvider;
            _commonInvoice01Service = commonInvoice01Service;
            _invoice01LockService = invoice01LockService;
        }

        public IEnumerable<ImportSyntheticInvoiceModel> DeserializeImportSyntheticInvoiceModels(ImportSyntheticInvoiceRequestModel request)
        {
            return JsonConvert.DeserializeObject<IEnumerable<ImportSyntheticInvoiceModel>>(request.Conditions)
                .Select((p, i) =>
                {
                    p.RowNumber = i + 4;
                    p.TemplateNo = request.TemplateNo;
                    p.SerialNo = request.SerialNo;
                    return p;
                });
        }

        public IEnumerable<ImportSyntheticInvoiceModel> CreateImportSyntheticInvoiceModels(ImportSyntheticInvoiceRequestModel request)
        {
            ValidateFile(request.File);

            var results = new List<ImportSyntheticInvoiceModel>();
            byte[] bytes = new byte[0];
            using (var streamMemory = new MemoryStream())
            {
                request.File.CopyTo(streamMemory);
                bytes = streamMemory.ToArray();
            }

            using (var streamMemory = new MemoryStream(bytes))
            {
                var package = new ExcelPackage(streamMemory);
                var worksheet = package.Workbook.Worksheets[0];
                if (worksheet?.Dimension == null || worksheet.Dimension.End.Row < 4)
                    throw new UserFriendlyException(_localizier["Vnis.BE.Invoice02.EmptyDataImportOrDataFormatIncorrect"]);

                for (var i = 4; i <= worksheet.Dimension.End.Row; i++)
                {
                    var rowNumber = i;
                    var groupId = worksheet.Cells[i, 1].GetValue<string>()?.Trim();
                    var invoiceType = worksheet.Cells[i, 2].GetValue<string>()?.Trim();
                    var templateNo = request.TemplateNo;
                    var serialNo = request.SerialNo;
                    var pCTime = worksheet.Cells[i, 3].GetValue<string>()?.Trim();
                    var invoiceDate = worksheet.Cells[i, 4].GetValue<string>()?.Trim();
                    var oldInvoiceNo = worksheet.Cells[i, 5].GetValue<string>()?.Trim();
                    var oldTemplateNo_oldSerialNo = worksheet.Cells[i, 6].GetValue<string>()?.Trim();
                    var oldTemplateNo = !string.IsNullOrWhiteSpace(oldTemplateNo_oldSerialNo) ? oldTemplateNo_oldSerialNo.Substring(0, 1) : string.Empty;
                    var oldSerialNo = !string.IsNullOrWhiteSpace(oldTemplateNo_oldSerialNo) ? oldTemplateNo_oldSerialNo.Substring(1) : string.Empty;
                    var oldInvoiceDate = worksheet.Cells[i, 7].GetValue<string>()?.Trim();
                    var creatorErp = worksheet.Cells[i, 8].GetValue<string>()?.Trim();
                    var tellSeq = worksheet.Cells[i, 9].GetValue<string>()?.Trim();
                    var paymentMethod = worksheet.Cells[i, 10].GetValue<string>()?.Trim();
                    var currency = worksheet.Cells[i, 11].GetValue<string>()?.Trim();
                    var exchangeRate = worksheet.Cells[i, 12].GetValue<string>()?.Trim();
                    var note = worksheet.Cells[i, 13].GetValue<string>()?.Trim();
                    var buyerCode = worksheet.Cells[i, 14].GetValue<string>()?.Trim();
                    var buyerFullName = worksheet.Cells[i, 15].GetValue<string>()?.Trim();
                    var buyerTaxCode = worksheet.Cells[i, 16].GetValue<string>()?.Trim();
                    var buyerAddressLine = worksheet.Cells[i, 17].GetValue<string>()?.Trim();
                    var buyerBankAccount = worksheet.Cells[i, 18].GetValue<string>()?.Trim();
                    var buyerBankName = worksheet.Cells[i, 19].GetValue<string>()?.Trim();
                    var buyerEmail = worksheet.Cells[i, 20].GetValue<string>()?.Trim();
                    var buyerType = worksheet.Cells[i, 21].GetValue<string>()?.Trim();

                    // Detail
                    var productIndex = worksheet.Cells[i, 22].GetValue<string>()?.Trim();
                    var productName = worksheet.Cells[i, 23].GetValue<string>()?.Trim();
                    var unitName = worksheet.Cells[i, 24].GetValue<string>()?.Trim();
                    var quantity = worksheet.Cells[i, 25].GetValue<string>()?.Trim();
                    var unitPrice = worksheet.Cells[i, 26].GetValue<string>()?.Trim();
                    var amount = worksheet.Cells[i, 27].GetValue<string>()?.Trim();
                    var vatPercent = worksheet.Cells[i, 28].GetValue<string>()?.Trim();
                    var vatAmount = worksheet.Cells[i, 29].GetValue<string>()?.Trim();
                    var paymentAmount = worksheet.Cells[i, 30].GetValue<string>()?.Trim();
                    var detailNote = worksheet.Cells[i, 31].GetValue<string>()?.Trim();

                    if (groupId == null && invoiceType == null && pCTime == null && invoiceDate == null && oldInvoiceNo == null && oldTemplateNo_oldSerialNo == null && oldInvoiceDate == null && creatorErp == null && tellSeq == null && paymentMethod == null && currency == null && exchangeRate == null && note == null && buyerCode == null && buyerFullName == null && buyerTaxCode == null && buyerAddressLine == null && buyerBankAccount == null && buyerBankName == null && buyerEmail == null && buyerType == null && productIndex == null && productName == null && unitName == null && quantity == null && unitPrice == null && amount == null && vatPercent == null && vatAmount == null && paymentAmount == null && detailNote == null)
                    {
                        continue;
                    }

                    var cif = buyerCode.DeleteZeroAtFirstChar();
                    if (string.IsNullOrWhiteSpace(cif))
                    {
                        throw new UserFriendlyException(_localizier["Vnis.BE.Invoice01.ImportSyntheticInvoice.BuyerCode.Invalid", new string[] { buyerCode }]);
                    }

                    var result = new ImportSyntheticInvoiceModel
                    {
                        RowNumber = rowNumber,
                        GroupId = groupId,
                        _InvoiceType = invoiceType,
                        _TemplateNo = templateNo.ToString(),
                        SerialNo = serialNo,
                        PCTime = pCTime,
                        InvoiceDate = invoiceDate,
                        OldInvoiceNo = oldInvoiceNo,
                        _OldTemplateNo = oldTemplateNo,
                        OldSerialNo = oldSerialNo,
                        OldInvoiceDate = oldInvoiceDate,
                        CreatorErp = string.IsNullOrWhiteSpace(creatorErp) ? string.Empty : creatorErp,
                        TellSeq = string.IsNullOrWhiteSpace(tellSeq) ? string.Empty : tellSeq,
                        PaymentMethod = paymentMethod,
                        Currency = currency,
                        _ExchangeRate = exchangeRate,
                        Note = note,
                        BuyerCode = cif,
                        BuyerFullName = buyerFullName,
                        BuyerTaxCode = buyerTaxCode,
                        BuyerAddressLine = buyerAddressLine,
                        BuyerBankAccount = buyerBankAccount,
                        BuyerBankName = buyerBankName,
                        BuyerEmail = buyerEmail,
                        BuyerType = buyerType,

                        // Detail
                        _ProductIndex = productIndex,
                        ProductName = productName,
                        UnitName = unitName,
                        _Quantity = quantity?.Replace(",", "."),
                        _UnitPrice = unitPrice,
                        _Amount = amount,
                        _VatPercent = vatPercent,
                        _VatAmount = vatAmount,
                        _PaymentAmount = paymentAmount,
                        DetailNote = detailNote
                    };

                    results.Add(result);
                }
            }

            return results;
        }

        public void ValidateFile(IFormFile file)
        {
            if (file.Length == 0)
                throw new UserFriendlyException(_localizier["Vnis.BE.Invoice01.ImportSyntheticInvoice.File.NotFound"]);

            if (file.Length > 5 * 1024 * 1024)
                throw new UserFriendlyException(_localizier["Vnis.BE.Invoice01.ImportSyntheticInvoice.File.Length"]);

        }

        #region ValidateImportSynthetic
        public async Task<IEnumerable<ResponseSyntheticInvoiceModel>> CreateImportSyntheticInvoiceModels(IEnumerable<ImportSyntheticInvoiceModel> importSyntheticInvoiceModels)
        {
            var responseSyntheticInvoiceModels = new List<ResponseSyntheticInvoiceModel>();
            foreach (var importSyntheticInvoiceModel in importSyntheticInvoiceModels)
            {
                var importSyntheticInvoiceModelValidator = new ImportSyntheticInvoiceModelValidator(_localizierException, _commonInvoice01Service);

                // Propertities
                var resultOfValidator = importSyntheticInvoiceModelValidator.Validate(importSyntheticInvoiceModel);

                if (!resultOfValidator.IsValid)
                {
                    return new List<ResponseSyntheticInvoiceModel> {
                        new ResponseSyntheticInvoiceModel {
                            ErrorMessage = string.Join(" ", resultOfValidator.Errors.Select(p => p.ErrorMessage))
                        }
                    };
                }

                // ErpId
                var erpId = _invoice01Service.GenerateErpId(
                    importSyntheticInvoiceModel.PCTime,
                    importSyntheticInvoiceModel.GetInvoiceDate(),
                    _currentTenant.TenantCode,
                    importSyntheticInvoiceModel.CreatorErp,
                    importSyntheticInvoiceModel.TellSeq);
                importSyntheticInvoiceModel.ErpId = erpId;

                // Headers
                var responseSyntheticInvoiceModel = ObjectMapperSyntheticInvoiceModel(importSyntheticInvoiceModel);

                var resultOfValidatorHeaders = (await ValidatorInvoice01Headers(responseSyntheticInvoiceModels, importSyntheticInvoiceModel))
                    .Where(p => !string.IsNullOrEmpty(p.ErrorMessage));
                foreach (var resultOfValidatorHeader in resultOfValidatorHeaders)
                {
                    resultOfValidator.Errors.Add(resultOfValidatorHeader);
                }

                // Details
                var resultOfValidatorDetails = (await ValidatorInvoice01Details(responseSyntheticInvoiceModels, importSyntheticInvoiceModel))
                    .Where(p => !string.IsNullOrEmpty(p.ErrorMessage));

                foreach (var resultOfValidatorDetail in resultOfValidatorDetails)
                {
                    resultOfValidator.Errors.Add(resultOfValidatorDetail);

                }

                // Validate Allowed characters
                var resultsOfValidatorAllowedCharacters = ValidatorAllowedCharacters(importSyntheticInvoiceModel);
                foreach (var resultsOfValidatorAllowedCharacter in resultsOfValidatorAllowedCharacters)
                {
                    resultOfValidator.Errors.Add(resultsOfValidatorAllowedCharacter);
                }

                if (!resultOfValidator.IsValid)
                {
                    var errorMessages = resultOfValidator.Errors.Select(p => p.ErrorMessage).Distinct();
                    responseSyntheticInvoiceModel.ErrorMessage = string.Join("; ", errorMessages);
                }


                responseSyntheticInvoiceModels.Add(responseSyntheticInvoiceModel);
            }
            return responseSyntheticInvoiceModels;
        }

        private ResponseSyntheticInvoiceModel ObjectMapperSyntheticInvoiceModel(ImportSyntheticInvoiceModel importSyntheticInvoiceModel)
        {
            try
            {
                var responseSyntheticInvoiceModel = _appFactory.ObjectMapper.Map<ImportSyntheticInvoiceModel, ResponseSyntheticInvoiceModel>(importSyntheticInvoiceModel);
                return responseSyntheticInvoiceModel;
            }
            catch (Exception ex)
            {
                throw new UserFriendlyException(_localizier["Vnis.BE.Invoice01.ImportSyntheticInvoice.File.WrongFormat"]);
            }
        }

        private async Task<List<ValidationFailure>> ValidatorInvoice01Headers(List<ResponseSyntheticInvoiceModel> responseSyntheticInvoiceModels, ImportSyntheticInvoiceModel importSyntheticInvoiceModel)
        {
            var results = new List<ValidationFailure> {
                await ValidatorHeaderInvoiceCashierCodeAsync(importSyntheticInvoiceModel),
                ValidatorHeaderInvoiceType(responseSyntheticInvoiceModels, importSyntheticInvoiceModel),
                await ValidatorHeaderInvoiceStatus(importSyntheticInvoiceModel),
                //await ValidatorErpId(importSyntheticInvoiceModel)

            };

            return results;
        }

        private async Task<ValidationFailure> ValidatorHeaderInvoiceCashierCodeAsync(ImportSyntheticInvoiceModel responseSyntheticInvoiceModel)
        {
            var result = new ValidationFailure(string.Empty, string.Empty);
            if (string.IsNullOrWhiteSpace(responseSyntheticInvoiceModel.CreatorErp))
            {
                return result;
            }

            var errorMessage = await _commonInvoice01Service.CheckCashierCode(responseSyntheticInvoiceModel.CreatorErp.ToString());
            if (!string.IsNullOrWhiteSpace(errorMessage))
            {
                result.ErrorMessage = errorMessage;
            }
            else
            {
                responseSyntheticInvoiceModel.CreatorErp = int.Parse(responseSyntheticInvoiceModel.CreatorErp).ToString();
            }

            //if (!int.TryParse(responseSyntheticInvoiceModel.CreatorErp, out var cashierCode))
            //{
            //    result.ErrorMessage = _localizier["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Header.CreatorErp.WrongFormat", new string[] { responseSyntheticInvoiceModel.OldTemplateNo.ToString(), responseSyntheticInvoiceModel.OldSerialNo, responseSyntheticInvoiceModel.OldInvoiceNo }];
            //}
            //else
            //{
            //    responseSyntheticInvoiceModel.CreatorErp = cashierCode.ToString();
            //}

            return result;
        }
        private ValidationFailure ValidatorHeaderInvoiceType(List<ResponseSyntheticInvoiceModel> responseSyntheticInvoiceModels, ImportSyntheticInvoiceModel importSyntheticInvoiceModel)
        {
            var result = new ValidationFailure(string.Empty, string.Empty);

            // TODO: Group - Type
            if (responseSyntheticInvoiceModels.Any(p =>
                p.GroupId == importSyntheticInvoiceModel.GroupId
                && p.InvoiceType != importSyntheticInvoiceModel.InvoiceType
            ))
            {
                result.ErrorMessage = _localizier["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Duplicate", new string[] { importSyntheticInvoiceModel.RowNumber.ToString() }];
                return result;
            }

            // TODO: AdjustmentHeader
            if (importSyntheticInvoiceModel.InvoiceType == (short)ImportSyntheticInvoiceEnum.AdjustmentHeader
                && responseSyntheticInvoiceModels.Any(p =>
                    p.GroupId == importSyntheticInvoiceModel.GroupId
                    && p.InvoiceType == (short)ImportSyntheticInvoiceEnum.AdjustmentHeader
                    && p.OldTemplateNo == importSyntheticInvoiceModel.OldTemplateNo
                    && p.OldSerialNo == importSyntheticInvoiceModel.OldSerialNo
                    && p.OldInvoiceNo == importSyntheticInvoiceModel.OldInvoiceNo
            ))
            {
                result.ErrorMessage = _localizier["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Header.AdjustmentHeader.Duplicate", new string[] {
                    importSyntheticInvoiceModel.OldTemplateNo.ToString(),
                    importSyntheticInvoiceModel.OldSerialNo,
                    importSyntheticInvoiceModel.OldInvoiceNo,
                    importSyntheticInvoiceModel.RowNumber.ToString(),
                }];
                return result;
            }

            // TODO: AdjustmentDetail


            // TODO: Replace
            if (responseSyntheticInvoiceModels.Any(p =>
                p.GroupId != importSyntheticInvoiceModel.GroupId
                && p.InvoiceType == (short)ImportSyntheticInvoiceEnum.Replace
                && p.OldTemplateNo == importSyntheticInvoiceModel.OldTemplateNo
                && p.OldSerialNo == importSyntheticInvoiceModel.OldSerialNo
                && p.OldInvoiceNo == importSyntheticInvoiceModel.OldInvoiceNo
            ))
            {
                result.ErrorMessage = _localizier["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Header.Replace.Duplicate", new string[] {
                    importSyntheticInvoiceModel.OldTemplateNo.ToString(),
                    importSyntheticInvoiceModel.OldSerialNo,
                    importSyntheticInvoiceModel.OldInvoiceNo,
                    importSyntheticInvoiceModel.RowNumber.ToString(),

                }];
                return result;
            }

            if (importSyntheticInvoiceModel.InvoiceType == (short)ImportSyntheticInvoiceEnum.Replace
                && responseSyntheticInvoiceModels.Any(p =>
                    p.GroupId != importSyntheticInvoiceModel.GroupId
                    && p.InvoiceType != (short)ImportSyntheticInvoiceEnum.Replace
                    && p.OldTemplateNo == importSyntheticInvoiceModel.OldTemplateNo
                    && p.OldSerialNo == importSyntheticInvoiceModel.OldSerialNo
                    && p.OldInvoiceNo == importSyntheticInvoiceModel.OldInvoiceNo
            ))
            {
                result.ErrorMessage = _localizier["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Duplicate", new string[] { importSyntheticInvoiceModel.RowNumber.ToString() }];
                return result;
            }

            return result;
        }

        private async Task<ValidationFailure> ValidatorHeaderInvoiceStatus(ImportSyntheticInvoiceModel importSyntheticInvoiceModel)
        {
            var result = new ValidationFailure(string.Empty, string.Empty);

            var invoiceHeader = await GetInvoice01HeaderAsync(importSyntheticInvoiceModel);

            if (invoiceHeader.InvoiceStatus == (short)InvoiceStatus.XoaHuy.GetHashCode())
            {
                result.ErrorMessage = _localizier["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Header.ApproveCancelStatus.Invalid", new string[] { importSyntheticInvoiceModel.RowNumber.ToString() }];
                return result;
            }

            if (invoiceHeader.InvoiceStatus == (short)InvoiceStatus.XoaBo.GetHashCode())
            {
                result.ErrorMessage = _localizier["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Header.ApproveDeleteStatus.Invalid", new string[] { importSyntheticInvoiceModel.RowNumber.ToString() }];
                return result;
            }

            if (invoiceHeader.ApproveStatus != (short)ApproveStatus.DaDuyet.GetHashCode() && invoiceHeader.ApproveStatus != (short)ApproveStatus.KhongQuyTrinhDuyet.GetHashCode())
            {
                result.ErrorMessage = _localizier["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Header.ApproveStatus.Invalid", new string[] { importSyntheticInvoiceModel.RowNumber.ToString() }];
                return result;
            }

            if (invoiceHeader.SignStatus != (short)SignStatus.DaKy.GetHashCode())
            {
                result.ErrorMessage = _localizier["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Header.SignStatus.Invalid", new string[] { importSyntheticInvoiceModel.RowNumber.ToString() }];
                return result;
            }

            if (importSyntheticInvoiceModel.InvoiceType != (short)ImportSyntheticInvoiceEnum.Replace && invoiceHeader.InvoiceStatus != (short)InvoiceStatus.Goc.GetHashCode())
            {
                result.ErrorMessage = _localizier["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Header.Adjustment.InvoiceStatus.Invalid", new string[] { importSyntheticInvoiceModel.RowNumber.ToString() }];
                return result;
            }

            if (importSyntheticInvoiceModel.InvoiceType == (short)ImportSyntheticInvoiceEnum.Replace
                && invoiceHeader.InvoiceStatus != (short)InvoiceStatus.Goc.GetHashCode()
                && invoiceHeader.InvoiceStatus != (short)InvoiceStatus.ThayThe.GetHashCode()
            )
            {
                result.ErrorMessage = _localizier["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Header.Replace.InvoiceStatus.Invalid", new string[] { importSyntheticInvoiceModel.RowNumber.ToString() }];
                return result;
            }

            return result;
        }

        private async Task<List<ValidationFailure>> ValidatorInvoice01Details(List<ResponseSyntheticInvoiceModel> responseSyntheticInvoiceModels, ImportSyntheticInvoiceModel importSyntheticInvoiceModel)
        {
            var results = new List<ValidationFailure> {
                ValidatorDuplicateProductIndex(responseSyntheticInvoiceModels, importSyntheticInvoiceModel) ,
                await ValidatorExistingProductIndex(importSyntheticInvoiceModel),
                await ValidatorAllDetails(responseSyntheticInvoiceModels, importSyntheticInvoiceModel),
                ValidateTax(importSyntheticInvoiceModel)

        };

            return results;
        }

        private ValidationFailure ValidatorDuplicateProductIndex(List<ResponseSyntheticInvoiceModel> responseSyntheticInvoiceModels, ImportSyntheticInvoiceModel importSyntheticInvoiceModel)
        {
            var result = new ValidationFailure(string.Empty, string.Empty);
            if (responseSyntheticInvoiceModels.Any(p =>
                    p.GroupId == importSyntheticInvoiceModel.GroupId
                    && p.InvoiceType == importSyntheticInvoiceModel.InvoiceType
                    && p.OldTemplateNo == importSyntheticInvoiceModel.OldTemplateNo
                    && p.OldSerialNo == importSyntheticInvoiceModel.OldSerialNo
                    && p.OldInvoiceNo == importSyntheticInvoiceModel.OldInvoiceNo
                    && p.ProductIndex == importSyntheticInvoiceModel.ProductIndex
                )
            )
                result.ErrorMessage = _localizier["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Detail.DuplicateIndex", new string[] { importSyntheticInvoiceModel.OldTemplateNo.ToString(), importSyntheticInvoiceModel.OldSerialNo, importSyntheticInvoiceModel.OldInvoiceNo }];
            return result;
        }
        private async Task<ValidationFailure> ValidatorExistingProductIndex(ImportSyntheticInvoiceModel importSyntheticInvoiceModel)
        {
            var result = new ValidationFailure(string.Empty, string.Empty);

            var invoiceHeader = await GetInvoice01HeaderAsync(importSyntheticInvoiceModel);
            var invoiceDetails = await GetInvoice01DetailsAsync(invoiceHeader);

            if (importSyntheticInvoiceModel.InvoiceType == (short)ImportSyntheticInvoiceEnum.AdjustmentDetail
                && !invoiceDetails.Any(p => p.Index == importSyntheticInvoiceModel.ProductIndex
            ))
            {
                result.ErrorMessage = _localizier["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Detail.IndexInvalid", new string[] { importSyntheticInvoiceModel.OldTemplateNo.ToString(), importSyntheticInvoiceModel.OldSerialNo, importSyntheticInvoiceModel.OldInvoiceNo }];
                return result;
            }

            return result;
        }

        private List<ValidationFailure> ValidatorAllowedCharacters(ImportSyntheticInvoiceModel importSyntheticInvoiceModel)
        {
            var results = new List<ValidationFailure>();
            var commonInvoiceService = _serviceProvider.GetRequiredService<ICommonInvoice01Service>();
            var validateResults = commonInvoiceService.TryValidateObject(importSyntheticInvoiceModel);
            foreach (var validateResult in validateResults)
            {
                var result = new ValidationFailure(string.Empty, validateResult.ErrorMessage);
                results.Add(result);
            }
            return results;
        }

        private async Task<ValidationFailure> ValidatorErpId(ImportSyntheticInvoiceModel importSyntheticInvoiceModel)
        {
            var result = new ValidationFailure(string.Empty, string.Empty);

            if (await (_invoice01Service.IsDuplicateErpId(importSyntheticInvoiceModel.ErpId)))
            {
                result.ErrorMessage = _localizier["Vnis.BE.VCB.Invoice01.ErpIdDuplicate", new string[] { importSyntheticInvoiceModel.RowNumber.ToString() }];
                return result;
            }

            return result;
        }


        private async Task<Invoice01HeaderEntity> GetInvoice01HeaderAsync(ImportSyntheticInvoiceModel importSyntheticInvoiceModel)
        {
            DateTime.TryParseExact(importSyntheticInvoiceModel.OldInvoiceDate, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime oldInvoiceDate);
            var invoiceHeader = _invoice01HeaderEntities.FirstOrDefault(p => p.TenantId == _currentTenant.Id.Value
                && p.InvoiceNo == importSyntheticInvoiceModel.OldInvoiceNo
                && p.TemplateNo == importSyntheticInvoiceModel.OldTemplateNo
                && p.SerialNo == importSyntheticInvoiceModel.OldSerialNo
                && p.InvoiceDate == oldInvoiceDate);
            if (invoiceHeader == null)
            {
                invoiceHeader = await _importSyntheticCommonService.GetInvoice01HeaderAsync(importSyntheticInvoiceModel);
                _invoice01HeaderEntities.Add(invoiceHeader);
            }
            return invoiceHeader;
        }
        private async Task<IEnumerable<Invoice01DetailEntity>> GetInvoice01DetailsAsync(Invoice01HeaderEntity invoiceHeader)
        {
            var invoiceDetails = _invoice01DetailEntities.Where(p => p.InvoiceHeaderId == invoiceHeader.Id);
            if (!invoiceDetails.Any())
            {
                invoiceDetails = await _appFactory.Repository<Invoice01DetailEntity, long>()
                    .Where(p => p.InvoiceHeaderId == invoiceHeader.Id)
                    .OrderBy(p => p.Id)
                    .ToListAsync();
                _invoice01DetailEntities.AddRange(invoiceDetails);
            }
            return invoiceDetails;
        }

        public async Task<List<ResponseSyntheticInvoiceModel>> CreateImportSyntheticInvoices(IEnumerable<ImportSyntheticInvoiceModel> importSyntheticInvoiceModels)
        {
            var results = new List<ResponseSyntheticInvoiceModel>();

            var groupingImportSyntheticInvoiceModels = importSyntheticInvoiceModels.GroupBy(p => new { p.GroupId, p.InvoiceType, p.OldTemplateNo, p.OldSerialNo, p.OldInvoiceNo });

            var counter = groupingImportSyntheticInvoiceModels.Count();
            _headerIds = await _invoice01Service.GetSEQsNextVal(counter, SEQ_Name.SEQ_Invoice01Header);
            for (int index = 0; index < counter; index++)
            {
                var groupingImportSyntheticInvoiceModel = groupingImportSyntheticInvoiceModels.ElementAt(index);
                var enumerableImportSyntheticInvoiceModel = groupingImportSyntheticInvoiceModel.AsEnumerable();
                var firstEnumerableImportSyntheticInvoiceModel = enumerableImportSyntheticInvoiceModel.First();

                // headerId
                firstEnumerableImportSyntheticInvoiceModel.HeaderId = _headerIds.ElementAt(index);

                // ErpId
                var resultOfValidatorErpId = await ValidatorErpId(firstEnumerableImportSyntheticInvoiceModel);
                if (!string.IsNullOrWhiteSpace(resultOfValidatorErpId.ErrorMessage))
                {
                    throw new UserFriendlyException(resultOfValidatorErpId.ErrorMessage);
                }

                // validate invoice
                var result = await CreateImportSyntheticInvoiceByInvoiceTypeAsync(enumerableImportSyntheticInvoiceModel);
                results.Add(result);

            }
            return results;
        }
        public async Task<ResponseSyntheticInvoiceModel> CreateImportSyntheticInvoiceByInvoiceTypeAsync(IEnumerable<ImportSyntheticInvoiceModel> importSyntheticInvoiceModels)
        {
            var importSyntheticInvoiceModel = importSyntheticInvoiceModels.First();
            var invoiceType = importSyntheticInvoiceModel.InvoiceType;
            var result = _appFactory.ObjectMapper.Map<ImportSyntheticInvoiceModel, ResponseSyntheticInvoiceModel>(importSyntheticInvoiceModel);
            try
            {
                var invoiceHeader = await GetInvoice01HeaderAsync(importSyntheticInvoiceModel);
                var invoiceDetails = await GetInvoice01DetailsAsync(invoiceHeader);

                // TODO: Validation invoiceType
                await _importSyntheticCommonService.ValidationInvoiceType(invoiceType, invoiceHeader);

                switch (invoiceType)
                {
                    case (short)ImportSyntheticInvoiceEnum.Replace:
                        var replaceInvoice = _importReplaceInvoiceService.CreateReplaceInvoice01Dto(importSyntheticInvoiceModels, invoiceHeader);
                        var validationResultReplaceInvoice = await _importReplaceInvoiceService.ValidatorInvoice(replaceInvoice);
                        if (!validationResultReplaceInvoice.Success)
                            result.ErrorMessage = validationResultReplaceInvoice.Message;

                        break;
                    case (short)ImportSyntheticInvoiceEnum.AdjustmentHeader:
                        var adjustmentHeaderInvoice = _importAdjustmentHeaderInvoiceService.CreateAdjustmentHeaderInvoice01HeaderDto(importSyntheticInvoiceModels, invoiceHeader, invoiceDetails);
                        var validationResultAdjustmentHeaderInvoice = await _importAdjustmentHeaderInvoiceService.ValidatorInvoice(adjustmentHeaderInvoice);
                        if (!validationResultAdjustmentHeaderInvoice.Success)
                            result.ErrorMessage = validationResultAdjustmentHeaderInvoice.Message;
                        break;

                    case (short)ImportSyntheticInvoiceEnum.AdjustmentDetail:
                        var adjustmentDetailInvoice = _importAdjustmentDetailInvoiceService.CreateInvoice01AdjustmentDetailRequest(importSyntheticInvoiceModels, invoiceHeader);
                        var validationResultAdjustmentDetailInvoice = await _importAdjustmentDetailInvoiceService.ValidatorInvoice(adjustmentDetailInvoice);
                        if (!validationResultAdjustmentDetailInvoice.Success)
                            result.ErrorMessage = validationResultAdjustmentDetailInvoice.Message;
                        break;
                    default:
                        result.ErrorMessage = _localizier["Vnis.BE.Invoice01.ImportSyntheticInvoice.ValidateInvoiceType"];
                        break;
                }
                var errorMesssages = !string.IsNullOrEmpty(result.ErrorMessage) ?
                    result.ErrorMessage.Split(';').ToList()
                    : new List<string>();

                if (errorMesssages.Any())
                {
                    var localizierExceptions = errorMesssages.Select(p => _localizierException[p.TrimStart().TrimEnd()]);
                    result.ErrorMessage = string.Join("; ", localizierExceptions.Select(p => _localizier[p.Value]));
                }
                return result;

            }
            catch (Exception ex)
            {
                result.ErrorMessage = _localizier[_localizierException[ex.Message].Value];
                return result;
            }

        }

        public async Task<ValidationFailure> ValidatorAllDetails(List<ResponseSyntheticInvoiceModel> responseSyntheticInvoiceModels, ImportSyntheticInvoiceModel importSyntheticInvoiceModel)
        {
            var result = new ValidationFailure(string.Empty, string.Empty);

            if (importSyntheticInvoiceModel.InvoiceType != (short)ImportSyntheticInvoiceEnum.AdjustmentDetail)
            {
                return result;
            }

            var invoiceHeader = await GetInvoice01HeaderAsync(importSyntheticInvoiceModel);
            var rootId = await _commonInvoice01Service.RecursiveGetRootId(invoiceHeader.Id);
            var ids = (await _commonInvoice01Service.RecursiveGetIds(rootId)).Distinct();
            Log.Information($"----------------> ValidatorAllDetails - ids {JsonConvert.SerializeObject(ids)}");

            var _invoiceHeaders = await _appFactory.Repository<Invoice01HeaderEntity, long>()
                .GetListAsync(p => ids.Contains(p.Id)
                    && (p.InvoiceStatus == (short)InvoiceStatus.Goc || p.InvoiceStatus == (short)InvoiceStatus.DieuChinhTangGiam)
                );

            var _invoiceHeaderIds = _invoiceHeaders.Select(p => p.Id);
            Log.Information($"----------------> ValidatorAllDetails - _invoiceHeaderIds {JsonConvert.SerializeObject(_invoiceHeaderIds)}");

            var _invoice01Details = await _appFactory.Repository<Invoice01DetailEntity, long>()
                .GetListAsync(p => _invoiceHeaderIds.Contains(p.InvoiceHeaderId));

            var invoice01Details = _invoice01Details
                .Where(item => item.Index == importSyntheticInvoiceModel.ProductIndex);
            Log.Information($"----------------> ValidatorAllDetails - invoice01Details {JsonConvert.SerializeObject(invoice01Details)}");

            var totalInvoice01Details = new
            {
                TotalQuantity = invoice01Details.Sum(p => p.Quantity),
                TotalAmount = invoice01Details.Sum(p => p.Amount),
                TotalVatAmount = invoice01Details.Sum(p => p.VatAmount),
                TotalUnitPrice = invoice01Details.Sum(p => p.UnitPrice),
                TotalPaymentAmount = invoice01Details.Sum(p => p.PaymentAmount)
            };
            Log.Information($"----------------> ValidatorAllDetails - invoice01Details {JsonConvert.SerializeObject(totalInvoice01Details)}");

            var rsis = responseSyntheticInvoiceModels
                    .Where(p =>
                        p.InvoiceType == importSyntheticInvoiceModel.InvoiceType
                        && p.OldInvoiceNo == importSyntheticInvoiceModel.OldInvoiceNo
                        && p.OldTemplateNo == importSyntheticInvoiceModel.OldTemplateNo
                        && p.OldSerialNo == importSyntheticInvoiceModel.OldSerialNo
                        && p.ProductIndex == importSyntheticInvoiceModel.ProductIndex
                    );

            var totalSyntheticInvoiceModel = new
            {
                TotalQuantity = importSyntheticInvoiceModel.Quantity + rsis.Sum(p => p.Quantity),
                TotalAmount = importSyntheticInvoiceModel.Amount + rsis.Sum(p => p.Amount),
                TotalVatAmount = importSyntheticInvoiceModel.VatAmount + rsis.Sum(p => p.VatAmount),
                TotalUnitPrice = importSyntheticInvoiceModel.UnitPrice + rsis.Sum(p => p.UnitPrice),
                TotalPaymentAmount = importSyntheticInvoiceModel.PaymentAmount + rsis.Sum(p => p.PaymentAmount),
                RowNumber = importSyntheticInvoiceModel.RowNumber
            };
            Log.Information($"----------------> ValidatorAllDetails - invoice01Details {JsonConvert.SerializeObject(totalSyntheticInvoiceModel)}");

            if (totalInvoice01Details.TotalQuantity + totalSyntheticInvoiceModel.TotalQuantity < 0
                || totalInvoice01Details.TotalAmount + totalSyntheticInvoiceModel.TotalAmount < 0
                || totalInvoice01Details.TotalVatAmount + totalSyntheticInvoiceModel.TotalVatAmount < 0
                || totalInvoice01Details.TotalUnitPrice + totalSyntheticInvoiceModel.TotalUnitPrice < 0
                || totalInvoice01Details.TotalPaymentAmount + totalSyntheticInvoiceModel.TotalPaymentAmount < 0

            )
            {
                result.ErrorMessage = _localizier["Vnis.BE.Invoice01.ImportSyntheticInvoice.ValidatorAllDetails", new string[] { totalSyntheticInvoiceModel.RowNumber.ToString() }];
                return result;
            }

            return result;
        }

        public ValidationFailure ValidateTax(ImportSyntheticInvoiceModel importSyntheticInvoiceModel)
        {
            var result = new ValidationFailure(string.Empty, string.Empty);

            foreach (int tax in Enum.GetValues(typeof(TaxType)))
            {
                if (tax == importSyntheticInvoiceModel.VatPercent)
                {
                    return result;
                }
            }
            result.ErrorMessage = _localizier["Vnis.BE.Invoice01.ImportSyntheticInvoice.ValidateTax", new string[] { importSyntheticInvoiceModel.RowNumber.ToString() }];
            return result;

        }

        #endregion ValidateImportSynthetic

        #region CreateSyntheticInvoices 
        public async Task<List<dynamic>> CreateSyntheticInvoices(IEnumerable<ImportSyntheticInvoiceModel> importSyntheticInvoiceModels)
        {
            var results = new List<dynamic>();

            var groupingImportSyntheticInvoiceModels = importSyntheticInvoiceModels.GroupBy(p => new { p.GroupId, p.InvoiceType, p.OldTemplateNo, p.OldSerialNo, p.OldInvoiceNo });
            var counter = groupingImportSyntheticInvoiceModels.Count();
            for (int index = 0; index < counter; index++)
            {
                var groupingImportSyntheticInvoiceModel = groupingImportSyntheticInvoiceModels.ElementAt(index);
                var enumerableImportSyntheticInvoiceModel = groupingImportSyntheticInvoiceModel.AsEnumerable();
                var firstEnumerableImportSyntheticInvoiceModel = enumerableImportSyntheticInvoiceModel.First();

                // headerId
                firstEnumerableImportSyntheticInvoiceModel.HeaderId = _headerIds.ElementAt(index);

                // create invoice
                var result = await CreateSyntheticInvocieByInvoiceTypeAsync(enumerableImportSyntheticInvoiceModel);
                results.Add(result);

            }
            return results;
        }
        public async Task<dynamic> CreateSyntheticInvocieByInvoiceTypeAsync(IEnumerable<ImportSyntheticInvoiceModel> importSyntheticInvoiceModels)
        {
            var importSyntheticInvoiceModel = importSyntheticInvoiceModels.First();
            var invoiceType = importSyntheticInvoiceModel.InvoiceType;

            var invoiceHeader = await GetInvoice01HeaderAsync(importSyntheticInvoiceModel);
            var invoiceDetails = await GetInvoice01DetailsAsync(invoiceHeader);

            switch (invoiceType)
            {
                case (short)ImportSyntheticInvoiceEnum.Replace:
                    _listReplaceId.Add(invoiceHeader.Id);
                    var dt = await _importReplaceInvoiceService.CreateReplaceInvoice01HeaderEventSendData(importSyntheticInvoiceModels, invoiceHeader);
                    dt.Ticks = _ticks;
                    return dt;

                case (short)ImportSyntheticInvoiceEnum.AdjustmentHeader:
                    return await _importAdjustmentHeaderInvoiceService.CreateAdjustmentHeaderInvoice01EventSendData(importSyntheticInvoiceModels, invoiceHeader, invoiceDetails);

                case (short)ImportSyntheticInvoiceEnum.AdjustmentDetail:
                    return await _importAdjustmentDetailInvoiceService.CreateAdjustmentDetailInvoice01HeaderEventSendData(importSyntheticInvoiceModels, invoiceHeader);

                default:
                    throw new UserFriendlyException(_localizier["Vnis.BE.Invoice01.ImportSyntheticInvoice.ValidateInvoiceType", new string[] { invoiceType.ToString() }]);
            }
        }
        public async Task AddSyntheticInvocieToLockAsync()
        {
            foreach (var item in _listReplaceId)
            {
                await _invoice01LockService.AddInvoiceInprocessAsync(item, _ticks);
            }
        }
        public async Task RemoveSyntheticInvocieToLockAsync()
        {
            foreach (var item in _listReplaceId)
            {
                await _invoice01LockService.RemoveInvoiceInprocessAsync(item);
            }
        }
        #endregion CreateSyntheticInvoices
    }
}