using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Vcb.Invoice01.Application.Invoice01.Dto;
using Vcb.Invoice01.Application.Invoice01.RabbitMqEventBus.ImportExcelInvoiceModify.MessageEventData;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;

namespace Vcb.Invoice01.Application.Invoice01.Services
{
    public interface ICreateImportExcelInvoiceModifyService : ICreateBaseInvoice01EntityService
    {
        /// <summary>
        /// Tạo một List Tuple bao gồm:
        /// - Invoice01HeaderEntity
        /// - Danh sách Invoice01DetailEntity
        /// - Danh sách Invoice01TaxBreakdownEntity
        /// - Danh sách Invoice01ReferenceEntity
        /// - Danh sách Invoice01ErpIdEntity
        /// - Danh sách Invoice01HeaderExtraPropertyEntity
        /// - Danh sách Invoice01DetailExtraPropertyEntity
        /// </summary>
        /// <param name="createBaseInvoice01EventSendData"></param>
        /// <returns></returns>
        Task<List<Tuple<Invoice01HeaderEntity, List<Invoice01DetailEntity>, List<Invoice01TaxBreakdownEntity>, List<Invoice01ReferenceEntity>, List<Invoice01ErpIdEntity>, List<Invoice01HeaderExtraPropertyEntity>, List<Invoice01DetailExtraPropertyEntity>>>> CreateListTupleInvoiceModifiesAsync(ImportExcelInvoiceModifyEventSendData createBaseInvoice01EventSendData);
    }
}
