using Core.Shared.Invoice.Interfaces;
using System;

namespace Vcb.Invoice01.Application.Invoice01.Services
{
    public class ImportExcelModifyFactoryService : IImportExcelAbstractFactory

    {
        private readonly IServiceProvider _serviceProvider;

        public ImportExcelModifyFactoryService(
            IServiceProvider serviceProvider
        )
        {
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// Tạo ImportExcelAbstractFile
        /// </summary>
        /// <returns></returns>
        public IImportExcelAbstractProcessorFile CreateImportExcelAbstractProcessorFile(IServiceProvider serviceProvider)
        {
            return  new ImportExcelModifyProcessorFileService(serviceProvider);
        }


        /// <summary>
        /// Tạo IImportExcelAbstractConvertData
        /// </summary>
        /// <typeparam name=""></typeparam>
        /// <returns></returns>
        public IImportExcelAbstractProcessorData CreateImportExcelAbstractProcessorData()
        {
            return new ImportExcelModifyProcessorDataService(_serviceProvider);
        }


        /// <summary>
        /// Publish danh sách hóa đơn vào queue
        /// Trong quá trình Publish khuyến nghị nên phân trang
        /// </summary>
        /// <returns></returns>
        public IImportExcelAbstractPublish CrateImportExcelAbstractPublish()
        {
            return new ImportExcelModifyPublishService(_serviceProvider);
        }


    }
}
