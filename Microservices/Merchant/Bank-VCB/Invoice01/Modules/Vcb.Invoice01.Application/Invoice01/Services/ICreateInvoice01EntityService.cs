using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Vcb.Invoice01.Application.Invoice01.Dto;
using Vcb.Invoice01.Application.Invoice01.RabbitMqEventBus.ImportExcel.MessageEventData;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;

namespace Vcb.Invoice01.Application.Invoice01.Services
{
    public interface ICreateInvoice01EntityService
    {
        /// <summary>
        /// Tạo một Tuple bao gồm:
        /// - Danh sách Invoice01HeaderEntity
        /// - Danh sách Invoice01DetailEntity
        /// - Danh sách Invoice01TaxBreakdownEntity
        /// - Danh sách Invoice01ErpIdEntity
        /// - Danh sách Invoice01HeaderExtraPropertyDto
        /// - Danh sách Invoice01DetailExtraPropertyDto
        /// </summary>
        /// <param name="importExcelEventSendData"></param>
        /// <returns></returns>
        Task<Tuple<List<Invoice01HeaderEntity>, List<Invoice01DetailEntity>, List<Invoice01TaxBreakdownEntity>, List<Invoice01ErpIdEntity>, List<Invoice01HeaderExtraPropertyEntity>, List<Invoice01DetailExtraPropertyEntity>>> CreateTupleInvoicesAsync(ImportExcelEventSendData importExcelEventSendData);
    }
}
