using Core.Shared.Extensions;
using Core.Shared.Invoice.Dtos;
using OfficeOpenXml;
using System;

namespace Vcb.Invoice01.Application.Invoice01.Dto
{
    public class ImportExcelModifyDto : ImportExcelBaseDto
    {
        /// <summary>
        /// Loại điều chỉnh *
        /// 1 - Điều chỉnh định danh
        /// 2 - Điều chỉnh tăng/giảm
        /// 3 - Thay thế
        /// </summary>
        public short? InvoiceType
        {
            get
            {
                if (string.IsNullOrWhiteSpace(_InvoiceType))
                {
                    return null;
                }

                if (!short.TryParse(_InvoiceType, out short result))
                {
                    return null;
                }
                return result;
            }
            set
            {
                _InvoiceType = value.HasValue ? value.ToString() : null;
            }
        }

        /// <summary>
        /// Loại điều chỉnh
        /// 1 - Điều chỉnh định danh
        /// 2 - Điều chỉnh tăng/giảm
        /// 3 - Thay thế
        /// </summary>
        public string _InvoiceType;


        /// <summary>
        /// Số hóa đơn liên quan
        /// </summary>
        public string InvoiceNoReference { get; set; }

        public int NumberReference
        {
            get
            {
                if (string.IsNullOrWhiteSpace(InvoiceNoReference))
                {
                    return default(int);
                }

                if (!int.TryParse(InvoiceNoReference, out int result))
                {
                    return default(int);
                }
                return result;
            }
            set
            {
                InvoiceNoReference = value.ToString();
            }
        }

        /// <summary>
        /// Mẫu số của hóa đơn liên quan
        /// </summary>

        public short? TemplateNoReference
        {
            get
            {
                if (string.IsNullOrWhiteSpace(_TemplateNoReference))
                {
                    return null;
                }

                if (!short.TryParse(_TemplateNoReference, out short result))
                {
                    return null;
                }
                return result;
            }
            set
            {
                _TemplateNoReference = value.HasValue ? value.ToString() : null;
            }
        }

        /// <summary>
        /// Mẫu số của hóa đơn liên quan
        /// </summary>
        public string _TemplateNoReference;

        /// <summary>
        /// Ký hiệu hóa đơn liên quan
        /// </summary>
        public string SerialNoReference { get; set; }


        /// <summary>
        /// Ngày hóa đơn liên quan
        /// </summary>
        public virtual DateTime? InvoiceDateReference
        {
            get
            {
                return _InvoiceDateReference.TryParseExactDDDashMMDashYYYY();
            }
            set
            {
                this._InvoiceDateReference = value.HasValue ? value.Value.ToString("dd/MM/yyyy") : null;
            }
        }

        /// <summary>
        /// Ngày hóa đơn liên quan
        /// </summary>
        public string _InvoiceDateReference { get; set; }


        /// <summary>
        /// Ghi chú
        /// </summary>
        public string Note { get; set; }

        /// <summary>
        /// STT hàng hóa
        /// </summary>
        public int ProductIndex
        {
            get
            {
                if (string.IsNullOrWhiteSpace(_ProductIndex))
                {
                    return 0;
                }

                if (!int.TryParse(_ProductIndex, out int result))
                {
                    return 0;
                }
                return result;
            }
            set
            {
                _ProductIndex = value.ToString();
            }
        }

        /// <summary>
        /// STT hàng hóa
        /// </summary>
        public string _ProductIndex;



        /// <summary>
        /// Ghi chú trong detail
        /// </summary>
        public string DetailNote { get; set; }


        /// <summary>
        /// Mã phòng ban
        /// </summary>
        public virtual string DepartmentCode { get; set; }


        /// <summary>
        /// ErpId
        /// </summary>
        public virtual string ErpId { get; set; }

        public ImportExcelModifyDto(
            ExcelRange cells,
            int row
        ) : base(row)
        {
            GroupId = cells[row, 1].GetValue<string>()?.Trim();
            _InvoiceType = cells[row, 2].GetValue<string>()?.Trim();
            PCTime = cells[row, 3].GetValue<string>()?.Trim();
            _InvoiceDate = cells[row, 4].GetValue<string>()?.Trim();
            InvoiceNoReference = cells[row, 5].GetValue<string>()?.Trim();
            var templateNoReferenceSerialNoReference = cells[row, 6].GetValue<string>()?.Trim();
            _TemplateNoReference = !string.IsNullOrWhiteSpace(templateNoReferenceSerialNoReference)
                ? templateNoReferenceSerialNoReference.Substring(0, 1)
                : string.Empty;
            SerialNoReference = !string.IsNullOrWhiteSpace(templateNoReferenceSerialNoReference)
                ? templateNoReferenceSerialNoReference.Substring(1)
                : string.Empty;
            _InvoiceDateReference = cells[row, 7].GetValue<string>()?.Trim();
            CreatorErp = cells[row, 8].GetValue<string>()?.Trim();
            TellSeq = cells[row, 9].GetValue<string>()?.Trim();
            PaymentMethod = cells[row, 10].GetValue<string>()?.Trim();
            Currency = cells[row, 11].GetValue<string>()?.Trim();
            _ExchangeRate = cells[row, 12].GetValue<string>()?.Trim()?.Replace(",", ".");
            Note = cells[row, 13].GetValue<string>()?.Trim();

            // Buyer
            BuyerCode = cells[row, 14].GetValue<string>()?.Trim();
            BuyerFullName = cells[row, 15].GetValue<string>()?.Trim();
            BuyerTaxCode = cells[row, 16].GetValue<string>()?.Trim();

            BudgetUnitCode = cells[row, 17].GetValue<string>()?.Trim();
            BuyerIDNumber = cells[row, 18].GetValue<string>()?.Trim();
            BuyerPassportNumber = cells[row, 19].GetValue<string>()?.Trim();


            BuyerAddressLine = cells[row, 20].GetValue<string>()?.Trim();
            BuyerBankAccount = cells[row, 21].GetValue<string>()?.Trim();
            BuyerBankName = cells[row, 22].GetValue<string>()?.Trim();
            BuyerEmail = cells[row, 23].GetValue<string>()?.Trim();
            BuyerType = cells[row, 24].GetValue<string>()?.Trim();

            // Detail
            _ProductIndex = cells[row, 25].GetValue<string>()?.Trim();
            ProductName = cells[row, 26].GetValue<string>()?.Trim();
            UnitName = cells[row, 27].GetValue<string>()?.Trim();
            _Quantity = cells[row, 28].GetValue<string>()?.Trim()?.Replace(",", ".");
            _UnitPrice = cells[row, 29].GetValue<string>()?.Trim()?.Replace(",", ".");
            _Amount = cells[row, 30].GetValue<string>()?.Trim()?.Replace(",", ".");
            _VatPercent = cells[row, 31].GetValue<string>()?.Trim()?.Replace(",", ".");
            _VatAmount = cells[row, 32].GetValue<string>()?.Trim()?.Replace(",", ".");
            _PaymentAmount = cells[row, 33].GetValue<string>()?.Trim()?.Replace(",", ".");
            DetailNote = cells[row, 34].GetValue<string>()?.Trim();
        }
    }
}
