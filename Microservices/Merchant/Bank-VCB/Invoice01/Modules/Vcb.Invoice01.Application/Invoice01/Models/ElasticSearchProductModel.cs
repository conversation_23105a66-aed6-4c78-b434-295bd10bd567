using System;

namespace VnisCore.Invoice01.Application.Invoice01.Models
{
    public class ElasticSearchProductModel
    {
        public long Id { get; set; }

        public string ErpId { get; set; }

        public string ProductCode { get; set; }

        public string Name { get; set; }

        public string NormalizedName { get; set; }

        public decimal Price { get; set; }

        public decimal TaxValue { get; set; }

        public bool IsDeleted { get; set; }

        public long? ProductTypeId { get; set; }

        public string ProductTypeName { get; set; }

        public long? UnitId { get; set; }

        public string UnitName { get; set; }

        public Guid TenantId { get; set; }

        public bool? HideQuantity { get; set; }

        public bool? HideUnit { get; set; }

        public bool? HideUnitPrice { get; set; }

    }
}