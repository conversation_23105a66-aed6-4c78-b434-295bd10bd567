using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace VnisCore.Invoice01.Application.Invoice01.Models.Responses.Queries
{
    public class PagingProductResponse
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public string ProductCode { get; set; }
        public long? UnitId { get; set; }
        public decimal Price { get; set; }
        public decimal TaxValue { get; set; }
        public bool? HideQuantity { get; set; }
        public bool? HideUnit { get; set; }
        public bool? HideUnitPrice { get; set; }

        public long TotalItems { get; set; }
    }
}
