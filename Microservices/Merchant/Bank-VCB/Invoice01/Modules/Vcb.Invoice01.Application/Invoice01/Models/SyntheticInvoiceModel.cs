using System;
using System.Collections.Generic;
using VnisCore.Invoice01.Application.Invoice01.Models;

namespace Vcb.Invoice01.Application.Invoice01.Models
{
    public class SyntheticInvoiceModel : ImportSyntheticInvoiceModel
    {
        public int Id { get; set; }
        public string ErpId { get; set; }
        public string TransactionId { get; set; }
        public string BuyerCode { get; set; }
        public string BuyerLegalName { get; set; }
        public string BuyerDistrictName { get; set; }
        public string BuyerCityName { get; set; }
        public string BuyerCountryCode { get; set; }
        public string BuyerPhoneNumber { get; set; }
        public string BuyerFaxNumber { get; set; }
        public string DepartmentCode { get; set; }
        public decimal TotalDiscountAmountBeforeTax { get; set; }
        public decimal TotalDiscountPercentAfterTax { get; set; }
        public decimal TotalDiscountAmountAfterTax { get; set; }
        public Dictionary<string, string> InvoiceHeaderExtra { get; set; } // Dữ liệu khác, ko đưa dc vào field nào nhưng cần lưu lại HeaderExtra
        public Dictionary<string, string> MetadataDetailExtra { get; set; } // Dữ liệu khác, ko đưa dc vào field nào nhưng cần lưu lại DetailExtra


        //public string MerNo { get; set; }

        //public string BidNo { get; set; }

        //public string AppName { get; set; }

        //public string TransactionAmount { get; set; }

        //public string OperationName { get; set; }

        //public string RefNo { get; set; }

        //public string Note2 { get; set; }

        //public string GlNo { get; set; }
    }
}
