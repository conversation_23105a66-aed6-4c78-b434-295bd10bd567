using Core.Shared.Constants;
using MediatR;
using System;
using System.Collections.Generic;
using VnisCore.Invoice01.Application.Invoice01.Models.Responses.Queries;

namespace VnisCore.Invoice01.Application.Invoice01.Models.Requests.Queries
{
    public class GetReadTemplateRequestModel : IRequest<List<GetReadTemplateResponseModel>>
    {
        public Guid TenantId { get; set; }
        public Guid UserId { get; set; }
        public VnisType InvoiceType { get; set; }
    }
}
