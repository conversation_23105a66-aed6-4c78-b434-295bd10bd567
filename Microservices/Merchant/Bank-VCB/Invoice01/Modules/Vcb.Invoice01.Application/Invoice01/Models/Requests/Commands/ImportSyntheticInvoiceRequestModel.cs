using MediatR;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace VnisCore.Invoice01.Application.Invoice01.Models.Requests.Commands
{
    public class ImportSyntheticInvoiceRequestModel : IRequest<List<ResponseSyntheticInvoiceModel>>
    {
        public IFormFile File { get; set; }
        [JsonIgnore]
        public string Conditions { get; set; }

        public short TemplateNo { get; set; }
        public string SerialNo { get; set; }
        public bool IsValidate { get; set; }

    }
}
