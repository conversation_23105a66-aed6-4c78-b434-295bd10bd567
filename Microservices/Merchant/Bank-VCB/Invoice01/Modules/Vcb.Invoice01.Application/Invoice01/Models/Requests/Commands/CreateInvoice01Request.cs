using Core.Shared.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace VnisCore.Invoice01.Application.Invoice01.Models.Requests.Commands
{
    public class CreateInvoice01Request
    {
        #region Thông tin chung
        
        /// <summary>
        /// Mẫu số của hóa đơn
        /// </summary>
        [Required(ErrorMessage = "Mẫu số hóa đơn không được để trống")]
        //[TemplateNo(ErrorMessage = "Định dạng mẫu số hóa đơn không đúng")]
        public short TemplateNo { get; set; }

        /// <summary>
        /// Ký hiệu hóa đơn
        /// </summary>
        [Required(ErrorMessage = "Ký hiệu hóa đơn không được để trống")]
        [SerialNo(ErrorMessage = "Ký hiệu hóa đơn không bao gồm các ký tự O, J, Z, W và phần số gồm 2 chữ số")]
        public string SerialNo { get; set; }

        [StringLength(50, ErrorMessage = "Id bản ghi hóa đơn chỉ được nhập tối đa 50 ký tự")]
        public string ErpId { get; set; }

        [StringLength(250, ErrorMessage = "Tài khoản người tạo hóa đơn chỉ được nhập tối đa 250 ký tự")]
        public string CreatorErp { get; set; }

        [StringLength(50, ErrorMessage = "Mã đơn hàng chỉ được nhập tối đa 50 ký tự")]
        public string TransactionId { get; set; }


        /// <summary>
        /// Ngày hóa đơn
        /// </summary>
        [DataType(DataType.DateTime)]
        [Required(ErrorMessage = "Ngày hóa đơn không được để trống")]
        [LessThanCurrentDate(ErrorMessage = "Phải nhỏ hơn ngày hiện tại")]
        public DateTime InvoiceDate { get; set; }

        /// <summary>
        /// Ghi chú
        /// </summary>
        [MaxLength(500, ErrorMessage = "Tối đa 500 ký tự")]
        public string Note { get; set; }
        #endregion

        #region Thông tin thanh toán
        /// <summary>
        /// Phương thức thanh toán 
        /// </summary>
        [Required(ErrorMessage = "Phương thức thanh toán không được để trống")]
        [StringLength(50, ErrorMessage = "Phương thức thanh toán chỉ được nhập tối đa 50 ký tự")]
        public string PaymentMethod { get; set; }

        /// <summary>
        /// Chuyến đến tiền tệ
        /// </summary>
        [Required(ErrorMessage = "Loại tiền tệ không được để trống")]
        [MaxLength(5, ErrorMessage = "Loại tiền tệ chỉ được nhập tối đa 5 ký tự")]
        public string Currency { get; set; }

        /// <summary>
        /// Tỷ giá
        /// </summary>
        [Required(ErrorMessage = "Tỷ giá chuyển đổi không được để trống")]
        [MoreThanValueAttribute("0", typeof(double), ErrorMessage = "Tỷ giá chuyển đổi phải lớn hơn 0")]
        public decimal ExchangeRate { get; set; }

        /// <summary>
        /// Tổng tiền hàng trước thuế
        /// </summary>
        //[Range(0, double.MaxValue, ErrorMessage = "Tổng tiền trước thuế phải lớn hơn hoặc bằng 0")]
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// Tổng tiền thuế
        /// </summary>
        //[Range(0, double.MaxValue, ErrorMessage = "Tiền thuế phải lớn hơn hoặc bằng 0")]
        public decimal TotalVatAmount { get; set; }

        /// <summary>
        /// Tổng tiền phải trả
        /// </summary>
        //[Range(0, double.MaxValue, ErrorMessage = "Tiền thanh toán phải lớn hơn hoặc bằng 0")]
        public decimal TotalPaymentAmount { get; set; }

        /// <summary>
        /// Tổng tiền chiết khấu trước thuế
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "Tổng tiền chiết khấu trước thuế phải lớn hơn hoặc bằng 0")]
        public decimal TotalDiscountAmountBeforeTax { get; set; }

        /// <summary>
        /// Tổng % chiết khấu sau thuế
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "Tổng % chiết khấu phải lớn hơn hoặc bằng 0")]
        public double TotalDiscountPercentAfterTax { get; set; }

        /// <summary>
        /// Tổng tiền chiết khấu sau thuế
        ///</summary>
        [Range(0, double.MaxValue, ErrorMessage = "Tổng tiền chiết khấu sau thuế phải lớn hơn hoặc bằng 0")]
        public decimal TotalDiscountAmountAfterTax { get; set; }
        #endregion

        #region Thông tin người mua
        /// <summary>
        /// Mã người mua
        /// </summary>
        [Required(ErrorMessage = "Số CIF không được để trống")]
        [MaxLength(50, ErrorMessage = "Tối đa 50 ký tự")]
        [CustomerId(ErrorMessage = "Số CIF viết không dấu, không chứa các ký tự đặc biệt ngoại trừ các ký tự .-_/@")]
        public string BuyerCode { get; set; }

        /// <summary>
        /// Email người mua
        /// </summary>
        [StringLength(500, ErrorMessage = "Email người mua chỉ được nhập tối đa 500 ký tự")]
        [Email(ErrorMessage = "Email người mua không đúng định dạng email")]
        public string BuyerEmail { get; set; }

        /// <summary>
        /// Họ tên người mua
        /// </summary>
        [Required(ErrorMessage = "Họ tên người mua không được để trống")]
        [StringLength(250, ErrorMessage = "Họ tên người mua chỉ được nhập tối đa 250 ký tự")]
        public string BuyerFullName { get; set; }

        /// <summary>
        /// Tên khách hàng
        /// </summary>
        [StringLength(250, ErrorMessage = "Tên khách hàng chỉ được nhập tối đa 250 ký tự")]
        public string BuyerLegalName { get; set; }

        /// <summary>
        /// Mã số thuế người mua
        /// </summary>
        [StringLength(50, ErrorMessage = "Mã số thuế người mua chỉ được nhập tối đa 50 ký tự")]
        [TaxCode(ErrorMessage = "Mã số thuế không đúng định dạng")]
        public string BuyerTaxCode { get; set; }

        /// <summary>
        /// Địa chỉ người mua
        /// </summary>
        [Required(ErrorMessage = "Địa chỉ người mua không được để trống")]
        [StringLength(500, ErrorMessage = "Địa chỉ người mua chỉ được nhập tối đa 500 ký tự")]
        public string BuyerAddressLine { get; set; }

        [StringLength(250, ErrorMessage = "Tên quận/huyện người mua chỉ được nhập tối đa 250 ký tự")]
        public string BuyerDistrictName { get; set; }

        [StringLength(250, ErrorMessage = "Tên tỉnh/thành phố người mua chỉ được nhập tối đa 250 ký tự")]
        public string BuyerCityName { get; set; }

        [StringLength(5, ErrorMessage = "Mã quốc gia người mua chỉ được nhập tối đa 5 ký tự")]
        public string BuyerCountryCode { get; set; }

        /// <summary>
        /// Số điện thoại người mua
        /// </summary>
        [StringLength(50, ErrorMessage = "Số điện thoại người mua chỉ được nhập tối đa 50 ký tự")]
        public string BuyerPhoneNumber { get; set; }

        [StringLength(50, ErrorMessage = "Số fax người mua chỉ được nhập tối đa 50 ký tự")]
        public string BuyerFaxNumber { get; set; }

        /// <summary>
        /// Số tài khoản người mua
        /// </summary>
        [StringLength(250, ErrorMessage = "Số tài khoản người mua chỉ được nhập tối đa 250 ký tự")]
        public string BuyerBankAccount { get; set; }

        /// <summary>
        /// Tên ngân hàng người mua
        /// </summary>
        [StringLength(250, ErrorMessage = "Tên ngân hàng người mua chỉ được nhập tối đa 250 ký tự")]
        public string BuyerBankName { get; set; }
        #endregion

        /// <summary>
        /// chi tiết hóa đơn
        /// </summary>
        [Required(ErrorMessage = "Chi tiết hóa đơn không được để trống")]
        [MinLength(1, ErrorMessage = "Chi tiết hóa đơn phải có ít nhất 1 chi tiết")]
        public List<CreateInvoice01DetailRequestModel> InvoiceDetails { get; set; }

        public List<CreateInvoice01HeaderExtraRequestModel> InvoiceHeaderExtras { get; set; }

        /// <summary>
        /// chi tiết thuế
        /// </summary>
        [Required(ErrorMessage = "Chi tiết thuế của hóa đơn")]
        [MinLength(1, ErrorMessage = "Chi tiết thuế phải có ít nhất 1 chi tiết")]
        public List<CreateInvoice01TaxBreakdownRequestModel> InvoiceTaxBreakdowns { get; set; }

        // additinal field
        public string ErrorMessages { get; set; }




        public class CreateInvoice01HeaderExtraRequestModel
        {
            /// <summary>
            /// Tên trường mở rộng của Header
            /// </summary>
            [Required(ErrorMessage = "Tên trường mở rộng củacủa Header không được để trống")]
            public string FieldName { get; set; }

            /// <summary>
            /// Giá trị của field mở rộng
            /// </summary>
            [MaxLength(2000, ErrorMessage = "Giá trị field mở rộng dài tối đa 2000 ký tự")]
            public string FieldValue { get; set; }
        }

        public class CreateInvoice01DetailExtraRequestModel
        {
            /// <summary>
            /// Tên trường mở rộng của Detail
            /// </summary>
            [Required(ErrorMessage = "Tên trường mở rộng của Detail không được để trống")]
            public string FieldName { get; set; }

            /// <summary>
            /// Giá trị của field mở rộng
            /// </summary>
            [MaxLength(2000, ErrorMessage = "Giá trị trường mở rộng dài tối đa 2000 ký tự")]
            public string FieldValue { get; set; }
        }

        public class CreateInvoice01DetailRequestModel
        {
            public int Index { get; set; }

            /// <summary>
            /// Chiết khấu
            /// </summary>
            [Range(0, double.MaxValue, ErrorMessage = "Chiết khấu phải lớn hơn hoặc bằng 0")]
            public decimal DiscountAmountBeforeTax { get; set; }

            /// <summary>
            /// Phần trăm chiết khấu
            /// </summary>
            [Range(0, double.MaxValue, ErrorMessage = "% Chiết khấu phải lớn hơn hoặc bằng 0")]
            public decimal DiscountPercentBeforeTax { get; set; }

            /// <summary>
            /// Tổng tiền sau thuế
            /// </summary>
            //[Range(0, double.MaxValue, ErrorMessage = "Tiền sau thuế phải lớn hơn hoặc bằng 0")]
            public decimal PaymentAmount { get; set; }

            /// <summary>
            /// Mã sản phẩm
            /// </summary>
            [MaxLength(50, ErrorMessage = "Mã vật tư/hàng hóa chỉ được nhập tối đa 50 ký tự")]
            public string ProductCode { get; set; }

            /// <summary>
            /// Tên sản phẩm
            /// </summary>
            [Required(ErrorMessage = "Tên vật tư/hàng hóa không được để trống")]
            [MaxLength(500, ErrorMessage = "Tên vật tư/hàng hóa chỉ được nhập tối đa 500 ký tự")]
            public string ProductName { get; set; }

            /// <summary>
            /// Tên đơn vị tính
            /// </summary>
            [Required(ErrorMessage = "Tên đơn vị tính không được để trống")]
            [MaxLength(50, ErrorMessage = "Tên đơn vị tính chỉ được nhập tối đa 50 ký tự")]
            public string UnitName { get; set; }

            /// <summary>
            /// Đơn giá
            /// </summary>
            [Range(0, double.MaxValue, ErrorMessage = "Đơn giá phải lớn hơn hoặc bằng 0")]
            public decimal UnitPrice { get; set; }

            /// <summary>
            /// Số lượng
            /// </summary>
            [Range(0, double.MaxValue, ErrorMessage = "Số lượng phải lớn hơn hoặc bằng 0")]
            public decimal Quantity { get; set; }

            /// <summary>
            /// Tổng tiền hàng
            /// </summary>
            //[Range(0, double.MaxValue, ErrorMessage = "Tiền hàng phải lớn hơn hoặc bằng 0")]
            public decimal Amount { get; set; }

            /// <summary>
            /// Phần trăm thuế (-2: Không kê khai, -1: Không chịu thuế, 0: 0%, 5: 5%, 10: 10%)
            /// </summary>
            public decimal VatPercent { get; set; }

            /// <summary>
            /// Tổng tiền thuế
            /// </summary>
            //[Range(0, double.MaxValue, ErrorMessage = "Tiền thuế phải lớn hơn hoặc bằng 0")]
            public decimal VatAmount { get; set; }

            /// <summary>
            /// Ghi chú
            /// </summary>
            [MaxLength(2000, ErrorMessage = "Nội dung chỉ được nhập tối đa 2000 ký tự")]
            public string Note { get; set; }

            public List<CreateInvoice01DetailExtraRequestModel> InvoiceDetailExtras { get; set; }
        }

        public class CreateInvoice01TaxBreakdownRequestModel
        {
            /// <summary>
            /// tien thue nguoi dung tinh
            /// </summary>
            public decimal VatAmount { get; set; }

            /// <summary>
            /// % chiết khấu
            /// </summary>
            public decimal VatPercent { get; set; }

            /// <summary>
            /// tieefn thue he thong tu tinh
            /// </summary>
            public decimal VatAmountBackUp { get; set; }
        }
    }
}
