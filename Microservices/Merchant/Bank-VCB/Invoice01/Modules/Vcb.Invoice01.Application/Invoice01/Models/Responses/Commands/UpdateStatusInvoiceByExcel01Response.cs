using System.Collections.Generic;

namespace VnisCore.Invoice01.Application.Invoice01.Models.Responses.Commands
{
    public class UpdateStatusInvoiceByExcel01Response
    {
        public List<InvoiceErrorMessage> ErrorMessages { get; set; }
        public UpdateStatusInvoiceByExcel01Response()
        {
            
        }
        public UpdateStatusInvoiceByExcel01Response(List<InvoiceErrorMessage> errorMessages)
        {
            this.ErrorMessages = errorMessages;
        }
    }

    public class InvoiceErrorMessage
    {
        public short TemplateNo { get; set; }

        public string SerialNo { get; set; }

        public string InvoiceNo { get; set; }

        public int CurrentStatus { get; set; }

        public int NewStatus { get; set; }

        public string ErrorMessage { get; set; }
    }
}
