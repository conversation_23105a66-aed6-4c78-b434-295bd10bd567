using Core.Application.Dtos;
using System;

namespace VnisCore.Invoice01.Application.Invoice01Document.Dto
{
    public class Invoice01DocumentInfoDto : EntityDto<long>
    {
        public new long Id { get; set; }

        public Guid TenantId { get; set; }

        public long Partition { get; set; }

        /// <summary>
        /// Field Code của bảng InvoiceHeader
        /// </summary>
        /// <value></value>
        public long InvoiceHeaderId { get; set; }

        /// <summary>
        /// Field Code của bảng InvoiceDocument
        /// </summary>
        /// <value></value>
        public long? FileId { get; set; }

        /// <summary>
        /// Tên file biên bản
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// Số biên bản
        /// </summary>
        public string DocumentNo { get; set; }

        /// <summary>
        /// Ngày biên bản
        /// </summary>
        public DateTime? DocumentDate { get; set; }

        /// <summary>
        /// L<PERSON> do biên bản
        /// </summary>
        public string DocumentReason { get; set; }

        /// <summary>
        /// loại biên bản
        /// </summary>
        public short? Type { get; set; }

        /// <summary>
        /// có phải file upload không
        /// </summary>
        public bool IsUploadFile { get; set; }
    }
}