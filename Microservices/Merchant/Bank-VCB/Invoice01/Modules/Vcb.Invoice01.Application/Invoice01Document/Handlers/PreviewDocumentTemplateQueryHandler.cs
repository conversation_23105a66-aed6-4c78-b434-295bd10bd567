using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Dto;
using Core.Shared.Factory;
using Core.Shared.Invoice.Repositories;
using MediatR;
using Microsoft.Extensions.Localization;
using System;
using System.Threading;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Invoice01.Application.Invoice01Document.Interfaces;
using VnisCore.Invoice01.Application.Invoice01Document.Models.Requests;
using VnisCore.Invoice01.Application.Invoice01Document.Models.Responses;

namespace VnisCore.Invoice01.Application.Invoice01Document.Handlers
{
    public class PreviewDocumentTemplateQueryHandler : IRequestHandler<PreviewDocumentTemplateRequestModel, FileDto>
    {
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly IInvoiceDocumentFactory _invoiceDocumentFactory;

        public PreviewDocumentTemplateQueryHandler(
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IInvoiceDocumentFactory invoiceDocumentFactory)
        {
            _appFactory = appFactory;
            _localizier = localizier;
            _invoiceDocumentFactory = invoiceDocumentFactory;
        }

        public async Task<FileDto> Handle(PreviewDocumentTemplateRequestModel request, CancellationToken cancellationToken)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;

            //lấy thông tin hóa đơn
            var repoInvoiceHeader = _appFactory.GetServiceDependency<IInvoiceHeaderRepository<Invoice01HeaderEntity>>();
            var invoiceHeader = await repoInvoiceHeader.GetByIdAsync(request.InvoiceHeaderId);

            if (invoiceHeader == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.Invoice01.DocumentInfo.InvoiceNotFound"]);

            var servicePdfDocument = _invoiceDocumentFactory.GetService(1);
            var result = await servicePdfDocument.GenerateDocumentTemplateAsync(invoiceHeader.Id, request.DocumentNo, request.DocumentDate, request.DocumentReason);

            return new FileDto
            {
                ContentType = ContentType.Pdf,
                FileBytes = result.Value,
                FileBase64 = Convert.ToBase64String(result.Value, 0, result.Value.Length),
                FileName = result.Key
            };
        }
    }
}
