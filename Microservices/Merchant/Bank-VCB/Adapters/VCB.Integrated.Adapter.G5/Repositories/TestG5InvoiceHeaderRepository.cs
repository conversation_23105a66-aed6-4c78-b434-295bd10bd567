using Core.Domain.Repositories;
using Core.Shared.Factory;
using CoreDbtg.Shared.Models.Invoices;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VCB.Integrated.Oracle.Domain.Entities.G5;
using static CoreDbtg.Shared.Constants.DbtgSharedConst;

namespace VCB.Integrated.Adapter.G5.Repositories
{
    public interface ITestG5InvoiceHeaderRepository : IBasicRepository<G5InvoiceHeaderEntity, long>
    {
        Task GetListG5Invoices(IEnumerable<string> grpInv, int invoiceDateNumber);
    }

    public class TestG5InvoiceHeaderRepository : BaseRepository<G5InvoiceHeaderEntity, long>, ITestG5InvoiceHeaderRepository
    {
        private readonly IAppFactory _appFactory;

        public TestG5InvoiceHeaderRepository(IAppFactory appFactory) : base(appFactory)
        {
            _appFactory = appFactory;
        }

        public async Task GetListG5Invoices(IEnumerable<string> grpInv, int invoiceDateNumber)
        {
            Log.Information(DateTime.Now.ToString());

            var _repo = _appFactory.Repository<G5InvoiceHeaderEntity, long>();

            var headers = new List<G5InvoiceHeaderEntity>();
            _repo.Where(x => x.CeationDateNumber == invoiceDateNumber && x.CreatorStatus == 1 && x.InvoiceStatus == 1 && grpInv.Contains(x.GrpInv))
                .Select(x => x)
                .AsParallel()
                .WithDegreeOfParallelism(3)
                .ForAll(
                           x => headers.Add(x)
                       );

            var _repoDetail = _appFactory.Repository<G5InvoiceDetailEntity, long>();

            var details = await _repoDetail.Join(_repo.Where(x => x.CeationDateNumber == invoiceDateNumber && x.CreatorStatus == 1 && x.InvoiceStatus == 1 && grpInv.Contains(x.GrpInv)),
                    d => d.InvoiceHeaderId,
                    h => h.ErpId,
                    (d, h) => d
                ).ToListAsync();

            Log.Information(DateTime.Now.ToString());
        }
    }
}
