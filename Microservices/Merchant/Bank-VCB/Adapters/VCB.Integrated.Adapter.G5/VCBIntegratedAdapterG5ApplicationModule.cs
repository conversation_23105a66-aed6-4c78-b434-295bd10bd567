using Core;
using Core.Application;
using Core.Auditing;
using Core.AuditLogging.MongoDB;
using Core.Autofac;
using Core.AutoMapper;
using Core.BackgroundWorkers;
using Core.Dapper;
using Core.EventBus.RabbitMq;
using Core.Modularity;
using Core.Shared;
using Core.Shared.DapperSqlTypeHandler;
using Core.Shared.Factory;
using CoreDbtg.Shared;
using Dapper;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using VCB.Integrated.Adapter.G5.Interfaces;
using VCB.Integrated.Adapter.G5.Repositories;
using VCB.Integrated.Adapter.G5.Services;
using VCB.Integrated.Adapter.G5.Workers;
using VCB.Integrated.Oracle.Application.Contracts;
using VCB.Integrated.Oracle.Domain;
using VCB.Integrated.Oracle.EntityFrameworkCore.EntityFrameworkCore;
using VnisCore.Core.Oracle.Application.Contracts;
using VnisCore.Core.Oracle.Domain;
using VnisCore.Core.Oracle.EntityFrameworkCore.EntityFrameworkCore;

namespace VCB.Integrated.Adapter.G5
{
    [DependsOn(
        typeof(AbpAutofacModule),
        typeof(AbpDddApplicationModule),

        typeof(VnisCoreOracleDomainModule),
        typeof(VnisCoreOracleModuleContractsModule),
        typeof(VnisCoreOracleEntityFrameworkCoreModule),
        typeof(VcbIntegratedOracleDomainModule),
        typeof(VcbIntegratedOracleModuleContractsModule),
        typeof(VcbIntegratedOracleEntityFrameworkCoreModule),

        typeof(AbpBackgroundWorkersModule),
        typeof(AbpAutoMapperModule),
        typeof(AbpDapperModule),
        typeof(CoreDbtgSharedModule),
        typeof(SharedModule),
        typeof(AbpEventBusRabbitMqModule),

        typeof(AbpAuditLoggingMongoDbModule)
     )]
    public class VCBIntegratedAdapterG5ApplicationModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            SqlMapper.RemoveTypeMap(typeof(Guid));
            SqlMapper.RemoveTypeMap(typeof(Guid?));
            SqlMapper.AddTypeHandler(typeof(Guid), new GuidTypeHandler());
            SqlMapper.AddTypeHandler(typeof(Dictionary<string, string>), new DictionaryTypeHandler());

            context.Services.AddScoped<IG5InvoiceHeaderRepository, G5InvoiceHeaderRepository>();
            context.Services.AddScoped<IG5InvoiceDetailRepository, G5InvoiceDetailRepository>();
            context.Services.AddScoped<IInvoiceG5Service, InvoiceG5Service>();
            //context.Services.AddScoped<ITestG5InvoiceHeaderRepository, TestG5InvoiceHeaderRepository>();

            context.Services.AddTransient(typeof(IAppFactory), typeof(AppFactory));

            Configure<AbpAuditingOptions>(options =>
            {
                options.IsEnabledForGetRequests = true;
                options.ApplicationName = "VCB.Integrated.Adapter.G5";
                options.EntityHistorySelectors.AddAllEntities();
            });
        }

        public override void OnApplicationInitialization(ApplicationInitializationContext context)
        {
            context.AddBackgroundWorker<CreateInvoiceWorker>();
        }
    }
}
