using Core.BackgroundWorkers;
using Core.Shared.Constants;
using Core.Threading;
using CoreDbtg.Shared.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Serilog;
using System;
using System.Linq;
using System.Threading.Tasks;
using VCB.Integrated.Adapter.G3.Interfaces;

namespace VCB.Integrated.Adapter.G3.Workers
{
    public class CreateInvoiceWorker : AsyncPeriodicBackgroundWorkerBase
    {
        public int CountTimes = 0;

        public CreateInvoiceWorker(AbpAsyncTimer timer, 
            IServiceScopeFactory serviceScopeFactory,
            IConfiguration configuration) : base(timer, serviceScopeFactory)
        {
            timer.Period = 1000;

            if (int.TryParse(configuration.GetSection("TimePeriod").Value, out int period))
                timer.Period = period * 1000;
        }

        protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
        {
            try
            {
                CountTimes++;

                var _settingDbtgService = workerContext.ServiceProvider.GetService<ISettingDbtgService>();
                var settings = await _settingDbtgService.GetG3Settings();
                if (settings == null || !settings.Any())
                {
                    Log.Information($"[Worker][Create]: Không có cấu hình G3");
                    return;
                }

                var pauseAdapterConst = SettingKey.PauseAdapterG3.ToString();
                if (settings.ContainsKey(pauseAdapterConst) && settings[pauseAdapterConst] == 1)
                {
                    Log.Information($"[Worker][Create]: Tạm dừng quét hóa đơn");
                    return;
                }

                var periodConst = SettingKey.PeriodG3.ToString();
                if (!settings.ContainsKey(periodConst))
                {
                    Log.Information($"[Worker][Create]: Không có cấu hình tần suất quét của G3");
                    return;
                }

                var period = settings[periodConst];
                if (period <= 0 || (period != 1 && (CountTimes < period)))
                {
                    Log.Information($"[Worker][Create]: Không có cấu hình G3 chạy ở thời điểm hiện tại");
                    return;
                }

                var service = workerContext.ServiceProvider.GetService<IInvoiceG3Service>();
                await service.CreateInvoiceAsync();

                CountTimes = 0;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message, ex.StackTrace);
                CountTimes = 0;
            }
        }
    }
}
