using Core.Shared.Extensions;
using CoreDbtg.Shared.Constants;
using CoreDbtg.Shared.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VCB.Integrated.Adapter.Validation.Abstractions;
using VCB.Integrated.Adapter.Validation.Interfaces;
using VCB.Integrated.Adapter.Validation.Models;
using VCB.Integrated.Adapter.Validation.Repositories;
using VCB.Integrated.Oracle.Domain.Entities.G5;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using static CoreDbtg.Shared.Constants.DbtgSharedConst;

namespace VCB.Integrated.Adapter.Validation.Services
{
    public class ValidateG5InvoiceService : BaseValidateService<G5InvoiceHeaderEntity, G5InvoiceDetailEntity>, IValidateService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IInvoiceHeaderRepository<G5InvoiceHeaderEntity, G5InvoiceDetailEntity> _repoInvoiceHeader;
        private readonly IInvoice01HeaderRepository _repoInvoice01HeaderRepository;

        public ValidateG5InvoiceService(
            IServiceProvider serviceProvider,
            IConfiguration configuration, 
            IInvoiceHeaderRepository<G5InvoiceHeaderEntity, G5InvoiceDetailEntity> repoInvoiceHeader, 
            IInvoice01HeaderRepository repoInvoice01HeaderRepository) 
                : base(serviceProvider, configuration, repoInvoiceHeader, repoInvoice01HeaderRepository)
        {
            _serviceProvider = serviceProvider;
            _repoInvoiceHeader = repoInvoiceHeader;
            _repoInvoice01HeaderRepository = repoInvoice01HeaderRepository;
        }

        public async Task ValidateAsync(IServiceProvider serviceProvider)
        {
            var _settingDbtgService = _serviceProvider.GetService<ISettingDbtgService>();

            // Ktra xem có dữ liệu để tạo hóa đơn không
            if (!await _repoInvoiceHeader.AnyInvoiceByStatusAsync(CreatorStatus.Untreated))
            {
                Log.Information($"Không tồn tại dữ liệu để tạo hóa đơn");
                return;
            }

            // Lấy tỷ giá trong bảng VCB_CORE
            var dictExchangeRate = await _settingDbtgService.GetExchangeRateSettingsAsync();
            if (dictExchangeRate == null || !dictExchangeRate.Any())
            {
                Log.Information($"Không có tỷ giá của VCB");
                return;
            }

            // Lấy danh sách Tenant
            var cacheTenants = await _settingDbtgService.GetTenantsAsync();
            if (cacheTenants == null || !cacheTenants.Any())
                return;

            // Lấy Tiền tệ trong hệ thống HDDT
            //var tenantHO = DbtgSharedConst.TENANT_HO_FAKE;
            var tenantHO = cacheTenants.First(x => !x.Value.ParentId.HasValue).Value;
            var dictCurrency = await _settingDbtgService.GetCurrenciesOfHO(tenantHO.Id);
            if (dictCurrency == null || !dictCurrency.Any())
            {
                Log.Information($"Không tìm thấy danh sách Tiền tệ trong hệ thống HDDT");
                return;
            }

            // Ktra chi nhánh bị khóa đến ngày không được tạo hóa đơn
            var tenantsOutOfDate = cacheTenants.Values.Where(x => x.EffectiveDeactiveDate.HasValue && x.EffectiveDeactiveDate.Value > DateTime.Now).ToList();
            if (tenantsOutOfDate != null && tenantsOutOfDate.Any())
            {
                await ValidateTenantsOutOfDate(tenantsOutOfDate);

                var tenantIdsOutOfDate = tenantsOutOfDate.Select(x => x.Id);
                cacheTenants.RemoveAll(x => tenantIdsOutOfDate.Contains(x.Key));

                if (cacheTenants == null || !cacheTenants.Any())
                {
                    Log.Information($"Không tìm thấy chi nhánh trong hệ thống HDDT sau khi ktra chi nhánh bị khóa");
                    return;
                }
            }

            var dictTenant = cacheTenants.ToDictionary(x => x.Key, y => y.Value.TenantCode);

            // Lấy hóa đơn ra validate
            var invoices = await GetListInvoicesAsync();

            if (!invoices.Any())
            {
                //Log.Information($"Không có hóa đơn trong bảng G5");
                return;
            }

            // Validate
            await ValidateInvoicesG5(invoices, dictTenant, dictCurrency, dictExchangeRate);

            // Gom lại update susscess và error
            await UpdateStatusInvoices(invoices);
        }

        private async Task ValidateInvoicesG5(
            List<GxHeaderValidationModel> invoices, 
            Dictionary<Guid, string> dictTenant,
            Dictionary<string, CurrencyEntity> dictCurrency,
            Dictionary<int, Dictionary<string, decimal>> dictExchangeRate)
        {
            foreach (var invoice in invoices)
            {
                if (!invoice.CreatorErp.IsNullOrEmpty() && invoice.CreatorErp.Length > 250)
                {
                    invoice.CreatorStatus = CreatorStatus.ValidateError;
                    invoice.Message = ErrorMessageEnum.CreatorErpMax250Char.ToString();

                    continue;
                }

                if (!dictTenant.Any(x => x.Value == invoice.TenantCode))
                {
                    invoice.CreatorStatus = CreatorStatus.ValidateError;
                    invoice.Message = ErrorMessageEnum.NotExistTenantCode.ToString();

                    continue;
                }

                if (invoice.TemplateCode != 1 && invoice.TemplateCode != 2)
                {
                    invoice.CreatorStatus = CreatorStatus.ValidateError;
                    invoice.Message = ErrorMessageEnum.TemplateCodeNotIn1Or2.ToString(); ;

                    continue;
                }

                if (invoice.ExchangeRate <= 0)
                {
                    invoice.CreatorStatus = CreatorStatus.ValidateError;
                    invoice.Message = ErrorMessageEnum.ExchangeRateAboveZero.ToString();

                    continue;
                }

                if (!invoice.HasDetail.HasValue || !invoice.HasDetail.Value)
                {
                    invoice.CreatorStatus = CreatorStatus.ValidateError;
                    invoice.Message = ErrorMessageEnum.NoDetail.ToString();

                    continue;
                }

                if (invoice.HasNotValidVatPercent.HasValue)
                {
                    invoice.CreatorStatus = CreatorStatus.ValidateError;
                    invoice.Message = ErrorMessageEnum.VatPercentInvalid.ToString();

                    continue;
                }

                if (!dictCurrency.Values.Any(x => x.IsDefault))
                {
                    invoice.CreatorStatus = CreatorStatus.ValidateError;
                    invoice.Message = ErrorMessageEnum.UnConfiguredOriginalCurrency.ToString();

                    continue;
                }

                if (!dictCurrency.ContainsKey(invoice.ToCurrency))
                {
                    invoice.CreatorStatus = CreatorStatus.ValidateError;
                    invoice.Message = ErrorMessageEnum.NoCurrencyFound.ToString();

                    continue;
                }

                if (!dictExchangeRate.ContainsKey(invoice.CeationDateNumber))
                {
                    invoice.CreatorStatus = CreatorStatus.ValidateError;
                    invoice.Message = ErrorMessageEnum.VCBCORE_TYGIA_NoCurrencyWithDate.ToString();

                    continue;
                }

                if (!dictExchangeRate[invoice.CeationDateNumber].ContainsKey(invoice.ToCurrency))
                {
                    invoice.CreatorStatus = CreatorStatus.ValidateError;
                    invoice.Message = ErrorMessageEnum.VCBCORE_TYGIA_NoExchangeReateWithDate.ToString();

                    continue;
                }

                if (invoice.HasExistErpId.HasValue)
                {
                    invoice.CreatorStatus = CreatorStatus.ValidateError;
                    invoice.Message = ErrorMessageEnum.ErpIdAlreadyExist.ToString();

                    continue;
                }

                if (!invoice.BuyerTaxCode.IsTaxCode())
                {
                    invoice.CreatorStatus = CreatorStatus.ValidateError;
                    invoice.Message = ErrorMessageEnum.TaxCodeInValid.ToString();

                    continue;
                }


                invoice.CreatorStatus = CreatorStatus.ValidateSuccess;
            }

            //var existErpIds = await ValidateErpIds(invoices.Where(x => x.CreatorStatus == CreatorStatus.ValidateSuccess));
            //if (existErpIds != null && existErpIds.Any())
            //{
            //    invoices.ForEach(x => {
            //        if (existErpIds.Contains(x.Id))
            //        {
            //            x.CreatorStatus = CreatorStatus.ValidateError;
            //            x.Message = ErrorMessageEnum.ErpIdAlreadyExist.ToString();
            //        }
            //    });
            //}
        }

        public override async Task<List<G5InvoiceHeaderEntity>> GetInvoicesAsync()
        {
            return await _repoInvoiceHeader.GetInvoiceAsync(CreatorStatus.Untreated, DefaultData.InvoiceSize);
        }

        public override async Task<List<GxHeaderValidationModel>> GetListInvoicesAsync()
        {
            return await _repoInvoiceHeader.GetInvoiceRawAsync(CreatorStatus.Untreated, DefaultData.InvoiceSize);
        }
    }
}
