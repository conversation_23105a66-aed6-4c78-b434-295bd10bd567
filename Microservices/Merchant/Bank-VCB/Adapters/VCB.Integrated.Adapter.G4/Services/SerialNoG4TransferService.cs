using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System;
using System.Threading.Tasks;

namespace VCB.Integrated.Adapter.G4.Services
{
    public class SerialNoG4TransferService : ISerialNoG4TransferService
    {
        private readonly IConfiguration _configuration;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly ISettingService _settingService;

        public SerialNoG4TransferService(
            IConfiguration configuration,
            IStringLocalizer<CoreLocalizationResource> localizer,
            ISettingService settingService
        )
        {
            _configuration = configuration;
            _localizer = localizer;
            _settingService = settingService;
        }

        public async Task<(string, string)> Transfer(string serialNo)
        {
            if (!serialNo.IsSerialNo())
            {
                var errorMessage = _localizer["Vnis.BE.VCB.Invoice01.UpdateStatusInvoice.SerialNoTransfer.SettingVaule.IsInValid", new string[] { serialNo }];
                return (errorMessage, string.Empty);
            }

            // OneTax
            var (oneTaxG4ErrorMessage, oneTaxG4SerialNo) = await BusinessTransformation(serialNo, SettingKey.OneTaxG4.ToString(), "OneTax");
            if (!string.IsNullOrWhiteSpace(oneTaxG4SerialNo))
            {
                return (oneTaxG4ErrorMessage, oneTaxG4SerialNo);
            }

            // MultipleTaxG4
            var (multiTaxG4ErrorMessage, multiTaxG4SerialNo) = await BusinessTransformation(serialNo, SettingKey.MultipleTaxG4.ToString(), "MultiTax");
            if (!string.IsNullOrWhiteSpace(multiTaxG4SerialNo))
            {
                return (multiTaxG4ErrorMessage, multiTaxG4SerialNo);

            }

            return ("Không tìm thấy SerialNo hợp lệ để chuyển đổi: " + serialNo, string.Empty);
        }

        private async Task<(string, string)> BusinessTransformation(string serialNo, string settingKey, string config)
        {
            var firstLetter = serialNo.Substring(0, 1);
            var year = serialNo.Substring(1, 2);
            var sn = serialNo.Substring(3, 2);
            var lastLetter = serialNo.Substring(5);
            var currentYear = DateTime.Now.ToString("yy");

            // TODO: Lấy cấu hình từ settings
            var setting = await _settingService.GetByCodeRawAsync(Guid.Empty, settingKey);
            var settingValue = setting?.Value;
            if (string.IsNullOrWhiteSpace(settingValue))
            {
                var errorMessage = _localizer["Vnis.BE.VCB.Invoice01.UpdateStatusInvoice.SerialNoTransfer.SettingKey.Notfound", new string[] { settingKey }];
                return (errorMessage, string.Empty);
            }
            //if (!settingValue.Substring(1).ToUpper().IsSerialNo())
            //{
            //    var errorMessage = _localizer["Vnis.BE.VCB.Invoice01.UpdateStatusInvoice.SerialNoTransfer.SettingVaule.IsInValid", new string[] { settingValue }];
            //    return (errorMessage, string.Empty);
            //}

            // TODO: Lấy từ cấu hình appsettings.json
            if (sn == settingValue.Substring(4, 2))
            {
                var serialNoG4Transfer = _configuration[$"SerialNoG4Transfer:{config}"];

                if (string.IsNullOrWhiteSpace(serialNoG4Transfer))
                {
                    var errorMessage = _localizer["Vnis.BE.VCB.Invoice01.UpdateStatusInvoice.SerialNoTransfer.SerialNoG4Transfer.Notfound", new string[] { config }];
                    return (errorMessage, string.Empty);
                }
                var serialNoG4 = serialNoG4Transfer.Substring(0, 2);

                return currentYear != year
                    ? (string.Empty, $"{firstLetter}{currentYear}{serialNoG4}{lastLetter}")
                    : (string.Empty, $"{firstLetter}{year}{serialNoG4}{lastLetter}");
            }

            return (string.Empty, string.Empty);
        }
    }
}
