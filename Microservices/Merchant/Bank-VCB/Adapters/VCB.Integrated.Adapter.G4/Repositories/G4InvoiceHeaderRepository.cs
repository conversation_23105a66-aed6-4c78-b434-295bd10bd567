using Core.DependencyInjection;
using Core.Shared.Factory;
using Dapper;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VCB.Integrated.Adapter.G4.Models;
using VCB.Integrated.Oracle.Domain.Entities.G4;
using VnisCore.Core.Oracle.Domain.Entities;
using static CoreDbtg.Shared.Constants.DbtgSharedConst;

namespace VCB.Integrated.Adapter.G4.Repositories
{
    public interface IG4InvoiceHeaderRepository
    {
        /// <summary>
        /// Lấy giao dịch trong DBTG để tạo mới hóca đơn của ngày T-1
        /// </summary>
        /// <param name="tenantCode"></param>
        /// <param name="invoiceDateNumber"></param>
        /// <param name="size"></param>
        /// <returns></returns>
        Task<List<G4InvoiceHeaderEntity>> GetInvoiceToCreateAsync(string tenantCode, int invoiceDateNumber, short creatorStatus, short invoiceStatus, int size = 0);

        /// <summary>
        /// Lấy giao dịch trong DBTG để tạo mới hóca đơn của ngày T-1
        /// </summary>
        /// <param name="tenantCode"></param>
        /// <param name="invoiceDateNumber"></param>
        /// <param name="creatorStatus"></param>
        /// <param name="invoiceStatus"></param>
        /// <param name="size"></param>
        /// <returns></returns>
        Task<List<G4InvoiceHeaderEntity>> GetInvoiceToCreateAdjustmentAsync(string tenantCode, int invoiceDateNumber, short creatorStatus, short invoiceStatus, int size = 0);

        List<G4InvoiceHeaderEntity> GetInvoiceToCreate(string tenantCode, int invoiceDateNumber, int size = 0);

        /// <summary>
        /// Lấy giao dịch trong DBTG để hủy hóa đơn của ngày T-1
        /// </summary>
        /// <param name="tenantCode"></param>
        /// <param name="invoiceDateNumber"></param>
        /// <param name="size"></param>
        /// <returns></returns>
        Task<List<G4InvoiceHeaderEntity>> GetDeleteInvoiceToCreateAsync(string tenantCode, int invoiceDateNumber, short creatorStatus, short invoiceStatus, int size = 0);


        Task<long> CountInvoiceByTenantAndInvoiceDate(string tenantCode, int invoiceDateNumber, short creatorStatus, short invoiceStatus);

        Task<long> CountInvoiceByTenantAndInvoiceDateAdjustmentAsync(string tenantCode, int invoiceDateNumber, short creatorStatus, short invoiceStatus);

        Task<int> UpdateAsync(string query, DynamicParameters paramSqls = null);

        int Update(string query);

        Task<Dictionary<string, List<string>>> GetTenantCodeGrpInvByInvoiceDateNumber(int invoiceDateNumber, short creatorStatus, short invoiceStatus);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="invoiceDateNumber"></param>
        /// <param name="creatorStatus"></param>
        /// <param name="invoiceStatus"></param>
        /// <returns></returns>
        Task<Dictionary<string, List<string>>> GetTenantCodeGrpInvByInvoiceDateNumberAdjustmentAsync(int invoiceDateNumber, short creatorStatus, short invoiceStatus);

        Task<long> CountInvoiceBackDateByTenant(string tenantCode);

        Task<List<G4InvoiceHeaderEntity>> GetInvoiceBackDateToCreateAsync(string tenantCode, int size);

        Task<MonitorInvoiceTemplateEntity> GetMonitorTemplate(long id);
    }

    public class G4InvoiceHeaderRepository : IG4InvoiceHeaderRepository, ITransientDependency
    {
        private readonly IAppFactory _appFactory;

        public G4InvoiceHeaderRepository(IAppFactory appFactory)
        {
            _appFactory = appFactory;
        }

        public async Task<List<G4InvoiceHeaderEntity>> GetInvoiceToCreateAsync(string tenantCode, int invoiceDateNumber, short creatorStatus, short invoiceStatus, int size = 0)
        {
            var query = new StringBuilder();

            query.Append($@"    SELECT * 
                                FROM ""G4InvoiceHeader"" 
                                WHERE ""CeationDateNumber"" = :CeationDateNumber
                                    AND ""CreatorStatus"" = :CreatorStatus 
                                    AND ""InvoiceStatus"" = :InvoiceStatus 
                                    AND ""TenantCode"" = :TenantCode 
                         ");

            if (size != 0)
                query.Append($@" FETCH FIRST {size} ROW ONLY ");

            var data = await _appFactory.VcbIntegratedOracle.Connection.QueryAsync<G4InvoiceHeaderEntity>(query.ToString(), new
            {
                CeationDateNumber = invoiceDateNumber,
                CreatorStatus = creatorStatus,
                InvoiceStatus = invoiceStatus,
                TenantCode = tenantCode
            });

            return data.ToList();
        }
        
        public async Task<List<G4InvoiceHeaderEntity>> GetInvoiceToCreateAdjustmentAsync(string tenantCode, int invoiceDateNumber, short creatorStatus, short invoiceStatus, int size = 0)
        {
            var query = new StringBuilder();

            query.Append($@"    SELECT * 
                                FROM ""G4InvoiceHeader"" 
                                WHERE ""CeationDateNumber"" = :CeationDateNumber
                                    AND ""CreatorStatus"" = :CreatorStatus 
                                    AND ""InvoiceStatus"" = :InvoiceStatus 
                                    AND ""TenantCode"" = :TenantCode 
                                    AND ""InvoiceHeaderIdReference"" IS NOT NULL 
                         ");

            if (size != 0)
                query.Append($@" FETCH FIRST {size} ROW ONLY ");

            var data = await _appFactory.VcbIntegratedOracle.Connection.QueryAsync<G4InvoiceHeaderEntity>(query.ToString(), new
            {
                CeationDateNumber = invoiceDateNumber,
                CreatorStatus = creatorStatus,
                InvoiceStatus = invoiceStatus,
                TenantCode = tenantCode
            });

            return data.ToList();
        }

        public List<G4InvoiceHeaderEntity> GetInvoiceToCreate(string tenantCode, int invoiceDateNumber, int size = 0)
        {
            var query = new StringBuilder();

            query.Append($@"    SELECT * 
                                FROM ""G4InvoiceHeader"" 
                                WHERE ""CeationDateNumber"" = {invoiceDateNumber}
                                    AND ""CreatorStatus"" = {CreatorStatus.ValidateSuccess} 
                                    AND ""InvoiceStatus"" = {InvoiceStatusDbtg.Create} 
                                    AND ""TenantCode"" = '{tenantCode}'
                         ");

            if (size != 0)
                query.Append($@" FETCH FIRST {size} ROW ONLY ");

            var data = _appFactory.VcbIntegratedOracle.Connection.Query<G4InvoiceHeaderEntity>(query.ToString());

            return data.ToList();
        }

        public async Task<List<G4InvoiceHeaderEntity>> GetDeleteInvoiceToCreateAsync(string tenantCode, int invoiceDateNumber, short creatorStatus, short invoiceStatus, int size = 0)
        {
            var query = new StringBuilder();

            query.Append($@"    SELECT * 
                                FROM ""G4InvoiceHeader"" 
                                WHERE ""CeationDateNumber"" = :CeationDateNumber 
                                    AND ""CreatorStatus"" = :CreatorStatus
                                    AND ""InvoiceStatus"" = :InvoiceStatus
                                    AND ""TenantCode"" = :TenantCode
                        ");

            if (size != 0)
                query.Append($@" FETCH FIRST {size} ROW ONLY ");

            var data = await _appFactory.VcbIntegratedOracle.Connection.QueryAsync<G4InvoiceHeaderEntity>(query.ToString(), new
            {
                CeationDateNumber = invoiceDateNumber,
                CreatorStatus = creatorStatus,
                InvoiceStatus = invoiceStatus,
                TenantCode = tenantCode
            });

            return data.ToList();
        }

        public async Task<long> CountInvoiceByTenantAndInvoiceDate(string tenantCode, int invoiceDateNumber, short creatorStatus, short invoiceStatus)
        {
            var query = $@"
                            SELECT COUNT(ROWNUM)
                            FROM ""G4InvoiceHeader"" 
                            WHERE ""CeationDateNumber"" = :CeationDateNumber
                                AND ""CreatorStatus"" = :CreatorStatus 
                                AND ""InvoiceStatus"" = :InvoiceStatus 
                                AND ""TenantCode"" = :TenantCode
                        ";

            var count = await _appFactory.VcbIntegratedOracle.Connection.QueryFirstAsync<long>(query, new
            {
                CeationDateNumber = invoiceDateNumber,
                CreatorStatus = creatorStatus,
                InvoiceStatus = invoiceStatus,
                TenantCode = tenantCode,
            });

            return count;
        }


        public async Task<long> CountInvoiceByTenantAndInvoiceDateAdjustmentAsync(string tenantCode, int invoiceDateNumber, short creatorStatus, short invoiceStatus)
        {
            var query = $@"
                            SELECT COUNT(ROWNUM)
                            FROM ""G4InvoiceHeader"" 
                            WHERE ""CeationDateNumber"" = :CeationDateNumber
                                AND ""CreatorStatus"" = :CreatorStatus 
                                AND ""InvoiceStatus"" = :InvoiceStatus 
                                AND ""InvoiceHeaderIdReference"" IS NOT NULL 
                                AND ""TenantCode"" = :TenantCode
                        ";

            var count = await _appFactory.VcbIntegratedOracle.Connection.QueryFirstAsync<long>(query, new
            {
                CeationDateNumber = invoiceDateNumber,
                CreatorStatus = creatorStatus,
                InvoiceStatus = invoiceStatus,
                TenantCode = tenantCode,
            });

            return count;
        }

        public async Task<int> UpdateAsync(string query, DynamicParameters paramSqls = null)
        {
            return await _appFactory.VcbIntegratedOracle.Connection.ExecuteAsync(query, paramSqls);
        }

        public int Update(string query)
        {
            return _appFactory.VcbIntegratedOracle.Connection.Execute(query);
        }

        public async Task<Dictionary<string, List<string>>> GetTenantCodeGrpInvByInvoiceDateNumber(int invoiceDateNumber, short creatorStatus, short invoiceStatus)
        {
            var query = $@"
                            SELECT ""TenantCode"", ""GrpInv""
                            FROM ""G4InvoiceHeader"" 
                            WHERE ""CeationDateNumber"" = :CeationDateNumber
                                AND ""CreatorStatus"" = :CreatorStatus  
                                AND ""InvoiceStatus"" = :InvoiceStatus 
                                AND ""GrpInv"" IS NOT NULL
                            GROUP BY ""TenantCode"", ""GrpInv""
                        ";

            var data = (await _appFactory.VcbIntegratedOracle.Connection.QueryAsync<TenantCodeGrpInv>(query, new
            {
                CeationDateNumber = invoiceDateNumber,
                CreatorStatus = creatorStatus,
                InvoiceStatus = invoiceStatus
            })).ToList();

            return (data != null && data.Any()) ? data.GroupBy(x => x.TenantCode).ToDictionary(x => x.Key, y => y.Select(z => z.GrpInv).ToList()) : null;
        }
        
        public async Task<Dictionary<string, List<string>>> GetTenantCodeGrpInvByInvoiceDateNumberAdjustmentAsync(int invoiceDateNumber, short creatorStatus, short invoiceStatus)
        {
            var query = $@"
                            SELECT ""TenantCode"", ""GrpInv""
                            FROM ""G4InvoiceHeader"" 
                            WHERE ""CeationDateNumber"" = :CeationDateNumber
                                AND ""CreatorStatus"" = :CreatorStatus  
                                AND ""InvoiceStatus"" = :InvoiceStatus 
                                AND ""InvoiceHeaderIdReference"" IS NOT NULL 
                                AND ""GrpInv"" IS NOT NULL
                            GROUP BY ""TenantCode"", ""GrpInv""
                        ";

            var data = (await _appFactory.VcbIntegratedOracle.Connection.QueryAsync<TenantCodeGrpInv>(query, new
            {
                CeationDateNumber = invoiceDateNumber,
                CreatorStatus = creatorStatus,
                InvoiceStatus = invoiceStatus
            })).ToList();

            return (data != null && data.Any()) ? data.GroupBy(x => x.TenantCode).ToDictionary(x => x.Key, y => y.Select(z => z.GrpInv).ToList()) : null;
        }

        public async Task<long> CountInvoiceBackDateByTenant(string tenantCode)
        {
            var query = $@"
                            SELECT COUNT(ROWNUM)
                            FROM ""G4InvoiceHeader"" 
                            WHERE ""CreatorStatus"" = :CreatorStatus 
                                AND ""InvoiceStatus"" = :InvoiceStatus 
                                AND ""TenantCode"" = :TenantCode
                        ";

            var count = await _appFactory.VcbIntegratedOracle.Connection.QueryFirstAsync<long>(query, new
            {
                CreatorStatus = CreatorStatus.WaittingToCreateBackDate,
                InvoiceStatus = InvoiceStatusDbtg.Create,
                TenantCode = tenantCode
            });

            return count;
        }

        public async Task<List<G4InvoiceHeaderEntity>> GetInvoiceBackDateToCreateAsync(string tenantCode, int size)
        {
            var query = new StringBuilder();

            query.Append($@"    SELECT * 
                                FROM ""G4InvoiceHeader"" 
                                WHERE ""CreatorStatus"" = :CreatorStatus
                                    AND ""InvoiceStatus"" = :InvoiceStatus
                                    AND ""TenantCode"" = :TenantCode
                         ");

            if (size != 0)
                query.Append($@" FETCH FIRST :SizeFetch ROW ONLY ");

            var data = await _appFactory.VcbIntegratedOracle.Connection.QueryAsync<G4InvoiceHeaderEntity>(query.ToString(), new
            {
                CreatorStatus = CreatorStatus.WaittingToCreateBackDate,
                InvoiceStatus = InvoiceStatusDbtg.Create,
                TenantCode = tenantCode,
                SizeFetch = size
            });

            return data.ToList();
        }

        public async Task<MonitorInvoiceTemplateEntity> GetMonitorTemplate(long id)
        {
            var query = $@"
                            SELECT *
                            FROM ""MonitorInvoiceTemplate"" 
                            WHERE ""Id"" = :Id
                        ";

            var data = await _appFactory.VnisCoreOracle.Connection.QueryFirstAsync<MonitorInvoiceTemplateEntity>(query, new
            {
                Id = id
            });

            return data;
        }
    }
}
