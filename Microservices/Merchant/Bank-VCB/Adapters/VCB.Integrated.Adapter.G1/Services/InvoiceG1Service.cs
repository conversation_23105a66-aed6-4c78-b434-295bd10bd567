using Core;
using Core.Domain.Repositories;
using Core.EventBus.Distributed;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Models;
using CoreDbtg.Shared.Constants;
using CoreDbtg.Shared.Interfaces;
using CoreDbtg.Shared.MessageEventsData;
using CoreDbtg.Shared.Models;
using CoreDbtg.Shared.Models.Invoices;
using Microsoft.Extensions.DependencyInjection;
using Dapper;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VCB.Integrated.Adapter.G1.Interfaces;
using VCB.Integrated.Adapter.G1.Repositories;
using VCB.Integrated.Oracle.Domain.Entities.G1;
using VnisCore.Core.Oracle.Domain.Entities;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using static CoreDbtg.Shared.Constants.DbtgSharedConst;

namespace VCB.Integrated.Adapter.G1.Services
{
    public class InvoiceG1Service : IInvoiceG1Service
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ISettingDbtgService _settingDbtgService;
        private readonly IInvoiceDbtgService _invoiceDbtgService;
        private readonly IDistributedEventBus _distributedEventBus;
        private readonly IG1InvoiceHeaderRepository _repoG1Header;
        private readonly IG1InvoiceDetailRepository _repoG1Detail;
        private const int INVOICE_PAGE_SIZE = 50;

        public InvoiceG1Service(
            IServiceProvider serviceProvider,
            ISettingDbtgService settingDbtgService,
            IInvoiceDbtgService invoiceDbtgService,
            IDistributedEventBus distributedEventBus,
            IG1InvoiceHeaderRepository repoG1Header,
            IG1InvoiceDetailRepository repoG1Detail)
        {
            _serviceProvider = serviceProvider;
            _settingDbtgService = settingDbtgService;
            _invoiceDbtgService = invoiceDbtgService;
            _distributedEventBus = distributedEventBus;
            _repoG1Detail = repoG1Detail;
            _repoG1Header = repoG1Header;
        }

        #region Create
        public async Task CreateInvoiceAsync()
        {
            try
            {
                var invoiceDate = DateTime.Now.AddDays(-1).Date;
                var invoiceDateNumber = int.Parse($"{invoiceDate.ToString(FormatDate._yyyyMMdd)}");

                // Kiểm tra xem đã đẩy dữ liệu ngày n-1 vào G1 chưa.
                if (!await _invoiceDbtgService.AnyGxValidInvoiceAsync(IntegratedGroup.G1TableName, invoiceDateNumber))
                {
                    Log.Information("Chưa có hóa đơn đã valid trong G1");
                    return;
                }

                // Lấy danh sách Tenant
                var cacheTenants = await _settingDbtgService.GetTenantsAsync();
                var dictTenant = cacheTenants.ToDictionary(x => x.Key, y => y.Value.TenantCode);
                if (dictTenant == null || !dictTenant.Any())
                {
                    Log.Information(LogMessageConst.TenantNotFound);
                    return;
                }

                // Ktra thông tin chi nhánh HO
                if (!cacheTenants.Any(x => !x.Value.ParentId.HasValue))
                {
                    Log.Information("Không tìm thấy chi nhánh HO trong hệ thống");
                    return;
                }

                // Lấy thông tin User tạo hóa đơn trên Adapter
                var tenantHO = cacheTenants.First(x => !x.Value.ParentId.HasValue).Value;
                //var tenantHO = DbtgSharedConst.TENANT_HO_FAKE;

                var creator = await _settingDbtgService.GetCreatorInvoiceOfHO(tenantHO.Id, "admin");
                if (creator == null)
                {
                    Log.Information("Không tìm thấy User admin trên chi nhánh HO trong hệ thống");
                    return;
                }

                // Lấy Tiền tệ trong hệ thống HDDT
                var dictCurrency = await _settingDbtgService.GetCurrenciesOfHO(tenantHO.Id);
                if (dictCurrency == null || !dictCurrency.Any())
                {
                    Log.Information($"Không tìm thấy danh sách Tiền tệ trong hệ thống HDDT");
                    return;
                }

                // Lấy danh sách mẫu HĐ đã cấu hình tích hợp mapping với mẫu HĐ đang sử dụng trong Core, nhóm theo TenantCode
                var templateSettings = await _settingDbtgService.GetTemplateIntegrateSettingsAsync(dictTenant, IntegratedGroup.G1);
                if (templateSettings == null || !templateSettings.Any())
                {
                    Log.Information(LogMessageConst.TemplateSettingNotFound);
                    return;
                }

                // Lấy danh sách định nghĩa tên loại hàng hóa của VCB
                var prodTypes = await _settingDbtgService.GetProductTypeVCBAsync();

                foreach (var tenant in dictTenant)
                {
                    List<G1InvoiceHeaderEntity> headers = null;
                    do
                    {
                        // Count invoice data 
                        var count = await CountInvoiceByTenantAndInvoiceDate(tenant.Value, invoiceDateNumber);
                        if (count <= 0) break;

                        // Get 1000 invoice data by TenantCode
                        headers = await GetInvoiceHeaderAsync(tenant.Value, invoiceDateNumber, DefaultData.InvoiceSize);

                        headers = _invoiceDbtgService.RemarkBuyerCode(headers);

                        if (!headers.Any()) break;

                        // Update CreatorStatus = Watting
                        await UpdateWaittingInvoiceToDbtg(headers.Select(x => x.Id), CreatorStatus.Waitting);

                        // Validate mapping Template and Mapping to Message
                        var invoices = await ValidateAndMapping(headers, invoiceDate, tenant.Value, templateSettings, prodTypes);

                        // Paging Message and Publish to Rabbit
                        await PagingAndPublishMessage(invoices, dictCurrency, cacheTenants[tenant.Key], creator);
                    }
                    while (headers != null && headers.Any());
                }

            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message, ex.StackTrace);
                return;
            }
        }

        public async Task CancelDeleteInvoiceAsync()
        {
            try
            {
                // Lấy danh sách Tenant
                var cacheTenants = await _settingDbtgService.GetTenantsAsync();
                var dictTenant = cacheTenants.ToDictionary(x => x.Key, y => y.Value.TenantCode);
                if (dictTenant == null || !dictTenant.Any())
                {
                    Log.Information(LogMessageConst.TenantNotFound);
                    return;
                }

                // Ktra thông tin chi nhánh HO
                if (!cacheTenants.Any(x => !x.Value.ParentId.HasValue))
                {
                    Log.Information("Không tìm thấy chi nhánh HO trong hệ thống");
                    return;
                }

                foreach (var tenant in dictTenant)
                {
                    List<G1InvoiceHeaderEntity> headers = null;
                    do
                    {
                        // Count invoice data 
                        var count = await CountInvoiceCancelByTenantAndInvoiceDate(tenant.Value);
                        if (count <= 0) break;

                        // Get 1000 invoice data by TenantCode
                        headers = await GetInvoiceHeaderToCancelDeleteAsync(tenant.Value, DefaultData.InvoiceSize);
                        if (!headers.Any()) break;

                        // Update CreatorStatus = Watting
                        await UpdateWaittingInvoiceToDbtg(headers.Select(x => x.Id), CreatorStatus.WaittingToGetDataCancelDelete);

                        // Paging Message and Publish to Rabbit
                        await _distributedEventBus.PublishAsync(new CancelDeleteInvoice01G1EventSendData
                        {
                            ErpIds = headers.Select(x => x.ErpId).ToList(),
                            TenantId = tenant.Key
                        });
                    }
                    while (headers != null && headers.Any());
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message, ex.StackTrace);
                return;
            }
        }

        private List<Invoice01IntegratedHeaderModel> MappingToRequestInvoice(DateTime invoiceDate, List<G1InvoiceHeaderEntity> headers, List<G1InvoiceDetailEntity> details, Dictionary<string, RegisterAvaibilitiesByTenantsModel> templates, Dictionary<short, string> prodTypes)
        {
            var invoices = new List<Invoice01IntegratedHeaderModel>();
            var year_yy = invoiceDate.ToString("yy");

            var indexDetail = details.GroupBy(x => x.InvoiceHeaderId).ToDictionary(x => x.Key, x => x.ToList());

            foreach (var item in headers)
            {
                var serialNo = templates[$"{item.TemplateCode}{year_yy}"];
                var lstDetail = indexDetail[item.ErpId];

                var invoice = _invoiceDbtgService.MappingToInvoiceModel(item, invoiceDate, lstDetail, serialNo, IntegratedGroup.G1, InvoiceSource.ShareAdapterG1, prodTypes);
                invoices.Add(invoice);
            }

            return invoices;
        }

        private async Task<List<Invoice01IntegratedHeaderModel>> ValidateAndMapping(List<G1InvoiceHeaderEntity> headers, DateTime invoiceDate, string tenantCode, Dictionary<string, Dictionary<string, RegisterAvaibilitiesByTenantsModel>> templateSettings, Dictionary<short, string> prodTypes)
        {
            if (!templateSettings.ContainsKey(tenantCode))
            {
                UpdateErrorInvoiceToDbtg(headers.Select(x => new ValidationShared<long>(x.Id, "Không tìm thấy cấu hình dải mẫu tích hợp tạo hóa đơn DBTG")).ToList());
                return null;
            }

            var year_yy = invoiceDate.ToString("yy");
            var invoiceGroupByTemplateCodes = headers.GroupBy(x => $"{x.TemplateCode}{year_yy}", y => y.Id);
            var errors = new List<ValidationShared<long>>();

            foreach (var item in invoiceGroupByTemplateCodes)
            {
                if (!templateSettings[tenantCode].ContainsKey(item.Key))
                {
                    errors.AddRange(item.Select(x => new ValidationShared<long>(x, "Không tìm thấy cấu hình dải mẫu tích hợp tạo hóa đơn DBTG")));
                    continue;
                }
            }

            if (errors != null && errors.Any())
            {
                UpdateErrorInvoiceToDbtg(errors);

                // Xóa các Header không có map được TemplateCode
                var removeIds = errors.Select(x => x.Data);
                headers.RemoveAll(x => removeIds.Contains(x.Id));
            }

            if (!headers.Any()) return null;

            var details = GetInvoiceDetailAsync(headers.Select(x => x.ErpId).ToList());

            var mappings = MappingToRequestInvoice(invoiceDate, headers, details, templateSettings[tenantCode], prodTypes);

            return mappings;
        }

        private async Task PagingAndPublishMessage(
            List<Invoice01IntegratedHeaderModel> invoices,
            Dictionary<string, CurrencyEntity> dictCurrency,
            TenantModel tenant,
            UserModel creator
            )
        {
            var fromCurrency = dictCurrency.First(x => x.Value.IsDefault).Value;

            if (invoices != null && invoices.Any())
            {
                var totalItems = invoices.Count();
                var pageSize = INVOICE_PAGE_SIZE;
                var totalPages = totalItems / pageSize;
                if (totalItems % pageSize > 0) totalPages++;

                for (var page = 0; page < totalPages; page++)
                {
                    var message = new CreateBatchInvoice01IntegratedEvent
                    {
                        IntegratedGroup = IntegratedGroup.G1,
                        Invoices = invoices.Skip(page * pageSize).Take(pageSize).ToList(),
                        TenantId = tenant.Id,
                        CreatorId = creator.Id,
                        UserNameCreator = creator.UserName,
                        FullNameCreator = creator.Name,
                        Tenant = tenant,
                        FromCurrencyModel = fromCurrency,
                        Currencies = dictCurrency.Values.ToList()
                    };

                    await _distributedEventBus.PublishAsync(message);
                }
            }
        }

        private async Task<List<G1InvoiceHeaderEntity>> GetInvoiceHeaderAsync(string tenantCode, int invoiceDateNumber, int size = 0)
        {
            return await _repoG1Header.GetInvoiceToCreateAsync(tenantCode, invoiceDateNumber, size);
        }

        private List<G1InvoiceDetailEntity> GetInvoiceDetailAsync(List<string> idInvoiceheaders)
        {
            return _repoG1Detail.QueryDetailByIdHeader(idInvoiceheaders);
        }

        private async Task<long> CountInvoiceByTenantAndInvoiceDate(string tenantCode, int invoiceDateNumber)
        {
            return await _repoG1Header.CountInvoiceByTenantAndInvoiceDate(tenantCode, invoiceDateNumber);
        }

        /// <summary>
        /// Update trạng thái hóa đơn
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="creatorStatus"></param>
        /// <returns></returns>
        private async Task UpdateWaittingInvoiceToDbtg(IEnumerable<long> ids, short creatorStatus)
        {
            var query = $@" UPDATE ""G1InvoiceHeader"" SET ""CreatorStatus"" = :CreatorStatus WHERE ""Id"" IN :Ids ";
            var dicParameter = new Dictionary<string, object>
            {
                {":CreatorStatus", creatorStatus },
                {":Ids", ids }
            };
            var queryDataModel = new QueryDataModel
            {
                Query = query,
                Parameters = new DynamicParameters(dicParameter)
            };
            await _repoG1Header.UpdateAsync(queryDataModel);

            Log.Debug(query);
        }

        /// <summary>
        /// Sẽ xóa đi khi hoàn thành task VCBINNB-2656
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="creatorStatus"></param>
        /// <returns></returns>

        private async Task UpdateWaittingInvoiceToDbtgOld(IEnumerable<long> ids, short creatorStatus)
        {
            var query = $@" UPDATE ""G1InvoiceHeader"" SET ""CreatorStatus"" = {creatorStatus} WHERE ""Id"" IN ( {String.Join(",", ids)} ) ";
            await _repoG1Header.UpdateAsync(query);

            Log.Debug(query);
        }

        private void UpdateErrorInvoiceToDbtg(List<ValidationShared<long>> errorInvoices)
        {
            var query = new StringBuilder($" BEGIN ");

            errorInvoices.ForEach(x =>
            {
                query.Append($@"    UPDATE ""G1InvoiceHeader"" SET ""CreatorStatus"" = {CreatorStatus.ValidateError}, ""Message"" = N'{x.ErrorMessage}' WHERE ""Id"" = {x.Data}; ");
            });

            query.Append(" END; ");

            _repoG1Header.Update(query.ToString());
        }


        private async Task<List<G1InvoiceHeaderEntity>> GetInvoiceHeaderToCancelDeleteAsync(string tenantCode, int size = 0)
        {
            return await _repoG1Header.GetInvoiceToCancelDeleteAsync(tenantCode, size);
        }

        private async Task<long> CountInvoiceCancelByTenantAndInvoiceDate(string tenantCode)
        {
            return await _repoG1Header.CountInvoiceCancelByTenantAndInvoiceDateAsync(tenantCode);
        }
        #endregion

        #region Create Back Date
        public async Task CreateInvoiceBackDateAsync()
        {
            try
            {
                //var invoiceDate = DateTime.Now.AddDays(-1).Date;
                //var invoiceDateNumber = int.Parse($"{invoiceDate.ToString(FormatDate._yyyyMMdd)}");

                // Kiểm tra xem đã đẩy dữ liệu ngày n-1 vào G1 chưa.
                if (!await _invoiceDbtgService.AnyGxValidInvoiceBackDateAsync(IntegratedGroup.G1TableName))
                {
                    Log.Information("Chưa có hóa đơn đã valid trong G1");
                    return;
                }

                // Lấy danh sách Tenant
                var cacheTenants = await _settingDbtgService.GetTenantsAsync();
                var dictTenant = cacheTenants.ToDictionary(x => x.Key, y => y.Value.TenantCode);
                if (dictTenant == null || !dictTenant.Any())
                {
                    Log.Information(LogMessageConst.TenantNotFound);
                    return;
                }

                // Ktra thông tin chi nhánh HO
                if (!cacheTenants.Any(x => !x.Value.ParentId.HasValue))
                {
                    Log.Information("Không tìm thấy chi nhánh HO trong hệ thống");
                    return;
                }

                // Lấy thông tin User tạo hóa đơn trên Adapter
                var tenantHO = cacheTenants.First(x => !x.Value.ParentId.HasValue).Value;
                //var tenantHO = DbtgSharedConst.TENANT_HO_FAKE;

                var creator = await _settingDbtgService.GetCreatorInvoiceOfHO(tenantHO.Id, "admin");
                if (creator == null)
                {
                    Log.Information("Không tìm thấy User admin trên chi nhánh HO trong hệ thống");
                    return;
                }

                // Lấy Tiền tệ trong hệ thống HDDT
                var dictCurrency = await _settingDbtgService.GetCurrenciesOfHO(tenantHO.Id);
                if (dictCurrency == null || !dictCurrency.Any())
                {
                    Log.Information($"Không tìm thấy danh sách Tiền tệ trong hệ thống HDDT");
                    return;
                }

                // Lấy danh sách mẫu hóa đơn lùi ngày (theo danh sách đơn vị, các mẫu hóa đơn trong năm nay và năm trước)
                // Lấy theo đơn vị, SerialNumber và có ApproveStatus = 2 (Đã được duyệt)

                //var listTemplate = new List<RegisterAvaibilitiesByTenantsModel>();
                var listTemplate = await _settingDbtgService.GetTemplateBackDateAsync(DateTime.Now);

                if (listTemplate == null || !listTemplate.Any())
                {
                    Log.Information(LogMessageConst.TemplateSettingNotFound);
                    return;
                }

                // Cập nhật mã chi nhánh cho danh sách template
                string tenantCode = "";
                foreach (var item in listTemplate)
                {
                    tenantCode = "";
                    dictTenant.TryGetValue(item.TenantId, out tenantCode);
                    item.TenantCode = tenantCode;
                }

                // Lấy danh sách định nghĩa tên loại hàng hóa của VCB
                var prodTypes = await _settingDbtgService.GetProductTypeVCBAsync();

                foreach (var tenant in dictTenant)
                {
                    List<G1InvoiceHeaderEntity> headers = null;
                    do
                    {
                        // Count invoice data 
                        var count = await CountInvoiceBackDateByTenant(tenant.Value);
                        if (count <= 0) break;

                        // Get 1000 invoice data by TenantCode
                        headers = await GetInvoiceHeaderBackDateAsync(tenant.Value, DefaultData.InvoiceSize);

                        headers = _invoiceDbtgService.RemarkBuyerCode(headers);

                        if (!headers.Any()) break;

                        Log.Information($"[Worker][Log.Information($\"[Worker][CreateBackDate]: Dang thuc hien tao {headers.Count} hoa don;]");

                        // Update CreatorStatus = Watting
                        await UpdateWaittingInvoiceToDbtg(headers.Select(x => x.Id), CreatorStatus.WaittingToCreateBackDate);

                        // Validate mapping Template and Mapping to Message
                        var invoices = await ValidateAndMappingInvoiceCreateBackDate(headers, tenant.Value, listTemplate, prodTypes);

                        // Paging Message and Publish to Rabbit
                        await PagingAndPublishMessage(invoices, dictCurrency, cacheTenants[tenant.Key], creator);
                    }
                    while (headers != null && headers.Any());
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message, ex.StackTrace);
                return;
            }
        }

        private async Task<long> CountInvoiceBackDateByTenant(string tenantCode)
        {
            return await _repoG1Header.CountInvoiceBackDateByTenant(tenantCode);
        }

        private async Task<long> CountReport01ByTenantAndDate(int reportMonth, int reportYear, string tenantCode)
        {
            return await _repoG1Header.CountReport01ByTenantAndDate(reportMonth, reportYear, tenantCode);
        }

        private async Task<MonitorInvoiceTemplateEntity> GetMonitorTemplate(long id)
        {
            return await _repoG1Header.GetMonitorTemplate(id);
        }

        private async Task<List<G1InvoiceHeaderEntity>> GetInvoiceHeaderBackDateAsync(string tenantCode, int size = 0)
        {
            return await _repoG1Header.GetInvoiceBackDateToCreateAsync(tenantCode, size);
        }

        private async Task<List<Invoice01IntegratedHeaderModel>> ValidateAndMappingInvoiceCreateBackDate(List<G1InvoiceHeaderEntity> headers, string tenantCode, List<RegisterAvaibilitiesByTenantsModel> templateSettings, Dictionary<short, string> prodTypes)
        {
            //var invoiceGroupByTemplateCodes = headers.GroupBy(x => $"{x.TemplateCode}{x.InvoiceDate.Value.Year.ToString().Substring(2, 2)}", y => y.Id);
            var errors = new List<ValidationShared<long>>();

            var details = GetInvoiceDetailAsync(headers.Select(x => x.ErpId).ToList());

            Dictionary<long, RegisterAvaibilitiesByTenantsModel> dictHeaderMapTemplate = new Dictionary<long, RegisterAvaibilitiesByTenantsModel>();

            DateTime today = DateTime.Today;
            int currentDateInMonth = today.Day;
            int lockDate = 20;

            DateTime firstDayOfMonth = new DateTime(today.Year, today.Month, 1);
            DateTime firstDayOfLastMonth = firstDayOfMonth.AddMonths(-1);

            List<InvoiceGxCheckReportModel> listApproveReport = new List<InvoiceGxCheckReportModel>();

            foreach (var item in headers)
            {
                try
                {
                    if (item.InvoiceDate.Value < firstDayOfLastMonth)
                    {
                        errors.Add(new ValidationShared<long>(item.Id, $"Không tạo hóa đơn lùi ngày nếu ngày hóa đơn < tháng T-1"));
                        continue;
                    }

                    // Bổ sung logic check báo cáo trong bảng TaxReport01Header đã được duyệt ApproveStatus thì không có phép tạo hóa đơn lùi ngày
                    /*
                        Ngày hiện tại >= ngày khóa lập hóa đơn (ngày 20) => Không quét tạo các hóa đơn thuộc tháng T-1, chỉ quét những hóa đơn thuộc tháng T

                        Ngày hiện tại <  ngày khóa lập hóa đơn (ngày 20) 
                        + Báo cáo của tháng T-1 ở trạng thái đã xác nhận => Không quét tạo các hóa đơn thuộc tháng T-1, chỉ quét những hóa đơn thuộc tháng T
                        + Báo cáo của tháng T-1 chưa xác nhận => Quét tạo tất cả các bản ghi có ngày hóa đơn thuộc tháng T và tháng T-1

                        - Check trạng thái BC01 của tháng T-1
                        - Check Ngày hiện tại >= ngày khóa lập hóa đơn (ngày 20) 
                        - Check Ngày hiện tại <  ngày khóa lập hóa đơn (ngày 20) 
                     */

                    /*
                        Tháng hiện tại = X (ví dụ = 8)
                        Lấy mốc là ngày 20
                        1. Nếu ngày hiện tại (18/8) là nhỏ hơn hoặc bằng ngày 20 tháng X (20/8)
                        => Hệ thống hiển thị lựa chọn lùi ngày từ ngày 1 tháng X-1 (1/7) đến ngày hiện tại -1 tháng X (17/8)
                        2. Nếu ngày hiện tại (25/8) lớn hơn ngày 20 tháng X (20/8)
                        => Hệ thống hiển thị lựa chọn lùi ngày từ ngày 1 tháng X (1/8) đến ngày hiện tại -1 tháng X (24/8)
                        Lưu ý: Chỉ quy chiếu mốc với tháng hiện tại, ví dụ ngày hiện tại là 25/8 sẽ chỉ tham chiếu với mốc là 20/8 chứ không phải là 20/7, 20/6..
                     */
                    if (currentDateInMonth > lockDate)
                    {
                        if (item.InvoiceDate.Value < firstDayOfMonth || item.InvoiceDate.Value >= today)
                        {
                            errors.Add(new ValidationShared<long>(item.Id, $"Chỉ có thể tạo hóa đơn trong khoảng từ ngày {firstDayOfMonth.ToString("dd/MM/yyyy")} đến ngày {today.AddDays(-1).ToString("dd/MM/yyyy")}"));
                            continue;
                        }
                    }
                    else
                    {
                        bool isReportLastMonthIsApprove = false;
                        // Kiểm tra trạng thái báo cáo tháng trước (năm báo cáo, tháng báo cáo, Id chi nhánh)

                        // Kiểm tra báo cáo tháng trước đã tạo chưa
                        // Kiểm tra dữ liệu từ danh sách, nếu chưa có thì load dữ liệu từ DB là lưu vào danh sách
                        if (!listApproveReport.Any(x => x.ReportMonth == firstDayOfLastMonth.Month && x.ReportYear == firstDayOfLastMonth.Year && x.TenantCode == item.TenantCode))
                        {
                            // Không tìm thấy dữ liệu => lấy dữ liệu từ DB và lưu vào lại vào danh sách nếu không tồn tại thì lưu ApproveStatus = -1
                            var countReport01Approve = await CountReport01ByTenantAndDate(firstDayOfLastMonth.Month, firstDayOfLastMonth.Year, item.TenantCode);
                            if (countReport01Approve > 0)
                            {
                                listApproveReport.Add(new InvoiceGxCheckReportModel()
                                {
                                    ReportMonth = firstDayOfLastMonth.Month,
                                    ReportYear = firstDayOfLastMonth.Year,
                                    TenantCode = item.TenantCode,
                                    ApproveStatus = (short)ApproveStatus.DaDuyet
                                });
                            }
                            else
                            {
                                listApproveReport.Add(new InvoiceGxCheckReportModel()
                                {
                                    ReportMonth = firstDayOfLastMonth.Month,
                                    ReportYear = firstDayOfLastMonth.Year,
                                    TenantCode = item.TenantCode,
                                    ApproveStatus = -1
                                });
                            }
                        }

                        isReportLastMonthIsApprove = listApproveReport.Any(x => x.ReportMonth == firstDayOfLastMonth.Month && x.ReportYear == firstDayOfLastMonth.Year && x.TenantCode == item.TenantCode && x.ApproveStatus == (int)ApproveStatus.DaDuyet);

                        if (isReportLastMonthIsApprove)
                        {
                            if (item.InvoiceDate.Value < firstDayOfMonth || item.InvoiceDate.Value >= today)
                            {
                                errors.Add(new ValidationShared<long>(item.Id, $"Báo cáo tháng {firstDayOfLastMonth.Month} đã được duyệt chỉ có thể tạo hóa đơn trong khoảng từ ngày {firstDayOfMonth.ToString("dd/MM/yyyy")} đến ngày {today.AddDays(-1).ToString("dd/MM/yyyy")}"));
                                continue;
                            }
                        }
                        else
                        {
                            if (item.InvoiceDate.Value < firstDayOfLastMonth || item.InvoiceDate.Value >= today)
                            {
                                errors.Add(new ValidationShared<long>(item.Id, $"Chỉ có thể tạo hóa đơn trong khoảng từ ngày {firstDayOfLastMonth.ToString("dd/MM/yyyy")} đến ngày {today.AddDays(-1).ToString("dd/MM/yyyy")}"));
                                continue;
                            }
                        }
                    }

                    // Lấy danh sách Template cho từng hóa đơn, nếu hóa đơn ko có thì bỏ qua
                    var serialNumber = GetSerialNumberInvoice(item.InvoiceDate.Value, details.Where(x => x.InvoiceHeaderId == item.ErpId).ToList());

                    // Lấy thông tin biểu mẫu
                    RegisterAvaibilitiesByTenantsModel templateModel = templateSettings.FirstOrDefault(x => x.SerialNo == serialNumber && x.TemplateNo == item.TemplateCode && x.TenantCode == item.TenantCode);

                    if (templateModel == null)
                    {
                        errors.Add(new ValidationShared<long>(item.Id, $"Không tìm thấy cấu hình mẫu {item.TemplateCode}{serialNumber} của đơn vị {item.TenantCode} để tạo hóa đơn DBTG"));
                        continue;
                    }

                    // Kiểm tra ngày chặn số hóa đơn
                    var currentMonitor = await GetMonitorTemplate(templateModel.Id);
                    if (currentMonitor == null)
                    {
                        errors.Add(new ValidationShared<long>(item.Id, $"Không tìm thấy mẫu hóa đơn TenantId: {templateModel.TenantCode} - TemplateNo: {templateModel.TemplateNo} - SerialNo: {templateModel.SerialNo}"));
                        continue;
                    }

                    if (currentMonitor.CurrentNumber == null)
                    {
                        errors.Add(new ValidationShared<long>(item.Id, $"Mẫu hóa đơn TenantId: {templateModel.TenantCode} - TemplateNo: {templateModel.TemplateNo} - SerialNo: {templateModel.SerialNo} chưa đăng ký phát hành"));
                        continue;
                    }

                    if (item.InvoiceDate.Value < currentMonitor.ActiveDate.Value.Date)
                    {
                        errors.Add(new ValidationShared<long>(item.Id, $"Mẫu hóa đơn TenantId: {templateModel.TenantCode} - TemplateNo: {templateModel.TemplateNo} - SerialNo: {templateModel.SerialNo} có dải từ số {currentMonitor.StartNumber} đến số {currentMonitor.EndNumber} sẽ được sử dụng vào ngày {currentMonitor.ActiveDate.Value:dd/MM/yyyy}"));
                        continue;
                    }

                    if (currentMonitor.LastDocumentDate.HasValue && currentMonitor.LastDocumentDate.Value.Date > item.InvoiceDate.Value)
                    {
                        errors.Add(new ValidationShared<long>(item.Id, $"Ngày tạo số phải bắt đầu từ ngày {currentMonitor.LastDocumentDate.Value:dd/MM/yyyy}"));
                        continue;
                    }

                    dictHeaderMapTemplate.Add(item.Id, templateModel);
                }
                catch (Exception ex)
                {
                    errors.Add(new ValidationShared<long>(item.Id, ex.Message));
                }
            }

            if (errors != null && errors.Any())
            {
                UpdateErrorInvoiceToDbtg(errors);

                // Xóa các Header không có map được TemplateCode
                var removeIds = errors.Select(x => x.Data);
                headers.RemoveAll(x => removeIds.Contains(x.Id));
            }

            if (!headers.Any()) return null;


            var mappings = MappingToRequestInvoiceBackDate(headers, details, dictHeaderMapTemplate, prodTypes);

            return mappings;
        }

        private List<Invoice01IntegratedHeaderModel> MappingToRequestInvoiceBackDate(List<G1InvoiceHeaderEntity> headers, List<G1InvoiceDetailEntity> details, Dictionary<long, RegisterAvaibilitiesByTenantsModel> dictHeaderMapTemplate, Dictionary<short, string> prodTypes)
        {
            var invoices = new List<Invoice01IntegratedHeaderModel>();
            var indexDetail = details.GroupBy(x => x.InvoiceHeaderId).ToDictionary(x => x.Key, x => x.ToList());

            foreach (var item in headers)
            {
                var serialNo = dictHeaderMapTemplate[item.Id];
                var lstDetail = indexDetail[item.ErpId];

                var invoice = _invoiceDbtgService.MappingToInvoiceModel(item, item.InvoiceDate.Value, lstDetail, serialNo, IntegratedGroup.G1, InvoiceSource.ShareAdapterG1, prodTypes);
                invoices.Add(invoice);
            }

            return invoices;
        }


        private string GetSerialNumberInvoice(DateTime invoiceDate, List<G1InvoiceDetailEntity> details)
        {
            // Lấy dữ liệu mẫu hóa đơn từ DB theo ngày tháng tạo, ngày tháng hiện tại lấy dữ liệu dựa vào map fix điều kiện hóa đơn lùi ngày
            #region Tìm mẫu hóa đơn lùi ngày theo cấu hình hardcode và lấy id từ DB
            var today = DateTime.Today;

            //if (invoiceDate >= today)
            //{
            //    throw new Exception("Không thể tạo hóa đơn ngày hiện tại hoặc tương lai");
            //}

            //var invoiceDateFirstDayOfMonth = new DateTime(invoiceDate.Year, invoiceDate.Month, 1);
            //var todayFirstDayOfMonth = new DateTime(today.Year, today.Month, 1);

            //if (invoiceDateFirstDayOfMonth.AddMonths(1) < todayFirstDayOfMonth)
            //{
            //    throw new Exception("Chỉ tạo được hóa đơn lùi ngày tháng T và T-1");
            //}

            string serialNumber = "";
            // Lấy thông tin hóa đơn nhiều thuế hay một thuế
            var isOneTax = details.Select(x => x.VatPercent).Distinct().Count() == 1;

            //Tạo lùi ngày cho tháng hiện tại
            if (invoiceDate.Month == today.Month)
            {
                if (isOneTax)
                {
                    serialNumber = $"K{invoiceDate.Year.ToString().Substring(2, 2)}{InvoiceTemplateBackDateCodeConstant.ListLuiNgayMotThueThangHienTai[invoiceDate.Day - 1]}";
                }
                else
                {
                    serialNumber = $"K{invoiceDate.Year.ToString().Substring(2, 2)}{InvoiceTemplateBackDateCodeConstant.ListLuiNgayNhieuThueThangHienTai[invoiceDate.Day - 1]}";
                }
            }

            //Tạo lùi ngày cho tháng trước
            if (invoiceDate.Month + 1 == today.Month)
            {
                if (isOneTax)
                {
                    serialNumber = $"K{invoiceDate.Year.ToString().Substring(2, 2)}{InvoiceTemplateBackDateCodeConstant.ListLuiNgayMotThueThangTruoc[invoiceDate.Day - 1]}";
                }
                else
                {
                    serialNumber = $"K{invoiceDate.Year.ToString().Substring(2, 2)}{InvoiceTemplateBackDateCodeConstant.ListLuiNgayNhieuThueThangTruoc[invoiceDate.Day - 1]}";
                }
            }
            #endregion

            return serialNumber;
        }
        #endregion
    }
}
