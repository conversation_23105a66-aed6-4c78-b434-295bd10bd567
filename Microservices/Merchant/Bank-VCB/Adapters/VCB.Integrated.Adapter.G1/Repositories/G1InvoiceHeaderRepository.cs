using Core.DependencyInjection;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.Models;
using Dapper;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VCB.Integrated.Oracle.Domain.Entities.G1;
using VnisCore.Core.Oracle.Domain.Entities;
using static CoreDbtg.Shared.Constants.DbtgSharedConst;

namespace VCB.Integrated.Adapter.G1.Repositories
{
    public interface IG1InvoiceHeaderRepository
    {
        /// <summary>
        /// Lấy giao dịch trong DBTG để tạo mới hóa đơn của ngày T-1
        /// </summary>
        /// <param name="tenantCode"></param>
        /// <param name="invoiceDateNumber"></param>
        /// <param name="size"></param>
        /// <returns></returns>
        Task<List<G1InvoiceHeaderEntity>> GetInvoiceToCreateAsync(string tenantCode, int invoiceDateNumber, int size = 0);

        Task<List<G1InvoiceHeaderEntity>> GetInvoiceBackDateToCreateAsync(string tenantCode, int size = 0);

        List<G1InvoiceHeaderEntity> GetInvoiceToCreate(string tenantCode, int invoiceDateNumber, int size = 0);

        /// <summary>
        /// Lấy giao dịch trong DBTG để hủy hóa đơn của ngày T-1
        /// </summary>
        /// <param name="tenantCode"></param>
        /// <param name="invoiceDateNumber"></param>
        /// <param name="size"></param>
        /// <returns></returns>
        Task<List<G1InvoiceHeaderEntity>> GetDeleteInvoiceToCreateAsync(string tenantCode, int invoiceDateNumber, int size = 0);

        Task<long> CountInvoiceByTenantAndInvoiceDate(string tenantCode, int invoiceDateNumber);

        Task<long> CountInvoiceBackDateByTenant(string tenantCode);

        Task<int> UpdateAsync(QueryDataModel queryDataModel);
        Task<int> UpdateAsync(string query);

        int Update(string query);

        Task<long> CountInvoiceCancelByTenantAndInvoiceDateAsync(string tenantCode);

        Task<List<G1InvoiceHeaderEntity>> GetInvoiceToCancelDeleteAsync(string tenantCode, int size = 0);

        Task<long> CountReport01ByTenantAndDate(int reportMonth, int reportYear, string tenantCode);

        Task<MonitorInvoiceTemplateEntity> GetMonitorTemplate(long id);
    }

    public class G1InvoiceHeaderRepository : IG1InvoiceHeaderRepository, ITransientDependency
    {
        private readonly IAppFactory _appFactory;

        public G1InvoiceHeaderRepository(IAppFactory appFactory)
        {
            _appFactory = appFactory;
        }

        /// <summary>
        /// Lấy thông tin hóa đơn
        /// </summary>
        /// <param name="tenantCode"></param>
        /// <param name="invoiceDateNumber"></param>
        /// <param name="size"></param>
        /// <returns></returns>
        public async Task<List<G1InvoiceHeaderEntity>> GetInvoiceToCreateAsync(string tenantCode, int invoiceDateNumber, int size = 0)
        {
            var query = new StringBuilder();

            query.Append($@"    SELECT * 
                                FROM ""G1InvoiceHeader"" 
                                WHERE ""CeationDateNumber"" = :CeationDateNumber
                                    AND ""CreatorStatus"" = :CreatorStatus 
                                    AND ""InvoiceStatus"" = :InvoiceStatus 
                                    AND ""TenantCode"" = :TenantCode
                         ");

            if (size != 0)
                query.Append($@" FETCH FIRST {size} ROW ONLY "); 

            var dicParameter = new Dictionary<string, object>
            {
                {":CeationDateNumber", invoiceDateNumber},
                {":CreatorStatus", CreatorStatus.ValidateSuccess},
                {":InvoiceStatus", InvoiceStatusDbtg.Create},
                {":TenantCode", tenantCode},
            };

            var data = await _appFactory.VcbIntegratedOracle.Connection.QueryAsync<G1InvoiceHeaderEntity>(query.ToString(), new DynamicParameters(dicParameter));
            return data.ToList();
        }

        /// <summary>
        /// Sẽ xóa đi khi hoàn thành task VCBINNB-2656
        /// </summary>
        /// <param name="tenantCode"></param>
        /// <param name="invoiceDateNumber"></param>
        /// <param name="size"></param>
        /// <returns></returns>
        public async Task<List<G1InvoiceHeaderEntity>> GetInvoiceToCreateOldAsync(string tenantCode, int invoiceDateNumber, int size = 0)
        {
            var query = new StringBuilder();

            query.Append($@"    SELECT * 
                                FROM ""G1InvoiceHeader"" 
                                WHERE ""CeationDateNumber"" = {invoiceDateNumber}
                                    AND ""CreatorStatus"" = {CreatorStatus.ValidateSuccess} 
                                    AND ""InvoiceStatus"" = {InvoiceStatusDbtg.Create} 
                                    AND ""TenantCode"" = '{tenantCode}'
                         ");

            if (size != 0)
                query.Append($@" FETCH FIRST {size} ROW ONLY ");

            var data = await _appFactory.VcbIntegratedOracle.Connection.QueryAsync<G1InvoiceHeaderEntity>(query.ToString());

            return data.ToList();
        }

        /// <summary>
        /// Lấy danh sách hóa đơn
        /// </summary>
        /// <param name="tenantCode"></param>
        /// <param name="size"></param>
        /// <returns></returns>
        public async Task<List<G1InvoiceHeaderEntity>> GetInvoiceBackDateToCreateAsync(string tenantCode, int size = 0)
        {
            var query = new StringBuilder();

            query.Append($@"    SELECT * 
                                FROM ""G1InvoiceHeader"" 
                                WHERE ""CreatorStatus"" = :CreatorStatus 
                                    AND ""InvoiceStatus"" = :InvoiceStatus 
                                    AND ""TenantCode"" = :TenantCode
                         ");

            if (size != 0)
                query.Append($@" FETCH FIRST {size} ROW ONLY ");

            var dicParameter = new Dictionary<string, object>
            {
                {":CreatorStatus", CreatorStatus.WaittingToCreateBackDate},
                {":InvoiceStatus", InvoiceStatusDbtg.Create},
                {":TenantCode", tenantCode},
            };

            var data = await _appFactory.VcbIntegratedOracle.Connection.QueryAsync<G1InvoiceHeaderEntity>(query.ToString(), new DynamicParameters(dicParameter));

            return data.ToList();
        }

        /// <summary>
        /// Sẽ xóa đi khi hoàn thành task VCBINNB-2656
        /// </summary>
        /// <param name="tenantCode"></param>
        /// <param name="size"></param>
        /// <returns></returns>

        public async Task<List<G1InvoiceHeaderEntity>> GetInvoiceBackDateToCreateOldAsync(string tenantCode, int size = 0)
        {
            var query = new StringBuilder();

            query.Append($@"    SELECT * 
                                FROM ""G1InvoiceHeader"" 
                                WHERE ""CreatorStatus"" = {CreatorStatus.WaittingToCreateBackDate} 
                                    AND ""InvoiceStatus"" = {InvoiceStatusDbtg.Create} 
                                    AND ""TenantCode"" = '{tenantCode}'
                         ");

            if (size != 0)
                query.Append($@" FETCH FIRST {size} ROW ONLY "); 

            var data = await _appFactory.VcbIntegratedOracle.Connection.QueryAsync<G1InvoiceHeaderEntity>(query.ToString());

            return data.ToList();
        }

        public List<G1InvoiceHeaderEntity> GetInvoiceToCreate(string tenantCode, int invoiceDateNumber, int size = 0)
        {
            var query = new StringBuilder();

            query.Append($@"    SELECT * 
                                FROM ""G1InvoiceHeader"" 
                                WHERE ""CeationDateNumber"" = {invoiceDateNumber}
                                    AND ""CreatorStatus"" = {CreatorStatus.ValidateSuccess} 
                                    AND ""InvoiceStatus"" = {InvoiceStatusDbtg.Create} 
                                    AND ""TenantCode"" = '{tenantCode}'
                         ");

            if (size != 0)
                query.Append($@" FETCH FIRST {size} ROW ONLY ");

            var data = _appFactory.VcbIntegratedOracle.Connection.Query<G1InvoiceHeaderEntity>(query.ToString());

            return data.ToList();
        }

        public async Task<List<G1InvoiceHeaderEntity>> GetDeleteInvoiceToCreateAsync(string tenantCode, int invoiceDateNumber, int size = 0)
        {
            var query = new StringBuilder();

            query.Append($@"    SELECT * 
                                FROM ""G1InvoiceHeader"" 
                                WHERE ""CeationDateNumber"" = {invoiceDateNumber}
                                    AND ""CreatorStatus"" = {CreatorStatus.WaittingToGetData}
                                    AND ""InvoiceStatus"" = {InvoiceStatusDbtg.Delete} 
                                    AND ""TenantCode"" = '{tenantCode}'
                        ");

            if (size != 0)
                query.Append($@" FETCH FIRST {size} ROW ONLY ");

            var data = await _appFactory.VcbIntegratedOracle.Connection.QueryAsync<G1InvoiceHeaderEntity>(query.ToString());

            return data.ToList();
        }

        /// <summary>
        /// Đếm số lượng hóa đơn theo CN
        /// </summary>
        /// <param name="tenantCode"></param>
        /// <param name="invoiceDateNumber"></param>
        /// <returns></returns>
        public async Task<long> CountInvoiceByTenantAndInvoiceDate(string tenantCode, int invoiceDateNumber)
        {
            var query = $@"
                            SELECT COUNT(ROWNUM)
                            FROM ""G1InvoiceHeader"" 
                            WHERE ""CeationDateNumber"" = :CeationDateNumber
                                AND ""CreatorStatus"" = :CreatorStatus 
                                AND ""InvoiceStatus"" = :InvoiceStatus 
                                AND ""TenantCode"" = :TenantCode
                        ";

            var dicParameter = new Dictionary<string, object>
            {
                {":CeationDateNumber", invoiceDateNumber},
                {":CreatorStatus", CreatorStatus.ValidateSuccess},
                {":InvoiceStatus", InvoiceStatusDbtg.Create},
                {":TenantCode", tenantCode},
            };

            var count = await _appFactory.VcbIntegratedOracle.Connection.QueryFirstAsync<long>(query, new DynamicParameters(dicParameter));
            return count;
        }

        /// <summary>
        /// Sẽ xóa đi khi hoàn thành task VCBINNB-2656
        /// </summary>
        /// <param name="tenantCode"></param>
        /// <param name="invoiceDateNumber"></param>
        /// <returns></returns>
        public async Task<long> CountInvoiceByTenantAndInvoiceDateOld(string tenantCode, int invoiceDateNumber)
        {
            var query = $@"
                            SELECT COUNT(ROWNUM)
                            FROM ""G1InvoiceHeader"" 
                            WHERE ""CeationDateNumber"" = {invoiceDateNumber}
                                AND ""CreatorStatus"" = {CreatorStatus.ValidateSuccess} 
                                AND ""InvoiceStatus"" = {InvoiceStatusDbtg.Create} 
                                AND ""TenantCode"" = '{tenantCode}'
                        ";

            var count = await _appFactory.VcbIntegratedOracle.Connection.QueryFirstAsync<long>(query);

            return count;
        }

        /// <summary>
        /// Đếm số lượng hóa đơn
        /// </summary>
        /// <param name="tenantCode"></param>
        /// <returns></returns>
        public async Task<long> CountInvoiceBackDateByTenant(string tenantCode)
        {
            var query = $@"
                            SELECT COUNT(ROWNUM)
                            FROM ""G1InvoiceHeader"" 
                            WHERE ""CreatorStatus"" = :CreatorStatus 
                                AND ""InvoiceStatus"" = :InvoiceStatus 
                                AND ""TenantCode"" = :TenantCode
                        ";

            var dicParameter = new Dictionary<string, object>
            {
                {":CreatorStatus", CreatorStatus.WaittingToCreateBackDate},
                {":InvoiceStatus", InvoiceStatusDbtg.Create},
                {":TenantCode", tenantCode},
            };

            var count = await _appFactory.VcbIntegratedOracle.Connection.QueryFirstAsync<long>(query, new DynamicParameters(dicParameter));
            return count;
        }

        /// <summary>
        /// Sẽ xóa đi khi hoàn thành task VCBINNB-2656
        /// </summary>
        /// <param name="tenantCode"></param>
        /// <returns></returns>
        public async Task<long> CountInvoiceBackDateByTenantOld(string tenantCode)
        {
            var query = $@"
                            SELECT COUNT(ROWNUM)
                            FROM ""G1InvoiceHeader"" 
                            WHERE ""CreatorStatus"" = {CreatorStatus.WaittingToCreateBackDate} 
                                AND ""InvoiceStatus"" = {InvoiceStatusDbtg.Create} 
                                AND ""TenantCode"" = '{tenantCode}'
                        ";

            var count = await _appFactory.VcbIntegratedOracle.Connection.QueryFirstAsync<long>(query);

            return count;
        }

        public async Task<int> UpdateAsync(QueryDataModel queryDataModel)
        {
            return await _appFactory.VcbIntegratedOracle.Connection.ExecuteAsync(queryDataModel.Query, queryDataModel.Parameters);
        }

        public async Task<int> UpdateAsync(string query)
        {
            return await _appFactory.VcbIntegratedOracle.Connection.ExecuteAsync(query);
        }

        public int Update(string query)
        {
            return _appFactory.VcbIntegratedOracle.Connection.Execute(query);
        }

        /// <summary>
        /// Đếm số lượng hóa đơn
        /// </summary>
        /// <param name="tenantCode"></param>
        /// <returns></returns>
        public async Task<long> CountInvoiceCancelByTenantAndInvoiceDateAsync(string tenantCode)
        {
            var query = $@"
                            SELECT COUNT(ROWNUM)
                            FROM ""G1InvoiceHeader"" 
                            WHERE ""CreatorStatus"" = :CreatorStatus 
                                AND ""InvoiceStatus"" = :InvoiceStatus 
                                AND ""TenantCode"" = :TenantCode
                        ";

            var dicParameter = new Dictionary<string, object>
            {
                {":CreatorStatus", CreatorStatus.Success.GetHashCode()},
                {":InvoiceStatus", InvoiceStatusDbtg.Delete},
                {":TenantCode", tenantCode},
            };

            var count = await _appFactory.VcbIntegratedOracle.Connection.QueryFirstAsync<long>(query, new DynamicParameters(dicParameter));

            return count;
        }

        /// <summary>
        /// Sẽ xóa đi khi hoàn thành task VCBINNB-2656
        /// </summary>
        /// <param name="tenantCode"></param>
        /// <returns></returns>
        public async Task<long> CountInvoiceCancelByTenantAndInvoiceDateOldAsync(string tenantCode)
        {
            var query = $@"
                            SELECT COUNT(ROWNUM)
                            FROM ""G1InvoiceHeader"" 
                            WHERE ""InvoiceStatus"" = {InvoiceStatusDbtg.Delete} 
                                    AND ""CreatorStatus"" = {CreatorStatus.Success.GetHashCode()}
                                    AND ""TenantCode"" = '{tenantCode}'
                        ";

            var count = await _appFactory.VcbIntegratedOracle.Connection.QueryFirstAsync<long>(query);

            return count;
        }

        /// <summary>
        /// Lấy danh sách hóa đơn
        /// </summary>
        /// <param name="tenantCode"></param>
        /// <param name="size"></param>
        /// <returns></returns>
        public async Task<List<G1InvoiceHeaderEntity>> GetInvoiceToCancelDeleteAsync(string tenantCode, int size = 0)
        {
            var query = new StringBuilder();

            query.Append($@"    SELECT * 
                                FROM ""G1InvoiceHeader"" 
                                WHERE ""CreatorStatus"" = :CreatorStatus 
                                    AND ""InvoiceStatus"" = :InvoiceStatus 
                                    AND ""TenantCode"" = :TenantCode
                         ");

            if (size != 0)
                query.Append($@" FETCH FIRST {size} ROW ONLY ");

            var dicParameter = new Dictionary<string, object>
            {
                {":CreatorStatus", CreatorStatus.Success.GetHashCode()},
                {":InvoiceStatus", InvoiceStatusDbtg.Delete},
                {":TenantCode", tenantCode},
            };

            var data = await _appFactory.VcbIntegratedOracle.Connection.QueryAsync<G1InvoiceHeaderEntity>(query.ToString(), new DynamicParameters(dicParameter));

            return data.ToList();
        }

        /// <summary>
        /// Sẽ xóa đi khi hoàn thành task VCBINNB-2656
        /// </summary>
        /// <param name="tenantCode"></param>
        /// <param name="size"></param>
        /// <returns></returns>
        public async Task<List<G1InvoiceHeaderEntity>> GetInvoiceToCancelDeleteOldAsync(string tenantCode, int size = 0)
        {
            var query = new StringBuilder();

            query.Append($@"    SELECT * 
                                FROM ""G1InvoiceHeader"" 
                                WHERE ""InvoiceStatus"" = {InvoiceStatusDbtg.Delete} 
                                    AND ""CreatorStatus"" = {CreatorStatus.Success.GetHashCode()}
                                    AND ""TenantCode"" = '{tenantCode}'
                         ");

            if (size != 0)
                query.Append($@" FETCH FIRST {size} ROW ONLY ");

            var data = await _appFactory.VcbIntegratedOracle.Connection.QueryAsync<G1InvoiceHeaderEntity>(query.ToString());

            return data.ToList();
        }

        /// <summary>
        /// Đếm số lượng bản ghi báo cáo 01
        /// </summary>
        /// <param name="reportMonth"></param>
        /// <param name="reportYear"></param>
        /// <param name="tenantCode"></param>
        /// <returns></returns>
        public async Task<long> CountReport01ByTenantAndDate(int reportMonth, int reportYear, string tenantCode)
        {
            var query = $@"
                            SELECT COUNT(ROWNUM)
                            FROM ""TaxReport01Header"" 
                            WHERE ""ReportMonth"" = :ReportMonth
                                AND ""ApproveStatus"" = :ApproveStatus 
                                AND ""ReportYear"" = :ReportYear 
                                AND ""TenantCode"" = :TenantCode
                        ";
            var dicParameter = new Dictionary<string, object>
            {
                {":ReportMonth", reportMonth},
                {":ApproveStatus", ApproveStatus.DaDuyet.GetHashCode()},
                {":ReportYear", reportYear},
                {":TenantCode", tenantCode},
            };
            var count = await _appFactory.VnisCoreOracle.Connection.QueryFirstAsync<long>(query, new DynamicParameters(dicParameter));

            return count;
        }
        /// <summary>
        /// Sẽ xóa đi khi hoàn thành task VCBINNB-2656
        /// </summary>
        /// <param name="reportMonth"></param>
        /// <param name="reportYear"></param>
        /// <param name="tenantCode"></param>
        /// <returns></returns>
        public async Task<long> CountReport01ByTenantAndDateOld(int reportMonth, int reportYear, string tenantCode)
        {
            var query = $@"
                            SELECT COUNT(ROWNUM)
                            FROM ""TaxReport01Header"" 
                            WHERE ""ReportMonth"" = {reportMonth}
                                AND ""ApproveStatus"" = {(int) ApproveStatus.DaDuyet} 
                                AND ""ReportYear"" = {reportYear} 
                                AND ""TenantCode"" = '{tenantCode}'
                        ";

            var count = await _appFactory.VnisCoreOracle.Connection.QueryFirstAsync<long>(query);

            return count;
        }

        /// <summary>
        /// Lấy danh sách MonitorInvoiceTemplate
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<MonitorInvoiceTemplateEntity> GetMonitorTemplate(long id)
        {
            var query = $@"
                            SELECT *
                            FROM ""MonitorInvoiceTemplate"" 
                            WHERE ""Id"" = :Id
                        ";
            var dicParameter = new Dictionary<string, object>
            {
                {":Id", id}
            };
            var data = await _appFactory.VnisCoreOracle.Connection.QueryFirstAsync<MonitorInvoiceTemplateEntity>(query, new DynamicParameters(dicParameter));

            return data;
        }

        /// <summary>
        /// Sẽ xóa đi khi hoàn thành task VCBINNB-2656
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<MonitorInvoiceTemplateEntity> GetMonitorTemplateOld(long id)
        {
            var query = $@"
                            SELECT *
                            FROM ""MonitorInvoiceTemplate"" 
                            WHERE ""Id"" = {id}
                        ";

            var data = await _appFactory.VnisCoreOracle.Connection.QueryFirstAsync<MonitorInvoiceTemplateEntity>(query);

            return data;
        }
    }
}
