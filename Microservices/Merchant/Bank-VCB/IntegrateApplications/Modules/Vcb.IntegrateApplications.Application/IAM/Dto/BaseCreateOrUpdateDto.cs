using System.ComponentModel.DataAnnotations;

namespace VCB.Integrate.Applications.Application.IAM.Dto
{
    public class BaseCreateOrUpdateDto
    {
        /// <summary>
        /// Mã cán bộ
        /// </summary>
        [Required]
        public string MaCB { get; set; }
        /// <summary>
        /// Tài khoản đăng nhập, tài khoản này sẽ dùng để gửi email cho cán bộ, thông thường sẽ lấy tài khoản AD (tài khoản OIG) để gửi sang ứng dụng tạo
        /// </summary>
        [Required]
        public string UserLogin
        {
            get => _userLogin;
            set => _userLogin = !string.IsNullOrEmpty(value) ? value.Trim().ToLower() : value;
        }

        private string _userLogin;

        public string Tai<PERSON>hoanAD { get; set; }
        /// <summary>
        /// Tài khoản Corebank
        /// </summary>
        public string UserHost { get; set; }
        /// <summary>
        /// Mã đơn vị trên Host (trùng với madv với HRM mới, HRm cũ là deptid)
        /// </summary>
        public string MaDVHost { get; set; }

        private string _tellerID;

        public string TellerID {
            get { return _tellerID; }
            set
            {
                _tellerID = string.IsNullOrWhiteSpace(value)
                    ? value
                    : value.TrimStart('0');
            }
        }

        public string OfficerCode { get; set; }
        /// <summary>
        /// Yêu cầu thực hiện
        /// 1 – tạo tài khoản
        /// </summary>
        [Required]
        public short Action { get; set; }
        /// <summary>
        /// Mã chi nhánh
        /// </summary>
        [Required]
        public string MaCN { get; set; }

        /// <summary>
        /// Mã phòng ban trung tâm
        /// </summary>
        [Required]
        public string MaDV { get; set; }

        /// <summary>
        /// Họ tên user
        /// </summary>
        [Required]
        public string FullName { get; set; }

        /// <summary>
        /// Quyền của user
        /// </summary>
        [Required]
        public string Role { get; set; }

        /// <summary>
        /// Email của user
        /// </summary>
        [Required]
        public string Email { get; set; }

        /// <summary>
        /// STT Request
        /// </summary>
        [Required]
        public string RequestID { get; set; }
    }
}