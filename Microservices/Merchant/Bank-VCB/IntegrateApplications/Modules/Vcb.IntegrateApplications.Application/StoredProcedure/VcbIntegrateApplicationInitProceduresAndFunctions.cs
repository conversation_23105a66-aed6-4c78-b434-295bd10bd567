using VCB.Integrate.Applications.Application.StoredProcedure.Procedures;
using VnisCore.AuthDatabase.Oracle.EntityFrameworkCore.EntityFrameworkCore;

namespace VCB.Integrate.Applications.Application.StoredProcedure
{
    public static class VcbIntegrateApplicationInitProceduresAndFunctions
    {
        public static void InitProcedures(VnisCoreAuthDatabaseOracleDbContext dbContext)
        {
            VcbIntegrateApplicationInitProcedure.CreateProcedureIamUserCheckExists(dbContext);
        }

        public static void InitFunctions(VnisCoreAuthDatabaseOracleDbContext dbContext)
        {
        }

    }
}