using MediatR;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using VnisCore.Portal.Application.Models.Responses;

namespace VnisCore.Portal.Application.Models.Requests
{
    public class GetPublicRequestModel : IRequest<List<GetPortalResponseModel>>
    {
        [Required(ErrorMessage = "Vnis.BE.Portal.GetPublicRequestModel.TransactionId.Required")]
        public string TransactionId { get; set; }

        //[Required(ErrorMessage = "Chưa nhập mã số thuế")]
        //public string SellerTaxCode { get; set; }

        [Required(ErrorMessage = "Vnis.BE.Portal.GetPublicRequestModel.Captcha.Required")]
        public string Captcha { get; set; }

        [Required(ErrorMessage = "Vnis.BE.Portal.GetPublicRequestModel.Secret.Required")]
        public string Secret { get; set; }
    }
}
