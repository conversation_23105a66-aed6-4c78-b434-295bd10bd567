using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.BaseEntities;

namespace VnisCore.Portal.Application.Repositories
{
    public interface IInvoiceHeaderRepository<THeader> where THeader : BaseInvoiceHeader
    {
        Task<THeader> GetByIdAsync(long id);

        /// <summary>
        /// lấy thông tin header hóa đơn theo code
        /// </summary>
        /// <param name="id"></param>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        Task<THeader> GetByIdAsync(long id, Guid tenantId);

        Task<List<THeader>> GetByIdsAsync(List<long> ids);

        /// <summary>
        /// Lấy hóa đơn theo Mẫu số + <PERSON>ý hiệu + S<PERSON> hóa đơn
        /// </summary>
        /// <param name="tenantId"></param>
        /// /// <param name="templateNo"></param>
        /// /// <param name="serialNo"></param>
        /// /// <param name="invoiceNo"></param>
        /// <returns></returns>
        Task<THeader> GetByIndexingAsync(Guid tenantId, short templateNo, string serialNo, string invoiceNo);

        Task<THeader> GetPreviousInvoiceNo(Guid tenantId, long invoiceTemplateId, int number);

        Task<THeader> GetNextInvoiceNo(Guid tenantId, long invoiceTemplateId, int number);

        Task<THeader> GetLastInvoiceNotCancelAndDelete(Guid tenantId, long templateId);

        /// <summary>
        /// lấy hóa đơn theo các thông tin public
        /// </summary>
        /// <param name="templateNo"></param>
        /// <param name="serialNo"></param>
        /// <param name="invoiceNo"></param>
        /// <param name="id"></param>
        /// <param name="transactionId"></param>
        /// <returns></returns>
        Task<THeader> GetInvoicePublicAsync(short templateNo, string serialNo, string invoiceNo, long id, string transactionId);
    }
}
