using Core.Domain.Repositories;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.BaseEntities;

namespace VnisCore.Portal.Application.Repositories
{
    public class InvoiceMediaRepository<T> : IInvoiceMediaRepository<T>
        where T : BaseInvoiceMedia
    {
        private readonly IRepository<T, long> _repoXml;

        public InvoiceMediaRepository(IRepository<T, long> repoXml)
        {
            _repoXml = repoXml;
        }

        public async Task<T> GetByIdAsync(long id)
        {
            return await _repoXml.FirstOrDefaultAsync(x => x.Id == id);
        }

        public async Task<T> GetLastByInvoiceIdAsync(long invoiceId)
        {
            return await _repoXml.Where(x => x.InvoiceHeaderId == invoiceId)
                              //.OrderBy(x => x.CreationTime)
                              .OrderByDescending(x => x.Id)
                              .FirstOrDefaultAsync();
        }

        public async Task<List<T>> QueryByIdAsync(List<long> ids)
        {
            return await _repoXml.Where(x => ids.Contains(x.Id)).ToListAsync();
        }
    }
}
