using Core.AspNetCore.Mvc;
using Core.Shared.Dto;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Vcb.Portal.Application.Interfaces;
using Vcb.Portal.Application.Models.Requests;
using VnisCore.Portal.Application;

namespace Vcb.Portal.Application.Services
{
    [Route(Utilities.ApiUrlBase)]
    public class PortalExportApiService : AbpController
    {
        private readonly IPortalExportBusiness _portalExportBusiness;

        public PortalExportApiService(
            IPortalExportBusiness portalExportBusiness)
        {
            _portalExportBusiness = portalExportBusiness;
        }

        /// <summary>
        /// xuất excel danh sách hóa đơn
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("excel")]
        public async Task<FileDto> ExportExcel([FromBody] PortalExportRequestModel request)
        {
            if (CurrentTenant.Id != null) request.TenantId = CurrentTenant.Id.Value;
            if (CurrentUser.Id != null) request.UserId = CurrentUser.Id.Value;

            return await _portalExportBusiness.ExportPortalExcel(request);
        }
    }
}
