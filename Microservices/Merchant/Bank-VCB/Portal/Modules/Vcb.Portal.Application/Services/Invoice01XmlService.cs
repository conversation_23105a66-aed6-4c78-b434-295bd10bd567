using Core;
using Core.Localization.Resources.AbpLocalization;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.Extensions.Localization;
using Nest;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.BaseEntities;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Portal.Application.Interfaces;
using VnisCore.Portal.Application.Repositories;

namespace VnisCore.Portal.Application.Services
{
    public class Invoice01XmlService : IInvoiceMediaService
    {
        private readonly IInvoiceHeaderRepository<Invoice01HeaderEntity> _repoInvoice01Header;
        private readonly IInvoice01HeaderRepository _repoBaseInvoice01Header;
        private readonly IInvoiceMediaRepository<Invoice01XmlEntity> _repoInvoice01Xml;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        public Invoice01XmlService(IInvoiceHeaderRepository<Invoice01HeaderEntity> repoInvoice01Header,
            IInvoiceMediaRepository<Invoice01XmlEntity> repoInvoice01Xml,
            IInvoice01HeaderRepository repoBaseInvoice01Header,
            IStringLocalizer<CoreLocalizationResource> localizier)
        {
            _repoInvoice01Header = repoInvoice01Header;
            _repoInvoice01Xml = repoInvoice01Xml;
            _localizier = localizier;
            _repoBaseInvoice01Header = repoBaseInvoice01Header;
        }

        public async Task<BaseInvoiceMedia> GetByInvoiceAsync(Guid tenantId, short templateNo, string serialNo, string invoiceNo)
        {
            var invoiceHeader = await _repoInvoice01Header.GetByIndexingAsync(tenantId, templateNo, serialNo, invoiceNo);

            if (invoiceHeader == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.Portal.CannotFindInvoice"]);

            var invoiceXml = await _repoInvoice01Xml.GetLastByInvoiceIdAsync(invoiceHeader.Id);

            if (invoiceXml == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.Portal.CannotFindXml"]);

            ////download file từ minio
            //var pathFileMinio = $"{MediaFileType.InvoiceXml}/{tenantCode}/{invoiceXml.CreatedAtUtc.Year}/{invoiceXml.CreatedAtUtc.Month:00}/{invoiceXml.CreatedAtUtc.Hour:00}/{invoiceXml.PhysicalFileName}";
            //var bytes = await _fileService.DownloadAsync(pathFileMinio);

            return invoiceXml;
        }

        public async Task<BaseInvoiceMedia> GetByIdInvoiceAsync(long invoiceId)
        {
            var invoiceHeader = await _repoInvoice01Header.GetByIdAsync(invoiceId);

            if (invoiceHeader == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.Portal.CannotFindInvoice"]);
            var invoiceXml = await _repoInvoice01Xml.GetLastByInvoiceIdAsync(invoiceHeader.Id);

            if (invoiceXml == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.Portal.CannotFindXml"]);

            ////download file từ minio
            //var pathFileMinio = $"{MediaFileType.InvoiceXml}/{tenantCode}/{invoiceXml.CreatedAtUtc.Year}/{invoiceXml.CreatedAtUtc.Month:00}/{invoiceXml.CreatedAtUtc.Hour:00}/{invoiceXml.PhysicalFileName}";
            //var bytes = await _fileService.DownloadAsync(pathFileMinio);

            return invoiceXml;
        }

        public async Task<BaseInvoiceMedia> GetMediaByIdInvoiceAsync(long invoiceId)
        {
            var invoiceXml = await _repoInvoice01Xml.GetLastByInvoiceIdAsync(invoiceId);

            if (invoiceXml == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.Portal.CannotFindXml"]);

            return invoiceXml;
        }

        public async Task<BaseInvoiceHeader> GetInvoiceAsync(Guid tenantId, short templateNo, string serialNo, string invoiceNo)
        {
            var invoiceHeader = await _repoInvoice01Header.GetByIndexingAsync(tenantId, templateNo, serialNo, invoiceNo);

            if (invoiceHeader == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.Portal.CannotFindInvoice"]);

            return invoiceHeader;
        }

        public async Task<List<Invoice01HeaderEntity>> GetListInvoiceAsync(List<long> ids, Guid tenantId, string buyerCode)
        {
            var invoiceHeader = await _repoBaseInvoice01Header.GetByIdsAsync(tenantId, buyerCode, ids);

            if (invoiceHeader == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.Portal.CannotFindInvoice"]);

            return invoiceHeader;
        }

        public async Task<List<Invoice01HeaderEntity>> GetListInvoiceAsync(List<long> ids, string buyerCode)
        {
            var invoiceHeader = await _repoBaseInvoice01Header.GetByIdsAsync(buyerCode, ids);

            if (invoiceHeader == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.Portal.CannotFindInvoice"]);

            return invoiceHeader;
        }

        public async Task<List<long>> GetIdInvoiceAsync(List<long> ids, string buyerCode)
        {
            var invoiceHeader = await _repoBaseInvoice01Header.GetByIdsAsync(buyerCode, ids);

            if (invoiceHeader == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.Portal.CannotFindInvoice"]);

            return invoiceHeader.Select(x => x.Id).ToList();
        }

        public async Task<BaseInvoiceHeader> GetInvoiceAsync(short templateNo, string serialNo, string invoiceNo, long id, string transactionId)
        {
            var invoiceHeader = await _repoInvoice01Header.GetInvoicePublicAsync(templateNo, serialNo, invoiceNo, id, transactionId);

            if (invoiceHeader == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.ExportPdf.Official.InvoiceNotFound"]);

            return invoiceHeader;
        }

        public async Task<BaseInvoiceHeader> GetInvoiceAsync(string buyerCode, short templateNo, string serialNo, string invoiceNo, long id)
        {
            var invoice01Entity = await _repoBaseInvoice01Header.GetInvoiceQueryPrivateAsync(buyerCode, templateNo, serialNo, invoiceNo, id);

            if (invoice01Entity == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.ExportPdf.Official.InvoiceNotFound"]);

            return invoice01Entity;
        }

        public async Task<BaseInvoiceHeader> GetInvoiceByIdAsync(long invoiceId)
        {
            var invoiceHeader = await _repoInvoice01Header.GetByIdAsync(invoiceId);

            if (invoiceHeader == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.ExportPdf.Official.InvoiceNotFound"]);

            return invoiceHeader;
        }
    }
}
