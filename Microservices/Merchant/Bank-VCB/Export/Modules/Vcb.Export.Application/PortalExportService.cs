using Core.AspNetCore.Mvc;
using Core.Shared.Dto;
using Core.Shared.Factory;

using Microsoft.AspNetCore.Mvc;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Vcb.Export.Application.Portal.Interface;
using Vcb.Export.Application.Portal.Models.Requests;

using VnisCore.Export.Application;

namespace Vcb.Export.Application
{
    [Route(Utilities.ApiUrlBase)]
    public class PortalExportService : AbpController
    {
        private readonly IAppFactory _factory;
        private readonly IPortalExportBusiness _portalExportBusiness;

        public PortalExportService(IAppFactory factory, IPortalExportBusiness portalExportBusiness)
        {
            _factory = factory;
            _portalExportBusiness = portalExportBusiness;
        }

        /// <summary>
        /// xuất excel danh sách hóa đơn
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("excel")]
        public async Task<FileDto> ExportExcel([FromBody] PortalExportRequestModel request)
        {
            if (CurrentTenant.Id != null) request.TenantId = CurrentTenant.Id.Value;
            if (CurrentUser.Id != null) request.UserId = CurrentUser.Id.Value;

            return await _portalExportBusiness.ExportPortalExcel(request);
        }
    }
}
