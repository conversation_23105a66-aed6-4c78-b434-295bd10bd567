using Core.Shared.Models;
using System;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Export.Application.Factories.Models;

namespace VnisCore.Export.Application.Factories.Repositories
{
    public interface IInvoice02HeaderRepository
    {
        /// <summary>
        /// query danh sách hóa đơn
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<Paged<Invoice02HeaderEntity>> PagingAsync(Guid tenantId, PagingInvoiceModel model);
        Task<int> CountAsNoTrackingAsync(Guid tenantCode, PagingInvoiceModel model);

        Task<Paged<Invoice02HeaderEntity>> PagingAsNoTrackingAsync(Guid tenantId, PagingInvoiceModel query);
    }
}
