using Core.Shared.Attributes;

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Vcb.Export.Application.DataFluctuationReport.Models
{
    public class DataFluctuationReportRequestModel
    {
        [DataType(DataType.Date)]
        [LessThanCurrentDate(ErrorMessage = "Vnis.BE.Export.DataFluctuationReportRequestModel.FromDate.LessThanCurrentDate")]
        [LessThanDate("ToDate", ErrorMessage = "Vnis.BE.Export.DataFluctuationReportRequestModel.FromDate.LessThanDate")]
        public DateTime? FromDate { get; set; }

        [DataType(DataType.Date)]
        [LessThanCurrentDate(ErrorMessage = "Vnis.BE.Export.DataFluctuationReportRequestModel.ToDate.ErrorMessage")]
        [MoreThanDate("FromDate", ErrorMessage = "Vnis.BE.Export.DataFluctuationReportRequestModel.ToDate.MoreThanDate")]
        public DateTime? ToDate { get; set; }
        public Guid TenantId { get; set; }
    }
}
