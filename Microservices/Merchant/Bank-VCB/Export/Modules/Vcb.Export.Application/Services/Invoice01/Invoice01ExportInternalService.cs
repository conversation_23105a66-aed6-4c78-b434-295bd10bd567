using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Export.Application.Abstractions;
using VnisCore.Export.Application.Factories.Repositories;
using VnisCore.Export.Application.Factories.Services;
using VnisCore.Export.Application.Interfaces;
using VnisCore.Export.Application.Models.Invoice01s;
using VnisCore.Export.Application.Models.Requests;
using VnisCore.Export.Application.Models.Responses;

namespace VnisCore.Export.Application.Services.Invoice01
{
    public class Invoice01ExportInternalService : BaseInvoiceExport01Service, IInvoiceExportService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IAppFactory _appFactory;
        public string REQUEST_DATE;

        public Invoice01ExportInternalService(
            IServiceProvider serviceProvider,
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizier,
            ILogger<Invoice01ExportInternalService> logger) : base(serviceProvider, appFactory, localizier, logger)
        {
            _serviceProvider = serviceProvider;
            _appFactory = appFactory;
        }


        /// <summary>
        /// Export theo định dạng file csv
        /// </summary>
        /// <param name="exportJob"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        public override async Task<ExportInvoiceApiResponseModel> ExportInvoiceAsync(Invoice01ExportJobEntity exportJob, ExportInvoiceApiRequestModel request)
        {
            //lấy dữ liệu và export ra file csv
            var invoices = await GetInvoicesAsync(request);

            //lấy cấu hình hiển thị các giá trị extra trên bảng kê
            //var fieldHeaderNames = "OperationName;";
            //var tellSeqNames = "TellSeq;";
            //var fieldDetailNames = "RefNo;";
            //var headerFields = await GetHeaderFieldSpecificationAsync(request.TenantId, fieldHeaderNames);
            //var detailFields = await GetDetailFieldSpecificationAsync(request.TenantId, fieldDetailNames);
            //var headerFieldstellSeq = await GetHeaderFieldSpecificationAsync(request.TenantId, tellSeqNames);

            //var operationName = headerFields.FirstOrDefault(x => x.FieldName == "OperationName");
            //var refNo = detailFields.FirstOrDefault(x => x.FieldName == "RefNo");
            //var tellSeq = headerFieldstellSeq.FirstOrDefault(x => x.FieldName == "TellSeq");

            var taxes = StaticData.TaxDefaults.ToDictionary(x => x.Value.Item1, x => x.Value.Item4);

            // tạo file Csv
            var exportHeaders = new List<string>()
            {
                    "STT",
                    "Ngày hóa đơn",
                    "Mẫu số hoá đơn",
                    "Ký hiệu hóa đơn",
                    "TTV",
                    "SEQ",
                    "Số hóa đơn",
                    "Mã số thuế người mua",
                    "Tên người mua",
                    "Địa chỉ người mua",
                    "Doanh số bán chưa có thuế",
                    "Tiền thuế GTGT",
                    "Tổng cộng tiền hàng",
                    "Tiền tệ",
                    "Tình trạng hóa đơn",
                    "User",
                    "Tình trạng ký",
            };

            var exportRecords = new List<List<string>>();

            for (int i = 0; i < invoices.Count; i++)
            {
                var invoice = invoices.ElementAt(i);

                var isAdjHeader = (invoice.InvoiceStatus == InvoiceStatus.DieuChinhDinhDanh.GetHashCode()) ? true : false;

                var signStatus = EnumExtension.TryToEnum<SignStatus>(invoice.SignStatus);
                var signStatusExport = (signStatus != default(SignStatus) && !int.TryParse(signStatus.ToString(), out int outSignStatus))
                                    ? signStatus.ToString()
                                    : invoice.SignStatus.ToString();

                var invoiceStatusDisplay = (invoice.InvoiceStatus != InvoiceStatus.XoaBo.GetHashCode() && invoice.InvoiceStatus != InvoiceStatus.XoaHuy.GetHashCode())
                                        ? EnumExtension.TryToEnum<InvoiceStatus>(invoice.InvoiceStatus).ToString()
                                        : (invoice.InvoiceStatus == InvoiceStatus.XoaBo.GetHashCode() ? "XoaDaDuyet" : "XoaChoDuyet");

                var obj = new List<string> {
                            (i + 1).ToString(),
                            invoice.InvoiceDate.ToString("dd/MM/yyyy"),
                            invoice.TemplateNo.ToString(),
                            invoice.SerialNo,
                            !string.IsNullOrEmpty(invoice.CreatorErp) ? invoice.CreatorErp.Replace(",", string.Empty).Replace(Environment.NewLine, string.Empty) : "",
                            invoice.InvoiceHeaderExtras != null && invoice.InvoiceHeaderExtras.Any(x => x.FieldName == "TellSeq") ? invoice.InvoiceHeaderExtras.First(x => x.FieldName == "TellSeq").FieldValue?.Replace(",", string.Empty).Replace(Environment.NewLine, string.Empty) : string.Empty,
                            //invoice.InvoiceHeaderExtras != null && invoice.InvoiceHeaderExtras.Any(x => x.InvoiceHeaderFieldId == tellSeq.Id) ? invoice.InvoiceHeaderExtras.FirstOrDefault(x => x.InvoiceHeaderFieldId == tellSeq.Id).FieldValue.Replace(",", string.Empty).Replace(Environment.NewLine, string.Empty) : string.Empty,
                            invoice.InvoiceNo,
                            invoice.BuyerTaxCode,
                            invoice.BuyerFullName.Replace(",", string.Empty).Replace(Environment.NewLine, string.Empty),
                            invoice.BuyerAddressLine.Replace(",", string.Empty).Replace(Environment.NewLine, string.Empty),
                            isAdjHeader ? "0" : invoice.TotalAmount.ToString().Replace(",", "."),
                            isAdjHeader ? "0" : invoice.TotalVatAmount.ToString().Replace(",", "."),
                            isAdjHeader ? "0" : invoice.TotalPaymentAmount.ToString().Replace(",", "."),
                            invoice.ToCurrency,
                            invoiceStatusDisplay,
                            invoice.UserNameCreator,
                            signStatusExport,
                        };

                if (invoice.InvoiceDetails == null)
                {
                    invoice.InvoiceDetails = new List<Invoice01DetailExportModel>
                        { new Invoice01DetailExportModel () };
                }

                exportRecords.Add(obj);
            }

            var exportCsv = new StringBuilder();

            exportRecords.ForEach(line =>
            {
                exportCsv.AppendLine(string.Join(",", line));
            });

            //Tạo file csv
            using var scoped = _serviceProvider.CreateScope();
            var configuation = scoped.ServiceProvider.GetService<IConfiguration>();
            var pathFolder = Path.Combine(configuation.GetSection("FileExport:PathFolder").Value.Trim(), exportJob.TaskId.ToString());

            if (!Directory.Exists(pathFolder))
                Directory.CreateDirectory(pathFolder);

            var filePath = Path.Combine(pathFolder, $"{exportJob.CurrentPage + 1}.csv");
            await File.WriteAllTextAsync(filePath, $"{string.Join(",", exportHeaders)}{Environment.NewLine}{exportCsv}".ToString(), Encoding.UTF8);
            var bytes = await File.ReadAllBytesAsync(filePath);

            //xóa file
            File.Delete(filePath);

            return new ExportInvoiceApiResponseModel
            {
                Bytes = bytes,
                FileName = Path.GetFileName(filePath)
            };
        }

        public override async Task<List<Invoice01ExportModel>> GetInvoicesAsync(ExportInvoiceApiRequestModel request)
        {
            using var scope = _serviceProvider.CreateScope();
            //var uowInvoice01 = scope.ServiceProvider.GetService<IInvoice01UnitOfWork>();

            List<long> ids = await GetInvoiceHeaderIdByHeaderExtra(request.TenantId, request.HeaderExtras);
            var pagingModel = ToPagingModel(request);
            pagingModel.Ids = ids;

            var repoInvoice01 = _serviceProvider.GetService<IInvoice01HeaderRepository>();
            var invoiceHeaders = (await repoInvoice01.PagingAsNoTrackingAsync(request.TenantId, pagingModel))
                                       .Data
                                       .Select(x => (Invoice01ExportModel)x)
                                       .ToDictionary(x => x.Id, x => x);

            var idInvoiceHeaders = invoiceHeaders.Keys.ToList();

            //detail
            var repoInvoiceDetail = _serviceProvider.GetService<IInvoiceDetailRepository<Invoice01DetailEntity>>();
            var entityInvoiceDetails = await repoInvoiceDetail.QueryByInvoiceHeaderIdsAsNoTrackingAsync(idInvoiceHeaders.ToList());

            //chuyen ve model
            var invoiceDetails = entityInvoiceDetails.Select(x => (Invoice01DetailExportModel)x).ToList();

            //add DetailExtras
            //foreach (var item in invoiceDetails)
            //{
            //    if (item.ExtraProperties != null && item.ExtraProperties.Any())
            //    {
            //        var extraProperties = new List<InvoiceDetailExtraModel>();
            //        if (item.ExtraProperties.ContainsKey("invoiceDetailExtras") && !string.IsNullOrEmpty(item.ExtraProperties["invoiceDetailExtras"]))
            //        {
            //            extraProperties = JsonConvert.DeserializeObject<List<InvoiceDetailExtraModel>>(item.ExtraProperties["invoiceDetailExtras"]);
            //        }
            //        if (extraProperties.Any())
            //        {
            //            var detailExtras = extraProperties.GroupBy(x => x.InvoiceDetailId).ToDictionary(x => x.Key, x => x.ToList());
            //            if (detailExtras.ContainsKey(item.Id))
            //                item.InvoiceDetailExtras = detailExtras[item.Id].Select(x => new Invoice01DetailExtraExportModel
            //                {
            //                    FieldValue = x.FieldValue,
            //                    InvoiceDetailFieldId = x.InvoiceDetailFieldId,
            //                    InvoiceDetailId = x.InvoiceDetailId,
            //                }).ToList();
            //        }
            //    }
            //}

            //invoiceDetails.ForEach(x => {
            //    if (x.ExtraProperties != null &&
            //        x.ExtraProperties.Any() &&
            //        x.ExtraProperties.ContainsKey("invoiceDetailExtras") &&
            //        !string.IsNullOrEmpty(x.ExtraProperties["invoiceDetailExtras"]))
            //    {
            //        x.InvoiceDetailExtras = JsonConvert.DeserializeObject<List<Invoice01DetailExtraExportModel>>(x.ExtraProperties["invoiceDetailExtras"]);
            //        x.ExtraProperties = null;
            //    }
            //});

            var groupInvoiceDetails = invoiceDetails.GroupBy(x => x.InvoiceHeaderId)
                                                    .ToDictionary(x => x.Key, x => x.ToList());

            //add details
            foreach (var item in groupInvoiceDetails)
            {
                if (invoiceHeaders.ContainsKey(item.Key))
                    invoiceHeaders[item.Key].InvoiceDetails = item.Value;
            }

            //add headerExtras
            //foreach (var item in invoiceHeaders.Values.ToList())
            //{
            //    if (item.ExtraProperties != null && item.ExtraProperties.Any())
            //    {
            //        if (item.ExtraProperties.ContainsKey("invoiceHeaderExtras") && !string.IsNullOrEmpty(item.ExtraProperties["invoiceHeaderExtras"]))
            //        {
            //            var headerExtras = JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(item.ExtraProperties["invoiceHeaderExtras"]);
            //            if (headerExtras.Any())
            //                item.InvoiceHeaderExtras = headerExtras.Select(x => new Invoice01HeaderExtraExportModel
            //                {
            //                    FieldValue = x.FieldValue,
            //                    InvoiceHeaderFieldId = x.InvoiceHeaderFieldId
            //                }).ToList();
            //        }
            //    }
            //}

            foreach (var item in invoiceHeaders)
            {
                if (item.Value.ExtraProperties != null &&
                    item.Value.ExtraProperties.Any() &&
                    item.Value.ExtraProperties.ContainsKey("invoiceHeaderExtras") &&
                    !string.IsNullOrEmpty(item.Value.ExtraProperties["invoiceHeaderExtras"]))
                {
                    item.Value.InvoiceHeaderExtras = JsonConvert.DeserializeObject<List<Invoice01HeaderExtraExportModel>>(item.Value.ExtraProperties["invoiceHeaderExtras"]);
                    item.Value.ExtraProperties = null;
                }
            }

            return invoiceHeaders.Select(x => x.Value).ToList();
        }

        public override async Task<byte[]> ConvertToExcelAsync(string path, List<string> lines, ExportInvoiceApiRequestModel request)
        {
            var data = await GetInvoicesAsync(request);
            var excelPath = Path.Combine(path, "outputConvertCSVToExcelFormats.xlsx");

            var fromDate = request.CreateFromDate.HasValue ? request.CreateFromDate.Value.ToString("dd/MM/yyyy") : data.Min(x => x.InvoiceDate).ToString("dd/MM/yyyy");
            var toDate = request.CreateToDate.HasValue ? request.CreateToDate.Value.ToString("dd/MM/yyyy") : data.Max(x => x.InvoiceDate).ToString("dd/MM/yyyy");

            using (var package = new ExcelPackage(new FileInfo(excelPath)))
            {
                var worksheet = package.Workbook.Worksheets.Add("worksheet");

                #region Tiêu đề
                worksheet.Row(1).Style.Font.Name = "Times New Roman";
                worksheet.Row(2).Style.Font.Name = "Times New Roman";
                worksheet.Row(3).Style.Font.Name = "Times New Roman";
                worksheet.Row(4).Style.Font.Name = "Times New Roman";
                worksheet.Row(5).Style.Font.Name = "Times New Roman";
                worksheet.Row(6).Style.Font.Name = "Times New Roman";

                worksheet.Row(1).Style.Font.Bold = true;
                worksheet.Row(2).Style.Font.Bold = true;
                worksheet.Row(3).Style.Font.Bold = true;
                worksheet.Row(4).Style.Font.Bold = true;
                worksheet.Row(6).Style.Font.Bold = true;

                worksheet.Cells[1, 1].Value = $"{_appFactory.CurrentTenant.Name}";
                worksheet.Cells[2, 1].Value = $"{_appFactory.CurrentTenant.Address}";
                worksheet.Cells[3, 1].Value = $"Mã số thuế: {_appFactory.CurrentTenant.TaxCode}";
                worksheet.Cells[4, 2].Value = "BẢNG KÊ TỔNG HỢP";
                worksheet.Cells[5, 10].Value = $"Từ ngày: {fromDate} Đến ngày: {toDate}";
                worksheet.Cells[5, 10].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                worksheet.Cells[1, 1, 1, 10].Merge = true;
                worksheet.Cells[4, 2, 4, 10].Merge = true;
                worksheet.Cells[2, 1, 2, 10].Merge = true;
                worksheet.Cells[3, 1, 3, 10].Merge = true;
                worksheet.Cells[5, 10, 5, 14].Merge = true;
                worksheet.Row(4).Style.Font.Size = 20;

                worksheet.Row(1).Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                worksheet.Row(2).Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                worksheet.Row(3).Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                worksheet.Row(4).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                worksheet.Row(6).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;


                //Tiêu đề
                var headerRow = new List<string[]>()
                {
                    lines[0].Split(",")
                };
                worksheet.Cells[6, 1, 6, headerRow.Count()].LoadFromArrays(headerRow);

                #endregion

                for (int i = 1; i < lines.Count; i++)
                {
                    var bodyRow = new List<string[]>()
                    {
                        lines[i].Split(",")
                    };
                    var row = bodyRow[0];
                    if (row.Length == 1)
                        continue;

                    worksheet.Cells[i + 6, 1].Value = row[0];
                    worksheet.Cells[i + 6, 2].Value = row[1];
                    worksheet.Cells[i + 6, 3].Value = row[2];
                    worksheet.Cells[i + 6, 4].Value = row[3];
                    worksheet.Cells[i + 6, 5].Value = row[4];
                    worksheet.Cells[i + 6, 6].Value = row[5];
                    worksheet.Cells[i + 6, 7].Value = row[6];
                    worksheet.Cells[i + 6, 8].Value = row[7];
                    worksheet.Cells[i + 6, 9].Value = row[8];
                    worksheet.Cells[i + 6, 10].Value = row[9];

                    worksheet.Cells[i + 6, 11].Value = row[10] != "" ? decimal.Parse(row[10].ToString().Replace(".", ",")) : 0;
                    worksheet.Cells[i + 6, 11].Style.Numberformat.Format = "#,##0.0000";

                    worksheet.Cells[i + 6, 12].Value = row[11] != "" ? decimal.Parse(row[11].ToString().Replace(".", ",")) : 0;
                    worksheet.Cells[i + 6, 12].Style.Numberformat.Format = "#,##0.0000";

                    worksheet.Cells[i + 6, 13].Value = row[12] != "" ? decimal.Parse(row[12].ToString().Replace(".", ",")) : 0;
                    worksheet.Cells[i + 6, 13].Style.Numberformat.Format = "#,##0.0000";

                    worksheet.Cells[i + 6, 14].Value = row[13];
                    worksheet.Cells[i + 6, 15].Value = row[14];
                    worksheet.Cells[i + 6, 16].Value = row[15];
                    worksheet.Cells[i + 6, 17].Value = row[16];

                }
                package.Save();
            }
            var bytes = await File.ReadAllBytesAsync(excelPath);
            return bytes;
        }
    }
}
