using System;
using System.Collections.Generic;
using System.Linq;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;

namespace VnisCore.Export.Application.Models.Invoice01s
{
    public class Invoice01DetailExportModel
    {
        public long Id { get; set; }

        public int Index { get; set; }

        public long InvoiceHeaderId { get; set; }

        public Guid TenantId { get; set; }

        /// <summary>
        /// Ghi chú
        /// </summary>
        public string Note { get; set; }

        /// <summary>
        /// code bản ghi hàng hóa
        /// </summary>
        public string ProductCode { get; set; }

        public long IdProduct { get; set; }

        /// <summary>
        /// Mã hàng hóa
        /// </summary>
        public string ProductId { get; set; }

        /// <summary>
        /// Tên hàng hóa
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// code bản ghi bảng Đơn vị tính (Unit)
        /// </summary>
        public Guid? UnitCode { get; set; }

        public long? IdUnit { get; set; }

        /// <summary>
        /// Tên <PERSON>ơn vị tính
        /// </summary>
        public string UnitName { get; set; }

        /// <summary>
        /// Đơn giá hàng hóa
        /// </summary>
        public decimal UnitPrice { get; set; }

        public int RoundingUnit { get; set; }

        /// <summary>
        /// Số lượng
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// Tổng tiền trước thuế / trước chiết khấu
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// Tổng tiền sau thuế
        /// </summary>
        public decimal PaymentAmount { get; set; }

        /// <summary>
        /// Tiền chiết khấu
        /// </summary>
        public decimal DiscountAmountBeforeTax { get; set; }

        /// <summary>
        /// Phần trăm chiết khấu
        /// </summary>
        public decimal DiscountPercentBeforeTax { get; set; }

        /// <summary>
        /// Phần trăm thuế
        /// </summary>
        public decimal VatPercent { get; set; }

        /// <summary>
        /// Giá trị hiển thị phần trăm thuế
        /// </summary>
        public string VatPercentDisplay { get; set; }

        /// <summary>
        /// Tổng tiền thuế
        /// </summary>
        public decimal VatAmount { get; set; }

        /// <summary>
        /// Tổng tiền chưa thuế, chưa chiết khấu
        /// </summary>
        public decimal TotalAmount { get; internal set; }

        /// <summary>
        /// có ấn số lương không
        /// true: ẩn, false/null: hiện
        /// </summary>
        public bool HideQuantity { get; set; }

        /// <summary>
        /// có ấn đơn vị tính không
        /// true: ẩn, false/null: hiện
        /// </summary>
        public bool HideUnit { get; set; }


        /// <summary>
        /// có ấn đơn giá không
        /// true: ẩn, false/null: hiện
        /// </summary>
        public bool HideUnitPrice { get; set; }

        public Dictionary<string, string> ExtraProperties { get; set; }
        public ICollection<Invoice01DetailExtraExportModel> InvoiceDetailExtras { get; set; }

        public static explicit operator Invoice01DetailExportModel(Invoice01DetailEntity entity)
        {
            if (entity == null)
                return null;

            return new Invoice01DetailExportModel
            {
                Id = entity.Id,
                Index = entity.Index,
                InvoiceHeaderId = entity.InvoiceHeaderId,
                Note = entity.Note,
                ProductCode = entity.ProductCode,
                IdProduct = entity.ProductId,
                ProductName = entity.ProductName,
                IdUnit = entity.UnitId,
                UnitName = entity.UnitName,
                UnitPrice = entity.UnitPrice,
                RoundingUnit = entity.RoundingUnit,
                Quantity = entity.Quantity,
                Amount = entity.Amount,
                PaymentAmount = entity.PaymentAmount,
                DiscountAmountBeforeTax = entity.DiscountAmountBeforeTax,
                DiscountPercentBeforeTax = entity.DiscountPercentBeforeTax,
                VatPercent = entity.VatPercent,
                VatPercentDisplay = entity.VatPercentDisplay,
                VatAmount = entity.VatAmount,
                HideQuantity = entity.HideQuantity,
                HideUnit = entity.HideUnit,
                HideUnitPrice = entity.HideUnitPrice,
                TenantId = entity.TenantId,
                ExtraProperties = entity.ExtraProperties != null ? entity.ExtraProperties.ToDictionary(x => x.Key, x => (string)x.Value) : new Dictionary<string, string>()

            };
        }

    }
}
