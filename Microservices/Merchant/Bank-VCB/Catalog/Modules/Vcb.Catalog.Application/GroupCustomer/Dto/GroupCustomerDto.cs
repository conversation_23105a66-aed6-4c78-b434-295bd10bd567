using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Core.Application.Dtos;
using Newtonsoft.Json;

namespace Vcb.Catalog.Application.GroupCustomer.Dto
{
    public class GroupCustomerDto : EntityDto<long>
    {
        public new long Id { get; set; }
        public string ErpId { get; set; }

        public long? ParentId { get; set; }

        [Required(ErrorMessage = "Vnis.BE.GroupCustomerDto.GroupCustomerCode.Required")]
        [MaxLength(50, ErrorMessage = "Vnis.BE.GroupCustomerDto.GroupCustomerCode.MaxLengthError")]
        [RegularExpression(@"^[a-zA-Z0-9][a-zA-Z0-9-_/]{0,48}[a-zA-Z0-9]{1,49}$|^[a-zA-Z0-9]{1}$", ErrorMessage = "Vnis.BE.GroupCustomerDto.GroupCustomerCode.WrongFormat")]
        public string GroupCustomerCode { get; set; }

        [Required(ErrorMessage = "Vnis.BE.GroupCustomerDto.Name.Required")]
        [MaxLength(250, ErrorMessage = "Vnis.BE.GroupCustomerDto.Name.MaxLengthError")]
        public string Name { get; set; }

        public bool IsDefault { get; set; }

        [JsonIgnore]
        public Guid CreatorId { get; set; }

        [JsonIgnore]
        public DateTime CreationTime { get; set; }

        [JsonIgnore]
        public DateTime? LastModificationTime { get; set; }

        [JsonIgnore]
        public Guid? LastModifierId { get; set; }

        [JsonIgnore]
        public Guid TenantId { get; set; }

        [JsonIgnore]
        public List<GroupCustomerDto> ChildrenGroups { get; set; }
    }
}