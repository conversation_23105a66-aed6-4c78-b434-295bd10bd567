using Core.DependencyInjection;
using Core.EventBus.Distributed;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.MessageEventsData.SyncDataTenant.Dto;
using Dapper;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Vcb.Catalog.Application.EmailTemplate.Dto;
using Vcb.Catalog.Application.RabbitMqEventBus.EmailTemplateTenantUpdate.MessageEventData;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;

namespace Vcb.Catalog.Application.RabbitMqEventBus.EmailTemplateTenantUpdate.ReceivedEventHandler
{
    public class EmailTemplateTenantUpdateApiReceivedEventHandler : IDistributedEventHandler<EmailTemplateTenantUpdateApiEventResultData>, ITransientDependency
    {
		private readonly IAppFactory _appFactory;
		public EmailTemplateTenantUpdateApiReceivedEventHandler(IAppFactory appFactory)
		{
			_appFactory = appFactory;
		}

		public async Task HandleEventAsync(EmailTemplateTenantUpdateApiEventResultData eventData)
		{
			var sqlInheritTenants = $@" SELECT * FROM ""VnisTenants"" 
                                    WHERE ""ParentId"" = '{OracleExtension.ConvertGuidToRaw(eventData.TenantId)}' AND ""IsDeleted"" = 0";
			var inheritTenants = await _appFactory.AuthDatabase.Connection.QueryAsync<VnisTenantDto>(sqlInheritTenants);
			//
			if (!inheritTenants.Any())
				return;
			//
			var tenantIds = inheritTenants.Select(x => OracleExtension.ConvertGuidToRaw(x.Id)).ToList();
			string tenantArr = "";
			foreach (var tenant in tenantIds)
			{
				tenantArr += "'" + tenant + "',";
			}
			tenantArr = tenantArr.Remove(tenantArr.Length - 1);
			//
			var sqlExistEmailTemplate = $@"SELECT * FROM ""EmailTemplate"" WHERE ""TenantId"" IN ({tenantArr}) AND ""IsDeleted"" = 0";

			var existEmailTemplate = await _appFactory.VnisCoreOracle.Connection.QueryAsync<EmailTemplateDto>(sqlExistEmailTemplate);

			var indexEmailTemplate = existEmailTemplate.GroupBy(x => x.TenantId).ToDictionary(x => x.Key, x => x.ToList());

            foreach (var tenant in inheritTenants)
            {
                if (!indexEmailTemplate.ContainsKey(tenant.Id))
                {
                    //tao moi nếu k phải trường hợp xóa
                    if (!eventData.IsDelete)
                    {
						var querysAdd = $@"INSERT INTO
	                        (
		                    SELECT * FROM ""EmailTemplate""
		                    WHERE ""TenantId"" IN ({tenantArr}) AND ""IsDeleted"" = 0
	                        )
                            (
								""Action"",
								""Name"",
								""Subject"",
								""Cc"",
								""Bcc"",
								""CreationTime"",
								""CreatorId"",
								""LastModificationTime"",
								""LastModifierId"",
								""IsDeleted"",
								""DeleterId"",
								""DeletionTime"",
								""TenantId"",
								""IsActive"",
								""Content""
                            )
                            VALUES
                            (
	                            '{eventData.Action}',
	                            '{eventData.Name}',
	                            '{eventData.Subject}',
	                            '{eventData.Cc}',
	                            '{eventData.Bcc}',
	                            '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
	                            NULL,
	                            NULL,
	                            NULL,
	                            0,
	                            NULL,
	                            NULL,
	                            '{OracleExtension.ConvertGuidToRaw(tenant.Id)}',
	                            0,
	                            '{eventData.Content}'
                            )";
						await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(querysAdd);
					}
                }
                else
                {
                    var exists = indexEmailTemplate[tenant.Id];

                    var exist = exists.FirstOrDefault(x => x.Action == eventData.OldAction);

                    if (eventData.IsDelete)
                    {
                        if (exist != null)
                        {
							var queryDelete = $@"UPDATE ""EmailTemplate""
                                                SET
	                                                ""IsDeleted"" = 1,
	                                                ""DeleterId"" = '{OracleExtension.ConvertGuidToRaw(eventData.UserId)}',
	                                                ""DeletionTime"" = '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}'
                                                WHERE
	                                                ""Id"" = {exist.Id}";

							await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(queryDelete);
						}
                    }
                    else
                    {
                        if (exist == null)
                        {
							var querysAdds = $@"INSERT INTO
	                        (
		                    SELECT * FROM ""EmailTemplate""
		                    WHERE ""TenantId"" IN ({tenantArr}) AND ""IsDeleted"" = 0
	                        )
                            (
								""Action"",
								""Name"",
								""Subject"",
								""Cc"",
								""Bcc"",
								""CreationTime"",
								""CreatorId"",
								""LastModificationTime"",
								""LastModifierId"",
								""IsDeleted"",
								""DeleterId"",
								""DeletionTime"",
								""TenantId"",
								""IsActive"",
								""Content""
                            )
                            VALUES
                            (
	                            '{eventData.Action}',
	                            '{eventData.Name}',
	                            '{eventData.Subject}',
	                            '{eventData.Cc}',
	                            '{eventData.Bcc}',
	                            '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
	                            NULL,
	                            NULL,
	                            NULL,
	                            0,
	                            NULL,
	                            NULL,
	                            '{OracleExtension.ConvertGuidToRaw(tenant.Id)}',
	                            0,
	                            '{eventData.Content}'
                            )";
							await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(querysAdds);
						}
                        else
                        {
							var queryUpdate = $@"UPDATE ""EmailTemplate""
                                                SET
	                                                ""Action"" = '{eventData.Action}',
													""Name"" = '{eventData.Name}',
													""Subject"" = '{eventData.Subject}',
													""Cc"" = '{eventData.Cc}',
													""Bcc"" = '{eventData.Bcc}',
													""LastModificationTime"" = '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
	                                                ""LastModifierId"" = '{OracleExtension.ConvertGuidToRaw(eventData.UserId)}',
													""Content"" = '{eventData.Content}'
                                                WHERE
	                                                ""Id"" = {exist.Id}";

							await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(queryUpdate);
						}
                    }
                }
            }
        }
	}
}
