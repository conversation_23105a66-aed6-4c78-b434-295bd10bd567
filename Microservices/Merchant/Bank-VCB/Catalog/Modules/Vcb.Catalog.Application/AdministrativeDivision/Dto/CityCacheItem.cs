using System;
using System.Collections.Generic;

namespace Vcb.Catalog.Application.AdministrativeDivision.Dto
{
    [Serializable]
    public class CityCacheItem
    {
        private const string CacheKeyFormat = "name:{0}";


        public List<CityDto> Value { get; set; }

        public CityCacheItem()
        {
        }

        public CityCacheItem(List<CityDto> value)
        {
            Value = value;
        }

        public static string CalculateCacheKey(string name)
        {
            return string.Format(CacheKeyFormat, (name.IsNullOrWhiteSpace() ? "null" : name));
        }
    }
}
