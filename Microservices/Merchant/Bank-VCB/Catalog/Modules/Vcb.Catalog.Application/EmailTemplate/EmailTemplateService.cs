using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using Vcb.Catalog.Application.EmailTemplate.Dto;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using Core.Application.Dtos;
using Core.Application.Services;
using Core.Domain.Repositories;
using Core.Shared.Factory;
using System;
using Core;
using System.Linq;
using VnisCore.Core.Oracle.Application.Contracts.Permissions.Catalog.EmailTemplate;
using Microsoft.AspNetCore.Authorization;
using Vcb.Catalog.Application.RabbitMqEventBus.EmailTemplateTenantUpdate.MessageEventData;
using Core.EventBus.Distributed;

namespace Vcb.Catalog.Application.EmailTemplate
{
    [Authorize(EmailTemplatePermissions.EmailTemplate.Default)]
    public class EmailTemplateService : CrudAppService<
            EmailTemplateEntity,
            EmailTemplateDto,
            long,
            EmailTemplatePagedRequestDto,
            EmailTemplateDto,
            EmailTemplateDto>, IEmailTemplateService
    {
        private readonly IRepository<EmailTemplateEntity, long> _emailTemplateRepository;
        private readonly IAppFactory _appFactory;
        private readonly IDistributedEventBus _distributedEventBus;

        public EmailTemplateService(IRepository<EmailTemplateEntity, long> emailTemplateRepository,
            IAppFactory appFactory, IDistributedEventBus distributedEventBus) : base(emailTemplateRepository)
        {
            _appFactory = appFactory;
            _emailTemplateRepository = emailTemplateRepository;
            _distributedEventBus = distributedEventBus;
        }

        [Authorize(EmailTemplatePermissions.EmailTemplate.Default)]
        [HttpGet(Utilities.ApiUrlBase + "GetById/{id:long}")]
        public override Task<EmailTemplateDto> GetAsync(long id)
        {
            return base.GetAsync(id);
        }

        [Authorize(EmailTemplatePermissions.EmailTemplate.Default)]
        [HttpPost(Utilities.ApiUrlBase + "GetList")]
        public override async Task<PagedResultDto<EmailTemplateDto>> GetListAsync(EmailTemplatePagedRequestDto input)
        {
            var tenantId = _appFactory.CurrentTenant.Id;
            var keyword = input.Keyword;

            var query = _emailTemplateRepository.AsNoTracking()
                .WhereIf(
                         !string.IsNullOrEmpty(input.Keyword),
                         p => p.Name == keyword
                            || p.Bcc == keyword
                            || p.Cc == keyword
                            || p.Subject == keyword
                )
                .WhereIf(tenantId.HasValue, x => x.TenantId == tenantId)
                .OrderByDescending(x => x.CreationTime);

            var emailTemplates = await query.Select(x => ObjectMapper.Map<EmailTemplateEntity, EmailTemplateDto>(x)).PageBy(input.SkipCount, input.MaxResultCount).ToListAsync();

            return new PagedResultDto<EmailTemplateDto>
            {
                TotalCount = await query.CountAsync(),
                Items = emailTemplates
            };
        }

        [HttpPost(Utilities.ApiUrlBase + "Create")]
        public override async Task<EmailTemplateDto> CreateAsync(EmailTemplateDto input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;

            if (await _emailTemplateRepository.AnyAsync(x => x.Name == input.Name && !x.IsDeleted && x.TenantId == tenantId))
                throw new UserFriendlyException(L["Vnis.BE.EmailTemplate.Create.DuplicatedName"]);

            input.IsActive = true;
            input.TenantId = tenantId;
            input.CreatorId = _appFactory.CurrentUser.Id.Value;
            input.CreationTime = DateTime.Now;

            //tạo/sửa cho các chi nhánh
            await _distributedEventBus.PublishAsync(new EmailTemplateTenantUpdateApiEventResultData(_appFactory.CurrentTenant.Id.Value, _appFactory.CurrentUser.Id.Value, input.Action, input, false));
            //await SyncDataInheritAsync(_appFactory.CurrentTenant.Id.Value, _appFactory.CurrentUser.Id.Value, input.Action, input, false);
            return await base.CreateAsync(input);
        }

        [Authorize(EmailTemplatePermissions.EmailTemplate.Update)]
        [HttpPost(Utilities.ApiUrlBase + "Update/{id:long}")]
        public override async Task<EmailTemplateDto> UpdateAsync(long id, EmailTemplateDto input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;

            var emailTemplate = await _emailTemplateRepository.FirstOrDefaultAsync(x => x.Id == id);
            if (emailTemplate == null)
                throw new UserFriendlyException(L["Vnis.BE.EmailTemplate.Update.NotFound"]);

            if (await _emailTemplateRepository.AnyAsync(x => x.Id != id && x.Name == input.Name && x.TenantId == tenantId && !x.IsDeleted))
                throw new UserFriendlyException(L["Vnis.BE.EmailTemplate.Update.DuplicatedName"]);

            emailTemplate.Name = input.Name;
            emailTemplate.Subject = input.Subject;
            emailTemplate.Content = input.Content;
            emailTemplate.Cc = input.Cc;
            emailTemplate.Bcc = input.Bcc;
            emailTemplate.LastModificationTime = DateTime.Now;
            emailTemplate.LastModifierId = _appFactory.CurrentUser.Id;

            input.Action = emailTemplate.Action;

            //tạo/sửa cho các chi nhánh
            await _distributedEventBus.PublishAsync(new EmailTemplateTenantUpdateApiEventResultData(tenantId, _appFactory.CurrentUser.Id.Value, emailTemplate.Action, input, false));
            //await SyncDataInheritAsync(_appFactory.CurrentTenant.Id.Value, _appFactory.CurrentUser.Id.Value, emailTemplate.Action, input, false);

            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            return await MapToGetOutputDtoAsync(emailTemplate);
        }

        [HttpPost(Utilities.ApiUrlBase + "Delete/{id:long}")]
        public override async Task DeleteAsync(long id)
        {
            await base.DeleteAsync(id);

            var input = new EmailTemplateDto
            {
                Action = null
            };
            //tạo/sửa cho các chi nhánh
            await _distributedEventBus.PublishAsync(new EmailTemplateTenantUpdateApiEventResultData(_appFactory.CurrentTenant.Id.Value, _appFactory.CurrentUser.Id.Value, null, input, true));
            //await SyncDataInheritAsync(_appFactory.CurrentTenant.Id.Value, _appFactory.CurrentUser.Id.Value, null, null, true);
        }


        //TODO: đưa hàm này vào rabbitmq
        //private async Task SyncDataInheritAsync(Guid tenantId, Guid userId, string action, EmailTemplateDto input, bool isDelete)
        //{
        //    var repoTenant = _appFactory.Repository<Tenant, Guid>();
        //    var inheritTenants = await repoTenant.Where(x => x.ParentId == tenantId).ToListAsync();
        //    if (!inheritTenants.Any())
        //        return;

        //    var tennatIds = inheritTenants.Select(x => x.Id).ToList();
        //    var repoEmailTemplate = _appFactory.Repository<EmailTemplateEntity, long>();
        //    var existsEmailTemplates = await repoEmailTemplate.Where(x => tennatIds.Contains(x.TenantId) && !x.IsDeleted).ToListAsync();
        //    var indexEmailTemplate = existsEmailTemplates.GroupBy(x => x.TenantId).ToDictionary(x => x.Key, x => x.ToList());

        //    var inserts = new List<EmailTemplateEntity>();

        //    foreach (var tenant in inheritTenants)
        //    {
        //        if (!indexEmailTemplate.ContainsKey(tenant.Id))
        //        {
        //            //tao moi nếu k phải trường hợp xóa
        //            if (!isDelete)
        //            {
        //                var insert = MapToEntity(input);
        //                insert.TenantId = tenant.Id;

        //                inserts.Add(insert);
        //            }
        //        }
        //        else
        //        {
        //            var exists = indexEmailTemplate[tenant.Id];

        //            var exist = exists.FirstOrDefault(x => x.Action == action);

        //            if (isDelete)
        //            {
        //                if (exist != null)
        //                {
        //                    exist.DeleterId = userId;
        //                    exist.IsDeleted = true;
        //                    exist.DeletionTime = DateTime.Now;
        //                }
        //            }
        //            else
        //            {
        //                if (exist == null)
        //                {
        //                    var insert = MapToEntity(input);
        //                    insert.TenantId = tenant.Id;

        //                    inserts.Add(insert);
        //                }
        //                else
        //                {
        //                    exist.Name = input.Name;
        //                    exist.Subject = input.Subject;
        //                    exist.Content = input.Content;
        //                    exist.Cc = input.Cc;
        //                    exist.Bcc = input.Bcc;
        //                    exist.LastModificationTime = DateTime.Now;
        //                    exist.LastModifierId = _appFactory.CurrentUser.Id;
        //                }
        //            }
        //        }
        //    }

        //    await repoEmailTemplate.InsertManyAsync(inserts);

        //    await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
        //}

    }
}
