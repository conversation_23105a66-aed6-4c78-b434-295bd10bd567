using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Vcb.Catalog.Application.CustomerVCB.Models.Requests;
using Vcb.Catalog.Application.CustomerVCB.Models.Responses;

namespace Vcb.Catalog.Application.CustomerVCB.Handlers.Requests
{
    public class GetListCustomerVCBRequestModelHandler : IRequestHandler<GetListCustomerVCBRequestModel, GetListCustomerVCBResponseModel>
    {
        public GetListCustomerVCBRequestModelHandler()
        {
        }

        public Task<GetListCustomerVCBResponseModel> Handle(GetListCustomerVCBRequestModel request, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }
    }
}
