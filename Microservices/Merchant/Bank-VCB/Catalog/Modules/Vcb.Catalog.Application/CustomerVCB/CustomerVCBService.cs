using Core;
using Core.Application.Dtos;
using Core.Application.Services;
using Core.Domain.Repositories;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Vcb.Catalog.Application.CustomerVCB.Dto;
using VnisCore.Core.Oracle.Application.Contracts.Permissions.Catalog.CustomerVCB;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;

namespace Vcb.Catalog.Application.CustomerVCB
{
    [Authorize(CustomerVCBPermissions.Customer.Default)]
    public class CustomerVCBService : CrudAppService<
        CustomerVCBEntity,
        CustomerVCBDto,
        long,
        CustomerVCBPagedRequestDto,
        CustomerVCBDto,
        CustomerVCBDto>,
        ICustomerVCBService
    {
        private readonly IAppFactory _appFactory;
        private readonly IRepository<CustomerVCBEntity, long> _repoCustomerVCB;

        public CustomerVCBService(
            IAppFactory appFactory,
            IRepository<CustomerVCBEntity, long> repoCustomerVCB
            ) : base(repoCustomerVCB)
        {
            _appFactory = appFactory;
            _repoCustomerVCB = repoCustomerVCB;
        }

        [Authorize(CustomerVCBPermissions.Customer.Default)]
        [HttpGet(Utilities.ApiUrlBase + "GetById/{id:long}")]
        public override Task<CustomerVCBDto> GetAsync(long id)
        {
            return base.GetAsync(id);
        }

        [Authorize(CustomerVCBPermissions.Customer.Default)]
        [HttpPost(Utilities.ApiUrlBase + "GetList")]
        public override async Task<PagedResultDto<CustomerVCBDto>> GetListAsync(CustomerVCBPagedRequestDto input)
        {
            var keyword = input.Keyword;
            //query search
            var query = _repoCustomerVCB.AsNoTracking()
                .WhereIf(
                         !string.IsNullOrEmpty(input.Keyword),
                         x =>
                         EF.Functions.Like(x.Cif, keyword)
                         || EF.Functions.Like(x.CustomerName, keyword)
                         || EF.Functions.Like(x.Stk, keyword)
                         || EF.Functions.Like(x.Address, keyword)
                         || EF.Functions.Like(x.TaxCode, keyword))
                .OrderByDescending(x => x.CreationTime);

                if (query == null)
                {
                    return new PagedResultDto<CustomerVCBDto>();
                }

                var count = await query.CountAsync();
                var listData = count != 0
                    ? await query.PageBy(input.SkipCount, input.MaxResultCount).Select(x => ObjectMapper.Map<CustomerVCBEntity, CustomerVCBDto>(x)).ToListAsync()
                    : new List<CustomerVCBDto>();
                var data = new PagedResultDto<CustomerVCBDto>
                {
                    TotalCount = count,
                    Items = listData
                };
                return data;
        }

        [Authorize(CustomerVCBPermissions.Customer.Create)]
        [HttpPost(Utilities.ApiUrlBase + "Create")]
        public override async Task<CustomerVCBDto> CreateAsync(CustomerVCBDto input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;

            // Kiểm tra mã khách hàng đã tồn tại: Nếu trùng CIF + Tên thì sẽ báo lỗi
            if (string.IsNullOrEmpty(input.Stk))
            {
                if (await _repoCustomerVCB.AnyAsync(x => x.Cif == input.Cif && string.IsNullOrEmpty(x.Stk) && !x.IsDeleted))
                    throw new UserFriendlyException(L["Vnis.BE.Customer.Create.DuplicatedCIFAndSTK"]);
            }
            else
            {
                if (await _repoCustomerVCB.AnyAsync(x => x.Cif == input.Cif && x.Stk == input.Stk && !x.IsDeleted))
                    throw new UserFriendlyException(L["Vnis.BE.Customer.Create.DuplicatedCIFAndSTK"]);
            }

            input.Status = CustomerVCBStatus.WaitApprove;
            input.CreationTime = DateTime.Now;
            input.CreatorId = userId;
            input.TenantId = tenantId;

            var response = await base.CreateAsync(input);

            ////Elastic
            //var data = new CustomerEventSendData((CustomerInfoRequest)(MapToEntity(response)));
            //await _distributedEventBus.PublishAsync(data);

            return response;
        }

        [Authorize(CustomerVCBPermissions.Customer.Update)]
        [HttpPost(Utilities.ApiUrlBase + "Update/{id:long}")]
        public override async Task<CustomerVCBDto> UpdateAsync(long id, CustomerVCBDto input)
        {
            var customer = await _repoCustomerVCB.FirstOrDefaultAsync(x => x.Id == id && !x.IsDeleted);
            if (customer == null)
                throw new UserFriendlyException(L["Vnis.BE.Customer.Update.NotFound"]);

            //Nếu người dùng sửa số CIF hoặc STK => kiểm tra trùng dữ liệu trong DB
            // Bổ sung code để check thêm trường hợp input.Stk = "" và customer.Stk = null
            if (string.IsNullOrEmpty(input.Stk))
            {
                input.Stk = null;
            }
            if (string.IsNullOrEmpty(customer.Stk))
            {
                customer.Stk = null;
            }
            if (customer.Cif != input.Cif || customer.Stk != input.Stk)
            {
                if (await _repoCustomerVCB.AnyAsync(x => x.Id != id && !x.IsDeleted 
                                    && x.Stk == input.Stk && x.Cif == input.Cif))
                    throw new UserFriendlyException(L["Vnis.BE.Customer.Create.DuplicatedCIFAndSTK"]);
            }

            if (customer.Status != (short)CustomerVCBStatus.WaitApprove
                && customer.Status != (short)CustomerVCBStatus.Reject)
            {
                throw new UserFriendlyException(L["Vnis.BE.Customer.Update.InvalidCustomerStatus"]);
            }

            customer.Status = (short)CustomerVCBStatus.WaitApprove;
            customer.CustomerType = (int)input.CustomerType;
            customer.Cif = input.Cif;
            customer.CustomerName = input.CustomerName;
            customer.Stk = input.Stk;
            customer.Address = input.Address;
            customer.TaxCode = input.TaxCode;
            customer.IsShowOnInvoice = input.IsShowOnInvoice;
            customer.TaxCodeActiveStatus = input.TaxCodeActiveStatus;
            customer.LastModificationTime = DateTime.Now;
            customer.LastModifierId = _appFactory.CurrentUser.Id.Value;

            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            return await MapToGetOutputDtoAsync(customer);
        }

        [Authorize(CustomerVCBPermissions.Customer.Delete)]
        [HttpPost(Utilities.ApiUrlBase + "Delete/{id:long}")]
        public override async Task DeleteAsync(long id)
        {
            var customer = await _repoCustomerVCB.FirstOrDefaultAsync(x => x.Id == id && !x.IsDeleted);
            if (customer == null)
                throw new UserFriendlyException(L["Vnis.BE.Customer.Delete.NotFound"]);

            /*
             - Trường hợp trạng thái bản ghi là “Chờ duyệt” hoặc "Từ chối duyệt", 
                thực hiện xóa bản ghi hiển thị trên hệ thống, 
                cập nhật trạng thái “Chờ duyệt” hoặc "Từ chối duyệt" thành “Đã xóa” trong DB 
                và lưu lại thông tin người xóa, thời gian thực hiện xóa.
            - Trường hợp trạng thái bản ghi là “Đã duyệt”, 
                thực hiện cập nhật trạng thái bản ghi thành Chờ duyệt xóa.
             */

            if (customer.Status == (short)CustomerVCBStatus.Approved)
            {
                // Cập nhật trạng thái sang chờ xóa
                customer.Status = (short)CustomerVCBStatus.WaitDelete;
                customer.LastModifierId = _appFactory.CurrentUser.Id.Value;
                customer.LastModificationTime = DateTime.Now;
            }
            else if (customer.Status == (short)CustomerVCBStatus.WaitApprove
                || customer.Status == (short)CustomerVCBStatus.Reject)
            {
                // Cập nhật sang trạng thái đã xóa
                customer.Status = (short)CustomerVCBStatus.Deleted;
                customer.IsDeleted = true;
                customer.DeleterId = _appFactory.CurrentUser.Id.Value;
                customer.DeletionTime = DateTime.Now;
            }
            else
            {
                throw new UserFriendlyException(L["Vnis.BE.Customer.Delete.InvalidCustomerStatus"]);
            }

            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
        }

        [Authorize(CustomerVCBPermissions.Customer.Approve)]
        [HttpPost(Utilities.ApiUrlBase + "Approve/{id:long}")]
        public async Task ApproveAsync(long id)
        {
            var customer = await _repoCustomerVCB.FirstOrDefaultAsync(x => x.Id == id && !x.IsDeleted);
            if (customer == null)
                throw new UserFriendlyException(L["Vnis.BE.Customer.Delete.NotFound"]);

            if (customer.Status == (short)CustomerVCBStatus.WaitApprove)
            {
                customer.Status = (short)CustomerVCBStatus.Approved;
                customer.LastModifierId = _appFactory.CurrentUser.Id.Value;
                customer.LastModificationTime = DateTime.Now;
            }
            else if (customer.Status == (short)CustomerVCBStatus.WaitDelete)
            {
                // Cập nhật sang trạng thái đã xóa
                customer.Status = (short)CustomerVCBStatus.Deleted;
                customer.IsDeleted = true;
                customer.DeleterId = _appFactory.CurrentUser.Id.Value;
                customer.DeletionTime = DateTime.Now;
            }
            else
            {
                throw new UserFriendlyException(L["Vnis.BE.Customer.Approve.InvalidCustomerStatus"]);
            }

            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
        }

        [Authorize(CustomerVCBPermissions.Customer.Approve)]
        [HttpPost(Utilities.ApiUrlBase + "Reject/{id:long}")]
        public async Task RejectAsync(long id)
        {
            var customer = await _repoCustomerVCB.FirstOrDefaultAsync(x => x.Id == id && !x.IsDeleted);
            if (customer == null)
                throw new UserFriendlyException(L["Vnis.BE.Customer.Delete.NotFound"]);

            if (customer.Status == (short)CustomerVCBStatus.WaitApprove)
            {
                customer.Status = (short)CustomerVCBStatus.Reject;
                customer.LastModifierId = _appFactory.CurrentUser.Id.Value;
                customer.LastModificationTime = DateTime.Now;
            }
            else if (customer.Status == (short)CustomerVCBStatus.WaitDelete)
            {
                customer.Status = (short)CustomerVCBStatus.Approved;
                customer.LastModifierId = _appFactory.CurrentUser.Id.Value;
                customer.LastModificationTime = DateTime.Now;
            }
            else
            {
                throw new UserFriendlyException(L["Vnis.BE.Customer.Reject.InvalidCustomerStatus"]);
            }

            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
        }

        [Authorize(CustomerVCBPermissions.Customer.CancelDelete)]
        [HttpPost(Utilities.ApiUrlBase + "CancelDetele/{id:long}")]
        public async Task CancelDeteleAsync(long id)
        {
            var customer = await _repoCustomerVCB.FirstOrDefaultAsync(x => x.Id == id && !x.IsDeleted);
            if (customer == null)
                throw new UserFriendlyException(L["Vnis.BE.Customer.Delete.NotFound"]);

            if (customer.Status == (short)CustomerVCBStatus.WaitDelete)
            {
                // Cập nhật sang trạng thái đã duyệt
                customer.Status = (short)CustomerVCBStatus.Approved;
                customer.LastModifierId = _appFactory.CurrentUser.Id.Value;
                customer.LastModificationTime = DateTime.Now;
            }
            else
            {
                throw new UserFriendlyException(L["Vnis.BE.Customer.Reject.InvalidCustomerStatus"]);
            }

            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
        }
    }
}
