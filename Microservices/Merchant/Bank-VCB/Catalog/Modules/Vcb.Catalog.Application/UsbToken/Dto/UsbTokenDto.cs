using Core.Application.Dtos;
using System;
using System.Collections.Generic;

namespace Vcb.Catalog.Application.Dto
{
    public class UsbTokenDto : EntityDto<long>
    {
        public new long Id { get; set; }
        public string CkaLabel { get; set; }
        public string CkaId { get; set; }
        public string TokenId { get; set; }
        public string SubjectName { get; set; }
        public string Issuer { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string SerialNumber { get; set; }
        public bool IsOnServer { get; set; }

        public List<TemplateUsbToken> Templates { get; set; }

        public class TemplateUsbToken
        {
            public long Id { get; set; }
            public string Name { get; set; }
            public bool Selected { get; set; }
        }
    }
}