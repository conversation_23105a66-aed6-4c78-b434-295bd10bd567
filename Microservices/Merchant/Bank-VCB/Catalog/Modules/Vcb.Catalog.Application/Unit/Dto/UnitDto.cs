using System;
using System.ComponentModel.DataAnnotations;
using Core.Application.Dtos;
using Newtonsoft.Json;

namespace Vcb.Catalog.Application.Unit.Dto
{
    public class UnitDto : EntityDto<long>
    {
        public new long Id { get; set; }

        [Required(ErrorMessage = "Vnis.BE.UnitDto.Name.Required")]
        [StringLength(50, ErrorMessage = "Vnis.BE.UnitDto.Name.MaxLengthError")]
        public string Name { get; set; }

        /// <summary>
        /// lưu cấu hình phần thập phân của Số lượng và Đơn giá (Min = 0, Max = 4)
        ///  (Min = 0, Max = 4) default 0
        /// </summary>
        [Required(ErrorMessage = "Vnis.BE.UnitDto.Rounding.Required")]
        [Range(0, 4, ErrorMessage = "Vnis.BE.UnitDto.Rounding.MaxLengthError")]
        public int Rounding { get; set; }

        public string ErpId { get; set; }
        public string NormalizedName { get; set; }
        public bool IsActive { get; set; }

        [JsonIgnore]
        public Guid CreatorId { get; set; }

        [JsonIgnore]
        public DateTime CreationTime { get; set; }

        [JsonIgnore]
        public DateTime? LastModificationTime { get; set; }

        [JsonIgnore]
        public Guid? LastModifierId { get; set; }

        [JsonIgnore]
        public Guid TenantId { get; set; }
    }
}
