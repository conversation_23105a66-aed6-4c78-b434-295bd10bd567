using Core.Shared.Attributes;
using System.ComponentModel.DataAnnotations;

namespace Vcb.Catalog.Application.Customer.Dto
{
    public class CustomerTvanRequestDto
    {
        [Required(ErrorMessage = "Vnis.BE.CustomersTvanDto.TaxCodes.Required")]
        [StringLength(14, ErrorMessage = "Vnis.BE.CustomersTvanDto.TaxCodes.MaxlengthError")]
        [TaxCode(ErrorMessage = "Vnis.BE.CustomersTvanDto.TaxCodes.WrongFormat")]
        public string TaxCode { get; set; }
    }
}
