using System;

namespace Vcb.Catalog.Application.Customer.Models
{
    public class PagingCustomerInvoiceByTvanModel
    {
        public Guid Code { get; set; }
        public string FullName { get; set; }
        public string LegalName { get; set; }
        public string CustomerId { get; set; }
        public string TaxCode { get; set; }
        public string Address { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public int? IdGroupCustomer { get; set; }
        public string GroupCustomerName { get; set; }
        public string BankAccount { get; set; }
        public string BankName { get; set; }
        public string City { get; set; }
        public string District { get; set; }
    }
}
