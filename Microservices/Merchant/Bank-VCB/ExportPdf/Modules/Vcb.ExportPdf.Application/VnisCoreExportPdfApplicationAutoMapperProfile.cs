using AutoMapper;

namespace VnisCore.ExportPdf.Application
{
    public class VnisCoreExportPdfApplicationAutoMapperProfile : Profile
    {
        public VnisCoreExportPdfApplicationAutoMapperProfile()
        {
            /* You can configure your AutoMapper mapping configuration here.
             * Alternatively, you can split your mapping configurations
             * into multiple profile classes for a better organization. */
            //CreateMap<StoreEntity, StoreDto>().ReverseMap();
            //CreateMap<CategoryEntity, CategoryDto>().ReverseMap();
            //CreateMap<ProductEntity, ProductDto>().ReverseMap();
        }
    }
}