using Core;
using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Dto;
using Core.Shared.Extensions;
using Core.Shared.FileManager.Interfaces;
using Core.Shared.Options;
using Core.Shared.Services;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Serilog;
using System;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using VnisCore.Core.MongoDB.Repository;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.ExportPdf.Application.UnOfficial.Models.Requests;

namespace VnisCore.MediaFile.Application.Handlers
{
    public class DownloadInvoiceXmlHandler : IRequestHandler<DownloadInvoiceXmlRequestModel, FileDto>
    {
        private readonly IFileService _fileService;
        private readonly IVnisCoreMongoXmlInvoice01SignedRepository _invoice01SignedRepository;
        private readonly IRepository<Invoice01XmlEntity> _invoice01XmlRepository;
        private readonly IZipInvoiceClientService _zipInvoiceClientService;
        private readonly ZipInvoiceClientOption _zipInvoiceClientOption;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly ILogger<DownloadInvoiceXmlHandler> _logger;
        private CancellationToken _cancellationToken;
        private MediaFileType _mediaFileType;

        public DownloadInvoiceXmlHandler(
            IFileService fileService,
            ILogger<DownloadInvoiceXmlHandler> logger,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IVnisCoreMongoXmlInvoice01SignedRepository invoice01SignedRepository,
            IRepository<Invoice01XmlEntity> invoice01XmlRepository,
            IZipInvoiceClientService zipInvoiceClientService,
            IOptions<ZipInvoiceClientOption> zipInvoiceClientOption
        )
        {
            _logger = logger;
            _fileService = fileService;
            _localizier = localizier;
            _invoice01SignedRepository = invoice01SignedRepository;
            _invoice01XmlRepository = invoice01XmlRepository;
            _zipInvoiceClientService = zipInvoiceClientService;
            _zipInvoiceClientOption = zipInvoiceClientOption.Value;
        }

        public async Task<FileDto> Handle(DownloadInvoiceXmlRequestModel request, CancellationToken cancellationToken)
        {
            _cancellationToken = cancellationToken;

            var invoice01Xml = (await _invoice01XmlRepository
                .Where(x => x.InvoiceHeaderId == request.InvoiceId)
                .AsNoTracking()
                .ToListAsync())
                .OrderByDescending(x => x.Id)
                .FirstOrDefault();

            if (invoice01Xml == null)
            {
                return await GetFileFromMongoDBAsync(request.InvoiceId);
            }

            _mediaFileType = GetMediaType(request.InvoiceType);

            var creationTime = long.Parse(invoice01Xml.CreationTime.ToString("yyyyMMdd"));
            var errorTime = long.Parse(new DateTime(2022, 04, 02).ToString("yyyyMMdd"));

            if (creationTime == errorTime)
            {
                for (var i = 6; i < 25; i++)
                {
                    var fileDto = await DownloadFileAsync(invoice01Xml, i);
                    if (fileDto != null)
                    {
                        return fileDto;
                    }
                }
            }

            return await DownloadFileAsync(invoice01Xml);
        }


        /// <summary>
        /// Get MediaFileType
        /// </summary>
        /// <param name="invoiceType"></param>
        /// <returns></returns>
        /// <exception cref="UserFriendlyException"></exception>
        private MediaFileType GetMediaType(VnisType invoiceType)
        {
            switch (invoiceType)
            {
                case VnisType._01GTKT:
                    return MediaFileType.Invoice01Xml;
                case VnisType._02GTTT:
                    return MediaFileType.Invoice02Xml;
                case VnisType._03XKNB:
                    return MediaFileType.Invoice03Xml;
                case VnisType._04HGDL:
                    return MediaFileType.Invoice04Xml;
                default:
                    throw new UserFriendlyException(_localizier["Vnis.BE.Invoice.DownloadXmlInvoiceTypeNotFound",
                        $"{invoiceType.GetName()}"]);
            }
        }

        /// <summary>
        /// Download file
        /// </summary>
        /// <param name="invoice01Xml"></param>
        /// <param name="idx"></param>
        /// <returns></returns>
        private async Task<FileDto> DownloadFileAsync(Invoice01XmlEntity invoice01Xml, int? idx = null)
        {
            var pathFileMinio = CreatePathFileMinIO(invoice01Xml, invoice01Xml.TenantId, idx);
            var fileDto = await CreateFileAsync(invoice01Xml, pathFileMinio);
            if (fileDto != null)
            {
                return fileDto;
            }

            var tenantId = OracleExtension.ConvertRawOracleToGuid(
                    OracleExtension.ConvertMongoDbGuidToRaw(invoice01Xml.TenantId));
            pathFileMinio = CreatePathFileMinIO(invoice01Xml, tenantId, idx);
            fileDto = await CreateFileAsync(invoice01Xml, pathFileMinio);
            if (fileDto != null)
            {
                return fileDto;
            }

            return await GetFileFromMongoDBAsync(invoice01Xml.InvoiceHeaderId);
        }

        /// <summary>
        /// Tạo đường dẫn để download MediaFile
        /// </summary>
        /// <param name="invoice01Xml"></param>
        /// <param name="tenantId"></param>
        /// <param name="idx"></param>
        /// <returns></returns>
        private string CreatePathFileMinIO(Invoice01XmlEntity invoice01Xml, Guid tenantId, int? idx = null)
        {
            if (idx.HasValue)
            {
                return $"{_mediaFileType}/{tenantId}/{invoice01Xml.CreationTime.Year}/{invoice01Xml.CreationTime.Month:00}/{invoice01Xml.CreationTime.Day:00}/{idx.Value:00}/{invoice01Xml.PhysicalFileName}";
            }

            return $"{_mediaFileType}/{tenantId}/{invoice01Xml.CreationTime.Year}/{invoice01Xml.CreationTime.Month:00}/{invoice01Xml.CreationTime.Day:00}/{invoice01Xml.PhysicalFileName}";
        }

        /// <summary>
        /// Tạo file
        /// </summary>
        /// <param name="invoice01Xml"></param>
        /// <param name="pathFileMinio"></param>
        /// <returns></returns>
        private async Task<FileDto> CreateFileAsync(Invoice01XmlEntity invoice01Xml, string pathFileMinio)
        {
            Log.Information($"-> pathFileMinio = {pathFileMinio}");

            var bytes = await GetBytesAsync(invoice01Xml.IsActive, invoice01Xml.Partition, pathFileMinio);

            if (bytes == null)
            {
                return null;
            }

            return new FileDto
            {
                ContentType = ContentType.Xml,
                FileBytes = bytes,
                FileBase64 = Convert.ToBase64String(bytes, 0, bytes.Length),
                FileName = invoice01Xml.FileName
            };
        }

        /// <summary>
        /// Lấy Bytes với điều kiện
        /// Nếu isActive = true thì gọi sang API
        /// Nếu isActive = false thì download từ MinIO
        /// </summary>
        /// <param name="isActive"></param>
        /// <param name="pathFileMinio"></param>
        /// <returns></returns>
        private async Task<byte[]> GetBytesAsync(bool isActive, long partition, string pathFileMinio)
        {
            if (isActive)
            {
                return await GetBytesFromApiAsync(partition, pathFileMinio);
            }

            return await GetBytesFromMinIOAsync(pathFileMinio);
        }


        /// <summary>
        /// Lấy Bytes từ API
        /// </summary>
        /// <param name="pathFileMinIO"></param>
        /// <returns></returns>
        private async Task<byte[]> GetBytesFromApiAsync(long partition, string pathFileMinIO)
        {
            Log.Information("-> GetBytesFromApiAsync");

            var path = pathFileMinIO.Replace($"{_mediaFileType}/", "");
            var url = $"{_zipInvoiceClientOption.EndPoint}/zipinvoices/{partition}?path={path}";

            var xml = await _zipInvoiceClientService.GetXmlAsync(url);

            if (string.IsNullOrWhiteSpace(xml))
            {
                return null;
            }

            var bytes = Encoding.UTF8.GetBytes(xml);

            return bytes;
        }


        /// <summary>
        /// Get Bytes từ MinIO
        /// </summary>
        /// <param name="pathFileMinio"></param>
        /// <returns></returns>
        private async Task<byte[]> GetBytesFromMinIOAsync(string pathFileMinio)
        {
            try
            {
                Log.Information("-> GetBytesFromMinIOAsync");

                var bytes = await _fileService.DownloadAsync(pathFileMinio);
                return bytes;

            }
            catch (Exception ex)
            {
                Log.Error(ex.Message, ex.StackTrace);
                return null;
            }
        }


        /// <summary>
        /// Get File từ MongoDB
        /// </summary>
        /// <param name="invoiceHeaderId"></param>
        /// <returns></returns>
        private async Task<FileDto> GetFileFromMongoDBAsync(long invoiceHeaderId)
        {
            Log.Information("-> GetFileFromMongoDBAsync");

            var invoiceXml = await _invoice01SignedRepository.GetByInvoiceId(invoiceHeaderId, _cancellationToken);
            if (invoiceXml == null)
            {
                Log.Information($"-> Không tìm thấy XmlInvoice01Signed có Id = {invoiceHeaderId}");
                return null;
            }

            return new FileDto
            {
                ContentType = ContentType.Xml,
                FileBytes = Convert.FromBase64String(invoiceXml.Xml),
                FileBase64 = invoiceXml.Xml,
                FileName = invoiceXml.FileName
            };
        }
    }
}
