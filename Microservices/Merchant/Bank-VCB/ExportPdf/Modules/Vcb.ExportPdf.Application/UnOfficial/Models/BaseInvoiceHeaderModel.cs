using Core.Shared.Constants;
using System;

namespace VnisCore.ExportPdf.Application.UnOfficial.Models
{
    public class BaseInvoiceHeaderModel
    {
        public long Id { get; set; }
        public Guid TenantId { get; set; }

        public DateTime CreatedTime { get; set; }
        public Guid CreatedId { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public Guid? LastModifierId { get; set; }
        public DateTime? CancelTime { get; set; }
        public Guid? CancelId { get; set; }
        public DateTime? DeleteTime { get; set; }
        public Guid? DeleteId { get; set; }

        /// <summary>
        /// Nguồn dữ liệu từ đâu: API, Form, Excel, ShareDb
        /// </summary>
        public int Source { get; set; }

        /// <summary>
        /// Gói tạo hóa đơn
        /// </summary>
        public Guid BatchId { get; set; }

        /// <summary>
        /// Mã tra cứu. Cá<PERSON> hóa đơn thay thế/điều chỉnh sẽ cùng mã này
        /// </summary>
        public string TransactionId { get; set; }

        /// <summary>
        /// Mã số thuế bên bán
        /// </summary>
        public string SellerTaxCode { get; set; }

        /// <summary>
        /// code bản ghi mẫu hóa đơn (Template)
        /// </summary>
        public long TemplateId { get; set; }

        /// <summary>
        /// Mẫu số (từ 1 -> 4)
        /// </summary>
        public short TemplateNo { get; set; }

        /// <summary>
        /// Ký hiệu (C21TAA)
        /// </summary>
        public string SerialNo { get; set; }

        /// <summary>
        /// Số hóa đơn gồm 7 ký tự số (0000001)
        /// </summary>
        public string InvoiceNo { get; set; }
        public int? Number { get; set; }

        /// <summary>
        /// Ghi chú
        /// </summary>
        public string Note { get; set; }

        /// <summary>
        /// Ngày hóa đơn NSD nhập vào. Chỉ lưu ngày, tháng, năm. Không lưu thời gian. Lưu dạng UTC
        /// </summary>
        public DateTime InvoiceDate { get; set; }

        /// <summary>
        /// Trạng thái hóa đơn
        /// </summary>
        public InvoiceStatus InvoiceStatus { get; set; }

        /// <summary>
        /// Trạng thái ký hóa đơn
        /// </summary>
        public SignStatus SignStatus { get; set; }

        /// <summary>
        /// Ghi bản ghi đăng ký phát hành hóa đơn, lưu lại để xác định hóa đơn được tạo theo thông báo phát hành nào
        /// </summary>
        public long? RegistrationHeaderId { get; set; }

        public long? RegistrationDetailId { get; set; }

        /// <summary>
        /// Id bản ghi bên ERP
        /// </summary>
        public string ErpId { get; set; }

        /// <summary>
        /// Tài khoản người tạo hóa đơn bên ERP
        /// </summary>
        public string CreatorErp { get; set; }
    }
}
