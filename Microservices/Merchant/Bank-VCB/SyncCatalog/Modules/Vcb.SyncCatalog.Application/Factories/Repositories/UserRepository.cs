using Core.Shared.Factory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace VnisCore.SyncCatalog.Application.Factories.Repositories
{
    public class UserRepository : IUserRepository
    {
        private readonly IAppFactory _appFactory;
        public UserRepository(IAppFactory appFactory)
        {
            _appFactory = appFactory;
        }

        //public async Task<UserEntity> FindUserByUsernameAsync(Guid idTenant, string username)
        //{

        //}
    }
}
