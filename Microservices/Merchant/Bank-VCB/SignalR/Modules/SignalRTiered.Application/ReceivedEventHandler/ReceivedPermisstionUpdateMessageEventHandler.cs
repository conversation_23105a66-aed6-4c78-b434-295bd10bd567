using Core.DependencyInjection;
using Core.EventBus.Distributed;

using Microsoft.AspNetCore.SignalR;


using SignalRTiered.Application.Hub;
using SignalRTiered.Application.Messages;

using System.Threading.Tasks;

namespace SignalRTiered.Application.ReceivedEventHandler
{
    public class ReceivedPermisstionUpdateMessageEventHandler : IDistributedEventHandler<ReceivedPermissionUpdateMessageDto>, ITransientDependency
    {
        private readonly IHubContext<EinvoiceHub> _hubContext;

        public ReceivedPermisstionUpdateMessageEventHandler(IHubContext<EinvoiceHub> hubContext)
        {
            _hubContext = hubContext;
        }
        public async Task HandleEventAsync(ReceivedPermissionUpdateMessageDto eventData)
        {
            string method = "PermisstionUpdateMessage";

            await _hubContext.Clients
            .User(eventData.TargetUserId.ToString())
            .SendAsync(method, eventData.ReceivedMessage);
        }
    }
}
