@using Core.Shared.Pdf.Models
@model PdfInvoiceDocumentModel

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- Core, khong duoc sua -->
    <style>
        *, :after, :before {
            box-sizing: border-box;
        }

        html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video {
            margin: 0;
            padding: 0;
        }

        article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
            display: block;
        }

        html, body {
            font-family: Times, "Times New Roman", serif;
            font-size: 18px;
            width: 1000px;
            height: auto;
            margin: 0 auto;
            color: #030303;
            /*customize*/
            word-spacing: 1px;
            line-height: 26px;
        }

        /***Always insert a page break before the element***/
        .pb_before {
            page-break-before: always !important;
        }

        /***Always insert a page break after the element***/
        .pb_after {
            page-break-after: always !important;
        }

        /***Avoid page break before the element (if possible)***/
        .pb_before_avoid {
            page-break-before: avoid !important;
        }

        /***Avoid page break after the element (if possible)***/
        .pb_after_avoid {
            page-break-after: avoid !important;
        }

        /* Avoid page break inside the element (if possible) */
        .pbi_avoid {
            page-break-inside: avoid !important;
        }

        table {
            word-break: break-word;
            width: 100%;
        }

            table > tbody > tr {
                page-break-inside: avoid;
            }

            /*Lap header 1 lan - footer 1 lan*/
            table.one-header-one-footer > thead {
                display: table-row-group;
            }

            table.one-header-one-footer > tfoot {
                display: table-row-group;
            }


            /*Lap header n lan - footer 1 lan*/
            table.many-header-one-footer > tbody > tr {
                page-break-inside: avoid;
            }

            table.many-header-one-footer > tfoot {
                display: table-row-group;
            }
    </style>

    <!--customize-->
    <style>
        body {
            padding: 15px 50px;
        }

        table tr td {
            vertical-align: top;
        }

        td.header-document {
            padding-bottom: 15px;
        }

        .header-document h3, .header-document h4 {
            text-align: center;
            line-height: 32px;
        }

        .header-document h2 {
            text-align: center;
            margin-top: 20px;
            margin-bottom: 8px;
        }

        .header-document p {
            text-align: center;
            font-weight: bold;
            font-size: 19px;
        }

        .header-info {
            padding-left: 20px;
        }

            .header-info p {
                font-style: italic;
                margin-bottom: 15px;
            }

        .seller h4, .buyer h4 {
            line-height: 28px;
        }

        .seller table, .buyer table, .document-decided p, .invoice-info table {
            margin-bottom: 15px;
        }

        .seller-full-name {
            text-transform: uppercase;
        }

        .seller-address, .buyer-address {
            padding-left: 20px;
        }

        .seller-phone-number, .seller-representative, .buyer-phone-number, .buyer-representative {
            padding-left: 20px;
            width: 50%
        }

        .template-no, .serial-no {
            padding-left: 20px;
        }

        .invoice-no, .invoice-date {
            width: 30%;
        }

        .total-payment-amount {
            padding-left: 20px;
            margin-bottom: 15px;
        }

        .document-commitment div {
            margin-bottom: 15px;
        }

            .document-commitment div p {
                padding-left: 20px;
            }

        .sign table tbody tr td {
            width: 50%;
            text-align: center;
        }

            .sign table tbody tr td p {
                font-style: italic;
            }
    </style>
</head>
<body>
    <table>
        <thead>
            <tr>
                <td class="header-document">
                    <h3>CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM</h3>
                    <h4>Độc lập - Tự do - Hạnh phúc</h4>
                    <h4>_ _ _ _***_ _ _ _</h4>
                    <h2>BIÊN BẢN ĐIỀU CHỈNH HÓA ĐƠN</h2>
                    <p>Số @Model.DocumentNo</p>
                </td>
            </tr>
            <tr>
                <td class="header-info">
                    <p>
                        - Căn cứ Nghị định Nghị định 123/2020/NĐ-CP Chính phủ quy định về hóa đơn bán hàng hóa, cung
                        ứng dịch vụ.<br />
                        - Căn cứ Thông tư Thông tư 78/2021/TT-BTC của Bộ Tài chính Hướng dẫn về khởi tạo, phát hành và
                        sử dụng hóa đơn điện tử bán hàng hóa, cung ứng dịch vụ.<br />
                        - Căn cứ thỏa thuận giữa các bên.
                    </p>
                </td>
            </tr>
            <tr>
                <td class="seller">
                    <h4>Bên A: <span class="seller-full-name">@Model.SellerFullName</span></h4>
                    <table>
                        <tbody>
                            <tr>
                                <td colspan="2" class="seller-address">Địa chỉ: @Model.SellerAddressLine</td>
                            </tr>
                            <tr>
                                <td class="seller-phone-number">Điện thoại: @Model.SellerPhoneNumber</td>
                                <td>Mã số thuế: @Model.SellerTaxCode</td>
                            </tr>
                            <tr>
                                <td class="seller-representative">Đại diện: @Model.SellerLegalName</td>
                                <td>Chức vụ:</td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
            <tr>
                <td class="buyer">
                    <h4>Bên B: @Model.BuyerFullName</h4>
                    <table>
                        <tbody>
                            <tr>
                                <td colspan="2" class="buyer-address">Địa chỉ: @Model.BuyerAddressLine</td>
                            </tr>
                            <tr>
                                <td class="buyer-phone-number">Điện thoại: @Model.BuyerPhoneNumber</td>
                                <td>Mã số thuế: @Model.BuyerTaxCode</td>
                            </tr>
                            <tr>
                                <td class="buyer-representative">Đại diện: @Model.BuyerLegalName</td>
                                <td>Chức vụ:</td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td class="document-decided">
                    <p>
                        Hôm nay ngày @Model.DocumentDate.Value.Day.ToString("00") tháng @Model.DocumentDate.Value.Month.ToString("00") năm @Model.DocumentDate.Value.Year, Hai bên thống nhất lập biên bản này để điều chỉnh hóa đơn với nội dung sau:
                    </p>
                </td>
            </tr>
            @if (Model.InvoiceReference != null)
            {
                <tr>
                    <td class="invoice-info">
                        <table>
                            <tbody>
                                <tr>
                                    <td colspan="3" class="template-no">- Mẫu số: <b>@Model.InvoiceReference.TemplateNo</b></td>
                                </tr>
                                <tr>
                                    <td class="serial-no">- Ký hiệu: <b>@Model.InvoiceReference.SerialNo</b></td>
                                    <td class="invoice-no">Số hóa đơn: <b>@Model.InvoiceReference.InvoiceNo</b></td>
                                    <td class="invoice-date">Ngày lập: <b>@($"{Model.InvoiceReference.InvoiceDate.Day.ToString("00")}/{Model.InvoiceReference.InvoiceDate.Month.ToString("00")}/{Model.InvoiceReference.InvoiceDate.Year}")</b></td>
                                </tr>
                                <tr>
                                    <td colspan="3" class="total-payment-amount">- Giá trị hóa đơn: <b>@Model.InvoiceReference.TotalPaymentAmount.ToString("#,##0.####")</b></td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td class="document-commitment">
                        @{
                            var oldValue = new List<string>();
                            var newValue = new List<string>();

                            if (string.Format("{0}", Model.BuyerFullName) != string.Format("{0}", Model.InvoiceReference.BuyerFullName))
                            {
                                oldValue.Add($"tên đơn vị cũ: {Model.InvoiceReference.BuyerFullName}");
                                newValue.Add($"tên đơn vị mới: {Model.BuyerFullName}");
                            }
                            if (string.Format("{0}", Model.BuyerLegalName) != string.Format("{0}", Model.InvoiceReference.BuyerLegalName))
                            {
                                oldValue.Add($"tên người mua cũ: {Model.InvoiceReference.BuyerLegalName}");
                                newValue.Add($"tên người mua mới: {Model.BuyerLegalName}");
                            }
                            if (string.Format("{0}", Model.BuyerAddressLine) != string.Format("{0}", Model.InvoiceReference.BuyerAddressLine))
                            {
                                oldValue.Add($"tên địa chỉ cũ: {Model.InvoiceReference.BuyerAddressLine}");
                                newValue.Add($"tên địa chỉ mới: {Model.BuyerAddressLine}");
                            }
                            if (string.Format("{0}", Model.BuyerTaxCode) != string.Format("{0}", Model.InvoiceReference.BuyerTaxCode))
                            {
                                oldValue.Add($"mã số thuế cũ: {Model.InvoiceReference.BuyerTaxCode}");
                                newValue.Add($"mã số thuế mới: {Model.BuyerTaxCode}");
                            }
                            if (string.Format("{0}", Model.BuyerEmail) != string.Format("{0}", Model.InvoiceReference.BuyerEmail))
                            {
                                oldValue.Add($"email cũ: {Model.InvoiceReference.BuyerEmail}");
                                newValue.Add($"email mới: {Model.BuyerEmail}");
                            }
                            if (string.Format("{0}", Model.BuyerBankAccount) != string.Format("{0}", Model.InvoiceReference.BuyerBankAccount))
                            {
                                oldValue.Add($"số tài khoản cũ: {Model.InvoiceReference.BuyerBankAccount}");
                                newValue.Add($"số tài khoản mới: {Model.BuyerBankAccount}");
                            }
                            var a = string.Format("{0}", Model.BuyerBankName);
                            var b = string.Format("{0}", Model.InvoiceReference.BuyerBankName);
                            if (string.Format("{0}", Model.BuyerBankName) != string.Format("{0}", Model.InvoiceReference.BuyerBankName))
                            {
                                oldValue.Add($"tên ngân hàng cũ: {Model.InvoiceReference.BuyerBankName}");
                                newValue.Add($"tên ngân hàng mới: {Model.BuyerBankName}");
                            }
                        }
                        <div>
                            <span>Lý do điều chỉnh: <b>@Model.DocumentReason</b></span>
                            <br />
                            <p>
                                - Trước ghi là:
                                @if (oldValue.Count > 0)
                                {
                                    <b>
                                        @($"{string.Join("; ", oldValue)}")
                                    </b>
                                }
                            </p>
                            <p>
                                - Nay điều chỉnh là:
                                @if (newValue.Count > 0)
                                {
                                    <b>
                                        @($"{string.Join("; ", newValue)}")
                                    </b>
                                }
                            </p>
                        </div>
                        <div>
                            Chúng tôi xin cam kết các thông tin khai ở trên là hoàn toàn chính xác. Nếu có bất kỳ sai sót nào chúng tôi xin
                            chịu trách nhiệm trước pháp luật.<br />
                            Biên bản này được lập thành 02 bản, bên A giữ 01 bản, bên B giữ 01 bản.
                        </div>
                    </td>
                </tr>
            }
            else
            {
                <tr>
                    <td class="invoice-info">
                        <table>
                            <tbody>
                                <tr>
                                    <td colspan="3" class="template-no">- Mẫu số: <b>@Model.TemplateNo</b></td>
                                </tr>
                                <tr>
                                    <td class="serial-no">- Ký hiệu: <b>@Model.SerialNo</b></td>
                                    <td class="invoice-no">Số hóa đơn: <b>@Model.InvoiceNo</b></td>
                                    <td class="invoice-date">Ngày lập: <b>@($"{Model.InvoiceDate.Day.ToString("00")}/{Model.InvoiceDate.Month.ToString("00")}/{Model.InvoiceDate.Year}")</b></td>
                                </tr>
                                <tr>
                                    <td colspan="3" class="total-payment-amount">- Giá trị hóa đơn: <b>@Model.TotalPaymentAmount.ToString("#,##0.####")</b></td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td class="document-commitment">
                        <div>
                            <span>Lý do điều chỉnh: <b>@Model.DocumentReason</b></span>
                        </div>
                        <div>
                            Chúng tôi xin cam kết các thông tin khai ở trên là hoàn toàn chính xác. Nếu có bất kỳ sai sót nào chúng tôi xin
                            chịu trách nhiệm trước pháp luật.<br />
                            Biên bản này được lập thành 02 bản, bên A giữ 01 bản, bên B giữ 01 bản.
                        </div>
                    </td>
                </tr>
            }
            <tr>
                <td class="sign">
                    <table>
                        <tbody>
                            <tr>
                                <td>
                                    <h4>ĐẠI DIỆN BÊN A</h4>
                                    <p>(Ký, đóng dấu, ghi rõ họ tên)</p>
                                </td>
                                <td>
                                    <h4>ĐẠI DIỆN BÊN B</h4>
                                    <p>(Ký, đóng dấu, ghi rõ họ tên)</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
        </tbody>
    </table>
</body>
</html>