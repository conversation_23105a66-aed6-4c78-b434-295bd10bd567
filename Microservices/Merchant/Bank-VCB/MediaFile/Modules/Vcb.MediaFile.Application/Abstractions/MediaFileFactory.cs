using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using VnisCore.Core.Oracle.Domain.Entities.Media;
using VnisCore.MediaFile.Application.Interfaces;

namespace VnisCore.MediaFile.Application.Abstractions
{
    public class MediaFileFactory : IMediaFileFactory
    {
        private readonly IEnumerable<IMediaFileService> _fileServices;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;

        public MediaFileFactory(IEnumerable<IMediaFileService> fileServices,
            IStringLocalizer<CoreLocalizationResource> localizer)
        {
            _fileServices = fileServices;
            _localizer = localizer;
        }

        public IMediaFileService GetService(MediaFileType fileType)
        {
            var type = GetTypeMedia(fileType);
            var name = $"{type.Name.Replace("Entity", string.Empty)}Service";

            var service = _fileServices.FirstOrDefault(x => x.GetType().Name == name);
            if (service == null)
                throw new Exception(string.Format(_localizer["Vnis.BE.MediaFile.GetService.NotFound"], typeof(IMediaFileService).Name, name));

            return service;
        }

        private Type GetTypeMedia(MediaFileType fileType)
        {
            switch (fileType)
            {
                case MediaFileType.File:
                    return typeof(MediaFileEntity);
                case MediaFileType.TemplateDesign:
                    return typeof(InvoiceTemplateDesignEntity);
                default:
                    throw new Exception(_localizer["Vnis.BE.MediaFile.FileIncorrect"]);
            }
        }
    }
}
