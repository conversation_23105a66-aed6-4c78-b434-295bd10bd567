using Core.Shared.Constants;
using MediatR;
using Microsoft.AspNetCore.Http;
using System;
using VnisCore.MediaFile.Application.Models.Responses;

namespace VnisCore.MediaFile.Application.Models.Requests
{
    public class UploadMediaFileRequestModel : IRequest<UploadMediaFileResponseModel>
    {
        public IFormFile File { get; set; }
        public Guid TenantId { get; set; }
        public Guid UserId { get; set; }

        public MediaFileType FileType { get; set; }
    }
}
