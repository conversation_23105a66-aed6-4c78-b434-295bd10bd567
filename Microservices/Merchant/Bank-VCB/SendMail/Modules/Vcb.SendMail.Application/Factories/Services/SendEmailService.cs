using Core;
using Core.Caching;
using Core.Localization.Resources.AbpLocalization;
using Core.MultiTenancy;
using Core.SettingManagement;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.Shared.Models;
using Core.Shared.Services;
using Core.Uow;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.MongoDB.Entities;
using VnisCore.Core.MongoDB.IRepository;
using VnisCore.Core.MongoDB.Repository;
using VnisCore.SendMail.Application.Factories.Interfaces;
using VnisCore.SendMail.Application.Factories.Models;
using VnisCore.SendMail.Application.Factories.Models.GenerateContents;

namespace VnisCore.SendMail.Application.Factories.Services
{
    public class SendEmailService : ISendEmailService
    {
        private readonly IAppFactory _appFactory;
        private readonly IServiceProvider _serviceProvider;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly ILogger<SendEmailService> _logger;
        protected IUnitOfWorkManager UnitOfWorkManager { get; }

        public SendEmailService(
            IAppFactory appFactory,
            IServiceProvider serviceProvider,
            ILogger<SendEmailService> logger,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IUnitOfWorkManager unitOfWorkManager)
        {
            _appFactory = appFactory;
            _serviceProvider = serviceProvider;
            _logger = logger;
            _localizer = localizer;
            UnitOfWorkManager = unitOfWorkManager;
        }

        public async Task SendMailAsync(SendMailModel model)
        {
            //lấy thông tin email
            var repoEmail = _appFactory.GetServiceDependency<IVnisCoreMongoEmailRepository>();
            var email = await repoEmail.GetAsync(model.Id);

            if (email == null)
                throw new UserFriendlyException(_localizer["Vnis.BE.SendMail.EmailInvoice.EmailNotFound"]);

            List<KeyValuePair<string, byte[]>> fileAttachments = await GetAttachments(email);

            var smtpOption = await GetConfigAsync(email.TenantId);
            var logger = _serviceProvider.GetService<ILogger<EmailSender>>();
            var emailSender = new EmailSender(smtpOption, logger, _localizer);

            //email.Status = (short)MailStatus.Sending.GetHashCode();
            //email.From = smtpOption.From;

            //using var uow = UnitOfWorkManager.Begin(true);
            //await repoEmail.UpdateAsync(email);
            //await uow.CompleteAsync();

            //var cc = string.IsNullOrEmpty(email.Cc) ? null : email.Cc.Split(';');
            //var bcc = string.IsNullOrEmpty(email.Bcc) ? null : email.Bcc.Split(';');

            //try
            //{
            //    await emailSender.SendAsync(email.Subject, email.Content, email.To, cc, bcc, fileAttachments);
            //    email.Status = (short)MailStatus.Success.GetHashCode();
            //}
            //catch (Exception ex)
            //{
            //    email.Status = (short)MailStatus.Error.GetHashCode();
            //    throw new UserFriendlyException(ex.Message);
            //}

            //await repoEmail.UpdateAsync(email);
            //await uow.CompleteAsync();

            email.Status = (short)MailStatus.Sending.GetHashCode();
            email.From = smtpOption.From;
            await repoEmail.UpdateAsync(email, true);

            var cc = string.IsNullOrEmpty(email.Cc) ? null : email.Cc.Split(';');
            var bcc = string.IsNullOrEmpty(email.Bcc) ? null : email.Bcc.Split(';');

            try
            {
                Log.Debug($"Bắt đầu gửi mail {email.EmailAction}-{email.Subject}-{email.UserName}");
                await emailSender.SendAsync(email.Subject, email.Content, email.To, cc, bcc, fileAttachments);
                Log.Debug($"Gửi mail {email.EmailAction}-{email.Subject}-{email.UserName} thành công!");

                email.Status = (short)MailStatus.Success.GetHashCode();
            }
            catch (Exception ex)
            {
                email.Status = (short)MailStatus.Error.GetHashCode();
                Log.Error(ex.Message);
            }

            await repoEmail.UpdateAsync(email, true);
        }

        private async Task<List<KeyValuePair<string, byte[]>>> GetAttachments(MongoEmailEntity email)
        {
            //lấy các giá trị file đính kém trong attachments
            //xem có file đính kèm không
            var attachments = new Dictionary<Guid, AttachmentModel>();
            var fileAttachments = new List<KeyValuePair<string, byte[]>>();

            if (!string.IsNullOrEmpty(email.Attachments))
            {
                attachments = JsonConvert.DeserializeObject<List<AttachmentModel>>(email.Attachments)
                                         .ToDictionary(x => x.Id, x => x);

                //lấy file nếu file là file export
                var attachmentExports = attachments.Where(x => x.Key == Guid.Empty);

                //lấy các file export
                foreach (var attachment in attachmentExports)
                {
                    //đọc file từ minio 
                    var fileService = _serviceProvider.GetService<IFileService>();
                    var pathFileMinio = $"Temps/{email.TenantId}/{attachment.Value.Name}";

                    try
                    {
                        //download file từ minio
                        fileAttachments.Add(new KeyValuePair<string, byte[]>(attachment.Value.Name, await fileService.DownloadAsync(pathFileMinio)));

                        //xóa file minio đi
                        await fileService.DeleteAsync(pathFileMinio);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, ex.Message);
                    }
                }
            }

            return fileAttachments;
        }

        /// <summary>
        /// lấy thông tin gửi mail của khách hàng
        /// </summary>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        public async Task<SmtpOption> GetConfigAsync(Guid tenantId)
        {
            var cache = _appFactory.GetServiceDependency<IDistributedCache<List<TenantSetting>>>();
            var cacheKey = SettingCacheItem.CalculateCacheKey($"{tenantId}:{GroupSettingKey.Smtp.ToString()}", null, null);

            var settingCacheValue = await cache.GetAsync(cacheKey);
            if (settingCacheValue != null && settingCacheValue.Any())
                return new SmtpOption
                {
                    Host = settingCacheValue.FirstOrDefault(x => x.Code == SettingKey.SmtpHost.ToString()).Value,
                    Port = string.IsNullOrEmpty(settingCacheValue.FirstOrDefault(x => x.Code == SettingKey.SmtpPort.ToString()).Value) ? 0 : int.Parse(settingCacheValue.FirstOrDefault(x => x.Code == SettingKey.SmtpPort.ToString()).Value),
                    Socket = string.IsNullOrEmpty(settingCacheValue.FirstOrDefault(x => x.Code == SettingKey.SmtpSocket.ToString()).Value) ? 0 : int.Parse(settingCacheValue.FirstOrDefault(x => x.Code == SettingKey.SmtpSocket.ToString()).Value),
                    UserName = settingCacheValue.FirstOrDefault(x => x.Code == SettingKey.SmtpUserName.ToString()).Value,
                    Password = settingCacheValue.FirstOrDefault(x => x.Code == SettingKey.SmtpPassword.ToString()).Value,
                    From = settingCacheValue.FirstOrDefault(x => x.Code == SettingKey.SmtpFrom.ToString()).Value,
                    FromName = settingCacheValue.FirstOrDefault(x => x.Code == SettingKey.SmtpFromName.ToString()).Value,
                    Authentication = !string.IsNullOrEmpty(settingCacheValue.FirstOrDefault(x => x.Code == SettingKey.SmtpAuthentication.ToString()).Value) &&
                                    settingCacheValue.FirstOrDefault(x => x.Code == SettingKey.SmtpAuthentication.ToString()).Value == "1",
                };

            var repoSetting = _serviceProvider.GetService<ISettingService>();

            var settings = await repoSetting.GetByGroupCodeAsync(tenantId, GroupSettingKey.Smtp.ToString());

            var setting = new TenantSetting();

            var host = settings.FirstOrDefault(x => x.Code == SettingKey.SmtpHost.ToString());
            if (host != null)
                settings.Add(host);

            var authentication = settings.FirstOrDefault(x => x.Code == SettingKey.SmtpAuthentication.ToString());
            if (authentication != null)
                settings.Add(authentication);

            var port = settings.FirstOrDefault(x => x.Code == SettingKey.SmtpPort.ToString());
            if (port != null)
                settings.Add(port);

            var socket = settings.FirstOrDefault(x => x.Code == SettingKey.SmtpSocket.ToString());
            if (socket != null)
                settings.Add(socket);
            //setting.Socket = string.IsNullOrEmpty(socket.Value) ? 0 : int.Parse(socket.Value);

            var username = settings.FirstOrDefault(x => x.Code == SettingKey.SmtpUserName.ToString());
            if (username != null)
                settings.Add(username);
            //setting.UserName = username.Value;

            var password = settings.FirstOrDefault(x => x.Code == SettingKey.SmtpPassword.ToString());
            if (password != null)
                settings.Add(password);
            //setting.Password = password.Value;

            var from = settings.FirstOrDefault(x => x.Code == SettingKey.SmtpFrom.ToString());
            if (from != null)
                settings.Add(from);
            //setting.From = from.Value;

            var fromname = settings.FirstOrDefault(x => x.Code == SettingKey.SmtpFromName.ToString());
            if (fromname != null)
                settings.Add(fromname);
            //setting.FromName = fromname.Value;

            await cache.SetAsync(cacheKey,
                settings,
                new DistributedCacheEntryOptions().SetSlidingExpiration(TimeSpan.FromMinutes(10)));
            return new SmtpOption
            {
                Host = host.Value,
                Port = string.IsNullOrEmpty(port.Value) ? 0 : int.Parse(port.Value),
                Socket = string.IsNullOrEmpty(socket.Value) ? 0 : int.Parse(socket.Value),
                UserName = username.Value,
                Password = password.Value,
                From = from.Value,
                FromName = fromname.Value,
                Authentication = !string.IsNullOrEmpty(authentication.Value) && authentication.Value == "1",
            };
        }

    }
}
