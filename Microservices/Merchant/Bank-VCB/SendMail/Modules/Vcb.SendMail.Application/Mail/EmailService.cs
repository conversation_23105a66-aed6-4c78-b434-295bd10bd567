using Core.Application.Dtos;
using Core.Application.Services;
using Core.Shared.Factory;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.MongoDB.Entities;
using VnisCore.Core.MongoDB.IRepository;
using VnisCore.Core.MongoDB.Repository;
using VnisCore.Core.Oracle.Application.Contracts.Permissions.System.SendMail;
using VnisCore.SendMail.Application.Mail.Dto;
using VnisCore.SendMail.Application.Mail.Model.Requests.Commands.Emails;

namespace VnisCore.SendMail.Application.EmailInvoice
{
    [Authorize(SendMailPermissions.SendMail.Default)]
    public class EmailService : ApplicationService, IEmailService
    {
        private readonly IAppFactory _appFactory;
        private readonly IVnisCoreMongoEmailRepository _repoEmail;

        public EmailService(IAppFactory appFactory,
            IVnisCoreMongoEmailRepository repoEmail)
        {
            _appFactory = appFactory;
            _repoEmail = repoEmail;
        }

        [Authorize(SendMailPermissions.SendMail.Default)]
        [HttpPost(Utilities.ApiUrlBase + "GetList")]
        public async Task<PagedResultDto<MongoEmailEntity>> GetList(EmailPagedRequestDto input)
        {
            var tenantId = _appFactory.CurrentTenant.Id;
            var keyword = input.Keyword;

            var count = await _repoEmail.CountAsync(tenantId, input.Sorting, input.Keyword);

            var emailInvoices = (await _repoEmail.GetListAsync(tenantId, input.Sorting, input.MaxResultCount, input.SkipCount, input.Keyword));

            return new PagedResultDto<MongoEmailEntity>
            {
                TotalCount = count,
                Items = emailInvoices
            };
        }

        //[HttpPost(Utilities.ApiUrlBase + "GetById")]
        //public async Task<EmailDto> GetById(Guid id)
        //{
        //    var entity = await _repoEmail.GetAsync(id);
        //    return ObjectMapper.Map<MongoEmailEntity, EmailDto>(entity);
        //}

        [Authorize(SendMailPermissions.SendMail.ReSend)]
        [HttpPost(Utilities.ApiUrlBase + "Resend/{id}")]
        public async Task Resend([FromRoute] Guid id)
        {
            await _appFactory.Mediator.Send(new ResendEmailRequestModel
            {
                Id = id
            });
        }
    }
}