using Core;
using Core.Application.Dtos;
using Core.Application.Services;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.DataExporting;
using Core.Shared.Dto;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Dapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Vcb.Report.Application.LoginLogoutReport.Dtos;
using Vcb.Report.Application.LoginLogoutReport.StoredProcedure;
using VnisCore.Core.Oracle.Application.Contracts.Permissions.Report.LoginLogoutReport;
using VnisCore.Report.Application;

namespace Vcb.Report.Application.LoginLogoutReport
{
    [Authorize(LoginLogoutReportPermissions.LoginLogoutReport.Default)]
    public class LoginLogoutReportService : ApplicationService, ILoginLogoutReportService
    {
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;

        public LoginLogoutReportService(IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizer)
        {
            _appFactory = appFactory;
            _localizer = localizer;
        }

        [HttpPost(Utilities.ApiUrlBase + "getlist")]
        public async Task<PagedResultDto<LoginLogoutReportDto>> GetListAsync(LoginLogoutReportPagedRequestDto input)
        {
            return await GetListDataAsync(input, false);
        }

        [Authorize(LoginLogoutReportPermissions.LoginLogoutReport.ExportExcel)]
        [HttpPost(Utilities.ApiUrlBase + "export-excel")]
        public async Task<FileDto> ExportExcelAsync(LoginLogoutReportPagedRequestDto input)
        {
            var _data = await GetListDataAsync(input, true);
            var data = _data.Items
                .Select((p, i) => new
                {
                    Index = i + 1,
                    p.UserName,
                    p.LoginTime,
                    p.LogoutTime
                });
            if (data.Count() == 0)
                throw new UserFriendlyException(_localizer["Vnis.BE.ExportData.NoData"]);

            // get setting
            var fileName = "Baocaotruycap";
            var reportCode = SettingKey.LoginLogoutReport.ToString();
            var setting = _appFactory.CurrentTenant.Settings.FirstOrDefault(p => p.Code == reportCode);
            if (setting != null)
            {
                var extraProperties = setting.ExtraProperties.ContainsKey("key")
                        ? JsonConvert.DeserializeObject<Dictionary<string, object>>(setting.ExtraProperties["key"])
                        : new Dictionary<string, object>();
                var _req = new ExportFlexCelReportByFluentConfigRequest
                {
                    SampleFileFolder = "vninvoice/excel/report/login-logout-report",
                    SampleFile = $"{fileName}.xlsx",
                    OutputFileNameNotExtension = $"{fileName}{DateTime.Now:yyyyMMddHHmmss}",
                    OutputFileType = OutputFileExtension.Excel,
                    FlexCelAction = fr =>
                    {
                        fr.SetValue("fromDate", input.FromDate.HasValue ? input.FromDate.Value.ToString("dd/MM/yyyy") : "");
                        fr.SetValue("toDate", input.ToDate.HasValue ? input.ToDate.Value.ToString("dd/MM/yyyy") : "");
                        fr.AddTable("tbData", data);
                    },
                    Font = extraProperties.ContainsKey("Font")
                        ? extraProperties["Font"].ToString()
                        : "Times New Roman",
                    FontSize = extraProperties.ContainsKey("FontSize")
                        ? int.Parse(extraProperties["FontSize"].ToString())
                        : 11,
                    PropertyCellDtos = extraProperties.ContainsKey("Colums")
                        ? JsonConvert.DeserializeObject<IEnumerable<PropertyCellDto>>(extraProperties["Colums"].ToString())
                        : Enumerable.Empty<PropertyCellDto>(),
                    MaxRowDataFrame = 7,
                    MaxColumnDataFrame = 4,
                    ExtraProperties = extraProperties.ContainsKey("ExtraProperties")
                     ? JsonConvert.DeserializeObject<IEnumerable<IDictionary<string, object>>>(extraProperties["ExtraProperties"].ToString())
                     : Enumerable.Empty<IDictionary<string, object>>(),
                };
                return await _appFactory.Mediator.Send(_req);
            }


            // default
            var req = new ExportFlexCelExcelReportRequest
            {
                FileFolder = "vninvoice/excel/report/login-logout-report",
                FileName = fileName,
                Data = data,
                FromeDate = input.FromDate.HasValue ? input.FromDate.Value.ToString("dd/MM/yyyy") : "",
                ToDate = input.ToDate.HasValue ? input.ToDate.Value.ToString("dd/MM/yyyy") : "",
                BeginRow = 7,
                BeginCol = 1
            };
            return await _appFactory.Mediator.Send(req);
        }
        private async Task<PagedResultDto<LoginLogoutReportDto>> GetListDataAsync(LoginLogoutReportPagedRequestDto input, bool isExport)
        {
            // tenantId
            var tenantId = _appFactory.CurrentTenant.Id;
            if (tenantId == null)
                throw new Exception(_localizer["Vnis.BE.TenantId.NoData"]);

            // UserNames
            var userNames = new List<string>();
            if (input.UserNames != null && input.UserNames.Any())
            {
                userNames = input.UserNames.ToList();
            }

            // fromDate
            DateTime? fromDate = null;
            if (input.FromDate.HasValue)
            {
                fromDate = input.FromDate.Value.AddMinutes(-1);
            }

            // toDate
            DateTime? toDate = null;
            if (input.ToDate.HasValue)
            {
                toDate = input.ToDate.Value.AddDays(1);
            }

            return await GetListDataToDbAsync(tenantId, userNames, fromDate, toDate, input.SkipCount, input.MaxResultCount, isExport);
        }
        private async Task<PagedResultDto<LoginLogoutReportDto>> GetListDataToDbAsync(Guid? tenantId, List<string> userNames, DateTime? fromDate, DateTime? toDate, int skipCount, int maxResultCount, bool isExport)
        {
            var dicItems = new Dictionary<string, object>();
            var items = await _appFactory
                .AuthDatabase
                .Connection
                .QueryAsync<LoginLogoutReportDto>(
                    LoginLogoutReportStoredProcedureName.GetCommandQueryListLoginLogoutReport(tenantId, userNames, fromDate, toDate, skipCount, maxResultCount, isExport, dicItems),
                    new DynamicParameters(dicItems),
                    null,
                    null,
                    CommandType.Text
                ) as List<LoginLogoutReportDto>;
            items.Select(p =>
            {
                p.LogoutTime = !string.IsNullOrWhiteSpace(p.LogoutTime) ? p.LogoutTime : _localizer["Vnis.BE.Report.LoginLogoutReport.PersistentLogin"];
                return p;
            }).ToList();

            if (isExport)
            {
                return new PagedResultDto<LoginLogoutReportDto>
                {
                    TotalCount = 0,
                    Items = items
                };
            }

            var dicTotal = new Dictionary<string, object>();
            var total = await _appFactory
                .AuthDatabase
                .Connection
               .QueryFirstAsync<int>(
                    LoginLogoutReportStoredProcedureName.CounterCommandQueryListLoginLogoutReport(tenantId, userNames, fromDate, toDate, dicTotal),
                    new DynamicParameters(dicTotal),
                    null,
                    null,
                    CommandType.Text
                );
            return new PagedResultDto<LoginLogoutReportDto>
            {
                TotalCount = total,
                Items = items
            };
        }
    }
}
