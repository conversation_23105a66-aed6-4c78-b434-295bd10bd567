using Core.Shared.Attributes;
using Core.Shared.Dto;
using IdentityServer4.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Vcb.Report.Application.StatusUserReport.Dtos
{
    public class StatusUserReportPagedRequestDto : PagedFullRequestDto
    {
        public string TypeUser { get; set; }

        public string StatusUser { get; set; }

        [DataType(DataType.Date)]
        [LessThanCurrentDate(ErrorMessage = "Vnis.BE.Report.DataFluctuationsReportPagedRequestDto.FromDate.LessThanCurrentDate")]
        [LessThanDate("ToDate", ErrorMessage = "Vnis.BE.Report.DataFluctuationsReportPagedRequestDto.FromDate.LessThanDate")]
        public DateTime? FromDate { get; set; }

        [DataType(DataType.Date)]
        [LessThanCurrentDate(ErrorMessage = "Vnis.BE.Report.DataFluctuationsReportPagedRequestDto.ToDate.LessThanCurrentDate")]
        //[MoreThanDate("FromDate", ErrorMessage = "Vnis.BE.Report.DataFluctuationsReportPagedRequestDto.ToDate.MoreThanDate")]
        public DateTime? ToDate { get; set; }
    }
}
