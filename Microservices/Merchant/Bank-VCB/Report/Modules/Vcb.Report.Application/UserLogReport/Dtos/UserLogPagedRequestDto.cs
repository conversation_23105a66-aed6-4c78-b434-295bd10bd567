using Core.Application.Dtos;
using Core.Shared.Attributes;
using Core.Shared.Dto;
using IdentityServer4.Models;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Vcb.Report.Application.UserLogReport.Dtos
{
    public class UserLogPagedRequestDto : PagedFullRequestDto
    {
        public IEnumerable<string> UserNames { get; set; } =  Enumerable.Empty<string>();

        [DataType(DataType.Date)]
        [LessThanCurrentDate(ErrorMessage = "Vnis.BE.Report.DataFluctuationsReportPagedRequestDto.FromDate.LessThanCurrentDate")]
        [LessThanDate("ToDate", ErrorMessage = "Vnis.BE.Report.DataFluctuationsReportPagedRequestDto.FromDate.LessThanDate")]
        public DateTime? FromDate { get; set; }

        [DataType(DataType.Date)]
        [LessThanCurrentDate(ErrorMessage = "Vnis.BE.Report.DataFluctuationsReportPagedRequestDto.ToDate.LessThanCurrentDate")]
        //[MoreThanDate("FromDate", ErrorMessage = "Vnis.BE.Report.DataFluctuationsReportPagedRequestDto.ToDate.MoreThanDate")]
        public DateTime? ToDate { get; set; }
    }
}
