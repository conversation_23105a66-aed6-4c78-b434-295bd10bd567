using Core;
using Core.Application.Dtos;
using Core.Application.Services;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.DataExporting;
using Core.Shared.Dto;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Dapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Nest;
using Newtonsoft.Json;
using NUglify.Helpers;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Vcb.Report.Application.ConfigReport.Dtos;
using Vcb.Report.Application.SettingReport.StoredProcedure;
using VnisCore.Core.Oracle.Application.Contracts.Permissions.Report.InvoiceSettingReport;
using VnisCore.Report.Application;

namespace Vcb.Report.Application.ConfigReport
{
    [Authorize(InvoiceSettingReportPermissions.InvoiceSettingReport.Default)]
    public class SettingReportService : ApplicationService, ISettingReportService
    {
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly ISettingReportStoredProcedureName _settingReportStoredProcedureName;

        public SettingReportService(IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizer,
            ISettingReportStoredProcedureName settingReportStoredProcedureName
            )
        {
            _appFactory = appFactory;
            _localizer = localizer;
            _settingReportStoredProcedureName = settingReportStoredProcedureName;
        }


        [HttpPost(Utilities.ApiUrlBase + "getlist")]
        public async Task<PagedResultDto<SettingReportDto>> GetListAsync(SettingReportPagedRequestDto input)
        {
            return await GetListDataAsync(input, false);
        }

        [Authorize(InvoiceSettingReportPermissions.InvoiceSettingReport.ExportExcel)]
        [HttpPost(Utilities.ApiUrlBase + "export-excel")]
        public async Task<FileDto> ExportExcelAsync(SettingReportPagedRequestDto input)
        {
            var _data = await GetListDataAsync(input, true);
            var data = _data.Items
                .Select((p, i) => new
                {
                    Index = i + 1,
                    p.SettingName,
                    p.OldSettingName,
                    p.NewSettingName,
                    p.ModificationTime,
                    p.ModificationBy,
                    p.ParentNameSetting,
                    p.GroupNameSetting,
                    p.Manual
                });
            if (data.Count() == 0)
                throw new UserFriendlyException(_localizer["Vnis.BE.ExportData.NoData"]);

            // get setting
            var fileName = "Baocaothaydoicauhinh";
            var reportCode = SettingKey.ChangeSettingReport.ToString();
            var setting = _appFactory.CurrentTenant.Settings.FirstOrDefault(p => p.Code == reportCode);
            if (setting != null)
            {
                var extraProperties = setting.ExtraProperties.ContainsKey("key")
                        ? JsonConvert.DeserializeObject<Dictionary<string, object>>(setting.ExtraProperties["key"])
                        : new Dictionary<string, object>();
                var _req = new ExportFlexCelReportByFluentConfigRequest
                {
                    SampleFileFolder = "vninvoice/excel/report/setting-report",
                    SampleFile = $"{fileName}.xlsx",
                    OutputFileNameNotExtension = $"{fileName}{DateTime.Now:yyyyMMddHHmmss}",
                    OutputFileType = OutputFileExtension.Excel,
                    FlexCelAction = fr =>
                    {
                        fr.SetValue("fromDate", input.FromDate.HasValue ? input.FromDate.Value.ToString("dd/MM/yyyy") : "");
                        fr.SetValue("toDate", input.ToDate.HasValue ? input.ToDate.Value.ToString("dd/MM/yyyy") : "");
                        fr.AddTable("tbData", data);
                    },
                    Font = extraProperties.ContainsKey("Font")
                        ? extraProperties["Font"].ToString()
                        : "Times New Roman",
                    FontSize = extraProperties.ContainsKey("FontSize")
                        ? int.Parse(extraProperties["FontSize"].ToString())
                        : 11,
                    PropertyCellDtos = extraProperties.ContainsKey("Colums")
                        ? JsonConvert.DeserializeObject<IEnumerable<PropertyCellDto>>(extraProperties["Colums"].ToString())
                        : Enumerable.Empty<PropertyCellDto>(),
                    MaxRowDataFrame = 7,
                    MaxColumnDataFrame = 8,
                    ExtraProperties = extraProperties.ContainsKey("ExtraProperties")
                     ? JsonConvert.DeserializeObject<IEnumerable<IDictionary<string, object>>>(extraProperties["ExtraProperties"].ToString())
                     : Enumerable.Empty<IDictionary<string, object>>(),
                };
                return await _appFactory.Mediator.Send(_req);
            }

            // default
            var req = new ExportFlexCelExcelReportRequest
            {
                FileFolder = "vninvoice/excel/report/setting-report",
                FileName = fileName,
                Data = data,
                FromeDate = input.FromDate.HasValue ? input.FromDate.Value.ToString("dd/MM/yyyy") : "",
                ToDate = input.ToDate.HasValue ? input.ToDate.Value.ToString("dd/MM/yyyy") : "",
                BeginRow = 7,
                BeginCol = 1
            };
            return await _appFactory.Mediator.Send(req);
        }
        private async Task<PagedResultDto<SettingReportDto>> GetListDataAsync(SettingReportPagedRequestDto input, bool isExport)
        {
            // tenantId
            var tenantId = _appFactory.CurrentTenant.Id;
            if (tenantId == null)
                throw new Exception(_localizer["Vnis.BE.TenantId.NoData"]);

            // typeSetting
            var typeSettings = new List<string>();
            if (input.TypeSettings != null)
            {
                typeSettings = input.TypeSettings?.ToList();
            }


            // fromDate
            DateTime? fromDate = null;
            if (input.FromDate.HasValue)
            {
                fromDate = input.FromDate.Value.AddMinutes(-1);
            }

            // toDate
            DateTime? toDate = null;
            if (input.ToDate.HasValue)
            {
                toDate = input.ToDate.Value.AddDays(1);
            }

            return await GetListDataToDbAsync(tenantId, typeSettings, fromDate, toDate, input.SkipCount, input.MaxResultCount, isExport);
        }
        private async Task<PagedResultDto<SettingReportDto>> GetListDataToDbAsync(Guid? tenantId, List<string> typeSettings, DateTime? fromDate, DateTime? toDate, int skipCount, int maxResultCount, bool isExport)
        {
            var dicItems = new Dictionary<string, object>();
            var items = await _appFactory
                .AuthDatabase
                .Connection
                .QueryAsync<SettingReportDto>(
                    _settingReportStoredProcedureName.GetCommandQueryListSettingReport(tenantId, typeSettings, fromDate, toDate, skipCount, maxResultCount, isExport, dicItems),
                    new DynamicParameters(dicItems),
                    null,
                    null,
                    CommandType.Text
                ) as List<SettingReportDto>;
            items = items.Select(p =>
            {
                p.SettingName = GetLocalizer(p.SettingName); //_localizer[$"Vnis.FE.GroupSetting.Lb.{p.SettingName}"];
                p.ParentNameSetting = GetLocalizer(p.ParentNameSetting); //_localizer[$"Vnis.FE.System.Lb.GroupSkill.{p.ParentNameSetting}"];
                p.OldSettingName = GetValueInOptions(p.Options, p.OldSettingName);
                p.NewSettingName = GetValueInOptions(p.Options, p.NewSettingName);
                p.GroupNameSetting = string.IsNullOrWhiteSpace(p.GroupCodeSetting)
                    ? string.Empty
                     : GetLocalizer(p.GroupCodeSetting); //_localizer[$"Vnis.Setting.Code.{p.GroupCodeSetting}"];
                // : EnumExtension.TryToEnum<GroupSettingKey>(p.GroupCodeSetting).ToDisplayName();
                p.Manual = _localizer[$"Vnis.FE.Setting.Lb.ChangeSettingReport.ExtraProperties.Manual.{EnumExtension.TryToEnum<SettingLogManual>(p.Manual).ToDisplayName()}"];
                return p;
            }).ToList();

            if (isExport)
            {
                return new PagedResultDto<SettingReportDto>
                {
                    TotalCount = 0,
                    Items = items
                };
            }

            var dicTotal = new Dictionary<string, object>();
            var total = await _appFactory
                .AuthDatabase
                .Connection
               .QueryFirstAsync<int>(
                    _settingReportStoredProcedureName.CounterCommandQueryListSettingReport(tenantId, typeSettings, fromDate, toDate, dicTotal),
                    new DynamicParameters(dicTotal),
                    null,
                    null,
                    CommandType.Text
                );
            return new PagedResultDto<SettingReportDto>
            {
                TotalCount = total,
                Items = items
            };
        }
        private string GetValueInOptions(string _options, string _value)
        {
            if (!string.IsNullOrWhiteSpace(_options) && !string.IsNullOrWhiteSpace(_value) && _options.Contains(_value) && int.TryParse(_value, out int value))
            {
                var options = _options.Split("|");
                foreach (var option in options)
                {
                    var keyValue = option.Split(":");
                    if (keyValue.Length == 2 && int.TryParse(keyValue[0], out int key) && key == value)
                        return keyValue[1];
                }
            }
            return _value;
        }
        private string GetLocalizer(string key)
        {
            if (string.IsNullOrWhiteSpace(key))
                return string.Empty;

            var value = _localizer[key];
            if (value != key)
                return value;

            value = _localizer[$"Vnis.FE.GroupSetting.Lb.{key}"];
            if (value != $"Vnis.FE.GroupSetting.Lb.{key}")
                return value;

            value = _localizer[$"Vnis.FE.System.Lb.GroupSkill.{key}"];
            if (value != $"Vnis.FE.System.Lb.GroupSkill.{key}")
                return value;

            value = _localizer[$"Vnis.Setting.Code.{key}"];
            if (value != $"Vnis.Setting.Code.{key}")
                return value;

            return key;
        }
    }
}
