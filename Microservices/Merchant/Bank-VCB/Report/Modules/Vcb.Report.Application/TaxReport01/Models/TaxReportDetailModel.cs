using System;
using System.Collections.Generic;

namespace Vcb.Report.Application.TaxReport01.Models
{
    public class TaxReportDetailModel
    {
        public string SellerTaxCode { get; set; }
        public int Month { get; set; }
        public long TaxReportNumber { get; set; }
        public List<long> InvoiceIds { get; set; }
        public int? AdditionalTimes { get; set; }
        public DateTime TaxReportCreationDate { get; set; }
        public string Currency { get; set; }
        public DateTime? SellerSignedTime { get; set; }
    }
}
