using System;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Core.DependencyInjection;
using Core.Domain.Repositories;
using Core.EventBus.Distributed;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.Uow;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Serilog;
using Vcb.Report.Application.ReportReconcileInvoiceMinio.RabbitMqEventBus.MessageEventData;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Core.Oracle.Domain.Entities.Reports;

namespace Vcb.Report.Application.ReportReconcileInvoiceMinio.RabbitMqEventBus.ReceivedEventHandler
{
    public class CheckInvoiceDataExistMinIOReceivedEventHandler : IDistributedEventHandler<CheckInvoiceDataExistMinIOSendData>, ITransientDependency
    {
        private readonly IAppFactory _appFactory;
        private readonly IRepository<ReportReconcileInvoiceMinioDetailEntity, long> _repoReportDetail;
        private readonly IRepository<Invoice01XmlEntity> _invoice01XmlRepository;
        private readonly IFileService _fileService;

        public CheckInvoiceDataExistMinIOReceivedEventHandler(IAppFactory appFactory,
            IRepository<ReportReconcileInvoiceMinioDetailEntity, long> repoReportDetail,
            IRepository<Invoice01XmlEntity> invoice01XmlRepository,
            IFileService fileService)
        {
            _appFactory = appFactory;
            _repoReportDetail = repoReportDetail;
            _invoice01XmlRepository = invoice01XmlRepository;
            _fileService = fileService;
        }

        public async Task HandleEventAsync(CheckInvoiceDataExistMinIOSendData eventData)
        {
            Log.Fatal("Receive Message ReconcileInvoiceMinio in MinIO: " + eventData.InvoiceHeaderId);

            using (var unitOfWork = _appFactory.UnitOfWorkManager.Begin())
            {
                try
                {
                    var detail = await _repoReportDetail.FirstOrDefaultAsync(x => x.InvoiceHeaderId == eventData.InvoiceHeaderId);

                    // Nếu tồn tại thông tin và trạng thái là chưa đồng bộ lên MinIO thì kiểm tra
                    // Nếu có lỗi xảy ra thì cập nhật Status về lỗi để kiểm tra lại
                    if (detail != null && detail.SyncStatus != SyncXmlStatus.SyncedMinio.GetHashCode())
                    {
                        detail.LastModificationTime = DateTime.Now;

                        var xmlEntity = await _invoice01XmlRepository.Where(x => x.InvoiceHeaderId == eventData.InvoiceHeaderId).OrderBy(x => x.Id).LastOrDefaultAsync();
                        if (xmlEntity != null)
                        {
                            try
                            {
                                var pathFileMinio =
                                    $"{MediaFileType.Invoice01Xml}/{xmlEntity.TenantId}/{xmlEntity.CreationTime.Year}/{xmlEntity.CreationTime.Month:00}/{xmlEntity.CreationTime.Day:00}/{xmlEntity.PhysicalFileName}";
                                var url = await _fileService.ViewAsync(pathFileMinio, 1);

                                detail.SyncStatus = (short)SyncXmlStatus.SyncedMinio.GetHashCode();
                            }
                            catch (Exception ex)
                            {
                                Log.Fatal("ReconcileInvoiceMinio MinIO InvoiceHeaderId Error: " + ex.Message);
                                detail.SyncStatus = (short)SyncXmlStatus.SyncedMinioError.GetHashCode();
                            }
                        }
                        else
                        {
                            detail.SyncStatus = (short)SyncXmlStatus.SyncedMinioError.GetHashCode();
                        }

                        Log.Fatal("ReconcileInvoiceMinio SyncStatus: " + detail.SyncStatus);
                        await unitOfWork.SaveChangesAsync();
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, ex.StackTrace);
                    throw;
                }
            }
        }
    }
}