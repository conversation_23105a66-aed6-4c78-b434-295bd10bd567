using Core.Domain.Repositories;
using Core.EventBus.Distributed;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.TenantManagement;
using Core.Tvan.Models.Xmls.ReportInvoice;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Vcb.Report.Application.ReportReconcileInvoiceMinio.RabbitMqEventBus.MessageEventData;
using VnisCore.Core.Oracle.Domain.Entities.Reports;

namespace Vcb.Report.Application.ReportReconcileInvoiceMinio
{
    public interface IReconcileInvoiceMinioService
    {
        Task DoworkAsync();
    }

    public class ReconcileInvoiceMinioService : IReconcileInvoiceMinioService
    {
        private readonly IAppFactory _appFactory;
        private readonly IDistributedEventBus _distributedEventBus;

        public ReconcileInvoiceMinioService(IAppFactory appFactory,
            IDistributedEventBus distributedEventBus)
        {
            _appFactory = appFactory;
            _distributedEventBus = distributedEventBus;
        }
        /// <summary>
        /// </summary>
        /// <returns></returns>
        public async Task DoworkAsync()
        {
            //// Quét danh sách hóa đơn trong bảng ReportReconcileInvoiceMinioDetail xem hóa đơn nào trong 1 ngày gần nhất đồng bộ lên MinIO lỗi thì thực hiện kiểm tra lại
            string queryData = $@"
                    SELECT 
                        ""InvoiceHeaderId""
                    FROM 
                        ""ReportReconcileInvoiceMinioDetail"" rrimd
                    WHERE 
                        ""SyncStatus"" = {(short)SyncXmlStatus.SyncedMinioError.GetHashCode()} AND ""CreationTime"" >= (SYSDATE - 1)
                    ";

            var listInvoiceHeaderId = await _appFactory.VnisCoreOracle.Connection.QueryAsync<long>(queryData);
            Log.Fatal("ReconcileInvoiceMinio: Count data in ReportReconcileInvoiceMinioDetail: " + listInvoiceHeaderId.Count());

            listInvoiceHeaderId = listInvoiceHeaderId.Distinct().ToList();

            Log.Fatal("ReconcileInvoiceMinio: Job push message check invoice in MinIO: " + string.Join(", ", listInvoiceHeaderId));
            foreach (var item in listInvoiceHeaderId)
            {
                await _distributedEventBus.PublishAsync(new CheckInvoiceDataExistMinIOSendData
                {
                    InvoiceHeaderId = item
                });
            }
        }
    }
}
