using System;
using System.Threading.Tasks;
using Core.Application.Dtos;
using Core.Identity;
using Core.Users;
using Microsoft.AspNetCore.Authorization;

namespace VnisCore.System.Application.User
{
    public class UserLookupService : IdentityUserLookupAppService
    {
        public UserLookupService(IdentityUserRepositoryExternalUserLookupServiceProvider userLookupServiceProvider) : base(userLookupServiceProvider)
        {
        }

        [Authorize(IdentityPermissions.UsersPrivate.Default)]
        public override async Task<UserData> FindByIdAsync(Guid id)
        {
            return await base.FindByIdAsync(id);
        }

        [Authorize(IdentityPermissions.UsersPrivate.Default)]
        public override async Task<UserData> FindByUserNameAsync(string userName, Guid? tenantId)
        {
            return await base.FindByUserNameAsync(userName, tenantId);
        }
    }
   
}