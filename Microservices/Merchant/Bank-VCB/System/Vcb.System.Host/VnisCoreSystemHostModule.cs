using Core;
using Core.AspNetCore.Mvc;
using Core.Auditing;
using Core.Data;
using Core.DependencyInjection;
using Core.EventBus.RabbitMq;
using Core.Keycloak.Host.Shared;
using Core.Modularity;
using Core.Swashbuckle;
using Core.Threading;
using Microsoft.Extensions.DependencyInjection;
using VnisCore.AuthDatabase.Oracle.EntityFrameworkCore.EntityFrameworkCore;
using VnisCore.System.Application;

namespace VnisCore.System.Host
{
    [DependsOn(
        typeof(KeycloakHostSharedModule),
        typeof(VnisCoreSystemApplicationModule),
        typeof(AbpEventBusRabbitMqModule),
        typeof(AbpSwashbuckleModule)
    )]
    public class VnisCoreSystemHostModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {

            var configuration = context.Services.GetConfiguration();

            Configure<AbpAspNetCoreMvcOptions>(options =>
            {
                options.ConventionalControllers.Create(typeof(VnisCoreSystemApplicationModule).Assembly, option =>
                {
                    option.RootPath = configuration["Service:BaseUrl"];
                    option.RemoteServiceName = configuration["Service:Name"];
                });
            });

            Configure<AbpAuditingOptions>(options =>
            {
                options.IsEnabledForGetRequests = true;
                options.ApplicationName = "System.Api";
                options.EntityHistorySelectors.AddAllEntities();
            });
        }

        public override void OnApplicationInitialization(ApplicationInitializationContext context)
        {
            var app = context.GetApplicationBuilder();

            SeedData(context);
        }

        private static void SeedData(IServiceProviderAccessor context)
        {
            AsyncHelper.RunSync(async () =>
            {
                using var scope = context.ServiceProvider.CreateScope();
                await scope.ServiceProvider
                    .GetRequiredService<IDataSeeder>()
                    .SeedAsync();
                var dbContext = scope.ServiceProvider
                    .GetRequiredService<VnisCoreAuthDatabaseOracleDbContext>();
                //SystemInitProceduresAndFunctions.InitProcedures(dbContext);
                //SystemInitProceduresAndFunctions.InitFunctions(dbContext);
            });
        }
    }
}