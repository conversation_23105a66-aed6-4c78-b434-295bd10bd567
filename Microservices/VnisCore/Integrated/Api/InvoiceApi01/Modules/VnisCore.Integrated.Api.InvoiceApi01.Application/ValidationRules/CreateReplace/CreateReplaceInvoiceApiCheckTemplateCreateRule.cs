using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.InvoiceTemplate;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;


namespace VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.CreateReplace
{
    /// <summary>
    /// kiểm tra mẫu của hóa đơn tài khoản hiện tại được phân quyền tạo không
    /// </summary>
    public class CreateReplaceInvoiceApiCheckTemplateCreateRule : IValidationRuleAsync<CreateReplaceInvoice01ApiRequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public CreateReplaceInvoiceApiCheckTemplateCreateRule(
            IValidationContext validationContext,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IAppFactory appFactory)
        {
            _localizier = localizier;
            _validationContext = validationContext;
            _appFactory = appFactory;
        }

        public async Task<ValidationResult> HandleAsync(CreateReplaceInvoice01ApiRequestModel input)
        {
            var userId = _validationContext.GetItem<Guid>("UserId");
            var template = _validationContext.GetItem<InvoiceTemplateEntity>("Template");

            var repoAtt = _appFactory.Repository<AccountTokenTemplateEntity, long>();
            var permissions = await repoAtt.Where(x => x.UserId == userId && x.TemplateId.HasValue).AsNoTracking().ToListAsync();

            if (!permissions.Any(x => x.TemplateId == template.Id))
                return new ValidationResult(false, _localizier["Vnis.BE.Invoice01.Api.Intergration.CreateReplace.UserCannotCreate"]);

            return new ValidationResult(true);
        }
    }
}
