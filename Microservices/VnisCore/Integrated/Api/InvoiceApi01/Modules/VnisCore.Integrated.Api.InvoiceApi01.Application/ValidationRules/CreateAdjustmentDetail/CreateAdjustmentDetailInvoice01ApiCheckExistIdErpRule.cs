using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;
using Core.Shared.Factory;
using Core;
using Core.Shared.Extensions;
using Core.Shared.Constants;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.CreateAdjustmentDetail
{
    /// <summary>
    /// kiểm tra iderp hóa đơn đã tồn tại chưa
    /// </summary>
    public class CreateAdjustmentDetailInvoice01ApiCheckExistIdErpRule : IValidationRuleAsync<CreateAdjustmentDetailInvoice01ApiRequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IRepository<Invoice01HeaderEntity, long> _repoInvoice01Header;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public CreateAdjustmentDetailInvoice01ApiCheckExistIdErpRule(
            IValidationContext validationContext,
            IRepository<Invoice01HeaderEntity, long> repoInvoice01Header,
            IStringLocalizer<CoreLocalizationResource> localizier)
        {
            _localizier = localizier;
            _validationContext = validationContext;
            _repoInvoice01Header = repoInvoice01Header;
        }

        public async Task<ValidationResult> HandleAsync(CreateAdjustmentDetailInvoice01ApiRequestModel input)
        {
            var tenantId = _validationContext.GetItem<Guid>("TenantId");

            var exist = await _repoInvoice01Header.FirstOrDefaultAsync(x => x.TenantId == tenantId && x.ErpId == input.ErpId);
            if (exist != null)
            {
                //return new ValidationResult(false, _localizier["Vnis.BE.Invoice01.Api.Intergration.DuplicateErpId"], result);

                throw new UserFriendlyException(_localizier["Vnis.BE.Invoice01.Api.Intergration.DuplicateErpId"])
                {
                    Data =
                    {
                        { "id", exist.Id },
                        { "serialNo", exist.SerialNo },
                        { "templateNo", exist.TemplateNo },
                        { "invoiceNo", exist.InvoiceNo },
                        { "transactionId", exist.TransactionId },
                        { "erpId", exist.ErpId },
                        { "invoiceStatus", EnumExtension.TryToEnum<InvoiceStatus>(exist.InvoiceStatus) },
                        { "signStatus", EnumExtension.TryToEnum<SignStatus>(exist.SignStatus) }
                    }
                };
            }

            return new ValidationResult(true);
        }
    }
}
