using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Interfaces;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;


namespace VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.UpdateReplace
{
    /// <summary>
    /// chekc ngày hóa đơn 
    /// </summary>
    public class UpdateReplaceInvoice01ApiCheckInvoiceDateRangeRule : IValidationRuleAsync<UpdateReplaceInvoice01ApiRequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IInvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity> _invoiceService;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public UpdateReplaceInvoice01ApiCheckInvoiceDateRangeRule(
            IValidationContext validationContext,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IInvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity> invoiceService)
        {
            _localizier = localizier;
            _validationContext = validationContext;
            _invoiceService = invoiceService;
        }

        public async Task<ValidationResult> HandleAsync(UpdateReplaceInvoice01ApiRequestModel input)
        {
            var tenantId = _validationContext.GetItem<Guid>("TenantId");
            var invoice = _validationContext.GetItem<Invoice01HeaderEntity>("Invoice");

            var range = await _invoiceService.InvoiceDateRangeAsync(tenantId, invoice.TemplateNo, invoice.SerialNo, invoice.InvoiceNo);
            if (range.Min.Date > DateTime.Now.Date)
                return new ValidationResult(false, _localizier["Vnis.BE.Invoice01.Api.Intergration.InvoiceDateMin", new[] { range.Min.ToString("dd/MM/yyyy") }]);

            if (input.InvoiceDate > range.Max || input.InvoiceDate < range.Min)
                return new ValidationResult(false, _localizier["Vnis.BE.Invoice01.Api.Intergration.InvoiceDateRange", new[] { range.Min.ToString("dd/MM/yyyy"), range.Max.ToString("dd/MM/yyyy") }]);

            return new ValidationResult(true);
        }
    }
}
