using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Repositories;


namespace VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.Create
{
    /// <summary>
    /// check các fieldname của headerextra có đúng không
    /// </summary>
    public class CreateInvoiceCheckHeaderExtraRule : IValidationRuleAsync<CreateInvoice01RequestModel, ValidationResult>
    {
        private readonly IInvoice01HeaderFieldRepository _repoInvoice01HeaderField;
        private readonly IValidationContext _validationContext;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly IAppFactory _appFactory;

        public CreateInvoiceCheckHeaderExtraRule(IInvoice01HeaderFieldRepository repoInvoice01HeaderField,
            IValidationContext validationContext,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IAppFactory appFactory)
        {
            _localizier = localizier;
            _validationContext = validationContext;
            _repoInvoice01HeaderField = repoInvoice01HeaderField;
            _appFactory = appFactory;
        }

        public async Task<ValidationResult> HandleAsync(CreateInvoice01RequestModel input)
        {
            if (input.InvoiceHeaderExtras == null || !input.InvoiceHeaderExtras.Any())
                return new ValidationResult(true);

            var tenantId = _validationContext.GetItem<Guid>("TenantId");

            //lấy các header field
            var headerFieldNames = await _repoInvoice01HeaderField.GetFieldNameByTenantIdAsNoTrackingAsync(tenantId);

            var commandHeaderFieldNames = input.InvoiceHeaderExtras.Select(x => x.FieldName).ToList();
            var expects = commandHeaderFieldNames.Except(headerFieldNames);

            if (expects.Any())
                return new ValidationResult(false, _localizier["Vnis.BE.Invoice01.Api.Intergration.InvoiceHeaderFieldNotFound", new string[] { string.Join(",", expects) }]);

            input.InvoiceHeaderExtras.RemoveAll(x => string.IsNullOrEmpty(x.FieldValue));

            return new ValidationResult(true);
        }
    }
}
