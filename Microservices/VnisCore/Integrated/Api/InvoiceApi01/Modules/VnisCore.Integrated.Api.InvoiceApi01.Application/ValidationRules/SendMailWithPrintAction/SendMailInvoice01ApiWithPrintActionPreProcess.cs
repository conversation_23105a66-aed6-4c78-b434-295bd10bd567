using Core.Shared.Factory;
using Core.Shared.FakeData;
using Core.Shared.Validations;
using System;
using System.Threading.Tasks;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.SendMailWithPrintAction
{
    public class SendMailInvoice01ApiWithPrintActionPreProcess : IValidationRule<SendMailInvoice01WithPrintActionApiRequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IAppFactory _appFactory;

        public SendMailInvoice01ApiWithPrintActionPreProcess(IValidationContext validationContext,
            IAppFactory appFactory)
        {
            _validationContext = validationContext;
            _appFactory = appFactory;
        }

        public ValidationResult Handle(SendMailInvoice01WithPrintActionApiRequestModel input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;

            _validationContext.GetOrAddItem("TenantId", () =>
            {
                return tenantId;
            });
            return new ValidationResult(true);
        }
    }
}
