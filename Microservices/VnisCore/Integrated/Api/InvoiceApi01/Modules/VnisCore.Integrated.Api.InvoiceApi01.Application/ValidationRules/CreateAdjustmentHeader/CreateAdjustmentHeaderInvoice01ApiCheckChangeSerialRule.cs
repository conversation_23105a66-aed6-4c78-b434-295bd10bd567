using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;


namespace VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.CreateAdjustmentHeader
{
    /// <summary>
    /// kiểm tra có được thay thế cho mẫu hóa đơn cùng mẫu số, khác ký hiệu không 
    /// </summary>
    public class CreateAdjustmentHeaderInvoice01ApiCheckChangeSerialRule : IValidationRule<CreateAdjustmentHeaderInvoice01ApiRequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public CreateAdjustmentHeaderInvoice01ApiCheckChangeSerialRule(IValidationContext validationContext,
            IStringLocalizer<CoreLocalizationResource> localizier)
        {
            _localizier = localizier;
            _validationContext = validationContext;
        }

        public ValidationResult Handle(CreateAdjustmentHeaderInvoice01ApiRequestModel input)
        {
            var invoiceReference = _validationContext.GetItem<Invoice01HeaderEntity>("InvoiceReference");

            //kiểm tra mẫu hóa đơn có được thay thế/điều chỉnh cho mẫu hóa đơn hiện tại không
            if (invoiceReference.TemplateNo != input.TemplateNo)
                return new ValidationResult(false, _localizier["Vnis.BE.Invoice01.Api.Intergration.CreateAdjHeader.CannotAdjHeaderOtherInvoiceTemplate"]);

            //kiểm tra có đúng ký hiệu không
            if (!ValidateChangeSerial(invoiceReference.SerialNo, input.SerialNo))
                return new ValidationResult(false, _localizier["Vnis.BE.Invoice01.Api.Intergration.CreateAdjHeader.CannotAdjHeaderOtherInvoiceTemplate"]);

            return new ValidationResult(true);
        }

        public bool ValidateChangeSerial(string rootSerialNo, string serialNo)
        {
            if (serialNo == rootSerialNo)
                return true;

            //kiểm tra năm của ký hiệu
            if (!int.TryParse(rootSerialNo.Substring(1, 2), out int rootYear)
                || !int.TryParse(serialNo.Substring(1, 2), out int newYear))
                return false;

            if (newYear < rootYear)
                return false;

            return true;
        }
    }
}
