using Core.Shared.Constants;
using MediatR;
using System;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Events
{
    public class AfterCreatedInvoice01ApiEventModel : INotification
    {
        public bool Succeeded { get; set; }
        public Exception Exception { get; set; }
        public DataAfterCreatedInvoice01ApiDraftModel Data { get; set; }
    }

    public class DataAfterCreatedInvoice01ApiDraftModel
    {
        public DateTime InvoiceDate { get; set; }
        public short TemplateNo { get; set; }
        public string SerialNo { get; set; }
        public long Id { get; set; }
        public Guid TenantId { get; set; }
        public Guid UserId { get; set; }
        public VnisType Type { get; set; }
        public string UserFullName { get; set; }
        public string UserName { get; set; }
        public long? InvoiceReferenceId { get; set; }
        public ApproveStatus ApproveStatus { get; set; }
        public InvoiceStatus InvoiceStatus { get; set; }
        public SignStatus SignStatus { get; set; }
        public InvoiceActionState State { get; set; }
    }
}
