using Core.Shared.Models;
using System;
using System.Collections.Generic;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.Models
{
    public class PagingInvoiceModel : Paging
    {
        public bool IsNullInvoice { get; set; }

        public List<long> Ids { get; set; }


        /// <summary>
        /// Id đánh dấu hóa đơn, ở đây mục đích search là 1 hồ sơ bệnh án
        /// </summary>
        public string TransactionId { get; set; }

        /// <summary>
        /// Id Erp
        /// </summary>
        public string ErpId { get; set; }

        /// <summary>
        /// người tạo Erp
        /// </summary>
        public string UserNameCreatorErp { get; set; }

        public short? TemplateNo { get; set; }

        /// <summary>
        /// code mẫu hóa đơn chọn ở danh sách hóa đơn
        /// </summary>
        public List<long> InvoiceTemplateIds { get; set; }

        /// <summary>
        /// danh sách tất cả mẫu hóa đơn CÓ THẺ được xem
        /// </summary>
        public List<long> AllReadTemplateIds { get; set; }

        /// <summary>
        /// số hóa đơn
        /// </summary>
        public string InvoiceNo { get; set; }

        /// <summary>
        /// Trạng tháu hóa đơn
        /// </summary>
        public List<short> InvoiceStatuses { get; set; }

        /// <summary>
        /// Trạng thái ký
        /// </summary>
        public List<short> SignStatuses { get; set; }


        /// <summary>
        /// Trạng thái duyệt
        /// </summary>
        public List<short> ApproveStatuses { get; set; }

        /// <summary>
        /// Ngày phát hành hóa đơn
        /// </summary>
        public DateTime? IssuedTime { get; set; }

        /// <summary>
        /// Tạo hóa đơn từ ngày
        /// </summary>
        public DateTime? CreateFromDate { get; set; }

        /// <summary>
        /// Tạo hóa đơn đến ngày
        /// </summary>
        public DateTime? CreateToDate { get; set; }

        /// <summary>
        /// Hủy hóa đơn từ ngày
        /// </summary>
        public DateTime? CancelFromDate { get; set; }

        /// <summary>
        /// Hủy hóa đơn đến ngày
        /// </summary>
        public DateTime? CancelToDate { get; set; }


        /// <summary>
        /// tìm theo người mua hàng/đại lý(FullName ConsumerInvoices) search dc cho ca email, dia chi, legalname, phone
        /// </summary>
        public string Customers { get; set; }

        /// <summary>
        /// Từ só hóa đơn
        /// </summary>
        public int? FromNumber { get; set; }

        /// <summary>
        /// Đến số hóa đơn
        /// </summary>
        public int? ToNumber { get; set; }

        /// <summary>
        /// Người tạo hóa đơn
        /// </summary>
        public string UserNameCreator { get; set; }
    }
}
