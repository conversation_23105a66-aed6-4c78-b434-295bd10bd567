using MediatR;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands
{
    public class SendMailInvoice01WithPrintActionApiRequestModel : IRequest
    {
        /// <summary>
        /// Số hóa đơn
        /// </summary>
        [JsonIgnore]
        public string PrintAction { get; set; }

        [Required(ErrorMessage = "ErpId không được để trống")]
        public string ErpId { get; set; }

        /// <summary>
        ///  Mail
        /// </summary>
        [Required(ErrorMessage = "Email không được để trống")]
        public string Mail { get; set; }
    }
}
