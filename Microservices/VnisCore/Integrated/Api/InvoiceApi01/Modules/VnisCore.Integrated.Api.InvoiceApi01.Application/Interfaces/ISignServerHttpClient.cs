using System.Collections.Generic;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Responses.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.Interfaces
{
    public interface ISignServerHttpClient
    {
        /// <summary>
        /// in hóa đơn
        /// </summary>
        /// <param name="idInvoiceHeaders"></param>
        /// <returns></returns>
        Task<Invoice01ApiSignServerResponseModel> SignServer(Invoice01HeaderEntity invoice);
    }
}
