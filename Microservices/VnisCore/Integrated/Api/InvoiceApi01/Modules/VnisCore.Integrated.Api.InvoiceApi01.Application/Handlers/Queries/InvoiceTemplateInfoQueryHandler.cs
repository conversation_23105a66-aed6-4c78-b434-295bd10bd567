using Core.Application.Dtos;
using Core.Domain.Repositories;
using Core.Shared.Factory;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.InvoiceTemplate;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Queries;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Responses.Queries;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.Handlers.Queries
{
    public class InvoiceTemplateInfoQueryHandler : IRequestHandler<InvoiceTemplateInfoRequestModel, List<InvoiceTemplateInfoResponseModel>>
    {
        private readonly IRepository<InvoiceTemplateEntity, long> _repoInvoiceTemplate;
        private readonly IRepository<AccountTokenTemplateEntity, long> _repoAccountToken;
        private readonly IAppFactory _factory;
        public InvoiceTemplateInfoQueryHandler(IRepository<InvoiceTemplateEntity, long> repoInvoiceTemplate,
                                     IRepository<AccountTokenTemplateEntity, long> repoAccountToken,
                                     IAppFactory factory)
        {
            _repoInvoiceTemplate = repoInvoiceTemplate;
            _repoAccountToken = repoAccountToken;
            _factory = factory;
        }

        public async Task<List<InvoiceTemplateInfoResponseModel>> Handle(InvoiceTemplateInfoRequestModel request, CancellationToken cancellationToken)
        {
            var tenantId = _factory.CurrentTenant.Id;
            var userId = _factory.CurrentUser.Id;

            var result = await (from invoiceTemplate in _repoInvoiceTemplate
                                join accountToken in _repoAccountToken
                                on invoiceTemplate.Id equals accountToken.TemplateId
                                where accountToken.UserId == userId && invoiceTemplate.TenantId == tenantId
                                && invoiceTemplate.TemplateNo == 1
                                select new InvoiceTemplateInfoResponseModel
                                {
                                    Id = invoiceTemplate.Id,
                                    TemplateNo = invoiceTemplate.TemplateNo,
                                    SerialNo = invoiceTemplate.SerialNo,
                                    Name = invoiceTemplate.Name
                                }).ToListAsync();

            return result;

        }
    }
}

