using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.Validations;
using MediatR;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Events;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Responses.Commands;
using Core.TenantManagement;
using Core.Shared.Extensions;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Handlers.Rpc;
using RabbitMQ.Client;
using VnisCore.Integrated.Api.InvoiceApi01.Application.RabbitClient;
using Core.Shared.Messages;
using Newtonsoft.Json;
using Core.Shared.RabbitMqConstants;
using Core.Localization.Resources.AbpLocalization;
using Microsoft.Extensions.Localization;
using Core;
using Core.Shared.MessageEventsData.IncreaseLicense;
using Microsoft.Extensions.Configuration;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Interfaces;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using Core.Shared.Services;
using Core.EventBus.Distributed;
using Core.Shared.MessageEventsData.SyncCatalog;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.Handlers.Commands
{
    public class CreateInvoice01CommandHandler : IRequestHandler<CreateInvoice01RequestModel, CreateInvoice01ResponseModel>
    {
        private readonly IValidationContext _validationContext;
        private readonly IAppFactory _appFactory;
        private readonly IInvoice01CommandHandler _invoice01Handler;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly IRabbitService _vnisBackgroundService;
        private readonly string _coreExchangeName;

        public CreateInvoice01CommandHandler(
            IValidationContext validationContext,
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IInvoice01CommandHandler invoice01CommandHandler,
            IRabbitService vnisBackgroundService,
            IConfiguration configuration)
        {
            _localizier = localizier;
            _invoice01Handler = invoice01CommandHandler;
            _validationContext = validationContext;
            _appFactory = appFactory;
            _vnisBackgroundService = vnisBackgroundService;
            _coreExchangeName = configuration.GetSection("RabbitMQ:EventBus:ExchangeName")?.Value;
        }

        public async Task<CreateInvoice01ResponseModel> Handle(CreateInvoice01RequestModel request, CancellationToken cancellationToken)
        {
            var tenantId = _validationContext.GetItem<Guid>("TenantId");
            var userId = _validationContext.GetItem<Guid>("UserId");
            var userName = _validationContext.GetItem<string>("UserName");
            var userFullName = _validationContext.GetItem<string>("UserFullName");

            var tenant = _validationContext.GetItem<Tenant>("Tenant");

            if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Any())
            {
                var headerExtras = request.InvoiceHeaderExtras.Where(x => !string.IsNullOrWhiteSpace(x.FieldValue)).ToList();
                request.InvoiceHeaderExtras = headerExtras;
            }

            foreach (var item in request.InvoiceDetails)
            {
                if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                {
                    var detailExtras = item.InvoiceDetailExtras.Where(x => !string.IsNullOrWhiteSpace(x.FieldValue)).ToList();
                    item.InvoiceDetailExtras = detailExtras;
                }
            }

            //HandleInvoice01
            var responseInvoice = await _invoice01Handler.PublishAsync(
                new InvoiceCommandRequestModel
                {
                    Action = InvoiceAction.CreateRoot,
                    Resource = InvoiceSource.Api,
                    TenantId = tenantId,
                    UserId = userId,
                    UserName = userName,
                    UserFullName = userFullName,
                    TenantCode = tenant.TenantCode,
                    TaxCode = tenant.TaxCode,
                    Address = tenant.Address,
                    Country = tenant.Country,
                    District = tenant.District,
                    City = tenant.City,
                    Phones = tenant.Phones,
                    Fax = tenant.Fax,
                    Email = tenant.Emails,
                    LegalName = tenant.LegalName,
                    BankName = tenant.BankName,
                    BankAccount = tenant.BankAccount,
                    SellerFullName = tenant.FullNameVi,
                    Date = request.InvoiceDate,
                    SerialNo = request.SerialNo,
                    TemplateNo = request.TemplateNo,
                    Data = CompressionExtensions.Zip(JsonConvert.SerializeObject(request)),
                    Type = VnisType._01GTKT
                },
                new PublicationAddress(ExchangeType.Topic, RabbitKey.Exchanges.Commands, string.Format(RabbitMqKey.Routings.CreateRootInvoice, "01")),
                new PublicationAddress(ExchangeType.Topic, RabbitKey.Exchanges.Rpc, string.Format(RabbitMqKey.Routings.GeneratedInvoice, "01")));

            if (!responseInvoice.Succeeded)
                throw new UserFriendlyException(_localizier["Vnis.BE.Invoice01.Api.Intergration.Create.CreateInvoiceFail", new string[] { responseInvoice.Exception?.Message }]);

            var response = new CreateInvoice01ResponseModel
            {
                Id = responseInvoice.Data.Id,
                ErpId = request.ErpId,
                TransactionId = request.TransactionId,
                TemplateNo = request.TemplateNo,
                SerialNo = request.SerialNo,
                InvoiceNo = responseInvoice.Data.InvoiceNo,
                InvoiceStatus = responseInvoice.Data.InvoiceStatus,
                SignStatus = responseInvoice.Data.SignStatus,
            };

            //await _appFactory.Mediator.Publish(new AfterCreatedInvoice01ApiEventModel
            //{
            //    Data = new DataAfterCreatedInvoice01ApiDraftModel
            //    {
            //        //Id = entity.Id,
            //        TenantId = tenantId,
            //        Type = VnisType._01GTKT,
            //        UserId = userId,
            //        InvoiceDate = request.InvoiceDate,
            //        SerialNo = request.SerialNo,
            //        TemplateNo = request.TemplateNo,
            //        UserName = userName,
            //        UserFullName = userFullName,
            //        //ApproveStatus = approveStatus,
            //        //InvoiceStatus = invoiceStatus,
            //        //SignStatus = signStatus,
            //        InvoiceReferenceId = null,
            //        State = InvoiceActionState.CreateRoot
            //    }
            //}, cancellationToken);

            var distributedEventBus = _appFactory.GetServiceDependency<IDistributedEventBus>();
            //sync Customer
            await distributedEventBus.PublishAsync(new SyncCustomerEventSendData(new SyncCustomerRequestModel
            {
                IdInvoice = responseInvoice.Data.Id,
                TenantId = tenantId,
                Type = VnisType._01GTKT,
            }));

            //sync Unit
            await distributedEventBus.PublishAsync(new SyncUnitEventSendData(new SyncUnitRequestModel
            {
                IdInvoice = responseInvoice.Data.Id,
                TenantId = tenantId,
                Type = VnisType._01GTKT,
            }));


            _vnisBackgroundService.Publish(new IncreaseLicenseEventSendData
            {
                Quantity = 1,
                TenantId = tenantId
            }, _coreExchangeName, RabbitMqKey.Routings.IncreaseLicense);


            return response;
        }
    }
}
