using MediatR;
using System.Threading;
using System.Threading.Tasks;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Events;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.Handlers.Events
{
    public class AfterCreatedAdjusmentHeaderInvoice01ApiEventHandler : INotificationHandler<AfterCreatedAdjustmentHeaderInvoice01ApiEventModel>
    {
        public AfterCreatedAdjusmentHeaderInvoice01ApiEventHandler()
        {
        }

        public Task Handle(AfterCreatedAdjustmentHeaderInvoice01ApiEventModel notification, CancellationToken UpdatedlationToken)
        {
            //_rabbitService.Publish(new RabbitResponseModel<InvoiceCommandResponseModel>
            //{
            //    EventType = InvoiceProcessEvent.Commit.ToString(),
            //    Data = new InvoiceCommandResponseModel
            //    {
            //        IsDraftInvoice = true,
            //        Resource = InvoiceSource.Api,
            //        SerialNo = notification.Data.SerialNo,
            //        TemplateNo = notification.Data.TemplateNo,
            //        TenantCode = notification.Data.TenantCode,
            //        UserCode = notification.Data.UserCode,
            //        UserFullName = notification.Data.UserFullName,
            //        UserName = notification.Data.UserName,
            //        Type = VnisType._01GTKT,
            //        State = InvoiceActionState.CreateReplaceDraft,
            //        Code = notification.Data.Code,
            //        ActionAtUtc = DateTime.UtcNow,
            //        ActionAt = DateTime.Now,
            //        ActionLogInvoice = ActionLogInvoice.CreateReplaceDraft,
            //        Action = InvoiceAction.CreateReplaceDraft,
            //        CodeInvoiceReference = notification.Data.CodeInvoiceReference,
            //        ApproveStatus = notification.Data.ApproveStatus,
            //        InvoiceStatus = notification.Data.InvoiceStatus,
            //        SignStatus = notification.Data.SignStatus,
            //    }
            //}, RabbitKey.Exchanges.Events, string.Format(RabbitMqKey.Routings.CreateReplaceInvoiceDraft, "01"));

            return Task.CompletedTask;
        }
    }
}
