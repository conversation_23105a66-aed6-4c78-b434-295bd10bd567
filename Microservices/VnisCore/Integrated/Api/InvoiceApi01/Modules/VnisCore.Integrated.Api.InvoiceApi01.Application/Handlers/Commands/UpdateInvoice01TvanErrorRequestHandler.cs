
using Core;
using Core.Shared.Constants;
using Core.Shared.Factory;

using MediatR;

using Microsoft.EntityFrameworkCore;

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands.InvoiceError;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Responses.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.Handlers.Commands
{
    public class UpdateInvoice01TvanErrorRequestHandler : IRequestHandler<UpdateInvoice01TvanErrorRequestModel, UpdateInvoice01TvanErrorResponseModel>
    {
        private readonly IAppFactory _appFactory;
        public UpdateInvoice01TvanErrorRequestHandler(IAppFactory appFactory)
        {
            _appFactory = appFactory;
        }

        public async Task<UpdateInvoice01TvanErrorResponseModel> Handle(UpdateInvoice01TvanErrorRequestModel request, CancellationToken cancellationToken)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;

            var invoice01Header = await _appFactory.Repository<Invoice01HeaderEntity, long>().AsNoTracking().FirstOrDefaultAsync(x => x.ErpId == request.ErpId && x.TenantId == tenantId);
            if (invoice01Header == null)
                throw new UserFriendlyException($"Không tìm thấy hóa đơn có ErpId = {request.ErpId} để thực hiện sửa thông báo sai sót");

            var invoiceErrorRepos = _appFactory.Repository<Invoice01ErrorEntity, long>();
            var invoiceError = await invoiceErrorRepos.Where(x => x.TenantId == tenantId && x.InvoiceHeaderId == invoice01Header.Id)
                .OrderByDescending(x => x.Id)
                .FirstOrDefaultAsync();

            if (invoiceError == null)
                throw new UserFriendlyException($"Không tìm thấy hóa đơn gửi thông báo sai sót lên TVAN có ErpId = {request.ErpId}");

            if (invoiceError.SignStatus == (short)SignStatus.DaKy.GetHashCode())
                throw new UserFriendlyException("Thông báo sai sót đã ký không thể sửa");

            invoiceError.CreationTime = DateTime.Now;
            invoiceError.CreatorId = userId;
            invoiceError.TenantId = tenantId;
            invoiceError.Reason = request.Reason;
            invoiceError.Action = (short)request.Action.GetHashCode();
            invoiceError.LastModificationTime = DateTime.Now;

            await invoiceErrorRepos.UpdateAsync(invoiceError, true);

            return new UpdateInvoice01TvanErrorResponseModel
            {
                InvoiceNo = invoice01Header.InvoiceNo,
                SerialNo = invoice01Header.SerialNo,
                TemplateNo = invoice01Header.TemplateNo
            };
        }
    }
}
