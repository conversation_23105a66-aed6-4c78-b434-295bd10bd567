using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using VnisCoreSignHsm.Application.Shared;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using HSM.SigningLibrary.HsmSignUtility;
using Serilog;

namespace VnisCoreSignHsm.Application.Services
{
    public interface ICertReadySignerService
    {
        /// <summary>
        /// Lấy thông tin cert và danh sách đối tượng cần ký theo danh sách truyền vào
        /// </summary>
        /// <param name="taxCode"></param>
        /// <param name="listObjectSign"></param>
        /// <returns></returns>
        Task<CertReadyModel> GetCertReadySignerAsync(string taxCode);

        Task<UsbTokenCacheItem> GetListUsbTokensAsync();
    }
    public class CertReadySignerService : ICertReadySignerService
    {
        private readonly IUsbTokenCacheService _usbTokenCacheService;
        private readonly ICertReadyService _certReadyService;

        public CertReadySignerService(
            IUsbTokenCacheService usbTokenCacheService,
            ICertReadyService certReadyService)
        {
            _usbTokenCacheService = usbTokenCacheService;
            _certReadyService = certReadyService;
        }

        public async Task<UsbTokenCacheItem> GetListUsbTokensAsync()
        {
            return await _usbTokenCacheService.GetCacheItemAsync();
        }

        public async Task<CertReadyModel> GetCertReadySignerAsync(string taxCode)
        {
            try
            {
                var usbTokens = await _usbTokenCacheService.GetCacheItemAsync();

                var dateNow = DateTime.Now;
                var clean = taxCode.Replace("-", "");

                // Bổ sung điều kiện chỉ lấy CTS có hiệu lực để ký
                var certs = usbTokens?.Value.Where(x => x.StartDate <= dateNow && x.EndDate >= dateNow && HsmXmlUtility.ExtractTaxCode(x.SubjectName) == clean).OrderBy(x => x.CreationTime).ToList();

                if (!certs.Any())
                {
                    Log.Error($"This taxCode {taxCode} no certificate found.");
                    return null;
                }

                var cert = certs.FirstOrDefault();

                if (cert == null)
                {
                    Log.Error($"This taxCode {taxCode} no certificate found.");
                    return null;
                }

                try
                {
                    return _certReadyService.GetCertSigner(cert);
                }
                catch (Exception e)
                {
                    Log.Error($"GetCertReadySignerAsync OpenSession InvoicesSignedError: taxCode {taxCode}-{cert.CkaLabel}: {e.Message}");
                    return null;
                }
            }
            catch (Exception e)
            {
                Log.Error($"GetCertReadySignerAsync InvoicesSignedError: taxCode {taxCode}: {e.Message}");
                return null;
            }
        }
    }
}
