using Core.DependencyInjection;
using Core.Shared.Factory;
using Dapper;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCoreSignHsm.Application.Dto.Invoice;

namespace VnisCoreSignHsm.Application.Services.Invoice01.Invoice01Detail
{
    public interface IInvoice01SpecificProductExtraGetByHeaderIdService : IScopedDependency
    {
        Task<List<SpecificProductExtraDto>> GetAsync(List<long> invoiceHeaderIds);
    }
    public class Invoice01SpecificProductExtraGetByHeaderIdService : IInvoice01SpecificProductExtraGetByHeaderIdService
    {
        private readonly IAppFactory _appFactory;
        public Invoice01SpecificProductExtraGetByHeaderIdService(IAppFactory appFactory)
        {
            _appFactory = appFactory;
        }

        public async Task<List<SpecificProductExtraDto>> GetAsync(List<long> invoiceHeaderIds)
        {
            var query = @"SELECT * 
                        FROM ""Invoice01SpecificProductExtra"" 
                        WHERE ""InvoiceHeaderId"" IN :ListInvoiceHeaderId";

            return (await _appFactory.VnisCoreOracle.Connection.QueryAsync<SpecificProductExtraDto>(query, new
            {
                ListInvoiceHeaderId = invoiceHeaderIds
            })).ToList();
        }
    }
}
