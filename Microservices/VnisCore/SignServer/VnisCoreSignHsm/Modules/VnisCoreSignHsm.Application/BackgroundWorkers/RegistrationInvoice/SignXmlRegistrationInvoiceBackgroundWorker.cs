using System;
using System.Threading.Tasks;
using Core.BackgroundWorkers;
using Core.Threading;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using VnisCoreSignHsm.Application.Dto;
using VnisCoreSignHsm.Application.Interface;
using VnisCoreSignHsm.Application.Shared;

namespace VnisCoreSignHsm.Application.BackgroundWorkers.RegistrationInvoice
{
    public class SignXmlRegistrationInvoiceBackgroundWorker : AsyncPeriodicBackgroundWorkerBase
    {
        private readonly IConfiguration _configuration;
        public SignXmlRegistrationInvoiceBackgroundWorker(
            AbpAsyncTimer timer,
            IConfiguration configuration,
            IServiceScopeFactory serviceScopeFactory) : 
            base(timer, serviceScopeFactory)
        {
            _configuration = configuration;
            Timer.Period = 100; //1 s
            int.TryParse(configuration.GetSection("Settings:TimePeriod").Value, out var period);
            if (period > 0)
                timer.Period = period * 1000;

        }

        protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
        {
            int.TryParse(_configuration.GetSection("Settings:TimeDelayStartBackgroundWorker").Value, out var timeDelay);
            if (timeDelay < 0) timeDelay = 0;
            if (SignHSMConstants.TimeStartService < DateTime.UtcNow.AddSeconds(timeDelay * -1) || timeDelay == 0)
                await workerContext
                .ServiceProvider
                .GetRequiredService<ISignRegistrationBusiness>()
                .SignRegistrationInvoice(new SignRequest());
        }
    }
}

