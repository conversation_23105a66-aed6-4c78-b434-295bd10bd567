using Core.BackgroundWorkers;
using Core.Threading;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCoreSignHsm.Application.Interface;
using VnisCoreSignHsm.Application.Shared;

namespace VnisCoreSignHsm.Application.BackgroundWorkers.Invoice.Invoice01.ReSign
{
    public class ReSignInvoiceMissingXmlWorker : AsyncPeriodicBackgroundWorkerBase
    {
        private readonly IConfiguration _configuration;
        public ReSignInvoiceMissingXmlWorker(
            AbpAsyncTimer timer,
            IConfiguration configuration,
            IServiceScopeFactory serviceScopeFactory) :
            base(timer, serviceScopeFactory)
        {
            _configuration = configuration;
            Timer.Period = 100; //1 s
            int.TryParse(configuration.GetSection("Settings:TimePeriodReSignMissingXml").Value, out var period);
            if (period > 0)
                Timer.Period = period * 1000;
        }

        protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
        {
            int.TryParse(_configuration.GetSection("Settings:TimeDelayStartBackgroundWorker").Value, out var timeDelay);
            if (timeDelay < 0) timeDelay = 0;
            if (SignHSMConstants.TimeStartService < DateTime.UtcNow.AddSeconds(timeDelay * -1) || timeDelay == 0)
                await workerContext
                            .ServiceProvider
                            .GetRequiredService<ISignServerBusiness>()
                            .ReSignMissingXmlInvoiceAutoAsync();
        }
    }
}
