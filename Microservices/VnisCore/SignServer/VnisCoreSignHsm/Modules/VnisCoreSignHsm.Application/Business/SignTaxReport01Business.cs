using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Services;
using Core.Tvan.Constants;
using Core.Tvan.Models.Xmls;
using Core.Tvan.Models.Xmls.ReportInvoice;
using Dapper;
using HSM.SigningLibrary.XMLSignLib;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Serilog;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.MongoDB.Entities.TaxReport01;
using VnisCore.Core.MongoDB.IRepository;
using VnisCoreSignHsm.Application.Dto;
using VnisCoreSignHsm.Application.Dto.TaxReport01;
using VnisCoreSignHsm.Application.Interface;
using VnisCoreSignHsm.Application.Shared;
using VnisCoreSignHsm.Application.Services;
using Nest;

namespace VnisCoreSignHsm.Application.Business
{
    public class SignTaxReport01Business : ISignTaxReport01Business
    {
        private readonly IAppFactory _appFactory;
        private readonly IConfiguration _configuration;
        private readonly IVnisCoreMongoXmlTaxReport01SignedRepository _mongoXmlTaxReport01SignedRepository;
        private readonly ISettingService _settingService;
        private readonly ICertReadySignerService _certReadySignerService;
        private readonly string _traceId = string.Empty;

        public SignTaxReport01Business(
            IConfiguration configuration,
            IAppFactory appFactory,
            IVnisCoreMongoXmlTaxReport01SignedRepository mongoXmlTaxReport01SignedRepository,
            ISettingService settingService,
            ICertReadySignerService certReadySignerService)
        {
            _configuration = configuration;
            _appFactory = appFactory;
            _mongoXmlTaxReport01SignedRepository = mongoXmlTaxReport01SignedRepository;
            _settingService = settingService;
            _certReadySignerService = certReadySignerService;
            _traceId = Guid.NewGuid().ToString();
        }

        public virtual async Task<bool> SignReport01(SignRequest input)
        {
            var sellerSignedRawId = _configuration.GetSection("Settings:SellerSignedId").Value;
            var sellerSignedId = OracleExtension.ConvertRawOracleToGuid(sellerSignedRawId);
            var sellerFullNameSigned = _configuration.GetSection("Settings:SellerFullNameSigned").Value;

            if (input.Ids != null && input.Ids.Any())
            {
                while (true)
                {
                    var taxReport01DetailMapping = await GetTaxReport01DetailMapping(input.Ids);
                    if (taxReport01DetailMapping == null) break;
                    return await SigningReport01(taxReport01DetailMapping, sellerSignedId, sellerFullNameSigned);
                }
            }
            else
            {
                var taxReport01DetailMapping = await GetTaxReport01DetailMapping(input.Ids);

                return await SigningReport01(taxReport01DetailMapping, sellerSignedId, sellerFullNameSigned);
            }
            return false;
        }

        private async Task<bool> SigningReport01(GetTaxReport01DetailMappingDto taxReport01DetailMapping, Guid sellerSignedId, string sellerFullNameSigned)
        {
            if (taxReport01DetailMapping == null)
                return false;

            //var fromDate = long.Parse(taxReport01DetailMapping.FromDate.ToString("yyyyMMdd0000"));
            //var toDate = long.Parse(taxReport01DetailMapping.ToDate.AddDays(1).ToString("yyyyMMdd0000"));


            //var skipCount = 0;
            //var maxResultCount = 1000;

            //// Index start 1
            //if (taxReport01DetailMapping.Index > 0)
            //    skipCount = maxResultCount * (taxReport01DetailMapping.Index - 1);

            //var rawTenantId = OracleExtension.ConvertGuidToRaw(taxReport01DetailMapping.TenantId);

            var taxCode = taxReport01DetailMapping.TaxCode;

            Log.Information($"[{_traceId}] TaxReport01 - Sign TaxReport01DetailMapping: {taxReport01DetailMapping.Id} - Taxcoce: {taxCode}");
            CertReadyModel certReadySigner = null;
            try
            {
                //Log.Fatal($"Start {taxCode}");

                var query = $@"
                                SELECT 
                                    ""InvoiceHeaderId"",
                                    ""TemplateNo"",
                                    ""SerialNo"",
                                    ""ReceiverName"",
                                    ""BuyerFullName"",
                                    ""BuyerName"",
                                    ""BuyerTaxCode"",
                                    ""InvoiceNo"",
                                    ""Number"",
                                    ""InvoiceDate"",
                                    ""TotalAmount"",
                                    ""TotalVatAmount"",
                                    ""TotalPaymentAmount"",
                                    ""Status"",
                                    ""InvoiceStatus"",
                                    ""InvoiceTemplateId"",
                                    ""InvoiceReferenceData"",
                                    ""InvoiceDateReference"",
                                    ""InvoiceType"",
                                    ""ProductName"",
                                    ""VatPercent"",
                                    ""Quantity"",
                                    ""Note"",
                                    ""TotalFeeAmount"",
                                    ""OtherTotalDiscountAmount"",
                                    ""ExchangeRate"",
                                    ""UnitName"",
                                    ""BudgetUnitCode""
                                FROM ""TaxReport01Summary""
                                WHERE ""TemplateNo"" = {taxReport01DetailMapping.InvoiceType} AND ""InvoiceHeaderId"" IN ({string.Join(",", taxReport01DetailMapping.InvoiceHeaderIds)})
                                ORDER BY ""InvoiceHeaderId""
                            ";

                var resultData = await _appFactory.VnisCoreOracle.Connection.QueryAsync<TaxReport01DetailDto>(query);

                //Log.Fatal($"Done Query {taxCode}");
                var taxReport01DetailDtos = resultData as TaxReport01DetailDto[] ?? resultData.ToArray();

                Log.Information($"[{_traceId}] TaxReport01 - TaxReport01DetailMapping: {taxReport01DetailMapping.Id} - Have {resultData.Count()} InvoiceHeaderId");

                //Log.Fatal($"Done Parse To Dto {taxCode}");

                if (taxReport01DetailDtos.Any())
                {
                    //Log.Information($"TaxReport01 TaxCode-TaxReport01Header-TaxReport01DetailMapping-TaxReport01DetailMappingIndex {taxCode}-{taxReport01DetailMapping.TaxReportHeaderId}-{taxReport01DetailMapping.Id}-{taxReport01DetailMapping.Index}");

                    certReadySigner = await _certReadySignerService.GetCertReadySignerAsync(taxCode);

                    if (certReadySigner == null)
                    {
                        Log.Information($"[{_traceId}] TaxReport01 - certReadySigner is null");
                        return false;
                    }

                    //Log.Information($"Init session cert: {taxCode}");

                    var modelXml = await Report01ToXmlModelAsync(taxReport01DetailMapping, taxReport01DetailDtos);

                    var xml = ObjToXmlUtils.NewObjToXml(modelXml);

                    XMLSignUtils.Signing(xml, taxReport01DetailMapping.Id.ToString(), certReadySigner.SigningCertificate,
                        certReadySigner.Session, certReadySigner.PrivateKeyHandle,
                        XmlSignatureType.Report01, XmlSignatureType.Report01Tag,
                        out xml, out var error, out bool isHSMError);

                    if (!string.IsNullOrEmpty(error))
                    {
                        Log.Information($"[{_traceId}] TaxReport01 - Sign error: {error}");
                        Log.Error($"[{_traceId}] TaxReport01 - Sign error: {error}");
                        return false;
                    }

                    //var fileName =
                    //    $"report-01-{taxReport01DetailMapping.TaxReportHeaderId}-{taxReport01DetailMapping.Id}-{taxReport01DetailMapping.Index}-{DateTime.UtcNow.Ticks}.xml"
                    //        .Replace("/", "-");

                    var entity = new MongoXmlTaxReport01SignedEntity
                    {
                        TaxReportDetailMappingId = taxReport01DetailMapping.Id,
                        TaxReportHeaderId = taxReport01DetailMapping.TaxReportHeaderId,
                        Index = taxReport01DetailMapping.Index,
                        ReportDate = taxReport01DetailMapping.ReportDate,
                        FromDate = taxReport01DetailMapping.FromDate,
                        ToDate = taxReport01DetailMapping.ToDate,
                        ReportYear = taxReport01DetailMapping.ReportYear,
                        ReportMonth = taxReport01DetailMapping.ReportMonth,
                        ReportQuarter = taxReport01DetailMapping.ReportQuarter,
                        IsFirstTimeInPeriod = taxReport01DetailMapping.IsFirstTimeInPeriod,
                        AdditionalTimes = taxReport01DetailMapping.AdditionalTimes,
                        TaxCode = taxReport01DetailMapping.TaxCode,

                        Xml = Convert.ToBase64String(Encoding.UTF8.GetBytes(xml)),
                        InvoiceHeaderIds = taxReport01DetailDtos.Select(x => x.InvoiceHeaderId).ToList(),

                        SyncedToMinioStatus = 0,
                        SendToTvanStatus = 0,

                        TenantId = taxReport01DetailMapping.TenantId,
                        CreatorId = sellerSignedId,
                        FullNameCreator = sellerFullNameSigned,
                        CreationTime = DateTime.Now,
                        SignerId = sellerSignedId,
                        FullNameSigner = sellerFullNameSigned,
                        SignedTime = DateTime.Now,
                    };

                    await _mongoXmlTaxReport01SignedRepository.InsertAsync(entity);

                    var sql =
                        $@"update ""TaxReport01DetailMapping"" set ""SignStatus"" = 5 where ""Id"" = {taxReport01DetailMapping.Id}";
                    await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(sql);

                    Log.Information($"[{_traceId}] TaxReport01 - Sign Completed TaxReport01DetailMapping: {taxReport01DetailMapping.Id} - Taxcoce: {taxCode}");

                    var notAnyIndexNext = await GetTaxReport01DetailMappingNotSign(taxReport01DetailMapping.TaxReportHeaderId);

                    if (!notAnyIndexNext)
                        return true;

                    Log.Information($"[{_traceId}] TaxReport01 - Update Sign TaxReport01Header Completed: {taxReport01DetailMapping.TaxReportHeaderId}");

                    var sqlHeader =
                        $@"update ""TaxReport01Header"" 
                           set ""SignStatus"" = 5, 
                               ""ApproveStatus"" = case when ""ApproveStatus"" != {ApproveStatus.DaDuyet.GetHashCode()} then {ApproveStatus.DaDuyet.GetHashCode()} else ""ApproveStatus"" end 
                          where ""Id"" = {taxReport01DetailMapping.TaxReportHeaderId}";
                    await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(sqlHeader);

                    return true;
                }
                else
                {
                    var sql =
                        $@"update ""TaxReport01DetailMapping"" set ""SignStatus"" = 1 and ""SignErrorMessage"" = 'TaxReport01Summary is empty' where ""Id"" = {taxReport01DetailMapping.Id}";
                    await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(sql);
                }

                return true;
            }
            catch (Exception e)
            {
                Log.Error($"TaxReport01SignedError TaxCode-TaxReport01Header-TaxReport01DetailMapping-TaxReport01DetailMappingIndex {taxCode}-{taxReport01DetailMapping.TaxReportHeaderId}-{taxReport01DetailMapping.Id}-{taxReport01DetailMapping.Index}");
                Log.Error(e, e.Message);
                Log.Error(e.StackTrace);
                return false;
            }
        }

        private async Task<GetTaxReport01DetailMappingDto> GetTaxReport01DetailMapping(List<long> taxReportHeaderIds)
        {
            var subQuery = "";
            if (taxReportHeaderIds != null && taxReportHeaderIds.Any())
                subQuery = $"and a.\"TaxReportHeaderId\" = {taxReportHeaderIds.First()}";

            var sql = $@"
                    SELECT * FROM (
                    SELECT a.""Id"", a.""Index"", a.""TaxReportHeaderId"", c.""ToDate"", c.""FromDate"", a.""TenantId"", b.""TaxCode"", b.""FullNameVi"", 
                    c.""IsFirstTimeInPeriod"", c.""AdditionalTimes"", c.""ReportDate"", c.""ReportMonth"", c.""ReportQuarter"", c.""ReportYear"", a.""RptNum"", a.""InvoiceHeaderIds"", a.""Currency"", a.""InvoiceType""  
                    FROM ""TaxReport01DetailMapping"" a 
                    INNER JOIN (SELECT ""Id"",""TaxCode"", ""FullNameVi"" FROM {_configuration["Settings:SchemaAuth"]}""VnisTenants"") b ON a.""TenantId"" = b.""Id""
                    INNER JOIN (SELECT ""Id"", ""FromDate"", ""ToDate"", ""IsFirstTimeInPeriod"", ""AdditionalTimes"", ""ReportDate"", ""ReportMonth"", ""ReportQuarter"", ""ReportYear"" FROM ""TaxReport01Header"" WHERE ""IsDeleted"" = 0) c ON a.""TaxReportHeaderId"" = c.""Id""
                    WHERE ""SignStatus"" = 3 {subQuery} 
                    ORDER BY  a.""TaxReportHeaderId"", a.""Index""
                    ) WHERE ROWNUM = 1
                    ";

            var result = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<GetTaxReport01DetailMappingDto>(sql);
            return result;
        }

        private async Task<bool> GetTaxReport01DetailMappingNotSign(long taxReportHeaderId)
        {
            var sql = $@"select count(""Id"") from ""TaxReport01DetailMapping"" where ""SignStatus"" IN (3,1) and ""TaxReportHeaderId"" = {taxReportHeaderId}";

            var result = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<int>(sql);
            return result <= 0;
        }

        private async Task<BTHDLieuReportInvoice01> Report01ToXmlModelAsync(GetTaxReport01DetailMappingDto reportDetail, IEnumerable<TaxReport01DetailDto> taxReportDetailDtos)
        {
            var result = new BTHDLieuReportInvoice01
            {
                DLBTHop = new DLBTHopReportInvoice01Model
                {
                    //Data = "data",
                    Data = $"Id-{reportDetail.Id}",
                    TTChung = new TTChungDLBTHopReportInvoice01Model
                    {
                        PBan = TvanInvoiceStaticData.PBan,
                        MSo = Mso._01TH_HDDT.ToDisplayName(),
                        Ten = Mso._01TH_HDDT.GetDescription(),
                        //SBTHDLieu = reportDetail.Index,
                        SBTHDLieu = reportDetail.RptNum,
                        LKDLieu = reportDetail.ReportMonth == 0 ? "Q" : "T", //TODO: xem lại có cả kỳ theo ngày
                        KDLieu = reportDetail.ReportMonth == 0 ? $"{reportDetail.ReportQuarter:00}/{reportDetail.ReportYear}" : $"{reportDetail.ReportMonth:00}/{reportDetail.ReportYear}",
                        LDau = reportDetail.IsFirstTimeInPeriod ? 1 : 0,
                        BSLThu = reportDetail.AdditionalTimes,
                        NLap = reportDetail.ReportDate.ToString("yyyy-MM-dd"),
                        TNNT = reportDetail.FullNameVi,
                        MST = reportDetail.TaxCode,
                        HDDIn = 0,
                        LHHoa = 9, //khác
                        DVTTe = reportDetail.Currency
                    },
                    NDBTHDLieu = new NDBTHDLieuReportInvoice01Model
                    {
                        DSDLieu = new DSDLieuTaxReport01Model
                        {
                            DLieu = new List<DLieuTaxReport01Model>()
                        }
                    }
                },
                DSCKS = new DSCKSReportInvoice01Model
                {
                    NNT = new CKSNNTModel
                    {
                        Signature = new SignatureNNTModel
                        {
                            //Id = "NNTSignature",
                            Id = $"NNT-{reportDetail.Id}",
                            Object = new ObjectNNTModel
                            {
                                //Id = $"SigningTime-{BitConverter.ToString((Guid.NewGuid().ToByteArray())).Replace("-", "")}",
                                Id = $"SigningTime-{reportDetail.Id}",
                                SignatureProperties = new SignaturePropertiesModel
                                {
                                    SignatureProperty = new List<SignaturePropertyModel>
                                    {
                                        new()
                                        {
                                            //Id = "NTTSignTimeStamp",
                                            Target = "signatureProperties",
                                            SigningTime = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss")
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            };

            var taxes = StaticData.TaxDefaults.Values.ToDictionary(x => x.Item1, x => x.Item5);
            var tThaiTBaos = Enum.GetValues(typeof(TThaiTBao)).Cast<TThaiTBao>().ToDictionary(x => x.ToDisplayName(), x => x.GetHashCode());
            //lấy cấu hình mẫu nào đc ẩn đơn giá, đơn vị tính, sô lượng ở xml/pdf
            var settingTemplatHideInfoInXml = await _settingService.GetByCodeAsync(reportDetail.TenantId, SettingKey.TemplateHideUnitQuantityUnitPrice.ToString());

            var details = taxReportDetailDtos?.OrderBy(x => x.Id).ToList();
            for (int i = 0; i < details.Count; i++)
            {
                var item = details.ElementAt(i);
                int? referenceTemplateNo = null;
                string referenceSerialNo = null;
                string referenceInvoiceNo = null;

                if (!string.IsNullOrEmpty(item.InvoiceReferenceData))
                {
                    referenceTemplateNo = int.Parse(item.InvoiceReferenceData.First().ToString());
                    referenceSerialNo = item.InvoiceReferenceData.Substring(1, 6);
                    referenceInvoiceNo = item.InvoiceReferenceData.Substring(7, 8);
                }

                var tThaiTag = !string.IsNullOrEmpty(item.Status) && tThaiTBaos.ContainsKey(item.Status) ? tThaiTBaos[item.Status] : 0;
                var isHideUnitQuantityUnitPrice = CommonService.CheckIsHideUnitQuantityUnitPrice(settingTemplatHideInfoInXml, item.SerialNo);

                // VCBINNB-2150: Bỏ validate các thẻ liên quan tới thuế ở BTH của HĐ 02 - Đối với các thẻ XML liên quan đến tiền thuế:  <TTCThue>, <TSuat>, <TgTThue> không cần điền giá trị.
                result.DLBTHop.NDBTHDLieu.DSDLieu.DLieu.Add(new DLieuTaxReport01Model
                {
                    STT = i + 1,
                    KHMSHDon = item.TemplateNo.ToString(),
                    KHHDon = item.SerialNo,
                    SHDon = item.Number.ToString("00000000"),
                    NLap = item.InvoiceDate.ToString("yyyy-MM-dd"),
                    TNMua = item.BuyerFullName,
                    MSTNMua = item.BuyerTaxCode,
                    MDVQHNSach = item.BudgetUnitCode,
                    MKHang = null,
                    MHHDVu = null,
                    THHDVu = item.ProductName,
                    // TODO: Thêm DVTinh áp dụng cho hd02
                    DVTinh = item.TemplateNo == VnisType._02GTTT.GetHashCode() 
                        ? item.UnitName
                        : isHideUnitQuantityUnitPrice ? null : item.UnitId,
                    SLuong = isHideUnitQuantityUnitPrice ? null : item.Quantity,
                    TTCThue = item.TemplateNo == (short)VnisType._02GTTT ? null : item.TotalAmount,
                    TSuat = item.TemplateNo == (short)VnisType._02GTTT
                                ? null :
                                (item.VatPercent.HasValue ? (taxes.ContainsKey(item.VatPercent.Value) ? taxes[item.VatPercent.Value] : $"KHAC:{string.Format(CultureInfo.InvariantCulture, "{0:00.00}", item.VatPercent)}%") : null),
                    TgTThue = item.TemplateNo == (short)VnisType._02GTTT ? null : item.TotalVatAmount,
                    TgTTToan = item.TotalPaymentAmount,
                    TThai = tThaiTag,
                    KHMSHDCLQuan = (tThaiTag == 0) ? null : (referenceTemplateNo.HasValue ? referenceTemplateNo.Value.ToString() : null),
                    KHHDCLQuan = (tThaiTag == 0) ? null : referenceSerialNo,
                    SHDCLQuan = (tThaiTag == 0) ? null : referenceInvoiceNo,
                    
                    // TODO: VCBINNB-2625 - hardcode
                    LHDCLQuan = (tThaiTag == 0) ? null : (referenceTemplateNo.HasValue ? LHDCLQuan.Loai1.GetHashCode().ToString() : null),
                    LKDLDChinh = (tThaiTag == 0) ? null : (referenceTemplateNo.HasValue ? reportDetail.ReportMonth == 0 ? "Q" : "T" : null), // là giá trị kỳ của hóa đơn lquan
                    KDLDChinh = (tThaiTag == 0 || !item.InvoiceDateReference.HasValue) ? null : $"{item.InvoiceDateReference.Value.Month:00}/{item.InvoiceDateReference.Value.Year}",  //là giá trị kỳ của hóa đơn lquan
                    STBao = null, //lấy từ xml thông báo hóa đơn
                    NTBao = null,
                    GChu = item.Note,
                    TTPhi = item.TotalFeeAmount,
                    TGTKhac = null,
                    TGia = item.ExchangeRate
                });
            }

            return result;
        }
    }
}