using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Models;
using Dapper;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using VnisCoreSignHsm.Application.Dto;
using VnisCoreSignHsm.Application.IQueries;

namespace VnisCoreSignHsm.Application.Queries
{
    public class GenerateDrawGetDataSignFormAndExcelQuery : IGenerateDrawGetDataSignFormAndExcelQuery
    {
        private readonly IConfiguration _configuration;

        public GenerateDrawGetDataSignFormAndExcelQuery(
            IConfiguration configuration
        )
        {
            _configuration = configuration;
        }
        public QueryDataModel GenerateDrawQuery(SignRequest input, short signStatus, List<InvoiceSource> invoiceSources = null)
        {
            var withClauseQueryDataModel = GenerateWithClause(input, signStatus, invoiceSources);
            var sql = new StringBuilder($@"
                    {withClauseQueryDataModel.Query}
                    SELECT JSON_OBJECT (
	                    KEY 'invoices' VALUE (
   		                    SELECT JSON_ARRAYAGG(
    		                    JSON_OBJECT (
          		                    a.*,
          		                    KEY 'InvoiceDetail' VALUE (
            		                    SELECT JSON_ARRAYAGG(
                 		                    JSON_OBJECT(
                   			                    b.*
                 		                    ) RETURNING CLOB
           			                    )
            		                    FROM ""Invoice01Detail"" b
            		                    WHERE b.""InvoiceHeaderId"" = a.""Id""
          		                    ),
				                    KEY 'InvoiceReference' VALUE (
    				                    SELECT JSON_ARRAYAGG(
    					                    JSON_OBJECT(
           					                    b.*
						                    ) RETURNING CLOB
   					                    )
		                                FROM ""Invoice01Reference"" b
        		                        WHERE b.""InvoiceHeaderId"" = a.""Id""
          		                    ),
          		                    KEY 'InvoiceTaxBreakdown' VALUE (
                	                    SELECT JSON_ARRAYAGG(
                 		                    JSON_OBJECT(
                       		                    b.*
                     	                    ) RETURNING CLOB

					                    )
                	                    FROM ""Invoice01TaxBreakdown"" b
            		                    WHERE b.""InvoiceHeaderId"" = a.""Id""
          		                    ) RETURNING CLOB
                                ) RETURNING CLOB
		                    )
   		                    FROM invoice01Header a
	                    ) RETURNING CLOB
                    ) AS invoices
                    FROM DUAL
            ");

            return new QueryDataModel
            {
                Query = sql.ToString(),
                Parameters = withClauseQueryDataModel.Parameters
            };
        }
        private QueryDataModel GenerateWithClause(SignRequest input, short signStatus, List<InvoiceSource> invoiceSources = null)
        {
            int.TryParse(_configuration["Settings:TakeRows"], out var takeRows);
            if (takeRows == 0)
            {
                takeRows = 100;
            }

            var conditionQueryDataModel = GenerateCondition(input, signStatus, invoiceSources);

            var withClause = new StringBuilder();

            if (input.IsSignManual)
            {
                withClause.Append($@"
                    WITH usbToken AS (
                        SELECT DISTINCT ""TenantId"" 
                        FROM ""UsbToken"" WHERE ""IsDeleted"" = :IsDeleted),
                    invoice01Header AS (
                        SELECT a.* FROM ""Invoice01Header"" a
		                    INNER JOIN  usbToken b
 			                    ON a.""TenantId"" = b.""TenantId""
	                    {conditionQueryDataModel.Query}
	                    OFFSET 0 ROWS FETCH NEXT {takeRows} ROWS ONLY
                    ) 
                ");
                return new QueryDataModel
                {
                    Query = withClause.ToString(),
                    Parameters = conditionQueryDataModel.Parameters
                };
            }

            withClause.Append($@"
                    WITH usbToken AS (
                        SELECT DISTINCT ""TenantId"" 
                        FROM ""UsbToken"" WHERE ""IsDeleted"" = :IsDeleted),
                    taxReport01Header AS (
                        SELECT ""ReportMonth"",  ""ReportYear"",  ""TenantId"",  ""ApproveStatus"" 
                        FROM ""TaxReport01Header"" WHERE ""IsDeleted"" = :IsDeleted),
                    invoice01Header AS (
                        SELECT a.* FROM ""Invoice01Header"" a
		                    INNER JOIN  usbToken b
 			                    ON a.""TenantId"" = b.""TenantId""
       	                    LEFT JOIN  taxReport01Header c
       		                    ON a.""TenantId"" = c.""TenantId""
   				                    AND c.""ReportMonth"" = a.""InvoiceDateMonth""
   				                    AND c.""ReportYear"" = a.""InvoiceDateYear""
	                    {conditionQueryDataModel.Query}
	                    OFFSET 0 ROWS FETCH NEXT {takeRows} ROWS ONLY
                    ) 
                ");
            return new QueryDataModel
            {
                Query = withClause.ToString(),
                Parameters = conditionQueryDataModel.Parameters
            };
        }

        private QueryDataModel GenerateCondition(SignRequest input, short signStatus, List<InvoiceSource> invoiceSources = null)
        {
            var dicParameter = new Dictionary<string, object>
            {
                {":IsDeleted", 0 },
                {":ListApproveStatus", new List<int>{ ApproveStatus.KhongQuyTrinhDuyet.GetHashCode() , ApproveStatus.DaDuyet.GetHashCode() }}
            };

            var condition = new StringBuilder($@" WHERE a.""ApproveStatus"" IN :ListApproveStatus AND a.""InvoiceNo"" IS NOT NULL");

            var hasValueIds = input is { Ids: { } } && input.Ids.Any();

            if (input.IsReSignError == false)
            {
                if (hasValueIds)
                {
                    dicParameter.Add(":Ids", input.Ids);
                    condition.Append($@" AND a.""Id"" IN :Ids");

                    dicParameter.Add(":ListSignStatus", new List<short> { 1, signStatus });
                    condition.Append($@" AND a.""SignStatus"" IN :ListSignStatus");
                }
                else
                {
                    if (invoiceSources != null && invoiceSources.Any())
                    {
                        dicParameter.Add(":Sources", invoiceSources.Select(p => p.GetHashCode()).ToList());
                        condition.Append(@$" AND ""Source"" IN :Sources");
                    }
                    else
                    {
                        dicParameter.Add(":Source", InvoiceSource.ShareDbForm.GetHashCode());
                        condition.Append(@" AND ""Source"" < :Source");
                    }

                    dicParameter.Add(":SignStatus", SignStatus.ChoKy.GetHashCode());
                    condition.Append(@" AND a.""SignStatus"" = :SignStatus");
                }
            }
            else
            {
                dicParameter.Add(":SignStatus", SignStatus.KyLoi.GetHashCode());
                condition.Append(@" AND a.""SignStatus"" = :SignStatus");
            }

            dicParameter.Add(":InvoiceStatus", InvoiceStatus.XoaHuy.GetHashCode());
            condition.Append(@$" AND a.""InvoiceStatus"" != :InvoiceStatus");

            if (!input.IsSignManual)
            {
                dicParameter.Add(":ApproveStatus", ApproveStatus.DaDuyet.GetHashCode());
                condition.Append($@" AND (c.""TenantId"" IS NULL OR c.""ApproveStatus"" != :ApproveStatus)");
            }

            return new QueryDataModel
            {
                Query = condition.ToString(),
                Parameters = new DynamicParameters(dicParameter)
            };
        }
    }
}
