using System;

namespace VnisCoreSignHsm.Application.Dto.TaxReport01
{
    public class GetTaxReport01DetailMappingDto
    {
        public long Id { get; set; }
        public long TaxReportHeaderId { get; set; }
        public int Index { get; set; }
        public DateTime ReportDate { get; set; } // Ng<PERSON>y lập báo cáo, người dùng tự chọn
        public DateTime FromDate { get; set; } // Ngày đầu tiên của kỳ báo cáo
        public DateTime ToDate { get; set; } // Ngày cuối cùng của kỳ báo cáo
        public short ReportYear { get; set; } // Năm báo cáo
        public short ReportMonth { get; set; } // Tháng báo cáo
        public short ReportQuarter { get; set; } // Quý báo cáo
        /// <summary>
        /// lần đầu báo cáo trong kỳ
        /// </summary>
        public bool IsFirstTimeInPeriod { get; set; }

        /// <summary>
        /// l<PERSON><PERSON> bổ sung
        /// </summary>
        public int AdditionalTimes { get; set; }

        public Guid TenantId { get; set; }
        public string TaxCode { get; set; }
        public string FullNameVi { get; set; }
        public int RptNum { get; set; }
        public string InvoiceHeaderIds { get; set; }
        public string Currency { get; set; }
        public short InvoiceType { get; set; }
    }
}