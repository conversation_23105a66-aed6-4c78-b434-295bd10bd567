using System;
using Org.BouncyCastle.Math;

namespace Org.BouncyCastle.Crypto
{
    /**
     * The basic interface that basic Di<PERSON>ie-<PERSON>man implementations
     * conforms to.
     */
    public interface IBasicAgreement
    {
        /**
         * initialise the agreement engine.
         */
        void Init(ICipherParameters parameters);

        /**
         * return the field size for the agreement algorithm in bytes.
         */
        int GetFieldSize();

        /**
         * given a public key from a given party calculate the next
         * message in the agreement sequence.
         */
        BigInteger CalculateAgreement(ICipherParameters pubKey);
    }

}
