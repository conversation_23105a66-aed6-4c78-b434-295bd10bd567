/*
 *  Copyright 2012-2017 The Pkcs11Interop Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

/*
 *  Written for the Pkcs11Interop project by:
 *  Jaroslav IMRICH <<EMAIL>>
 */

using System;
using HSM.SigningLibrary.Common;
using HSM.SigningLibrary.LowLevelAPI81.MechanismParams;

namespace HSM.SigningLibrary.HighLevelAPI81.MechanismParams
{
    /// <summary>
    /// Parameters for the CKM_SEED_CBC_ENCRYPT_DATA mechanism
    /// </summary>
    public class CkSeedCbcEncryptDataParams : IMechanismParams, IDisposable
    {
        /// <summary>
        /// Flag indicating whether instance has been disposed
        /// </summary>
        private bool _disposed = false;

        /// <summary>
        /// Low level mechanism parameters
        /// </summary>
        private CK_SEED_CBC_ENCRYPT_DATA_PARAMS _lowLevelStruct = new CK_SEED_CBC_ENCRYPT_DATA_PARAMS();

        /// <summary>
        /// Initializes a new instance of the CkSeedCbcEncryptDataParams class.
        /// </summary>
        /// <param name='iv'>IV value (16 bytes)</param>
        /// <param name='data'>Data value part that must be a multiple of 16 bytes long</param>
        public CkSeedCbcEncryptDataParams(byte[] iv, byte[] data)
        {
            _lowLevelStruct.Iv = new byte[16];
            _lowLevelStruct.Data = IntPtr.Zero;
            _lowLevelStruct.Length = 0;

            if (iv == null)
                throw new ArgumentNullException("iv");

            if (iv.Length != 16)
                throw new ArgumentOutOfRangeException("iv", "Array has to be 16 bytes long");

            Array.Copy(iv, _lowLevelStruct.Iv, iv.Length);

            if (data != null)
            {
                _lowLevelStruct.Data = UnmanagedMemory.Allocate(data.Length);
                UnmanagedMemory.Write(_lowLevelStruct.Data, data);
                _lowLevelStruct.Length = Convert.ToUInt64(data.Length);
            }
        }

        #region IMechanismParams

        /// <summary>
        /// Returns managed object that can be marshaled to an unmanaged block of memory
        /// </summary>
        /// <returns>A managed object holding the data to be marshaled. This object must be an instance of a formatted class.</returns>
        public object ToMarshalableStructure()
        {
            if (this._disposed)
                throw new ObjectDisposedException(this.GetType().FullName);

            return _lowLevelStruct;
        }

        #endregion

        #region IDisposable

        /// <summary>
        /// Disposes object
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Disposes object
        /// </summary>
        /// <param name="disposing">Flag indicating whether managed resources should be disposed</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!this._disposed)
            {
                if (disposing)
                {
                    // Dispose managed objects
                }

                // Dispose unmanaged objects
                UnmanagedMemory.Free(ref _lowLevelStruct.Data);
                _lowLevelStruct.Length = 0;

                _disposed = true;
            }
        }

        /// <summary>
        /// Class destructor that disposes object if caller forgot to do so
        /// </summary>
        ~CkSeedCbcEncryptDataParams()
        {
            Dispose(false);
        }

        #endregion
    }
}
