using System;
using System.Collections.Generic;
using System.Text;
using Org.BouncyCastle.Crypto;
using System.Security.Cryptography;
using Org.BouncyCastle.OpenSsl;
using Org.BouncyCastle.Security;
using Org.BouncyCastle.Crypto.Parameters;
using System.IO;
using System.Reflection;
using System.Xml;
using Org.BouncyCastle.Asn1.Nist;
using Org.BouncyCastle.Crypto.IO;
using Org.BouncyCastle.Utilities.Encoders;
using System.Linq;

namespace HSM.SigningLibrary.HsmSignUtility
{
    /// <summary>
    /// Configuration management
    /// </summary>
    public class Configuration
    {
        private static string _aesKey;
        private static byte[] _aesByteKey;
        private static Random random = new Random();
        private static byte[] N = Hex.Decode("62EC67F9C3A4A407FCB2A8C49031A8B3");
        /*****************************************************/
        /// <summary>
        /// Decode the config file
        /// </summary>
        /// <param name="inputXML"></param>
        /// <param name="outXML"></param>
        /// <param name="oMsg"></param>
        /// <returns></returns>
        public static bool RestoreConfig(string inputXML, out string outXML, out string oMsg)
        {
            oMsg = "";
            outXML = "";
            try
            {
                string[] data = inputXML.Split('|');
                if (data.Length != 2)
                {
                    oMsg = "Invalid licence";
                    return false;
                }

                Assembly assembly = Assembly.GetExecutingAssembly(); 
                string[] names = assembly.GetManifestResourceNames();
                if (names.Length != 19)
                {
                    oMsg = "Missing streams";
                    return false;
                }

                using (Stream stream = assembly.GetManifestResourceStream(names[3]))
                {
                    using (StreamReader reader = new StreamReader(stream))
                    {
                        AsymmetricKeyParameter key = (Org.BouncyCastle.Crypto.AsymmetricKeyParameter)new PemReader(reader).ReadObject();
                        IBufferedCipher c = CipherUtilities.GetCipher("RSA");
                        byte[] input = Convert.FromBase64String(data[0]);
                        c.Init(false, key);
                        byte[] outBytes = c.DoFinal(input);
                        _aesKey = System.Text.Encoding.UTF8.GetString(outBytes);
                        _aesByteKey = System.Text.Encoding.UTF8.GetBytes(_aesKey);
                    }

                }

                if (!Decrypt(data[1], out outXML, out oMsg)) return false;
            }
            catch (Exception e)
            {
                oMsg = e.ToString();
                return false;
            }

            return true;
        }
        /*****************************************************/
        /// <summary>
        /// 
        /// </summary>
        /// <param name="length"></param>
        private static void PasswordGenerator(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz`~!@#$%^&*()-_=+|][}{,.<>/?";
            _aesKey = new string(Enumerable.Repeat(chars, length).Select(s => s[random.Next(s.Length)]).ToArray());
            _aesByteKey = System.Text.Encoding.UTF8.GetBytes(_aesKey);
        }
        /*****************************************************/
        /// <summary>
        /// 
        /// </summary>
        /// <param name="inStr"></param>
        /// <param name="outStr"></param>
        /// <param name="oMsg"></param>
        /// <returns></returns>        
        private static bool Decrypt(string inStr, out string outStr, out string oMsg)
        {
            outStr = "";
            oMsg = "";
            try
            {
                KeyParameter key = ParameterUtilities.CreateKeyParameter("AES", _aesByteKey);
                IBufferedCipher outCipher = CipherUtilities.GetCipher("AES/EAX/NoPadding");
                outCipher.Init(false, new ParametersWithIV(key, N));
                byte[] C = Convert.FromBase64String(inStr);
                byte[] dec = outCipher.DoFinal(C);
                outStr = System.Text.Encoding.UTF8.GetString(dec);
            }
            catch (Exception e)
            {
                oMsg = "Decryption failed: " + e.ToString();
                return false;
            }
            return true;
        }

        /*****************************************************/
        private static bool EncryptFile(string inFile, out string outStr, out string oMsg)
        {
            outStr = "";
            oMsg = "";
            try
            {
                KeyParameter key = ParameterUtilities.CreateKeyParameter("AES", _aesByteKey);
                IBufferedCipher inCipher = CipherUtilities.GetCipher("AES/EAX/NoPadding");
                using (FileStream fsSource = new FileStream(inFile, FileMode.Open, FileAccess.Read))
                {

                    // Read the source file into a byte array.
                    byte[] P = new byte[fsSource.Length];
                    int numBytesToRead = (int)fsSource.Length;
                    int numBytesRead = 0;
                    while (numBytesToRead > 0)
                    {
                        // Read may return anything from 0 to numBytesToRead.
                        int n = fsSource.Read(P, numBytesRead, numBytesToRead);

                        // Break when the end of the file is reached.
                        if (n == 0)
                            break;

                        numBytesRead += n;
                        numBytesToRead -= n;
                    }
                    numBytesToRead = P.Length;
                    inCipher.Init(true, new ParametersWithIV(key, N));
                    byte[] enc = inCipher.DoFinal(P);
                    outStr = Convert.ToBase64String(enc);
                }
            }
            catch (Exception e)
            {
                oMsg = "Encryption failed: " + e.ToString();
                return false;
            }
            return true;
        }
        /*****************************************************/        
    }//END CLASS
}
