using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

[assembly: AssemblyTitle("HSMSigningLibrary")]
[assembly: AssemblyDescription("Library for managing HSM devices")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("VNI")]
[assembly: AssemblyCopyright("Copyright 2017-2034 The HSMSigningLibrary Project")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]
[assembly: ComVisible(false)]
[assembly: Guid("752ec6ad-5efc-408f-8563-9e134d982a59")]
[assembly: AssemblyVersion("3.3.0")]
[assembly: AssemblyFileVersion("3.3.0")]
