/*
 *  Copyright 2012-2017 The Pkcs11Interop Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

/*
 *  Written for the Pkcs11Interop project by:
 *  Jaroslav IMRICH <<EMAIL>>
 */

using System;
using System.Runtime.InteropServices;

namespace HSM.SigningLibrary.LowLevelAPI80.MechanismParams
{
    /// <summary>
    /// Structure that is returned by all OTP mechanisms in successful calls to C_Sign (C_SignFinal)
    /// </summary>
#if SILVERLIGHT
    [StructLayout(LayoutKind.Sequential, Pack = 0, CharSet = CharSet.Unicode)]
    public class CK_OTP_SIGNATURE_INFO
#else
    [StructLayout(LayoutKind.Sequential, Pack = 0, CharSet = CharSet.Unicode)]
    public struct CK_OTP_SIGNATURE_INFO
#endif
    {
        /// <summary>
        /// Pointer to an array of OTP parameter values (CK_OTP_PARAM structures)
        /// </summary>
        public IntPtr Params;

        /// <summary>
        /// The number of parameters in the array
        /// </summary>
        public ulong Count;
    }
}
