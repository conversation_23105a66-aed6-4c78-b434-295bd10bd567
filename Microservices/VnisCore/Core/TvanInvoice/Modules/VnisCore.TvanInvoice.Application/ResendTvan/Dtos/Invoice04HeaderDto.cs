using Core.Application.Dtos;

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace VnisCore.TvanInvoice.Application.ResendTvan.Dtos
{
    public class Invoice04HeaderDto : EntityDto<long>
    {
        public new long Id { get; set; }

        [JsonIgnore]
        public DateTime CreationTime { get; set; }

        /// <inheritdoc />
        [JsonIgnore]
        public Guid CreatorId { get; set; }

        public Guid BatchId { get; set; }

        public Guid TenantId { get; set; }

        public int? Number { get; set; }

        /// <summary>
        /// Nguồn dữ liệu từ đâu: API, Form, Excel, ShareDb
        /// </summary>
        public short Source { get; set; }

        /// <summary>
        /// Ghi bản ghi đăng ký phát hành hóa đơn, lưu lại để xác định hóa đơn được tạo theo thông báo phát hành nào
        /// </summary>
        public long? RegistrationHeaderId { get; set; }

        /// <summary>
        /// Ghi bản ghi đăng ký phát hành hóa đơn, lưu lại để xác định hóa đơn được tạo theo thông báo phát hành nào
        /// </summary>
        public long? RegistrationDetailId { get; set; }
        #region Thông tin chung của hóa đơn
        /// <summary>
        /// Mẫu số (1C21TAA)
        /// </summary>
        public short TemplateNo { get; set; }

        /// <summary>
        /// Ký hiệu (PT/17E)
        /// </summary>
        public string SerialNo { get; set; }

        /// <summary>
        /// Số hóa đơn gồm 7 ký tự số (0000001)
        /// </summary>
        public string InvoiceNo { get; set; }

        /// <summary>
        /// Id bản ghi mẫu hóa đơn (InvoiceTemplate)
        /// </summary>
        public long InvoiceTemplateId { get; set; }

        /// <summary>
        /// Ghi chú
        /// </summary>
        public string Note { get; set; }

        /// <summary>
        /// Ngày hóa đơn NSD nhập vào. Chỉ lưu ngày, tháng, năm. Không lưu thời gian. Lưu dạng UTC
        /// </summary>
        public DateTime InvoiceDate { get; set; }
        public short InvoiceDateYear { get; set; }
        public short InvoiceDateQuater { get; set; }
        public short InvoiceDateMonth { get; set; }
        public short InvoiceDateWeek { get; set; }
        public short InvoiceDateNumber { get; set; }

        /// <summary>
        /// Dùng để đánh dấu các hóa đơn có cùng giao dịch. Ví dụ hóa đơn A là gốc, B là điều chỉnh cho A, C là thay thế cho B
        /// </summary>
        public string TransactionId { get; set; }

        /// <summary>
        /// Trạng thái hóa đơn
        /// </summary>
        public short InvoiceStatus { get; set; }

        /// <summary>
        /// Trạng thái ký hóa đơn
        /// </summary>
        public short SignStatus { get; set; }

        /// <summary>
        /// Trạng thái duyệt hóa đơn để ký
        /// </summary>
        public short ApproveStatus { get; set; }

        /// <summary>
        /// Trạng thái duyệt hóa đơn xóa hủy
        /// </summary>
        public short ApproveCancelStatus { get; set; }

        /// <summary>
        /// Trạng thái duyệt hóa đơn xóa bỏ
        /// </summary>
        public short ApproveDeleteStatus { get; set; }

        /// <summary>
        /// kiểm tra thông tin hóa đơn đã được xem chưa
        /// </summary>
        public bool IsOpened { get; set; }

        /// <summary>
        /// thời gian xem hóa đơn
        /// </summary>
        public DateTime? OpenedTime { get; set; }

        /// <summary>
        /// kiểm tra thông tin hóa đơn đã được tra cứu ở portal chưa
        /// </summary>
        public bool IsViewed { get; set; }

        /// <summary>
        /// thời gian tra cứu hóa đơn ở portal
        /// </summary>
        public DateTime? ViewedTime { get; set; }

        /// <summary>
        /// thời gian phát hành/sinh số
        /// </summary>
        public DateTime? IssuedTime { get; set; }

        /// <summary>
        /// thời gian thực hiện xóa hủy
        /// nếu có quy trình duyệt => thời gian thực hiện xóa hủy, k phải thời gian thực hiện duyệt xóa hủy
        /// </summary>
        public DateTime? CancelTime { get; set; }

        /// <summary>
        /// người thực hiện xóa hủy
        /// nếu có quy trình duyệt => người thực hiện xóa hủy, k phải người thực hiện duyệt xóa hủy
        /// </summary>
        public Guid? CancelId { get; set; }

        /// <summary>
        /// thời gian thực hiện xóa bỏ
        /// nếu có quy trình duyệt => thời gian thực hiện xóa bỏ, k phải thời gian thực hiện duyệt xóa bỏ
        /// </summary>
        public DateTime? DeleteTime { get; set; }

        /// <summary>
        /// người thực hiện xóa bỏ
        /// nếu có quy trình duyệt => người thực hiện xóa bỏ, k phải người thực hiện duyệt xóa bỏ
        /// </summary>
        public Guid? DeleteId { get; set; }

        #endregion

        #region Thông tin Phiếu xuất kho hàng giao gửi đại lý
        /// <summary>
        /// Hợp đồng kinh tế số  
        /// MaxLength 250
        /// </summary>
        public string EconomicContractNumber { get; set; }

        /// <summary>
        /// Ngày hợp đồng kinh tế
        /// </summary>
        public DateTime EconomicContractDate { get; set; }

        /// <summary>
        /// Họ và Tên người vận chuyển
        /// MaxLength 250
        /// </summary>
        public string DeliveryBy { get; set; }

        /// <summary>
        /// Họ và tên người xuất hàng
        /// MaxLength 100
        /// </summary>
        public string DeliveryOrderBy { get; set; }

        /// <summary>
        /// hợp đồng số
        /// MaxLength 250
        /// </summary>
        public string ContractNumber { get; set; }

        /// <summary>
        /// Phương tiện vận chuyển
        /// MaxLength 250
        /// </summary>
        public string TransportationMethod { get; set; }
        #endregion

        #region Thông tin người bán
        /// <summary>
        /// Id bản ghi người bán (Organization)
        /// </summary>
        public Guid SellerId { get; set; }

        /// <summary>
        /// Mã công ty/chi nhánh
        /// </summary>
        public string SellerCode { get; set; }

        /// <summary>
        /// Người đại diện pháp nhân bên bán
        /// </summary>
        public string SellerLegalName { get; set; }

        /// <summary>
        /// Mã số thuế bên bán
        /// </summary>
        public string SellerTaxCode { get; set; }

        /// <summary>
        /// Địa chỉ bên bán
        /// </summary>
        public string SellerAddressLine { get; set; }

        /// <summary>
        /// Mã quốc gia người bán (Việt Nam là VN)
        /// </summary>
        public string SellerCountryCode { get; set; }

        /// <summary>
        /// Tên phường/xã người bán
        /// </summary>
        public string SellerDistrictName { get; set; }

        /// <summary>
        /// Tên thành phố người bán
        /// </summary>
        public string SellerCityName { get; set; }

        /// <summary>
        /// Số điện thoại người bán
        /// </summary>
        public string SellerPhoneNumber { get; set; }

        /// <summary>
        /// Số FAX người bán
        /// </summary>
        public string SellerFaxNumber { get; set; }

        /// <summary>
        /// Email người bán
        /// </summary>
        public string SellerEmail { get; set; }

        /// <summary>
        /// Tên ngân hàng người bán
        /// </summary>
        public string SellerBankName { get; set; }

        /// <summary>
        /// Tài khoản ngân hàng người bán
        /// </summary>
        public string SellerBankAccount { get; set; }

        /// <summary>
        /// Tên công ty bán
        /// </summary>
        public string SellerFullName { get; set; }

        /// <summary>
        /// Thời điểm hóa đơn được ký
        /// </summary>
        public DateTime? SellerSignedTime { get; set; }

        /// <summary>
        /// Họ tên người ký hóa đơn (Sử dụng tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string SellerFullNameSigned { get; set; }

        /// <summary>
        /// Tài khoản người ký hóa đơn (Sử dụng tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public Guid? SellerSignedId { get; set; }
        #endregion

        #region Thông tin thanh toán
        /// <summary>
        /// Nguyên tệ
        /// </summary>
        public string FromCurrency { get; set; }

        /// <summary>
        /// Ngoại tệ
        /// </summary>
        public string ToCurrency { get; set; }

        public int RoundingCurrency { get; set; }

        /// <summary>
        ///  lưu giá trị chuyển đổi từ đơn vị cao sang đơn vị thấp. vd 1 đô la = 100 cents
        /// </summary>
        public int CurrencyConversion { get; set; }

        /// <summary>
        /// Tỷ giá (Bằng 1 nếu Nguyên tệ = Ngoại tệ)
        /// </summary>
        public decimal ExchangeRate { get; set; }

        /// <summary>
        /// Ngày thanh toán
        /// </summary>
        public DateTime? PaymentDate { get; set; }

        /// <summary>
        /// Số tiền bằng chữ (Đọc tiếng việt)
        /// </summary>
        public string PaymentAmountWords { get; set; }

        /// <summary>
        /// Số tiền bằng chữ (Đọc tiếng anh)
        /// </summary>
        public string PaymentAmountWordsEn { get; set; }

        /// <summary>
        /// Tổng tiền chưa thuế, chưa chiết khấu
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// Tổng tiền phải trả
        /// </summary>
        public decimal TotalPaymentAmount { get; set; }
        #endregion

        #region Thông tin tạo/sửa/xóa/duyệt/in
        /// <summary>
        /// Họ tên người insert bản ghi vào DB (lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string FullNameCreator { get; set; }

        /// <summary>
        /// UserName insert bản ghi vào DB (lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string UserNameCreator { get; set; }

        /// <summary>
        /// Ngày in chuyển đổi, khác null tức là đã in chuyển đổi
        /// </summary>
        public DateTime? PrintedTime { get; set; }

        /// <summary>
        /// Họ tên người in chuyển đổi (lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string FullNamePrinter { get; set; }

        /// <summary>
        /// Tài khoản đăng nhập người in chuyển đôi (lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public Guid? PrintedId { get; set; }

        /// <summary>
        /// Tài khoản đăng nhập người duyệt hóa đơn để ký (lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public Guid? ApprovedId { get; set; }

        /// <summary>
        /// Thời điểm duyệt hóa đơn chờ ký
        /// </summary>
        public DateTime? ApprovedTime { get; set; }

        /// <summary>
        /// Họ tên người duyệt hóa đơn chờ ký (lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string FullNameApprover { get; set; }


        /// <summary>
        /// Tài khoản đăng nhập người duyệt hóa đơn để xóa hủy (lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public Guid? ApprovedCancelId { get; set; }

        /// <summary>
        /// Thời điểm duyệt hóa đơn xóa hủy
        /// </summary>
        public DateTime? ApprovedCancelTime { get; set; }

        /// <summary>
        /// Họ tên người duyệt hóa đơn xóa hủy(lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string FullNameApproverCancel { get; set; }

        /// <summary>
        /// Tài khoản đăng nhập người duyệt hóa đơn để xóa bỏ (lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public Guid? ApprovedDeleteId { get; set; }

        /// <summary>
        /// Thời điểm duyệt hóa đơn xóa bỏ
        /// </summary>
        public DateTime? ApprovedDeleteTime { get; set; }

        /// <summary>
        /// Họ tên người duyệt hóa đơn xóa bỏ(lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string FullNameApproverDelete { get; set; }

        /// <summary>
        /// nguồn xóa hủy/xóa bỏ hóa đơn
        /// </summary>
        public short InvoiceDeleteSource { get; set; }

        #endregion

        #region Thông tin tích hợp
        /// <summary>
        /// Id bản ghi bên ERP
        /// </summary>
        public string ErpId { get; set; }

        /// <summary>
        /// Tài khoản người tạo hóa đơn bên ERP
        /// </summary>
        public string CreatorErp { get; set; }
        #endregion


        public long Partition { get; set; }

        #region Thông tin người mua
        /// <summary>
        /// Id bản ghi người mua (Customer), có thể null nếu là loại hóa đơn 03XKNB
        /// </summary>
        public long? BuyerId { get; set; }

        /// <summary>
        /// Mã số thuế người mua
        /// </summary>
        public string BuyerTaxCode { get; set; }

        /// <summary>
        /// Tên (Tên người nhận hàng)
        /// </summary>
        public string BuyerName { get; set; }

        /// <summary>
        /// Họ tên người mua (nếu là khách lẻ không thuộc công ty nào thì đây là tên công ty)
        /// </summary>
        public string BuyerFullName { get; set; }

        /// <summary>
        /// Địa chỉ người mua
        /// </summary>
        public string BuyerAddressLine { get; set; }

        /// <summary>
        /// Email người mua
        /// </summary>
        public string BuyerEmail { get; set; }

        /// <summary>
        /// Thời điểm người mua ký
        /// </summary>
        public DateTime? BuyerSignedTime { get; set; }

        /// <summary>
        /// Họ tên người mua ký (Sử dụng tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string BuyerFullNameSigned { get; set; }

        /// <summary>
        /// Tài khoản người mua ký hóa đơn (Sử dụng tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public Guid? BuyerSignedId { get; set; }
        #endregion


        #region Thông tin tích hợp
        /// <summary>
        /// Id bản ghi người mua bên ERP
        /// </summary>
        public string BuyerErpId { get; set; }
        #endregion

        public Dictionary<string, string> ExtraProperties { get; set; }
    }
}
