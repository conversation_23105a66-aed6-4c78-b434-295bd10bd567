using Core.Shared.Constants;
using Core.Shared.Dto;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.Tvan.Constants;
using Core.Tvan.Interfaces;

using Microsoft.EntityFrameworkCore;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Reports;
using VnisCore.TvanInvoice.Application.ResendTvan.Interfaces;

namespace VnisCore.TvanInvoice.Application.ResendTvan.Services
{
    public class ResendTax01ReportService : IResendToTvanService
    {
        private readonly IAppFactory _appFactory;
        private readonly ITvanInvoiceService _tvanInvoiceService;
        private readonly IFileService _fileService;


        public ResendTax01ReportService(IAppFactory appFactory,
                                        ITvanInvoiceService tvanInvoiceService,
                                        IFileService fileService)
        {
            _appFactory = appFactory;
            _tvanInvoiceService = tvanInvoiceService;
            _fileService = fileService;
        }

        public Task ResendInvoiceHasCodeAsync(List<long> ids)
        {
            throw new NotImplementedException();
        }

        public async Task ResendTvanAsync(Guid tenantId, string rawTenantId, string taxCode)
        {
            var tax01ReportRepos = _appFactory.Repository<TaxReport01HeaderEntity, long>();
            var idsTax01Report = await tax01ReportRepos.Where(x => x.TenantId == tenantId && !x.IsDeleted &&
                                                                  (x.StatusTvan == (short)TvanStatus.SendError || x.StatusTvan == (short)TvanStatus.UnSent))
                                                       .Select(x => x.Id)
                                                       .ToListAsync();

            if (idsTax01Report.Any())
            {
                foreach (var id in idsTax01Report)
                {
                    var fileDto = await DownloadAsync(id, tenantId);
                    if (fileDto == null)
                        continue;

                    // send to TVAN
                    await _tvanInvoiceService.SendReport01Async(Encoding.UTF8.GetString(fileDto.FileBytes));

                    // update status DB 
                    var report = await tax01ReportRepos.FirstOrDefaultAsync(x => x.Id == id);
                    report.StatusTvan = (short)TvanStatus.Sended;
                    await tax01ReportRepos.UpdateAsync(report);
                }
            }
        }

        private async Task<FileDto> DownloadAsync(long id, Guid tenantId)
        {
            var taxReport01HeaderRepos = _appFactory.Repository<ReportXmlEntity, long>();
            var taxReport01Header = await taxReport01HeaderRepos.FirstOrDefaultAsync(x => x.ReportHeaderId == id &&
                                                                                                   x.TenantId == tenantId);
            if (taxReport01Header == null)
                return null;

            var pathFileMinio = $"{MediaFileType.ReportXml}/{taxReport01Header.TenantId}/{taxReport01Header.CreationTime.Year}/{taxReport01Header.CreationTime.Month:00}/{taxReport01Header.CreationTime.Day:00}/{taxReport01Header.CreationTime.Hour:00}/{taxReport01Header.FileName}";
            var bytes = await _fileService.DownloadAsync(pathFileMinio);

            return new FileDto
            {
                ContentType = ContentType.Xml,
                FileBytes = bytes,
                FileBase64 = Convert.ToBase64String(bytes, 0, bytes.Length),
                FileName = taxReport01Header.FileName
            };
        }
    }
}
