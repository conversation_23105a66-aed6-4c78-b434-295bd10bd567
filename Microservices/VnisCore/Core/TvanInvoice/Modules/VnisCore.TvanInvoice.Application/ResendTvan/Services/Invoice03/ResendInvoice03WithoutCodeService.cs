using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.Tvan.Constants;
using Core.Tvan.Interfaces;
using Core.Tvan.Models.Xmls;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Linq;

using VnisCore.Core.Oracle.Domain.Entities.Tvan.Invoice03;
using VnisCore.TvanInvoice.Application.ResendTvan.Constants;
using VnisCore.TvanInvoice.Application.ResendTvan.Interfaces;
using VnisCore.TvanInvoice.Application.ResendTvan.Repositories.Invoice03;

namespace VnisCore.TvanInvoice.Application.ResendTvan.Services.Invoice03
{
    public class ResendInvoice03WithoutCodeService : IResendToTvanService
    {
        private readonly IAppFactory _appFactory;
        private readonly IFileService _fileService;
        private readonly ITvanInvoiceService _tvanInvoiceService;
        private readonly IInvoice03HeaderRepository _invoice03HeaderRepository;
        private readonly IInvoice03XmlRepository _invoice03XmlRepository;
        private readonly IDownloadFileInvoice03Service _downloadFileInvoice03Service;

        public ResendInvoice03WithoutCodeService(IAppFactory appFactory,
                                                 IFileService fileService,
                                                 ITvanInvoiceService tvanInvoiceService,
                                                 IInvoice03HeaderRepository invoice03HeaderRepository,
                                                 IInvoice03XmlRepository invoice03XmlRepository,
                                                 IDownloadFileInvoice03Service downloadFileInvoice03Service)
        {
            _appFactory = appFactory;
            _fileService = fileService;
            _tvanInvoiceService = tvanInvoiceService;
            _invoice03HeaderRepository = invoice03HeaderRepository;
            _invoice03XmlRepository = invoice03XmlRepository;
            _downloadFileInvoice03Service = downloadFileInvoice03Service;
        }

        public async Task ResendTvanAsync(Guid tenantId, string rawTenantId, string taxCode)
{
            var invoice03Header = await _invoice03HeaderRepository.GetListUnSendTvanAsync(rawTenantId, InvoiceType.WithoutCode);
            if (!invoice03Header.Any())
                return;

            var idsInvoiceHeader = invoice03Header.ToDictionary(x => x.Id, x => x.InvoiceTemplateId);
            var idsInvoice03Header = await BuildAndSendXmlToTvan(idsInvoiceHeader, taxCode, tenantId, rawTenantId);
            if (idsInvoice03Header.Count > 0)
                await _invoice03HeaderRepository.UpdateTvanStatusAsync(idsInvoice03Header);
            
        }

        private async Task<List<long>> BuildAndSendXmlToTvan(Dictionary<long, long> idsInvoiceHeader, string taxCode, Guid tenantId, string rawTenantId)
        {
            var tdiep = new TDiepModel<TTChungModel, object>
            {
                TTChung = new TTChungModel
                {
                    PBan = TvanInvoiceStaticData.PBan,
                    MST = taxCode,
                    MNGui = "0101352495",
                    MNNhan = "0103182292",
                    MLTDiep = (int)MLTDiep._203,
                    MTDiep = $"0101352495{Guid.NewGuid()}".Replace("-", ""),
                    MTDTChieu = null,
                    SLuong = 0
                }
            };

            string fullXml = XmlExtension.RemoveNewLine(XmlExtension.ObjToXml(tdiep));

            // Create a new XML document.
            XmlDocument document = new XmlDocument();

            // Load an XML file into the XmlDocument object.
            document.PreserveWhitespace = true;
            document.LoadXml(fullXml);

            var tdiepNode = document.SelectSingleNode("TDiep");

            var elementDLieu = document.CreateElement("DLieu");

            tdiepNode.AppendChild(elementDLieu);

            document.InnerXml = tdiepNode.OuterXml;

            //  ********* Thêm nhiều thẻ HDon lấy từ MINIO  *********
            var invoiceXmls = await _invoice03XmlRepository.GetByInvoiceHeaderIdsAsync(rawTenantId, idsInvoiceHeader.Keys.ToList());
            if (!invoiceXmls.Any())
                return new List<long>();

            var idsHeaderSended = new List<long>();

            int numberOfHDon = 0;
            int i = 0;

            foreach (var invoiceXmlEntity in invoiceXmls)
            {
                i++;

                var fileDto = await _downloadFileInvoice03Service.DownloadAsync(tenantId, invoiceXmlEntity);

                var xmlHDon = Encoding.UTF8.GetString(fileDto.FileBytes);

                XmlDocument hdonDoc = new XmlDocument();
                hdonDoc.LoadXml(xmlHDon);
                XmlNode hdonNode = hdonDoc.DocumentElement;

                var dLieu = document.SelectSingleNode("//DLieu");
                dLieu.AppendChild(document.ImportNode(hdonNode, true));

                var fileByte = new UTF8Encoding(true).GetBytes(document.InnerXml);

                numberOfHDon++;
                idsHeaderSended.Add(invoiceXmlEntity.InvoiceHeaderId);

                // Check dung lượng file ko đc vượt quá 2MB
                if (fileByte.Length < 1.5 * 1034 * 1034 && i < invoiceXmls.Count)
                    continue;

                tdiepNode.AppendChild(document.ImportNode(elementDLieu, true));

                XmlExtension.RemoveNullFields(document);
                XmlExtension.RemoveNewLine(document.InnerXml);

                //var nodeTTChung = document.SelectSingleNode("//TTChung");
                var nodeTTChung = document.DocumentElement.ChildNodes[0];
                nodeTTChung["SLuong"].InnerText = numberOfHDon.ToString();

                var tvanResponse = await _tvanInvoiceService.SendInvoiceWithoutCodeAsync(document.InnerXml);
                if (!tvanResponse.Success)
                {
                    elementDLieu.RemoveAll();
                    numberOfHDon = 0;
                    idsHeaderSended = new List<long>();
                    continue;
                }

                //update TvanInvoice03WithoutCodeMonitor Thông tin xml gửi lên TVAN 
                var invoiceTvanInfoId = await SaveRequest(document.InnerXml, tdiep.TTChung, tenantId);

                var invoiceHeaderSended = await _invoice03HeaderRepository.GetListByIdsAsync(idsHeaderSended, rawTenantId);
                if (invoiceHeaderSended.Any())
                {
                    var toNumber = (int)invoiceHeaderSended[invoiceHeaderSended.Count - 1].Number;
                    var fromNumber = (int)invoiceHeaderSended[0].Number;
                    await UpdateMonitor(invoiceTvanInfoId, idsInvoiceHeader[invoiceXmlEntity.InvoiceHeaderId], fromNumber, toNumber, tenantId);
                    await _invoice03HeaderRepository.UpdateManyAsync(idsHeaderSended);
                }

                elementDLieu.RemoveAll();
                numberOfHDon = 0;
            }

            return idsHeaderSended;
        }

        private async Task<long> SaveRequest(string file, TTChungModel ttChung, Guid tenantId)
        {
            //lưu xml
            //up lên minio trước
            //lưu file vào minio trước rồi mới lưu vào db

            var fileName = $"{ttChung.MST + "-" + DateTime.Now.Ticks.ToString()}.xml".Replace("/", "_");
            var tvanInvoice03WithoutCodeXmlEntity = new TvanInvoice03WithoutCodeXmlEntity
            {
                ContentType = ContentType.Xml,
                FileName = fileName,
                PhysicalFileName = fileName,
                Length = Encoding.UTF8.GetBytes(file).Length,
                TenantId = tenantId
            };

            var pathFileMinio = $"{MediaFileType.Invoice03WithoutCodeTvanXml}/{tenantId}/{DateTime.Now.Year}/{DateTime.Now.Month:00}/{tvanInvoice03WithoutCodeXmlEntity.CreationTime.Day:00}/{DateTime.Now.Hour:00}/{fileName}";

            await _fileService.UploadAsync(pathFileMinio, Encoding.UTF8.GetBytes(file));
            var repoXml = _appFactory.Repository<TvanInvoice03WithoutCodeXmlEntity, long>();
            await repoXml.InsertAsync(tvanInvoice03WithoutCodeXmlEntity);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            // lưu thông tin phản hồi của TCT
            var reposInvoice03WithoutCodeTvanInfo = _appFactory.Repository<TvanInfoInvoice03WithoutCodeEntity, long>();
            var invoice03WithoutCodeTvanInfoEntity = new TvanInfoInvoice03WithoutCodeEntity
            {
                MessageTypeCode = ttChung.MLTDiep.ToString(),
                MessageCode = ttChung.MTDiep,
                MessageCodeReference = ttChung.MTDTChieu,
                FileId = tvanInvoice03WithoutCodeXmlEntity.Id,
                TenantId = tenantId
            };

            await reposInvoice03WithoutCodeTvanInfo.InsertAsync(invoice03WithoutCodeTvanInfoEntity);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            return invoice03WithoutCodeTvanInfoEntity.Id;
        }

        private async Task UpdateMonitor(long invoiceTvanInfoId, long invoiceTemplateId, int fromNumber, int toNumber, Guid tenantId)
        {
            var reposTvanInvoice03WithoutCodeMonitor = _appFactory.Repository<TvanInvoice03WithoutCodeMonitorEntity, long>();
            var tvanInvoice03WithoutCodeMonitorEntity = new TvanInvoice03WithoutCodeMonitorEntity
            {
                FromNumber = fromNumber,
                ToNumber = toNumber,
                InvoiceTvanInfoId = invoiceTvanInfoId,
                InvoiceTemplateId = invoiceTemplateId,
                IsActive = true,
                TenantId = tenantId
            };
            await reposTvanInvoice03WithoutCodeMonitor.InsertAsync(tvanInvoice03WithoutCodeMonitorEntity);
        }

        private XmlDocument RemoveNullFields(XmlDocument xmldoc)
        {
            var mgr = new XmlNamespaceManager(xmldoc.NameTable);
            mgr.AddNamespace("xsi", "http://www.w3.org/2003/XMLSchema-instance");

            var nilFields = xmldoc.SelectNodes("//*[@xsi:nil='true']", mgr);

            if (nilFields != null && nilFields.Count > 0)
            {
                for (var i = 0; i < nilFields.Count; i++)
                {
                    nilFields[i].ParentNode.RemoveChild(nilFields[i]);
                }
            }

            var doc = XElement.Parse(xmldoc.OuterXml);

            var nullFields = (from child in doc.Descendants().Reverse()
                              where !child.HasElements && string.IsNullOrEmpty(child.Value) && !child.HasAttributes
                              select xmldoc.GetElementsByTagName(child.Name.LocalName)[0]).ToList();

            if (!nullFields.Any()) return xmldoc;

            foreach (var nullField in nullFields)
            {
                nullField?.ParentNode?.RemoveChild(nullField);
            }
            nullFields.RemoveRange(0, nullFields.Count);
            RemoveNullFields(xmldoc);

            return xmldoc;
        }

        public Task ResendInvoiceHasCodeAsync(List<long> ids)
        {
            throw new NotImplementedException();
        }
    }
}
