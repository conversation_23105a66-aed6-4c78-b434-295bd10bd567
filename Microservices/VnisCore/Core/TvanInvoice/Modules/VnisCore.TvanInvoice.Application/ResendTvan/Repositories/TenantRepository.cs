using AutoMapper;

using Core.Shared.Factory;
using Core.TenantManagement;

using Dapper;

using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using VnisCore.TvanInvoice.Application.ResendTvan.Dtos;

namespace VnisCore.TvanInvoice.Application.ResendTvan.Repositories
{
    public interface ITenantRepository
    {
        Task<List<Tenant>> GetListAsync();
    }

    public class TenantRepository : ITenantRepository
    {
        private readonly IAppFactory _appFactory;

        public TenantRepository(IAppFactory appFactory)
        {
            _appFactory = appFactory;
        }

        public async Task<List<Tenant>> GetListAsync()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<VnisTenantDto, Tenant>().ReverseMap();
            });
            var mapper = new Mapper(config);

            var sql = @$"SELECT * FROM ""VnisTenants"" WHERE ""IsDeleted"" = 0";

            var tenantDtos = (await _appFactory.AuthDatabase.Connection.QueryAsync<VnisTenantDto>(sql)).ToList();
            if (!tenantDtos.Any())
                return null;

            return tenantDtos.Select(x => mapper.Map<VnisTenantDto, Tenant>(x)).ToList();
        }
    }
}
