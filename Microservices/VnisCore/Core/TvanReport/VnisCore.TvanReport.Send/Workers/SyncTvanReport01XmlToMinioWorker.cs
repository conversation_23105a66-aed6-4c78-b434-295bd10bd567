using Core.BackgroundWorkers;
using Core.Threading;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Serilog;
using System.Threading.Tasks;
using VnisCore.TvanReport.Send.Interfaces;

namespace VnisCore.TvanReport.Send.Workers
{
    public class SyncTvanReport01XmlToMinioWorker : AsyncPeriodicBackgroundWorkerBase
    {
        public SyncTvanReport01XmlToMinioWorker(
            AbpAsyncTimer timer,
            IConfiguration configuration,
            IServiceScopeFactory serviceScopeFactory) : base(timer, serviceScopeFactory)
        {
            timer.Period = 1000; //thời gian gi<PERSON>a cách lần chạy

            if (int.TryParse(configuration.GetSection("TimePeriodSyncMinio").Value, out int period))
                timer.Period = period * 1000;
        }

        protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
        {
            try
            {
                var service = workerContext.ServiceProvider.GetService<ISyncTaxReport01XmlToMinioService>();

                await service.DoworkAsync();
            }
            catch (System.Exception ex)
            {
                Log.Error(ex, ex.Message, ex.StackTrace);
            }
        }
    }
}
