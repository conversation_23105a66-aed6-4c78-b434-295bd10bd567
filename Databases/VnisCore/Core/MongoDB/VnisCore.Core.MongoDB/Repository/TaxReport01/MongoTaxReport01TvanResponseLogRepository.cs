using Core.Domain.Repositories.MongoDB;
using Core.MongoDB;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using VnisCore.Core.MongoDB.Entities.TaxReport01;
using VnisCore.Core.MongoDB.IRepository.TaxReport01;

namespace VnisCore.Core.MongoDB.Repository.TaxReport01
{
    public class MongoTaxReport01TvanResponseLogRepository : MongoDbRepository<IVnisCoreMongoDbContext, MongoTaxReport01TvanResponseLogEntity, Guid>, IMongoTaxReport01TvanResponseLogRepository
    {
        public MongoTaxReport01TvanResponseLogRepository(IMongoDbContextProvider<IVnisCoreMongoDbContext> dbContextProvider) : base(dbContextProvider)
        {
        }

        public async Task<MongoTaxReport01TvanResponseLogEntity> GetById(Guid id, CancellationToken cancellationToken = default)
        {
            return await (await GetMongoQueryableAsync(cancellationToken))
                    .FirstOrDefaultAsync(ct => ct.Id == id,
                        GetCancellationToken(cancellationToken));
        }

        public async Task<List<MongoTaxReport01TvanResponseLogEntity>> GetListHandleXmlTakeAsync(short numberTake, CancellationToken cancellationToken = default)
        {
            return await (await GetMongoQueryableAsync(cancellationToken))
                .Where(x => x.HandleXmlStatus == 0)
                .OrderBy(x => x.CreationTime)
                .As<IMongoQueryable<MongoTaxReport01TvanResponseLogEntity>>()
                .Take(numberTake)
                .ToListAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<List<MongoTaxReport01TvanResponseLogEntity>> GetListSyncedToMinioTakeAsync(short numberTake, CancellationToken cancellationToken = default)
        {
            return await (await GetMongoQueryableAsync(cancellationToken))
                .Where(x => x.SyncedToMinioStatus == 0)
                .OrderBy(x => x.CreationTime)
                .As<IMongoQueryable<MongoTaxReport01TvanResponseLogEntity>>()
                .Take(numberTake)
                .ToListAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<bool> AnyHandleXmlAsync(CancellationToken cancellationToken = default)
        {
            return await (await GetMongoQueryableAsync(cancellationToken))
                .Where(x => x.HandleXmlStatus == 0)
                .As<IMongoQueryable<MongoTaxReport01TvanResponseLogEntity>>()
                .AnyAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<bool> AnySyncedToMinioAsync(CancellationToken cancellationToken = default)
        {
            return await (await GetMongoQueryableAsync(cancellationToken))
                .Where(x => x.SyncedToMinioStatus == 0)
                .As<IMongoQueryable<MongoTaxReport01TvanResponseLogEntity>>()
                .AnyAsync(GetCancellationToken(cancellationToken));
        }

        public async Task UpdateManyHandleXmlStatusAsync(List<MongoTaxReport01TvanResponseLogEntity> entities, int status, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoTaxReport01TvanResponseLogEntity>.Filter.In(x => x.Id, entities.Select(x => x.Id));
            var update = Builders<MongoTaxReport01TvanResponseLogEntity>.Update.Set(x => x.HandleXmlStatus, status);
            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }

        public async Task UpdateHandleXmlStatusAsync(MongoTaxReport01TvanResponseLogEntity entity, int status, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoTaxReport01TvanResponseLogEntity>.Filter.Eq(x => x.Id, entity.Id);
            var update = Builders<MongoTaxReport01TvanResponseLogEntity>.Update.Set(x => x.HandleXmlStatus, status);
            await (await GetCollectionAsync(cancellationToken)).UpdateOneAsync(filter, update, null, cancellationToken);
        }

        public async Task UpdateManySyncedToMinioStatusAsync(List<MongoTaxReport01TvanResponseLogEntity> entities, int status, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoTaxReport01TvanResponseLogEntity>.Filter.In(x => x.Id, entities.Select(x => x.Id));
            var update = Builders<MongoTaxReport01TvanResponseLogEntity>.Update.Set(x => x.SyncedToMinioStatus, status);
            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }

        public async Task UpdateSyncedToMinioStatusAsync(MongoTaxReport01TvanResponseLogEntity entity, int status, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoTaxReport01TvanResponseLogEntity>.Filter.Eq(x => x.Id, entity.Id);
            var update = Builders<MongoTaxReport01TvanResponseLogEntity>.Update
                .Set(x => x.XmlFilePath, entity.XmlFilePath)
                .Set(x => x.SyncedToMinioStatus, status);
            await (await GetCollectionAsync(cancellationToken)).UpdateOneAsync(filter, update, null, cancellationToken);
        }
    }
}
