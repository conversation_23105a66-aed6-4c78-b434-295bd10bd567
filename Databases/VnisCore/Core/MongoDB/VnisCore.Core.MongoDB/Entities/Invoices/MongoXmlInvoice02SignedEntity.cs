using Core.Domain.Entities;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;

namespace VnisCore.Core.MongoDB.Entities.Invoices
{
    [Table("XmlInvoice02Signed")]
    [BsonIgnoreExtraElements]
    public class MongoXmlInvoice02SignedEntity : Entity<Guid>
    {
        public long InvoiceHeaderId { get; set; }

        public string SellerTaxCode { get; set; }

        public short TemplateNo { get; set; }

        public string SerialNo { get; set; }

        public string InvoiceNo { get; set; }

        public short InvoiceStatus { get; set; }

        public string FileName { get; set; }
        
        public string BuyerEmail { get; set; }

        public Guid UserId { get; set; }

        public string FullNameCreator { get; set; }

        public string UserNameCreator { get; set; }

        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        public DateTime CreationTime { get; set; }

        public string Xml { get; set; }

        /// <summary>
        /// 0: chưa đồng bộ
        /// 1: đang đồng bộ
        /// 2: đã đồng bộ
        /// 3: đồng bộ lỗi
        /// </summary>
        [DefaultValue(0)]
        public short IsSynced { get; set; }

        public bool IsGeneratedContentMail { get; set; }

        /// <summary>
        /// Enum: SendMailStatus
        /// 0 or null: Chưa lấy ra để generate content mail
        /// 1: Đang thực hiện generate content email
        /// 2: Đang thực hiện gửi mail
        /// 3. Đã hoàn thành gửi mail
        /// </summary>
        public short? SendMailStatus { get; set; }

        public Guid TenantId { get; set; }

        /// <summary>
        /// Ngày hóa đơn
        /// </summary>
        public DateTime InvoiceDate { get; set; }
    }
}
