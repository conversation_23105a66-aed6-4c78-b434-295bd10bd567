using Core.Domain.Entities;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;

namespace VnisCore.Core.MongoDB.Entities.TaxReport01
{
    [Table("TaxReport01TvanResponseLog")]
    public class MongoTaxReport01TvanResponseLogEntity : Entity<Guid>
    {
        public string Xml { get; set; }

        public string XmlFilePath { get; set; }

        /// <summary>
        /// 0: chưa đồng bộ
        /// 1: đang đồng bộ
        /// 2: đã đồng bộ thành công
        /// -1: đồng bộ lỗi
        /// </summary>
        [DefaultValue(0)]
        public short SyncedToMinioStatus { get; set; }

        /// <summary>
        /// 0: chưa xử lý
        /// 1: đang xủ lý
        /// 2: đã xử lý thành công
        /// -1: xử lý lỗi
        /// </summary>
        [DefaultValue(0)]
        public short HandleXmlStatus { get; set; }

        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        public DateTime CreationTime { get; set; }

        public int Partition { get; set; }
    }
}
