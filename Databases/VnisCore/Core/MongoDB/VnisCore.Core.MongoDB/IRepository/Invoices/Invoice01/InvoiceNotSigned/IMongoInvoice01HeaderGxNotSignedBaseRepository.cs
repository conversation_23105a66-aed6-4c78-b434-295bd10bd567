using Core.Domain.Repositories;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using VnisCore.Core.MongoDB.Base;
using VnisCore.Core.MongoDB.Entities.Invoices.Invoice01.InvoiceNotSigned;

namespace VnisCore.Core.MongoDB.IRepository.Invoices.Invoice01.InvoiceNotSigned
{
    public interface IMongoInvoice01HeaderGxNotSignedBaseRepository<THeaderNotSigned, TDetail> : IBasicRepository<THeaderNotSigned, Guid>
        where THeaderNotSigned : MongoBaseInvoice01NotSigned<TDetail>
        where TDetail : MongoBaseInvoice01Detail
    {
        /// <summary>
        /// Lấy dữ liệu hóa đơn chưa ký từ mongoDB bằng InvoiceHeaderId của hđ trên Core
        /// </summary>
        /// <param name="id">id của hđ trên Core nếu đã sinh số. Chưa sinh số thì trên Core chưa được đồng bộ về</param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        Task<THeaderNotSigned> GetByInvoiceId(long? id, CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách hóa đơn trên MongoDB theo InvoiceHeaderId
        /// </summary>
        /// <param name="lstId"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        Task<List<THeaderNotSigned>> GetByInvoiceIds(List<long> lstId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy dữ liệu hóa đơn chưa ký từ mongoDB bằng Id của Document
        /// </summary>
        /// <param name="id">id của document trên mongoDB</param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        Task<THeaderNotSigned> GetById(Guid id, CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách hóa đơn chưa đồng bộ 
        /// </summary>
        /// <param name="numberTake">số lượng records lấy ra</param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        Task<List<THeaderNotSigned>> GetListNotYetSyncedTakeAsync(short numberTake, CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách hóa đơn chưa đồng bộ về Core theo từng nhóm SlotAdapter
        /// </summary>
        /// <param name="numberTake">số lượng records lấy ra</param>
        /// <param name="indexSlotAdapter">nhóm của từng Adapter</param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        Task<List<THeaderNotSigned>> GetListNotYetSyncedTakeGroupSlotAsync(short numberTake, int indexSlotAdapter, CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách hóa đơn chưa sinh số
        /// </summary>
        /// <param name="numberTake">số lượng records lấy ra</param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        Task<List<THeaderNotSigned>> GetListNotYetGeneratedTakeAsync(short numberTake, CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách hóa đơn đã sinh số, đã đồng bộ về Core chưa đồng bộ về DBTG
        /// </summary>
        /// <param name="numberTake">số lượng records lấy ra</param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        Task<List<THeaderNotSigned>> GetListNotYetSyncToIntegrateDBTakeAsync(short numberTake, CancellationToken cancellationToken = default);

        Task<List<THeaderNotSigned>> GetListNotYetSyncedAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Lấy danh sách khách hàng Portal chưa đồng bộ về Core
        /// </summary>
        /// <param name="numberTake">số lượng records lấy ra</param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        Task<List<THeaderNotSigned>> GetListNotYetSyncAccountPortalTakeAsync(short numberTake, CancellationToken cancellationToken = default);

        Task<long> CountNotYetSyncedAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Kiểm tra tồn tại hóa đơn chưa đồng bộ về Core
        /// </summary>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        Task<bool> AnyNotYetSyncedAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Kiểm tra tồn tại hóa đơn chưa đồng bộ về Core theo nhóm SlotAdapter
        /// </summary>
        /// <param name="indexSlotAdapter"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        Task<bool> AnyNotYetSyncedGroupSlotAsync(int indexSlotAdapter, CancellationToken cancellationToken = default);

        /// <summary>
        /// Kiểm tra có tồn tại hóa đơn chưa sinh số
        /// </summary>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        Task<bool> AnyNotYetGeneratedAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Kiểm tra tồn tại hóa đơn đã sinh số, đã đồng bộ về Core nhưng chưa đồng bộ về DBTG
        /// </summary>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        Task<bool> AnyNotYetSyncToIntegrateDBAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Kiểm tra tồn tại khách hàng Portal chưa đồng bộ về Core
        /// </summary>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        Task<bool> AnyNotYetSyncAccountPortalTakeAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Cập nhật danh sách hóa đơn sinh số lỗi
        /// </summary>
        /// <param name="list"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        Task UpdateManyInvoiceGenerateNumberErrorAsync(List<THeaderNotSigned> list, CancellationToken cancellationToken = default);

        /// <summary>
        /// Cập nhật hóa đơn sinh số thành công
        /// </summary>
        /// <param name="list"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        Task UpdateManyInvoiceGenerateNumberSuccessAsync(List<THeaderNotSigned> list, CancellationToken cancellationToken = default);

        Task UpdateManyInvoiceIsSyncedCoreSuccessAsync(List<THeaderNotSigned> list, CancellationToken cancellationToken = default);

        Task UpdateManyInvoiceIsSyncedCoreErrorAsync(List<THeaderNotSigned> list, CancellationToken cancellationToken = default);
       
        Task UpdateManyIsSyncedInvoiceSignedMongoDocumentSuccessAsync(List<THeaderNotSigned> list, CancellationToken cancellationToken = default);

        Task UpdateManyIsSyncedInvoiceSignedMongoDocumentErrorAsync(List<THeaderNotSigned> list, CancellationToken cancellationToken = default);

        Task UpdateManyIsSyncedIntegrateDBSuccessAsync(List<THeaderNotSigned> list, CancellationToken cancellationToken = default);

        Task UpdateManyIsSyncedIntegrateDBErrorAsync(List<THeaderNotSigned> list, CancellationToken cancellationToken = default);
   
        Task UpdateInvoiceGenerateNumberSuccessAsync(THeaderNotSigned data, CancellationToken cancellationToken = default);
    }
}
