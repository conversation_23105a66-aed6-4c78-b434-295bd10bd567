using Core;
using Microsoft.EntityFrameworkCore;
using VnisCore.Core.Oracle.Domain.Entities;
using VnisCore.Core.Oracle.Domain.Entities.AdministrativeDivision;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.InputInvoice.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.InputInvoice.InputInvoice01;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice04;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Core.Oracle.Domain.Entities.InvoiceTemplate;
using VnisCore.Core.Oracle.Domain.Entities.Licenses;
using VnisCore.Core.Oracle.Domain.Entities.Media;
using VnisCore.Core.Oracle.Domain.Entities.PurchaseInvoice;
using VnisCore.Core.Oracle.Domain.Entities.RegisterInvoice;
using VnisCore.Core.Oracle.Domain.Entities.Reports;
using VnisCore.Core.Oracle.Domain.Entities.SettingG4;
using VnisCore.Core.Oracle.Domain.Entities.StatisticSummary;
using VnisCore.Core.Oracle.Domain.Entities.Tvan;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Invoice01;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Invoice03;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Invoice04;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.ReportInvoice;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Ticket;

namespace VnisCore.Core.Oracle.EntityFrameworkCore.EntityFrameworkCore
{
    public static class VnisCoreOracleDbContextModelCreatingExtensions
    {
        public static void ConfigureVnisCoreOracle(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.ConfigureVnisCoreOracleCountryEntity();
            builder.ConfigureVnisCoreOracleAdministrativeDivisionEntity();
            builder.ConfigureVnisCoreOracleAccountTokenTemplateEntity();
            builder.ConfigureVnisCoreOracleCurrencyEntity();
            builder.ConfigureVnisCoreOracleCustomerEntity();
            builder.ConfigureVnisCoreOracleCustomerTvanEntity();
            builder.ConfigureVnisCoreOracleEmailEntity();
            builder.ConfigureVnisCoreOracleEmailInvoiceEntity();
            builder.ConfigureVnisCoreOracleEmailTemplateEntity();
            builder.ConfigureVnisCoreOracleGroupCustomerEntity();
            builder.ConfigureVnisCoreOracleProductEntity();
            builder.ConfigureVnisCoreOracleProductTypeEntity();
            builder.ConfigureVnisCoreOracleTaxDepartmentEntity();
            builder.ConfigureVnisCoreOracleTaxEntity();
            builder.ConfigureVnisCoreOracleUnitEntity();
            builder.ConfigureVnisCoreOracleUsbTokenEntity();
            builder.ConfigureVnisCoreOracleUserReadTemplateEntity();
            builder.ConfigureSpecificProductFieldEntity();

            //invoice01
            builder.ConfigureVnisCoreOracleInvoice01DetailEntity();
            builder.ConfigureVnisCoreOracleInvoice01TransactionDetailEntity();
            builder.ConfigureVnisCoreOracleInvoice01DetailExtraEntity();
            builder.ConfigureVnisCoreOracleInvoice01DetailFieldEntity();
            builder.ConfigureVnisCoreOracleInvoice01HeaderEntity();
            builder.ConfigureVnisCoreOracleInvoice01HeaderExtraEntity();
            builder.ConfigureVnisCoreOracleInvoice01TaxBreakdownEntity();
            builder.ConfigureVnisCoreOracleInvoice01HeaderFieldEntity();
            builder.ConfigureVnisCoreOracleInvoice01XmlEntity();
            builder.ConfigureVnisCoreOracleInvoice01DocumentEntity();
            builder.ConfigureVnisCoreOracleInvoice01DocumentInfoEntity();
            builder.ConfigureVnisCoreOracleInvoice01UnOfficialEntity();
            builder.ConfigureVnisCoreOracleInvoice01ReferenceEntity();
            builder.ConfigureVnisCoreOracleInvoice01ReferenceOldDecreeEntity();
            builder.ConfigureVnisCoreOracleInvoice01ExportJobEntity();
            builder.ConfigureVnisCoreOracleInvoice01LogEntity();
            builder.ConfigureVnisCoreOracleInvoice01ErrorEntity();
            builder.ConfigureVnisCoreOracleInvoice01DetailExtraPropertyEntity();
            builder.ConfigureVnisCoreOracleInvoice01HeaderExtraPropertyEntity();
            
            builder.ConfigureVnisCoreOracleInvoice01ErrorXmlEntity();
            builder.ConfigureVnisCoreOracleInvoice03ErrorXmlEntity();
            builder.ConfigureVnisCoreOracleInvoice04ErrorXmlEntity();
            builder.ConfigureVnisCoreOracleTicketErrorXmlEntity();


            builder.ConfigureInvoice01SpecificProductExtraEntity();

            //invoice02
            builder.ConfigureVnisCoreOracleInvoice02DetailEntity();
            builder.ConfigureVnisCoreOracleInvoice02DetailExtraEntity();
            builder.ConfigureVnisCoreOracleInvoice02DetailFieldEntity();
            builder.ConfigureVnisCoreOracleInvoice02DocumentEntity();
            builder.ConfigureVnisCoreOracleInvoice02DocumentInfoEntity();
            builder.ConfigureVnisCoreOracleInvoice02ErpIdEntity();
            builder.ConfigureVnisCoreOracleInvoice02ErrorEntity();
            builder.ConfigureVnisCoreOracleInvoice02ErrorXmlEntity();
            builder.ConfigureVnisCoreOracleInvoice02ExportJobEntity();
            builder.ConfigureVnisCoreOracleInvoice02HeaderEntity();
            builder.ConfigureVnisCoreOracleInvoice02HeaderExtraEntity();
            builder.ConfigureVnisCoreOracleInvoice02HeaderFieldEntity();
            builder.ConfigureVnisCoreOracleInvoice02LogEntity();
            builder.ConfigureVnisCoreOracleInvoice02ReferenceEntity();
            builder.ConfigureVnisCoreOracleInvoice02ReferenceOldDecreeEntity();
            builder.ConfigureVnisCoreOracleInvoice02UnOfficialEntity();
            builder.ConfigureVnisCoreOracleInvoice02XmlEntity();

            //tax report
            builder.ConfigureVnisCoreOracleTaxReport01HeaderEntity();
            builder.ConfigureVnisCoreOracleTaxReport01DetailEntity();
            builder.ConfigureVnisCoreOracleTaxReport01DetailMappingEntity();
            builder.ConfigureVnisCoreOracleTaxReport01TvanInfoMappingEntity();
            //builder.ConfigureVnisCoreOracleResultTaxReport01HeaderEntity();


            builder.ConfigureVnisCoreOracleTaxReport03HeaderEntity();
            builder.ConfigureVnisCoreOracleTaxReport03DetailEntity();
            builder.ConfigureVnisCoreOracleTaxReport03DetailDataEntity();
            builder.ConfigureVnisCoreOracleReportXmlEntity();
            builder.ConfigureVnisCoreOracleTaxReport01LogEntity();
            builder.ConfigureVnisCoreOracleDataFluctuationsReportEntity();
            builder.ConfigureVnisCoreOracleDataFluctuationEmailEntity();
            builder.ConfigureVnisCoreOracleReconcileReportEntity();
            builder.ConfigureVnisCoreOracleReportReconcileInvoiceMinioDetailEntity();


            //statistic summary
            builder.ConfigureVnisCoreOracleStatisticSummaryEntity();

            //setting G4
            builder.ConfigureVnisCoreOracleSettingG4HeaderEntity();
            builder.ConfigureVnisCoreOracleSettingG4DetailEntity();
            builder.ConfigureVnisCoreOracleSettingG4LogEntity();
            //builder.ConfigureVnisCoreOracleInvoice01SignedEntity();


            //invoice03
            builder.ConfigureVnisCoreOracleInvoice03DetailEntity();
            builder.ConfigureVnisCoreOracleInvoice03DetailFieldEntity();
            builder.ConfigureVnisCoreOracleInvoice03HeaderEntity();
            builder.ConfigureVnisCoreOracleInvoice03HeaderFieldEntity();
            builder.ConfigureVnisCoreOracleInvoice03XmlEntity();
            builder.ConfigureVnisCoreOracleInvoice03DocumentEntity();
            builder.ConfigureVnisCoreOracleInvoice03DocumentInfoEntity();
            builder.ConfigureVnisCoreOracleInvoice03UnOfficialEntity();
            builder.ConfigureVnisCoreOracleInvoice03ReferenceEntity();
            builder.ConfigureVnisCoreOracleInvoice03ReferenceOldDecreeEntity();
            builder.ConfigureVnisCoreOracleInvoice03ExportJobEntity();
            builder.ConfigureVnisCoreOracleInvoice03LogEntity();
            builder.ConfigureVnisCoreOracleInvoice03ErrorEntity();

            //TODO: bỏ
            //builder.ConfigureVnisCoreOracleInvoice03SignedEntity();

            //invoice04
            builder.ConfigureVnisCoreOracleInvoice04DetailEntity();
            builder.ConfigureVnisCoreOracleInvoice04DetailFieldEntity();
            builder.ConfigureVnisCoreOracleInvoice04HeaderEntity();
            builder.ConfigureVnisCoreOracleInvoice04HeaderFieldEntity();
            builder.ConfigureVnisCoreOracleInvoice04XmlEntity();
            builder.ConfigureVnisCoreOracleInvoice04DocumentEntity();
            builder.ConfigureVnisCoreOracleInvoice04DocumentInfoEntity();
            builder.ConfigureVnisCoreOracleInvoice04UnOfficialEntity();
            builder.ConfigureVnisCoreOracleInvoice04ReferenceEntity();
            builder.ConfigureVnisCoreOracleInvoice04ReferenceOldDecreeEntity();
            builder.ConfigureVnisCoreOracleInvoice04ExportJobEntity();
            builder.ConfigureVnisCoreOracleInvoice04LogEntity();
            builder.ConfigureVnisCoreOracleInvoice04ErrorEntity();

            //invoice05
            builder.ConfigureVnisCoreOracleTicketDetailEntity();
            builder.ConfigureVnisCoreOracleTicketDetailFieldEntity();
            builder.ConfigureVnisCoreOracleTicketHeaderEntity();
            builder.ConfigureVnisCoreOracleTicketTaxBreakdownEntity();
            builder.ConfigureVnisCoreOracleTicketHeaderFieldEntity();
            builder.ConfigureVnisCoreOracleTicketXmlEntity();
            builder.ConfigureVnisCoreOracleTicketDocumentEntity();
            builder.ConfigureVnisCoreOracleTicketDocumentInfoEntity();
            builder.ConfigureVnisCoreOracleTicketUnOfficialEntity();
            builder.ConfigureVnisCoreOracleTicketReferenceEntity();
            builder.ConfigureVnisCoreOracleTicketReferenceOldDecreeEntity();
            builder.ConfigureVnisCoreOracleTicketExportJobEntity();
            builder.ConfigureVnisCoreOracleTicketLogEntity();
            builder.ConfigureVnisCoreOracleTicketErrorEntity();

            builder.ConfigureVnisCoreOracleInvoiceTemplateDesignEntity();
            builder.ConfigureVnisCoreOracleDocumentTemplateEntity();
            builder.ConfigureVnisCoreOracleInvoiceTemplateEntity();
            builder.ConfigureVnisCoreOracleDesignInvoiceTemplateEntityEntity();
            builder.ConfigureVnisCoreOracleMonitorInvoiceTemplateEntity();
            builder.ConfigureVnisCoreOracleLicenseEntity();
            builder.ConfigureVnisCoreOracleMediaFileEntity();
            builder.ConfigureVnisCoreOracleRegisterInvoiceEntity();
            builder.ConfigureVnisCoreOracleRegisterTemplateInvoiceEntity();

            builder.ConfigureVnisCoreOracleNewRegisterInvoiceEntity();
            builder.ConfigureVnisCoreOracleNewRegisterDetailInvoiceEntity();
            builder.ConfigureVnisCoreOracleNewRegistrationDetailExtensionEntity();

            builder.ConfigureVnisCoreOracleBc26DetailEntity();
            builder.ConfigureVnisCoreOracleBc26Entity();
            builder.ConfigureVnisCoreOracleRegistrationInvoiceXmlEntity();

            //invoice input
            builder.ConfigureVnisCoreOracleInputCustomerEntity();
            builder.ConfigureVnisCoreOracleInputCurrencyEntity();
            builder.ConfigureVnisCoreOracleInputProductEntity();
            builder.ConfigureVnisCoreOracleInputUnitEntity();
            builder.ConfigureVnisCoreOracleInputInvoice01HeaderEntity();
            builder.ConfigureVnisCoreOracleInputInvoice01DetailEntity();
            builder.ConfigureVnisCoreOracleInputInvoice01HeaderExtraEntity();
            builder.ConfigureVnisCoreOracleInputInvoice01DetailExtraEntity();
            builder.ConfigureVnisCoreOracleInputInvoice01HeaderFieldEntity();
            builder.ConfigureVnisCoreOracleInputInvoice01DetailFieldEntity();
            builder.ConfigureVnisCoreOracleInputInvoice01RequestDataEntity();
            builder.ConfigureVnisCoreOracleInputInvoice01TaxBreakdownEntity();
            builder.ConfigureVnisCoreOracleInputInvoice01XmlEntity();
            builder.ConfigureVnisCoreOracleInputInvoice01LogEntity();

            // tvan builder.ConfigureVnisCoreOracleTvanInvoice01HasCodeXmlEntity();
            builder.ConfigureVnisCoreOracleInvoice01HasCodeTvanInfoEntity();

            builder.ConfigureVnisCoreOracleTvanInvoice01ErrorXmlEntity();
            builder.ConfigureVnisCoreOracleTvanInfoInvoice01ErrorEntity();

            builder.ConfigureVnisCoreOracleTvanInvoice01WithoutCodeXmlEntity();
            builder.ConfigureVnisCoreOracleInvoice01WithoutCodeTvanInfoEntity();
            builder.ConfigureVnisCoreOracleTvanInvoice01WithoutCodeMonitorEntity();

            builder.ConfigureVnisCoreOracleTvanRegistrationXmlEntity();
            builder.ConfigureVnisCoreOracleRegistrationTvanInfoEntity();

            builder.ConfigureVnisCoreOracleTvanReportXmlEntity();
            builder.ConfigureVnisCoreOracleTaxReport01TvanInfoEntity();
            builder.ConfigureVnisCoreOracleTaxReportTvanMonitorEntity();
            builder.ConfigureVnisCoreOracleTaxReport03TvanInfoEntity();

            builder.ConfigureVnisCoreOracleTvanCheckInvoice01XmlEntity();
            builder.ConfigureVnisCoreOracleTvanInfoCheckInvoice01Entity();

            // Tvan Invoice 02
            builder.ConfigureVnisCoreOracleTvanInvoice02HasCodeXmlEntity();
            builder.ConfigureVnisCoreOracleTvanInvoice02WithoutCodeXmlEntity();
            builder.ConfigureVnisCoreOracleTvanInvoice02ErrorXmlEntity();

            builder.ConfigureVnisCoreOracleTvanCheckInvoice02XmlEntity();
            builder.ConfigureVnisCoreOracleTvanInfoCheckInvoice02Entity();
            builder.ConfigureVnisCoreOracleTvanInfoInvoice02ErrorEntity();

            builder.ConfigureVnisCoreOracleTvanInfoInvoice02HasCodeEntity();
            builder.ConfigureVnisCoreOracleTvanInfoInvoice02WithoutCodeEntity();
            builder.ConfigureVnisCoreOracleTvanInvoice02WithoutCodeMonitorEntity();
            
            // Tvan Invoice 03
            builder.ConfigureVnisCoreOracleTvanInvoice03HasCodeXmlEntity();
            builder.ConfigureVnisCoreOracleTvanInvoice03WithoutCodeXmlEntity();
            builder.ConfigureVnisCoreOracleTvanInvoice03ErrorXmlEntity();

            builder.ConfigureVnisCoreOracleTvanCheckInvoice03XmlEntity();
            builder.ConfigureVnisCoreOracleTvanInfoCheckInvoice03Entity();
            builder.ConfigureVnisCoreOracleTvanInfoInvoice03ErrorEntity();

            builder.ConfigureVnisCoreOracleTvanInfoInvoice03HasCodeEntity();
            builder.ConfigureVnisCoreOracleTvanInfoInvoice03WithoutCodeEntity();
            builder.ConfigureVnisCoreOracleTvanInvoice03WithoutCodeMonitorEntity();
             
            // Tvan Invoice 04
            builder.ConfigureVnisCoreOracleTvanInvoice04HasCodeXmlEntity();
            builder.ConfigureVnisCoreOracleTvanInvoice04WithoutCodeXmlEntity();
            builder.ConfigureVnisCoreOracleTvanInvoice04ErrorXmlEntity();

            builder.ConfigureVnisCoreOracleTvanCheckInvoice04XmlEntity();
            builder.ConfigureVnisCoreOracleTvanInfoCheckInvoice04Entity();
            builder.ConfigureVnisCoreOracleTvanInfoInvoice04ErrorEntity();

            builder.ConfigureVnisCoreOracleTvanInfoInvoice04HasCodeEntity();
            builder.ConfigureVnisCoreOracleTvanInfoInvoice04WithoutCodeEntity();
            builder.ConfigureVnisCoreOracleTvanInvoice04WithoutCodeMonitorEntity();

            // Tvan Invoice 05
            builder.ConfigureVnisCoreOracleTvanTicketHasCodeXmlEntity();
            builder.ConfigureVnisCoreOracleTicketHasCodeTvanInfoEntity();
            builder.ConfigureVnisCoreOracleTvanTicketErrorXmlEntity();

            builder.ConfigureVnisCoreOracleTvanInfoTicketErrorEntity();
            builder.ConfigureVnisCoreOracleTvanTicketWithoutCodeXmlEntity();
            builder.ConfigureVnisCoreOracleTicketWithoutCodeTvanInfoEntity();

            builder.ConfigureVnisCoreOracleTvanTicketWithoutCodeMonitorEntity();
            builder.ConfigureVnisCoreOracleTvanCheckTicketXmlEntity();
            builder.ConfigureVnisCoreOracleTvanInfoCheckTicketEntity();

            // customer VCB
            builder.ConfigureVnisCoreOracleCustomerVCBEntity();

            // Purchase Invoice
            builder.ConfigureVnisCoreOraclePurchaseInvoiceHeaderEntity();
            builder.ConfigureVnisCoreOraclePurchaseInvoiceDetailEntity();
            builder.ConfigureVnisCoreOraclePurchaseInvoiceTaxBreakdownEntity();
            builder.ConfigureVnisCoreOraclePurchaseInvoiceFeeEntity();
            builder.ConfigureVnisCoreOraclePurchaseInvoiceLogEntity();
            builder.ConfigureVnisCoreOraclePurchaseInvoiceMediaFileEntity();
            builder.ConfigureVnisCoreOraclePurchaseInvoiceTemplateEntity();
            builder.ConfigureVnisCoreOraclePurchaseInvoiceMonitorNumberEntity();
            builder.ConfigureVnisCoreOraclePurchaseInvoiceErrorHeaderEntity();
            builder.ConfigureVnisCoreOraclePurchaseInvoiceErrorMediaFileEntity();
        }
    }
}