using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using VnisCore.Core.Oracle.Domain.BaseEntities;
using Core;
using Core.EntityFrameworkCore.Modeling;
using System;

namespace VnisCore.Core.Oracle.Domain.Entities.Media
{
    [Table("DocumentTemplate")]
    public class DocumentTemplateEntity : TenantFullAuditedEntity<long>
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public new long Id { get; set; }

        /// <summary>
        /// loại biên bản
        /// </summary>
        public short Type { get; set; }

        /// <summary>
        /// tên file vật lý ở minio, đánh index để tên file không bị trùng
        /// </summary>
        public string PhysicalFileName { get; set; }

        /// <summary>
        /// tên file để download
        /// </summary>
        public string FileName { get; set; }
        public new DateTime CreationTime { get; set; }

    }

    public static class VnisCoreOracleDbContextModeDocumentTemplateCreatingExtensions
    {
        public static void ConfigureVnisCoreOracleDocumentTemplateEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<DocumentTemplateEntity>(b =>
            {
                b.ConfigureByConvention(); //auto configure for the base class props
                b.Property(x => x.PhysicalFileName).HasMaxLength(500).IsUnicode(false);
                b.Property(x => x.FileName).HasMaxLength(500).IsUnicode(true);
            });

        }
    }
}
