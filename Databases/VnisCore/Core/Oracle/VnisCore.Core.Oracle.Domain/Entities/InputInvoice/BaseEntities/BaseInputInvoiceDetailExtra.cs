using Core.Domain.Entities;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using VnisCore.Core.Oracle.Domain.BaseEntities;

namespace VnisCore.Core.Oracle.Domain.Entities.InputInvoice.BaseEntities
{
    public class BaseInputInvoiceDetailExtra : Entity<long>, ITenantFullAuditedEntity
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public new long Id { get; set; }

        public string FieldValue { get; set; }
        public long InvoiceDetailFieldId { get; set; }
        public long InvoiceDetailId { get; set; }
        public long Partition { get; set; }

        public Guid TenantId { get; set; }
    }
}
