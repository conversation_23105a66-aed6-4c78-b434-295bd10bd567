using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using VnisCore.Core.Oracle.Domain.BaseEntities;
using Core;
using Core.EntityFrameworkCore.Modeling;
using Core.Data;

namespace VnisCore.Core.Oracle.Domain.Entities.Catalog
{
    [Table("Customer")]
    public class CustomerEntity : TenantFullAuditedEntity<long>, IHasExtraProperties
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public new long Id { get; set; }
        public string CustomerCode { get; set; }// Mã khách hàng
        public string ErpId { get; set; }
        public Guid UserId { get; set; }
        public string Address { get; set; }
        public string BankAccount { get; set; }
        public string BankName { get; set; }
        public string City { get; set; } // Thành phố (HN, HP...)
        public string Country { get; set; } //Quốc gia (VN, TQ...)
        public string District { get; set; } //Quận (BĐ, HBT...)
        public string Email { get; set; }
        public string Fax { get; set; }
        public string FullName { get; set; }
        public string LegalName { get; set; }
        public string Phone { get; set; }
        public string TaxCode { get; set; } // mã số thuế

        /// <summary>
        /// trạng thái hoạt động của mst trên tvan
        /// </summary>
        public string StatusTvan { get; set; } 
        
        /// <summary>
        /// Trạng thái phê duyệt email khách hàng
        /// </summary>
        public short? ApproveStatus { get; set; } 

        public ExtraPropertyDictionary ExtraProperties { get; set; }

        //FK
        public long? GroupCustomerId { get; set; }

        public virtual GroupCustomerEntity GroupCustomer { get; set; }
    }

    public static class VnisCoreOracleDbContextModeCustomerCreatingExtensions
    {
        public static void ConfigureVnisCoreOracleCustomerEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<CustomerEntity>(b =>
            {
                b.ConfigureByConvention(); //auto configure for the base class props

                b.Property(x => x.CustomerCode).HasMaxLength(50);
                b.Property(x => x.FullName).IsRequired().HasMaxLength(400);
                b.Property(x => x.LegalName).HasMaxLength(400);
                b.Property(x => x.TaxCode).HasMaxLength(14);
                b.Property(x => x.Address).IsRequired().HasMaxLength(400);
                b.Property(x => x.District).HasMaxLength(500);
                b.Property(x => x.City).HasMaxLength(500);
                b.Property(x => x.Country).HasMaxLength(500);
                b.Property(x => x.Phone).HasMaxLength(20);
                b.Property(x => x.Fax).HasMaxLength(20);
                b.Property(x => x.Email).HasMaxLength(500);
                b.Property(x => x.BankName).HasMaxLength(400);
                b.Property(x => x.BankAccount).HasMaxLength(30);
                b.Property(x => x.StatusTvan).HasMaxLength(5);
                b.Property(x => x.ApproveStatus).HasColumnName(nameof(CustomerEntity.ApproveStatus));
                b.Property(x => x.ExtraProperties).IsUnicode(false).HasColumnType("CLOB");
                //b.Property(x => x.NewExtraProperties).IsUnicode(false).HasColumnType("CLOB");
                b.HasOne(x => x.GroupCustomer).WithMany(x => x.Customers).HasForeignKey(x => x.GroupCustomerId).HasConstraintName("FK_GroupCustomerId");

                //TODO: thêm index unique CustomerCode-tenantId- DeleteId

            });

        }
    }
}
