using Core;
using Core.EntityFrameworkCore.Modeling;

using Microsoft.EntityFrameworkCore;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using VnisCore.Core.Oracle.Domain.BaseEntities;

namespace VnisCore.Core.Oracle.Domain.Entities.Tvan.Invoice04
{
    [Table("TvanInvoice04ErrorXml")]
    public class TvanInvoice04ErrorXmlEntity : BaseMediaFile
    {
    }

    public static class VnisCoreOracleDbContextModelTvanReport04ErrorXmlCreatingExtensions
    {
        public static void ConfigureVnisCoreOracleTvanInvoice04ErrorXmlEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<TvanInvoice04ErrorXmlEntity>(b =>
            {
                //TODO: thêm index
                b.ConfigureByConvention(); //auto configure for the base class props
                b.Property(x => x.PhysicalFileName).HasMaxLength(500).IsUnicode(false);
                b.Property(x => x.FileName).HasMaxLength(500).IsUnicode(true);
            });
        }
    }
}
