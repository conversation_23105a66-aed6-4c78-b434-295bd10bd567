using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.BaseEntities;
using Core;
using Microsoft.EntityFrameworkCore;
using Core.EntityFrameworkCore.Modeling;
using Core.Domain.Entities;

namespace VnisCore.Core.Oracle.Domain.Entities.Reports
{
    [Table("ReconcileReports")]
    public class ReconcileReportEntity : Entity<long>
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public new long Id { get; set; }
        public string ErpId { get; set; }
        public string BuyerTaxCode { get; set; }
        public string BuyerFullName { get; set; }
        public string BuyerAddressLine { get; set; }
        public decimal ExchangeRate { get; set; }
        public string ToCurrency { get; set; }
        public DateTime? InvoiceDate { get; set; }
        public string InvoiceNo { get; set; }
        public string PaymentMethod { get; set; }
        public string Products { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal TotalVatAmount { get; set; }
        public decimal TotalPaymentAmount { get; set; }
        public string CreatorErp { get; set; }
        public string TellSeq { get; set; }
        public string BuyerBankAccount { get; set; }
        public DateTime CreationTime { get; set; }
        public string Message { get; set; }
        public int TemplateCode { get; set; }
        public string TenantCode { get; set; }
        //public string SerialNo { get; set; }
    }
    public static class VnisCoreOracleDbContextModeReconcileReportCreatingExtensions
    {
        public static void ConfigureVnisCoreOracleReconcileReportEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<ReconcileReportEntity>(b =>
            {
                b.ConfigureByConvention(); //auto configure for the base class props

                b.Property(x => x.ErpId).HasMaxLength(50).IsRequired();
                b.Property(x => x.BuyerTaxCode).HasMaxLength(14);
                b.Property(x => x.BuyerFullName).HasMaxLength(400).IsRequired();
                b.Property(x => x.BuyerAddressLine).HasMaxLength(400).IsRequired();
                b.Property(x => x.ExchangeRate).HasPrecision(7, 2).IsRequired();
                b.Property(x => x.ToCurrency).HasMaxLength(3).IsRequired();
                b.Property(x => x.InvoiceNo).HasMaxLength(8);
                b.Property(x => x.PaymentMethod).HasMaxLength(50);
                b.Property(x => x.Products).HasMaxLength(2000);
                b.Property(x => x.TotalAmount).HasPrecision(19, 4).IsRequired();
                b.Property(x => x.TotalVatAmount).HasPrecision(19, 4).IsRequired();
                b.Property(x => x.TotalPaymentAmount).HasPrecision(19, 4).IsRequired();
                b.Property(x => x.CreatorErp).HasMaxLength(250);
                b.Property(x => x.TellSeq).HasMaxLength(50);
                b.Property(x => x.BuyerBankAccount).HasMaxLength(30);
                b.Property(x => x.Message).HasMaxLength(2000);
                b.Property(x => x.TemplateCode).HasPrecision(5);
                b.Property(x => x.TenantCode).HasMaxLength(500);
            });

        }
    }
}
