using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;
using VnisCore.Core.Oracle.Domain.BaseEntities;
using Core;
using Core.EntityFrameworkCore.Modeling;

namespace VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket
{
    [Table("TicketReferenceOldDecree")]
    public class TicketReferenceOldDecreeEntity : BaseInvoiceReferenceOldDecree
    {

    }

    public static class VnisCoreOracleDbContextModeTicketReferenceOldDecreeCreatingExtensions
    {
        public static void ConfigureVnisCoreOracleTicketReferenceOldDecreeEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<TicketReferenceOldDecreeEntity>(b =>
            {
                b.ConfigureByConvention(); //auto configure for the base class props

                b.Property(x => x.TemplateNoReference).IsRequired().HasMaxLength(11);
                b.Property(x => x.SerialNoReference).IsRequired().HasMaxLength(8);
                b.Property(x => x.InvoiceNoReference).IsRequired().HasMaxLength(8);
                b.Property(x => x.InvoiceDateReference).IsRequired();
                b.Property(x => x.Note).HasMaxLength(255);
            });

        }
    }
}
