using Core;
using Core.EntityFrameworkCore.Modeling;

using Microsoft.EntityFrameworkCore;

using System.ComponentModel.DataAnnotations.Schema;

using VnisCore.Core.Oracle.Domain.BaseEntities;

namespace VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket
{
    /// <summary>
    /// lưu thông tin sai sót 04-TBSS sau khi ký
    /// </summary>
    [Table("TicketErrorXml")]
    public class TicketErrorXmlEntity : BaseInvoiceErrorMediaFile
    {
    }

    public static class VnisCoreOracleDbContextModelTicketErrorXmlCreatingExtensions
    {
        public static void ConfigureVnisCoreOracleTicketErrorXmlEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<TicketErrorXmlEntity>(b =>
            {
                //TODO: thêm index
                b.ConfigureByConvention(); //auto configure for the base class props
                b.Property(x => x.GroupCode).IsRequired();
                b.Property(x => x.PhysicalFileName).HasMaxLength(500).IsUnicode(false);
                b.Property(x => x.FileName).HasMaxLength(500).IsUnicode(true);
            });
        }
    }
}
