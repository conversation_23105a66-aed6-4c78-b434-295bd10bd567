using Core;
using Core.Data;
using Core.EntityFrameworkCore.Modeling;

using Microsoft.EntityFrameworkCore;
using System;
using System.ComponentModel.DataAnnotations.Schema;

using VnisCore.Core.Oracle.Domain.BaseEntities;

namespace VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02
{
    [Table("Invoice02Detail")]
    public class Invoice02DetailEntity : BaseInvoiceDetail<Invoice02DetailExtraEntity>, IHasExtraProperties
    {

        /// <summary>
        /// Số lượng
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// Nếu là mặt hàng khuyến mãi thì set TRUE
        /// </summary>
        public bool IsPromotion { get; set; }

        /// <summary>
        /// Tiền chiết khấu
        /// </summary>
        public decimal DiscountAmount { get; set; }


        /// <summary>
        /// Phần trăm chiết khấu
        /// </summary>
        public decimal DiscountPercent { get; set; }

        /// <summary>
        /// có ấn số lương không
        /// true: ẩn, false/null: hiện
        /// </summary>
        public bool HideQuantity { get; set; }

        /// <summary>
        /// có ấn đơn vị tính không
        /// true: ẩn, false/null: hiện
        /// </summary>
        public bool HideUnit { get; set; }

        /// <summary>
        /// có ấn đơn giá không
        /// true: ẩn, false/null: hiện
        /// </summary>
        public bool HideUnitPrice { get; set; }

        /// <summary>
        /// Ngày hóa đơn NSD nhập vào. Chỉ lưu ngày, tháng, năm. Không lưu thời gian. Lưu dạng UTC
        /// </summary>
        // public DateTime InvoiceDate { get; set; }

        public ExtraPropertyDictionary ExtraProperties { get; set; }
        //FK
        public virtual Invoice02HeaderEntity InvoiceHeader { get; set; }
    }

    public static class VnisCoreOracleDbContextModeInvoice02DetailCreatingExtensions
    {
        public static void ConfigureVnisCoreOracleInvoice02DetailEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<Invoice02DetailEntity>(b =>
            {
                //TODO: thêm index
                b.ConfigureByConvention(); //auto configure for the base class props

                b.Property(x => x.ProductCode).IsRequired().HasMaxLength(50);
                b.Property(x => x.ProductName).IsRequired().HasMaxLength(500);
                b.Property(x => x.UnitName).HasMaxLength(50);
                b.Property(x => x.Note).HasMaxLength(500);
                b.Property(x => x.UnitPrice).HasColumnType("decimal(27,6)");
                b.Property(x => x.Amount).HasColumnType("decimal(27,6)");
                b.Property(x => x.PaymentAmount).HasColumnType("decimal(27,6)");
                b.Property(x => x.DiscountAmount).HasColumnType("decimal(27,6)");
                b.Property(x => x.DiscountPercent).HasColumnType("decimal(10,4)");
                b.Property(x => x.Quantity).HasColumnType("decimal(27,6)");
                b.Property(x => x.Partition).IsRequired();
                b.Property(x => x.ExtraProperties).IsUnicode(false).HasColumnType("CLOB");
                b.Property(x => x.InvoiceDate).IsRequired().HasDefaultValue(DateTime.Now.Date);

                b.HasIndex(x => x.InvoiceHeaderId).HasDatabaseName("IX_Invoice02_Detail_1");
            });

        }
    }
}

