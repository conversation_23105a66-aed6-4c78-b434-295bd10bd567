using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;
using VnisCore.Core.Oracle.Domain.BaseEntities;
using Core;
using Core.EntityFrameworkCore.Modeling;

namespace VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02
{
    [Table("Invoice02Document")]
    public class Invoice02DocumentEntity : BaseInvoiceDocument
    {
    }


    public static class VnisCoreOracleDbContextModeInvoice02DocumentCreatingExtensions
    {
        public static void ConfigureVnisCoreOracleInvoice02DocumentEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<Invoice02DocumentEntity>(b =>
            {
                //TODO: thêm index
                b.ConfigureByConvention(); //auto configure for the base class props
                b.Property(x => x.PhysicalFileName).HasMaxLength(500).IsUnicode(false);
                b.Property(x => x.FileName).HasMaxLength(500).IsUnicode(true);
                b.Property(x => x.InvoiceHeaderId).IsRequired();
                b.Property(x => x.Partition).IsRequired();
            });
        }
    }
}
