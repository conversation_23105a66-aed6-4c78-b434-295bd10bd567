using Core;
using Core.EntityFrameworkCore.Modeling;
using Microsoft.EntityFrameworkCore;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using VnisCore.Core.Oracle.Domain.BaseEntities;

namespace VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02
{
    [Table("Invoice02Log")]
    public class Invoice02LogEntity : BaseInvoiceLog
    {
        /// <summary>
        /// Ngày hóa đơn NSD nhập vào. Chỉ lưu ngày, tháng, năm. Không lưu thời gian. Lưu dạng UTC
        /// </summary>
        public DateTime InvoiceDate { get; set; }

        //FK
        public virtual Invoice02HeaderEntity InvoiceHeader { get; set; }
    }

    public static class VnisCoreOracleDbContextModeInvoice02LogCreatingExtensions
    {
        public static void ConfigureVnisCoreOracleInvoice02LogEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<Invoice02LogEntity>(b =>
            {
                //TODO: thêm index
                b.ConfigureByConvention(); //auto configure for the base class props

                b.Property(x => x.Partition).IsRequired();
                
                b.Property(x => x.InvoiceDate).IsRequired().HasDefaultValue(DateTime.Now.Date);


                b.HasIndex(x => x.InvoiceHeaderId).HasDatabaseName("IX_Invoice02_Log");
            });

        }
    }
}
