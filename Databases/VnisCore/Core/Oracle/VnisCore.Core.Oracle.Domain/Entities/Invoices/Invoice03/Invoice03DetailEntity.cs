using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using VnisCore.Core.Oracle.Domain.BaseEntities;
using Core;
using Core.EntityFrameworkCore.Modeling;
using Core.Data;
using System.ComponentModel.DataAnnotations;

namespace VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03
{
    [Table("Invoice03Detail")]
    public class Invoice03DetailEntity : BaseInvoiceDetail, IHasExtraProperties
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public new long Id { get; set; }

        /// <summary>
        /// Số lượng 
        /// </summary>
        public decimal Quantity { get; set; }

        public ExtraPropertyDictionary ExtraProperties { get; set; }

        public virtual Invoice03HeaderEntity InvoiceHeader { get; set; }
    }

    public static class VnisCoreOracleDbContextModeInvoice03DetailCreatingExtensions
    {
        public static void ConfigureVnisCoreOracleInvoice03DetailEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<Invoice03DetailEntity>(b =>
            {
                b.ConfigureByConvention(); //auto configure for the base class props

                b.Property(x => x.ProductCode).IsRequired().HasMaxLength(50);
                b.Property(x => x.ProductName).IsRequired().HasMaxLength(500);
                b.Property(x => x.UnitName).HasMaxLength(50);
                b.Property(x => x.Note).HasMaxLength(2000);

                b.Property(x => x.UnitPrice).HasColumnType("decimal(21,6)");
                b.Property(x => x.Amount).HasColumnType("decimal(21,6)");
                b.Property(x => x.PaymentAmount).HasColumnType("decimal(21,6)");
                b.Property(x => x.Quantity).HasColumnType("decimal(21,6)");
                
                b.Property(x => x.Partition).IsRequired();
                b.Property(x => x.ExtraProperties).IsUnicode(false).HasColumnType("CLOB");

                //b.Property(x => x.UnitPriceAfterAdjustment).HasColumnType("decimal(19,4)");
                //b.Property(x => x.AmountAfterAdjustment).HasColumnType("decimal(19,4)");
                //b.Property(x => x.PaymentAmountAfterAdjustment).HasColumnType("decimal(19,4)");

                //b.Property(x => x.OriginUnitPrice).HasColumnType("decimal(19,4)");
                //b.Property(x => x.OriginAmount).HasColumnType("decimal(19,4)");
                //b.Property(x => x.OriginPaymentAmount).HasColumnType("decimal(19,4)");


                b.HasIndex(x => x.InvoiceHeaderId).HasDatabaseName("IX_Invoice03_Detail_1");

                //FK
                //b.HasOne(x => x.InvoiceHeader).WithMany(x => x.InvoiceDetails).HasForeignKey(x => x.InvoiceHeaderId).HasConstraintName("FK_Detail_I03HeaderId");

                b.Ignore(x => x.InvoiceDate);
            });

        }
    }
}