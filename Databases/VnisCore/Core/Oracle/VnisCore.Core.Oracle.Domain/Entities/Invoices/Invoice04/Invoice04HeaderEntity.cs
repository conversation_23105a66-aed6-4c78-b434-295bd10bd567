using System;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using VnisCore.Core.Oracle.Domain.BaseEntities;
using Core;
using Core.EntityFrameworkCore.Modeling;
using Core.Data;

namespace VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice04
{
    [Table("Invoice04Header")]
    public class Invoice04HeaderEntity : BaseInvoiceHeader<Invoice04DetailEntity>, IHasExtraProperties
    {
        #region Thông tin người mua
        /// <summary>
        /// Id bản ghi người mua (Customer), có thể null nếu là loại hóa đơn 03XKNB
        /// </summary>
        public long? BuyerId { get; set; }

        /// <summary>
        /// Tên (Tên người nhận hàng)
        /// Bắt buộc
        /// </summary>
        public string BuyerName { get; set; }

        /// <summary>
        /// Họ và tên người nhận hàng 
        /// </summary>
        public string BuyerFullName { get; set; }
        
        /// <summary>
        /// Họ tên người mua (nếu là khách lẻ không thuộc công ty nào thì đây là tên công ty)
        /// </summary>
        public string BuyerTaxCode { get; set; }

        /// <summary>
        /// Địa chỉ người mua
        /// </summary>
        public string BuyerAddressLine { get; set; }

        /// <summary>
        /// Email người mua
        /// </summary>
        public string BuyerEmail { get; set; }

        /// <summary>
        /// Thời điểm người mua ký
        /// </summary>
        public DateTime? BuyerSignedTime { get; set; }

        /// <summary>
        /// Họ tên người mua ký (Sử dụng tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string BuyerFullNameSigned { get; set; }

        /// <summary>
        /// Tài khoản người mua ký hóa đơn (Sử dụng tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public Guid? BuyerSignedId { get; set; }
        #endregion

        #region Thông tin Phiếu xuất kho hàng giao gửi đại lý
        /// <summary>
        /// Hợp đồng kinh tế số  
        /// MaxLength 250
        /// </summary>
        public string EconomicContractNumber { get; set; }

        /// <summary>
        /// Ngày hợp đồng kinh tế
        /// </summary>
        public DateTime EconomicContractDate { get; set; }

        /// <summary>
        /// Họ và tên người xuất hàng
        /// </summary>
        public string DeliveryOrderBy { get; set; }

        /// <summary>
        /// Họ và Tên người vận chuyển
        /// MaxLength 250
        /// </summary>
        public string DeliveryBy { get; set; }

        /// <summary>
        /// hợp đồng số
        /// MaxLength 250
        /// </summary>
        public string ContractNumber { get; set; }

        /// <summary>
        /// Phương tiện vận chuyển
        /// MaxLength 250
        /// </summary>
        public string TransportationMethod { get; set; }

        ///// <summary>
        ///// Kho xuất
        ///// MaxLength 250
        ///// </summary>
        //public string FromWarehouseName { get; set; }

        ///// <summary>
        ///// Kho nhập
        ///// MaxLength 250
        ///// </summary>
        //public string ToWarehouseName { get; set; }
        #endregion

        /// <summary>
        /// Id bản ghi người mua bên ERP
        /// </summary>
        public string BuyerErpId { get; set; }

        public ExtraPropertyDictionary ExtraProperties { get; set; }


        //FK
        //public virtual Invoice04SignedEntity InvoiceSigned { get; set; }
    }

    public static class VnisCoreOracleDbContextInvoice04HeaderCreatingExtensions
    {
        public static void ConfigureVnisCoreOracleInvoice04HeaderEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<Invoice04HeaderEntity>(b =>
            {
                b.ConfigureByConvention(); //auto configure for the base class props
                b.Property(x => x.ErpId).HasMaxLength(50);

                b.Property(x => x.Source).IsRequired();
                b.Property(x => x.BatchId).IsRequired();

                b.Property(x => x.TemplateNo).IsRequired();
                b.Property(x => x.SerialNo).IsRequired().HasMaxLength(6);
                b.Property(x => x.InvoiceNo).HasMaxLength(8);
                b.Property(x => x.Note).HasMaxLength(500);
                b.Property(x => x.FromCurrency).IsRequired().HasMaxLength(3);
                b.Property(x => x.ToCurrency).IsRequired().HasMaxLength(3);
                b.Property(x => x.InvoiceStatus).IsRequired();
                b.Property(x => x.SignStatus).IsRequired();
                b.Property(x => x.PaymentAmountWords).HasMaxLength(255);
                b.Property(x => x.PaymentAmountWordsEn).HasMaxLength(255);
                b.Property(x => x.TransactionId).IsRequired().HasMaxLength(50);
                b.Property(x => x.ErpId).HasMaxLength(250);
                b.Property(x => x.FullNameApprover).HasMaxLength(400);
                b.Property(x => x.FullNameCreator).HasMaxLength(400);
                b.Property(x => x.FullNamePrinter).HasMaxLength(400);
                b.Property(x => x.UserNameCreator).HasMaxLength(250);

                b.Property(x => x.SellerFullName).IsRequired().HasMaxLength(400);
                b.Property(x => x.SellerLegalName).HasMaxLength(400);
                b.Property(x => x.SellerAddressLine).IsRequired().HasMaxLength(400);
                b.Property(x => x.SellerTaxCode).IsRequired().HasMaxLength(14);
                b.Property(x => x.SellerCityName).HasMaxLength(500);
                b.Property(x => x.SellerCountryCode).HasMaxLength(500);
                b.Property(x => x.SellerDistrictName).HasMaxLength(500);
                b.Property(x => x.SellerPhoneNumber).HasMaxLength(20);
                b.Property(x => x.SellerFaxNumber).HasMaxLength(20);
                b.Property(x => x.SellerBankAccount).HasMaxLength(30);
                b.Property(x => x.SellerBankName).HasMaxLength(400);
                b.Property(x => x.SellerEmail).HasMaxLength(250);

                b.Property(x => x.BuyerName).IsRequired().HasMaxLength(400);
                b.Property(x => x.BuyerFullName).HasMaxLength(100);
                b.Property(x => x.BuyerTaxCode).HasMaxLength(14);
                b.Property(x => x.BuyerAddressLine).IsRequired().HasMaxLength(400);
                b.Property(x => x.BuyerEmail).HasMaxLength(500);

                b.Property(x => x.EconomicContractNumber).IsRequired().HasMaxLength(255);
                b.Property(x => x.EconomicContractDate).IsRequired();
                b.Property(x => x.DeliveryBy).IsRequired().HasMaxLength(100);
                b.Property(x => x.ContractNumber).HasMaxLength(50);
                b.Property(x => x.TransportationMethod).HasMaxLength(50);
                b.Property(x => x.DeliveryOrderBy).HasMaxLength(100);
                //b.Property(x => x.FromWarehouseName).IsRequired().HasMaxLength(250);
                //b.Property(x => x.ToWarehouseName).IsRequired().HasMaxLength(250);
                b.Property(x => x.ExchangeRate).HasColumnType("decimal(7,2)");
                b.Property(x => x.TotalAmount).HasColumnType("decimal(21,6)");
                b.Property(x => x.TotalPaymentAmount).HasColumnType("decimal(21,6)");
                b.Property(x => x.VerificationCode).HasMaxLength(34);

                b.Property(x => x.ReferenceInvoiceType).HasDefaultValue(1);
                b.Property(x => x.ExtraProperties).IsUnicode(false).HasColumnType("CLOB");

                b.Property(x => x.ApproveCancelStatus).HasDefaultValue(0);
                b.Property(x => x.ApproveDeleteStatus).HasDefaultValue(0);
                b.Property(x => x.ApproveStatus).HasDefaultValue(0);
                b.Property(x => x.InvoiceDeleteSource).HasDefaultValue(0);
                b.Property(x => x.StatusTvan).HasDefaultValue(0);
                b.Property(x => x.IsDeclared).HasDefaultValue(0);

                //index
                b.HasIndex(x => new { x.InvoiceTemplateId, x.Number, x.IsDeleted }).IsUnique();
                b.HasIndex(x => x.Number);
                b.HasIndex(x => x.InvoiceTemplateId);
                b.HasIndex(x => x.InvoiceDateYear);
                b.HasIndex(x => x.InvoiceDateMonth);
                b.HasIndex(x => x.InvoiceDateQuater);
                b.HasIndex(x => x.TransactionId);
                b.HasIndex(x => x.InvoiceStatus);
                b.HasIndex(x => x.SignStatus);
                b.HasIndex(x => x.ApproveStatus);
                b.HasIndex(x => x.SellerTaxCode);
                b.HasIndex(x => x.StatusTvan);
                b.HasIndex(x => x.BuyerTaxCode);
                b.HasIndex(x => x.ErpId);
                b.HasIndex(x => x.VerificationCode);

                //b.Property(x => x.TotalAmountAfterAdjustment).HasColumnType("decimal(19,4)");
                //b.Property(x => x.TotalPaymentAmountAfterAdjustment).HasColumnType("decimal(19,4)");
                //b.Property(x => x.OriginTotalAmount).HasColumnType("decimal(19,4)");
                //b.Property(x => x.OriginTotalPaymentAmount).HasColumnType("decimal(19,4)");
                //b.HasOne(x => x.InvoiceSigned).WithOne(x => x.InvoiceHeader).HasForeignKey<Invoice04SignedEntity>(x => x.Id).HasConstraintName("FK_Signed_I04HeaderId");
            });

        }
    }
}