using Core;
using Core.EntityFrameworkCore.Modeling;
using Microsoft.EntityFrameworkCore;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using VnisCore.Core.Oracle.Domain.BaseEntities;

namespace VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01
{
    [Table("Invoice01Log")]
    public class Invoice01LogEntity : BaseInvoiceLog
    {
        /// <summary>
        /// Ngày hóa đơn NSD nhập vào. Chỉ lưu ngày, tháng, năm. Không lưu thời gian. Lưu dạng UTC
        /// </summary>
        public DateTime InvoiceDate { get; set; }

        //FK
        public virtual Invoice01HeaderEntity InvoiceHeader { get; set; }
    }

    public static class VnisCoreOracleDbContextModeInvoice01LogCreatingExtensions
    {
        public static void ConfigureVnisCoreOracleInvoice01LogEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<Invoice01LogEntity>(b =>
            {
                //TODO: thêm index
                b.ConfigureByConvention(); //auto configure for the base class props

                b.Property(x => x.Partition).IsRequired();
                
                b.Property(x => x.InvoiceDate).IsRequired().HasDefaultValue(DateTime.Now.Date);


                b.HasIndex(x => x.InvoiceHeaderId).HasDatabaseName("IX_Invoice01_Log");
            });

        }
    }
}
