using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using VnisCore.Core.Oracle.Domain.BaseEntities;
using Core;
using Core.EntityFrameworkCore.Modeling;

namespace VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01
{
    [Table("Invoice01HeaderExtra")]
    public class Invoice01HeaderExtraEntity : BaseInvoiceHeaderExtra
    {
        ////FK
        //public virtual Invoice01HeaderFieldEntity InvoiceHeaderField { get; set; }

        //public virtual Invoice01HeaderEntity InvoiceHeader { get; set; }
    }

    public static class VnisCoreOracleDbContextModeInvoice01HeaderExtraCreatingExtensions
    {
        public static void ConfigureVnisCoreOracleInvoice01HeaderExtraEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<Invoice01HeaderExtraEntity>(b =>
            {
                b.ConfigureByConvention(); //auto configure for the base class props

                b.Property(x => x.FieldValue).HasMaxLength(500);
                b.<PERSON>ndex(x => x.InvoiceHeaderFieldId).HasDatabaseName("IX_I01_HeaderExtras_1");
                b.HasIndex(x => x.InvoiceHeaderId).HasDatabaseName("IX_I01_HeaderExtras_2");

                //b.HasOne(x => x.InvoiceHeaderField).WithMany(x => x.InvoiceHeaderExtras).HasForeignKey(x => x.InvoiceHeaderFieldId).HasConstraintName("FK_Field_I01HeaderId");
                //b.HasOne(x => x.InvoiceHeader).WithMany(x => x.InvoiceHeaderExtras).HasForeignKey(x => x.InvoiceHeaderId).HasConstraintName("FK_Extra_I01HeaderId");
            });

        }
    }
}
