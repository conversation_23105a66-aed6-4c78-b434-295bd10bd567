using Core;
using Core.Domain.Entities;
using Core.EntityFrameworkCore.Modeling;
using Microsoft.EntityFrameworkCore;
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01
{
    [Table("Invoice01TransactionDetail")]
    public class Invoice01TransactionDetailEntity : Entity<long>
    {
        public int Index { get; set; }

        /// <summary>
        /// Id hóa đơn
        /// Id bảng Invoice01Header
        /// </summary>
        public long InvoiceHeaderId { get; set; }

        /// <summary>
        /// Loại hàng hóa dịch vụ:
        /// 1: Thu phí dịch vụ cho vay
        /// 2: <PERSON><PERSON> dịch vụ ngân hàng
        /// 3: <PERSON><PERSON> dịch vụ Thanh toán thẻ
        /// </summary>
        public int ProductType { get; set; }

        /// <summary>
        /// Ghi chú
        /// </summary>
        public string Note { get; set; }

        /// <summary>
        /// Thành tiền trước thuế
        /// - Hóa đơn G4 gốc:l<PERSON>y giá trị theo DBTG
        /// - Hóa đơn điều chỉnh thủ công: TNgười dùng không tác động, hệ thống tự tính toán theo công thức = "Đơn giá" x "Số lượng"
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// Thành tiền sau thuế
        /// - Hóa đơn G4 gốc: lấy giá trị theo DBTG
        /// - Hóa đơn điều chỉnh thủ công: Người dùng không tác động, hệ thống tự tính toán theo công thức = Thành tiền trước thuế + Tiền thuế
        /// </summary>
        public decimal PaymentAmount { get; set; }

        /// <summary>
        /// % thuế
        /// </summary>
        public decimal VatPercent { get; set; }

        /// <summary>
        /// Tiền thuế
        /// - Hóa đơn G4 gốc: lấy giá trị theo DBTG
        /// - Hóa đơn điều chỉnh thủ công: Người dùng không tác động, hệ thống tự tính toán theo công thức = Thành tiền trước thuế x Phần trăm thuế
        /// </summary>
        public decimal VatAmount { get; set; }

        /// <summary>
        /// Tên hiển thị của thuế trên bản in
        /// </summary>
        public string VatPercentDisplay { get; set; }

        /// <summary>
        /// GL ghi nhận phí
        /// </summary>
        public string GLNo { get; set; }


        /// <summary>
        /// VD: Số REF của món liên quan TF
        /// </summary>
        public string RefNo { get; set; }

        /// <summary>
        /// Mã thanh toán viên
        /// - Hóa đơn G4 gốc: 
        /// + DBTG có dữ liệu: lấy giá trị theo DBTG
        /// + DBTG không có dữ liệu: bỏ trống
        /// - Hóa đơn điều chỉnh thủ công: 
        /// + Nếu người dùng chỉnh sửa dữ liệu trên màn hình hệ thống: lấy giá trị người dùng nhập
        /// + Được phép bỏ trống
        /// - Ràng buộc chỉ lưu vào DBCore nếu người dùng điều chỉnh đồng thời 2 trường Mã TTV và Số chứng từ
        /// </summary>
        public string CreatorErp { get; set; }

        /// <summary>
        /// Số chứng từ giao dịch
        /// - Hóa đơn G4 gốc: 
        /// + DBTG có dữ liệu -> lấy giá trị theo DBTG
        /// + DBTG không có dữ liệu -> bỏ trống
        /// - Hóa đơn điều chỉnh thủ công: 
        /// + Nếu người dùng chỉnh sửa dữ liệu trên màn hình hệ thống: lấy giá trị người dùng nhập
        /// + Được phép bỏ trống
        /// - Ràng buộc chỉ lưu vào DBCore nếu người dùng điều chỉnh đồng thời 2 trường Mã TTV và Số chứng từ"
        /// </summary>
        public string TellSeq { get; set; }

        /// <summary>
        /// Số lượng tại mỗi dòng trên bảng kê, mặc định = 1
        /// - Hóa đơn G4 gốc: mặc định = 1
        /// - Hóa đơn điều chỉnh thủ công: 
        ///+ Mặc định = 1 như hóa đơn gốc
        ///+ Không cho phép người dùng chỉnh sửa
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// "Đơn giá tại mỗi dòng trên bảng kê
        /// - Hóa đơn G4 gốc: Tính toán theo công thức = Amount/Quantity
        /// - Hóa đơn điều chỉnh thủ công: lấy giá trị người dùng nhập trên hệ thống"
        /// </summary>
        public decimal UnitPrice { get; set; }


        /// <summary>
        /// Số tài khoản khách hàng
        /// </summary>
        public string BuyerBankAccount { get; set; }

        /// <summary>
        /// "Tên hàng hóa, dịch vụ dựa theo ProductType:
        /// 1: Thu phí dịch vụ cho vay
        /// 2: Phí dịch vụ ngân hàng
        /// 3: Phí dịch vụ thanh toán thẻ
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// Đơn vị tính, mặc định = Lần
        /// </summary>
        public string UnitName { get; set; }

        /// <summary>
        /// Tỷ giá của từng dòng chi tiết
        /// </summary>
        public decimal ExchangeRate { get; set; }

        /// <summary>
        /// Tiền tệ
        /// </summary>
        public string ToCurrency { get; set; }

        /// <summary>
        /// Ngày từng giao dịch chi tiết
        /// </summary>
        public DateTime TransactionDate { get; set; }

        /// <summary>
        /// Giờ giao dịch
        /// </summary>
        public int Pctime { get; set; }

        /// <summary>
        /// TenantId
        /// </summary>
        public Guid TenantId { get; set; }

        public int RoundingUnit { get; set; }

        /// <summary>
        /// Id bản ghi hàng hóa
        /// </summary>
        public long ProductId { get; set; }

        /// <summary>
        /// Mã hàng hóa
        /// </summary>
        public string ProductCode { get; set; }

        /// <summary>
        /// Id bản ghi bảng Đơn vị tính (Unit)
        /// </summary>
        public long UnitId { get; set; }

        public long Partition { get; set; }
    }

    public static class VnisCoreOracleDbContextModeInvoice01TransactionDetailCreatingExtensions
    {
        public static void ConfigureVnisCoreOracleInvoice01TransactionDetailEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<Invoice01TransactionDetailEntity>(b =>
            {
                //TODO: thêm index
                b.ConfigureByConvention(); //auto configure for the base class props

                b.Property(x => x.InvoiceHeaderId).IsRequired();
                b.Property(x => x.Index).IsRequired();
                b.Property(x => x.ProductCode).IsRequired().HasMaxLength(50);
                b.Property(x => x.ProductName).IsRequired().HasMaxLength(500);
                b.Property(x => x.UnitName).HasMaxLength(50).IsRequired();
                b.Property(x => x.VatPercentDisplay).IsRequired().HasMaxLength(50);
                b.Property(x => x.Note).HasMaxLength(500);
                b.Property(x => x.UnitPrice).IsRequired().HasColumnType("decimal(27,6)");
                b.Property(x => x.Amount).IsRequired().HasColumnType("decimal(27,6)");
                b.Property(x => x.PaymentAmount).IsRequired().HasColumnType("decimal(27,6)");
                b.Property(x => x.VatAmount).HasColumnType("decimal(27,6)");
                b.Property(x => x.Quantity).HasColumnType("decimal(27,6)");
                b.Property(x => x.VatPercent).HasColumnType("decimal(4,2)").IsRequired();
                b.Property(x => x.Partition).IsRequired();
                b.Property(x => x.GLNo).HasMaxLength(500);
                b.Property(x => x.RefNo).HasMaxLength(500);

                b.HasIndex(x => x.InvoiceHeaderId);
            });

        }
    }
}
