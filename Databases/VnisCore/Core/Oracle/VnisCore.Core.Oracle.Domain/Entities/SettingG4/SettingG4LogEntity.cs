using Core;
using Core.Auditing;
using Core.Domain.Entities;
using Core.EntityFrameworkCore.Modeling;
using Microsoft.EntityFrameworkCore;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using VnisCore.Core.Oracle.Domain.BaseEntities;

namespace VnisCore.Core.Oracle.Domain.Entities.SettingG4
{
    [Table("SettingG4Log")]
    public class SettingG4LogEntity : Entity<long>, ITenantFullAuditedEntity, IDeletionAuditedObject
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public new long Id { get; set; }

        public long SettingG4Id { get; set; }

        public Guid TenantId { get; set; }

        public Guid UserId { get; set; }

        public string UserName { get; set; }

        public int Action { get; set; }

        public DateTime CreationTime { get; set; }

        public virtual SettingG4Entity SettingG4 { get; set; }

        public Guid? DeleterId { get; set; }
        
        public DateTime? DeletionTime { get; set; }
        
        public bool IsDeleted { get; set; }
    }


    public static class VnisCoreOracleDbContextModeSettingG4LogCreatingExtensions
    {
        public static void ConfigureVnisCoreOracleSettingG4LogEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<SettingG4LogEntity>(b =>
            {
                //TODO: thêm index
                b.ConfigureByConvention(); //auto configure for the base class props

                b.HasIndex(x => x.SettingG4Id).HasDatabaseName("IX_SettingG4_Log");
            });

        }
    }
}
