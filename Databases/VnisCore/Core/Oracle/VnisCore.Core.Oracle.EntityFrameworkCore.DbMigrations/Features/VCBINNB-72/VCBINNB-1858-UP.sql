declare
   l_nullable user_tab_columns.nullable % type;
begin 
   select nullable into l_nullable 
   from user_tab_columns 
  where table_name = 'PurchaseInvoiceTaxBreakdowns' 
  and column_name = 'VatAmount' 
;
   if l_nullable = 'N' then 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceTaxBreakdowns" MODIFY "VatAmount" DECIMAL(27,6) NULL';
 else 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceTaxBreakdowns" MODIFY "VatAmount" DECIMAL(27,6)';
 end if;
end;
/

declare
   l_nullable user_tab_columns.nullable % type;
begin 
   select nullable into l_nullable 
   from user_tab_columns 
  where table_name = 'PurchaseInvoiceTaxBreakdowns' 
  and column_name = 'Amount' 
;
   if l_nullable = 'N' then 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceTaxBreakdowns" MODIFY "Amount" DECIMAL(27,6) NULL';
 else 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceTaxBreakdowns" MODIFY "Amount" DECIMAL(27,6)';
 end if;
end;
/

declare
   l_nullable user_tab_columns.nullable % type;
begin 
   select nullable into l_nullable 
   from user_tab_columns 
  where table_name = 'PurchaseInvoiceHeaders' 
  and column_name = 'TotalVatAmount' 
;
   if l_nullable = 'N' then 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceHeaders" MODIFY "TotalVatAmount" DECIMAL(27,6) NULL';
 else 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceHeaders" MODIFY "TotalVatAmount" DECIMAL(27,6)';
 end if;
end;
/

declare
   l_nullable user_tab_columns.nullable % type;
begin 
   select nullable into l_nullable 
   from user_tab_columns 
  where table_name = 'PurchaseInvoiceHeaders' 
  and column_name = 'TotalReduceOtherAmount' 
;
   if l_nullable = 'N' then 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceHeaders" MODIFY "TotalReduceOtherAmount" DECIMAL(27,6) NULL';
 else 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceHeaders" MODIFY "TotalReduceOtherAmount" DECIMAL(27,6)';
 end if;
end;
/

declare
   l_nullable user_tab_columns.nullable % type;
begin 
   select nullable into l_nullable 
   from user_tab_columns 
  where table_name = 'PurchaseInvoiceHeaders' 
  and column_name = 'TotalReduceAmountNotTax' 
;
   if l_nullable = 'N' then 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceHeaders" MODIFY "TotalReduceAmountNotTax" DECIMAL(27,6) NULL';
 else 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceHeaders" MODIFY "TotalReduceAmountNotTax" DECIMAL(27,6)';
 end if;
end;
/

declare
   l_nullable user_tab_columns.nullable % type;
begin 
   select nullable into l_nullable 
   from user_tab_columns 
  where table_name = 'PurchaseInvoiceHeaders' 
  and column_name = 'TotalPaymentAmount' 
;
   if l_nullable = 'N' then 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceHeaders" MODIFY "TotalPaymentAmount" DECIMAL(27,6) NULL';
 else 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceHeaders" MODIFY "TotalPaymentAmount" DECIMAL(27,6)';
 end if;
end;
/

declare
   l_nullable user_tab_columns.nullable % type;
begin 
   select nullable into l_nullable 
   from user_tab_columns 
  where table_name = 'PurchaseInvoiceHeaders' 
  and column_name = 'TotalDiscountAmountBeforeTax' 
;
   if l_nullable = 'N' then 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceHeaders" MODIFY "TotalDiscountAmountBeforeTax" DECIMAL(27,6) NULL';
 else 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceHeaders" MODIFY "TotalDiscountAmountBeforeTax" DECIMAL(27,6)';
 end if;
end;
/

declare
   l_nullable user_tab_columns.nullable % type;
begin 
   select nullable into l_nullable 
   from user_tab_columns 
  where table_name = 'PurchaseInvoiceHeaders' 
  and column_name = 'TotalAmount' 
;
   if l_nullable = 'N' then 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceHeaders" MODIFY "TotalAmount" DECIMAL(27,6) NULL';
 else 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceHeaders" MODIFY "TotalAmount" DECIMAL(27,6)';
 end if;
end;
/

declare
   l_nullable user_tab_columns.nullable % type;
begin 
   select nullable into l_nullable 
   from user_tab_columns 
  where table_name = 'PurchaseInvoiceHeaders' 
  and column_name = 'ExchangeRate' 
;
   if l_nullable = 'N' then 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceHeaders" MODIFY "ExchangeRate" DECIMAL(9,2) NULL';
 else 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceHeaders" MODIFY "ExchangeRate" DECIMAL(9,2)';
 end if;
end;
/

declare
   l_nullable user_tab_columns.nullable % type;
begin 
   select nullable into l_nullable 
   from user_tab_columns 
  where table_name = 'PurchaseInvoiceFees' 
  and column_name = 'FeeAmout' 
;
   if l_nullable = 'N' then 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceFees" MODIFY "FeeAmout" DECIMAL(27,6) NULL';
 else 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceFees" MODIFY "FeeAmout" DECIMAL(27,6)';
 end if;
end;
/

declare
   l_nullable user_tab_columns.nullable % type;
begin 
   select nullable into l_nullable 
   from user_tab_columns 
  where table_name = 'PurchaseInvoiceDetails' 
  and column_name = 'UnitPrice' 
;
   if l_nullable = 'N' then 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceDetails" MODIFY "UnitPrice" DECIMAL(27,6) NULL';
 else 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceDetails" MODIFY "UnitPrice" DECIMAL(27,6)';
 end if;
end;
/

declare
   l_nullable user_tab_columns.nullable % type;
begin 
   select nullable into l_nullable 
   from user_tab_columns 
  where table_name = 'PurchaseInvoiceDetails' 
  and column_name = 'Quantity' 
;
   if l_nullable = 'N' then 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceDetails" MODIFY "Quantity" DECIMAL(27,6) NULL';
 else 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceDetails" MODIFY "Quantity" DECIMAL(27,6)';
 end if;
end;
/

declare
   l_nullable user_tab_columns.nullable % type;
begin 
   select nullable into l_nullable 
   from user_tab_columns 
  where table_name = 'PurchaseInvoiceDetails' 
  and column_name = 'DiscountPercentBeforeTax' 
;
   if l_nullable = 'N' then 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceDetails" MODIFY "DiscountPercentBeforeTax" DECIMAL(10,4) NULL';
 else 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceDetails" MODIFY "DiscountPercentBeforeTax" DECIMAL(10,4)';
 end if;
end;
/

declare
   l_nullable user_tab_columns.nullable % type;
begin 
   select nullable into l_nullable 
   from user_tab_columns 
  where table_name = 'PurchaseInvoiceDetails' 
  and column_name = 'DiscountAmountBeforeTax' 
;
   if l_nullable = 'N' then 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceDetails" MODIFY "DiscountAmountBeforeTax" DECIMAL(27,6) NULL';
 else 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceDetails" MODIFY "DiscountAmountBeforeTax" DECIMAL(27,6)';
 end if;
end;
/

declare
   l_nullable user_tab_columns.nullable % type;
begin 
   select nullable into l_nullable 
   from user_tab_columns 
  where table_name = 'PurchaseInvoiceDetails' 
  and column_name = 'Amount' 
;
   if l_nullable = 'N' then 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceDetails" MODIFY "Amount" DECIMAL(27,6) NULL';
 else 
        EXECUTE IMMEDIATE 'ALTER TABLE "PurchaseInvoiceDetails" MODIFY "Amount" DECIMAL(27,6)';
 end if;
end;
/

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES (N'20240330110102_VCBINNB_1858_NANG_KIEU_DU_LIEU_THEO_QD_TCT', N'5.0.17')
/

