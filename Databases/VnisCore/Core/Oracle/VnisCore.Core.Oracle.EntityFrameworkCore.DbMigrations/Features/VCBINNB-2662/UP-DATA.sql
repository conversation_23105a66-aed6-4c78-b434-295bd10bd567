-- PurchaseInvoice
MERGE INTO "PurchaseInvoiceTaxBreakdowns" t 
USING  "PurchaseInvoiceHeaders" s  
ON (t."InvoiceHeaderId" = s."Id")
    WHEN MATCHED THEN
        UPDATE SET t."InvoiceDate" = s."InvoiceDate"
/
    

MERGE INTO "PurchaseInvoiceLogs" t 
USING  "PurchaseInvoiceHeaders" s  
ON (t."InvoiceHeaderId" = s."Id")
    WHEN MATCHED THEN
        UPDATE SET t."InvoiceDate" = s."InvoiceDate"
/


MERGE INTO "PurchaseInvoiceDetails" t 
USING  "PurchaseInvoiceHeaders" s  
ON (t."InvoiceHeaderId" = s."Id")
    WHEN MATCHED THEN
        UPDATE SET t."InvoiceDate" = s."InvoiceDate"
/
      
-- Invoice02
MERGE INTO "Invoice02Xml" t 
USING  "Invoice02Header" s  
ON (t."Id" = s."Id")
    WHEN MATCHED THEN
        UPDATE SET t."InvoiceDate" = s."InvoiceDate"
/


MERGE INTO "Invoice02Log" t 
USING  "Invoice02Header" s  
ON (t."InvoiceHeaderId" = s."Id")
    WHEN MATCHED THEN
        UPDATE SET t."InvoiceDate" = s."InvoiceDate"
/


MERGE INTO "Invoice02Detail" t 
USING  "Invoice02Header" s  
ON (t."InvoiceHeaderId" = s."Id")
    WHEN MATCHED THEN
        UPDATE SET t."InvoiceDate" = s."InvoiceDate"
/


-- Invoice01
MERGE INTO "Invoice01Xml" t 
USING  "Invoice01Header" s  
ON (t."Id" = s."Id")
    WHEN MATCHED THEN
        UPDATE SET t."InvoiceDate" = s."InvoiceDate"
/


MERGE INTO "Invoice01TaxBreakdown" t 
USING  "Invoice01Header" s  
ON (t."InvoiceHeaderId" = s."Id")
    WHEN MATCHED THEN
        UPDATE SET t."InvoiceDate" = s."InvoiceDate"
/


MERGE INTO "Invoice01Log" t 
USING  "Invoice01Header" s  
ON (t."InvoiceHeaderId" = s."Id")
    WHEN MATCHED THEN
        UPDATE SET t."InvoiceDate" = s."InvoiceDate"
/


MERGE INTO "Invoice01Detail" t 
USING  "Invoice01Header" s  
ON (t."InvoiceHeaderId" = s."Id")
    WHEN MATCHED THEN
        UPDATE SET t."InvoiceDate" = s."InvoiceDate"