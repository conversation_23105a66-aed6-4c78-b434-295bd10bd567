ALTER TABLE "NewRegistrationHeader" MODIFY "LegalRepresentativeSex" DEFAULT NULL
/

declare
   l_nullable user_tab_columns.nullable % type;
begin 
   select nullable into l_nullable 
   from user_tab_columns 
  where table_name = 'NewRegistrationHeader' 
  and column_name = 'LegalRepresentativeSex' 
;
   if l_nullable = 'N' then 
        EXECUTE IMMEDIATE 'ALTER TABLE "NewRegistrationHeader" MODIFY "LegalRepresentativeSex" NUMBER(1) ';
 else 
        EXECUTE IMMEDIATE 'ALTER TABLE "NewRegistrationHeader" MODIFY "LegalRepresentativeSex" NUMBER(1) NOT NULL';
 end if;
end;
/

ALTER TABLE "NewRegistrationHeader" MODIFY "LegalRepresentativePhone" DEFAULT NULL
/

declare
   l_nullable user_tab_columns.nullable % type;
begin 
   select nullable into l_nullable 
   from user_tab_columns 
  where table_name = 'NewRegistrationHeader' 
  and column_name = 'LegalRepresentativePhone' 
;
   if l_nullable = 'N' then 
        EXECUTE IMMEDIATE 'ALTER TABLE "NewRegistrationHeader" MODIFY "LegalRepresentativePhone" NVARCHAR2(20) ';
 else 
        EXECUTE IMMEDIATE 'ALTER TABLE "NewRegistrationHeader" MODIFY "LegalRepresentativePhone" NVARCHAR2(20) NOT NULL';
 end if;
end;
/

ALTER TABLE "NewRegistrationHeader" MODIFY "LegalRepresentativePassportNumber" DEFAULT NULL
/

declare
   l_nullable user_tab_columns.nullable % type;
begin 
   select nullable into l_nullable 
   from user_tab_columns 
  where table_name = 'NewRegistrationHeader' 
  and column_name = 'LegalRepresentativePassportNumber' 
;
   if l_nullable = 'N' then 
        EXECUTE IMMEDIATE 'ALTER TABLE "NewRegistrationHeader" MODIFY "LegalRepresentativePassportNumber" NVARCHAR2(20) NULL';
 else 
        EXECUTE IMMEDIATE 'ALTER TABLE "NewRegistrationHeader" MODIFY "LegalRepresentativePassportNumber" NVARCHAR2(20)';
 end if;
end;
/

ALTER TABLE "NewRegistrationHeader" MODIFY "LegalRepresentativeName" DEFAULT NULL
/

declare
   l_nullable user_tab_columns.nullable % type;
begin 
   select nullable into l_nullable 
   from user_tab_columns 
  where table_name = 'NewRegistrationHeader' 
  and column_name = 'LegalRepresentativeName' 
;
   if l_nullable = 'N' then 
        EXECUTE IMMEDIATE 'ALTER TABLE "NewRegistrationHeader" MODIFY "LegalRepresentativeName" NVARCHAR2(50) ';
 else 
        EXECUTE IMMEDIATE 'ALTER TABLE "NewRegistrationHeader" MODIFY "LegalRepresentativeName" NVARCHAR2(50) NOT NULL';
 end if;
end;
/

ALTER TABLE "NewRegistrationHeader" MODIFY "LegalRepresentativeIDNumber" DEFAULT NULL
/

declare
   l_nullable user_tab_columns.nullable % type;
begin 
   select nullable into l_nullable 
   from user_tab_columns 
  where table_name = 'NewRegistrationHeader' 
  and column_name = 'LegalRepresentativeIDNumber' 
;
   if l_nullable = 'N' then 
        EXECUTE IMMEDIATE 'ALTER TABLE "NewRegistrationHeader" MODIFY "LegalRepresentativeIDNumber" NVARCHAR2(12) ';
 else 
        EXECUTE IMMEDIATE 'ALTER TABLE "NewRegistrationHeader" MODIFY "LegalRepresentativeIDNumber" NVARCHAR2(12) NOT NULL';
 end if;
end;
/

ALTER TABLE "NewRegistrationHeader" MODIFY "LegalRepresentativeBirthday" DEFAULT NULL
/

declare
   l_nullable user_tab_columns.nullable % type;
begin 
   select nullable into l_nullable 
   from user_tab_columns 
  where table_name = 'NewRegistrationHeader' 
  and column_name = 'LegalRepresentativeBirthday' 
;
   if l_nullable = 'N' then 
        EXECUTE IMMEDIATE 'ALTER TABLE "NewRegistrationHeader" MODIFY "LegalRepresentativeBirthday" TIMESTAMP(7) ';
 else 
        EXECUTE IMMEDIATE 'ALTER TABLE "NewRegistrationHeader" MODIFY "LegalRepresentativeBirthday" TIMESTAMP(7) NOT NULL';
 end if;
end;
/

declare
   l_nullable user_tab_columns.nullable % type;
begin 
   select nullable into l_nullable 
   from user_tab_columns 
  where table_name = 'NewRegistrationDetailExtension' 
  and column_name = 'EndDate' 
;
   if l_nullable = 'N' then 
        EXECUTE IMMEDIATE 'ALTER TABLE "NewRegistrationDetailExtension" MODIFY "EndDate" TIMESTAMP(7) NULL';
 else 
        EXECUTE IMMEDIATE 'ALTER TABLE "NewRegistrationDetailExtension" MODIFY "EndDate" TIMESTAMP(7)';
 end if;
end;
/

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES (N'20250510040917_VCBINNB_2984_ALTER_NewRegistrationHeader_NewRegistrationDetailExtension', N'5.0.17')
/

