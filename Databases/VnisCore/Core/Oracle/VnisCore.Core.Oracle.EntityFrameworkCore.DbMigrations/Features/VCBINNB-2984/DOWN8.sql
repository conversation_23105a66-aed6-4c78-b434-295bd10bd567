ALTER TABLE "Invoice01SpecificProductExtra" DROP COLUMN "Type"
/

BEGIN 
EXECUTE IMMEDIATE 'CREATE TABLE 
"Invoice02SpecificProductExtra" (
    "Id" NUMBER(19) DEFAULT ("ISEQ_Invoice02SpecificProductExtra_Id".NEXTVAL) NOT NULL,
    "CreationTime" TIMESTAMP(7) NOT NULL,
    "CreatorId" RAW(16),
    "DeleterId" RAW(16),
    "DeletionTime" TIMESTAMP(7),
    "FieldName" NVARCHAR2(20) NOT NULL,
    "FieldValue" NVARCHAR2(200) NOT NULL,
    "InvoiceDetailId" NUMBER(19) NOT NULL,
    "InvoiceHeaderId" NUMBER(19) NOT NULL,
    "IsActive" NUMBER(1) NOT NULL,
    "IsDeleted" NUMBER(1) DEFAULT 0 NOT NULL,
    "LastModificationTime" TIMESTAMP(7),
    "LastModifierId" RAW(16),
    "SpecificProductFieldId" NUMBER(19) NOT NULL,
    "TenantId" RAW(16) NOT NULL,
    CONSTRAINT "PK_Invoice02SpecificProductExtra" PRIMARY KEY ("Id")
)';
END;
/

COMMENT ON TABLE "Invoice02SpecificProductExtra" is N'Bảng lưu trữ các giá trị của thông tin hàng hóa đặc trưng của từng hàng hóa'
/

COMMENT ON COLUMN "Invoice02SpecificProductExtra"."FieldName" is N'Tên trường hàng hóa đặc trưng'
/

COMMENT ON COLUMN "Invoice02SpecificProductExtra"."FieldValue" is N'Giá trị mô tả cho tên trường hàng hóa đặc trưng'
/

COMMENT ON COLUMN "Invoice02SpecificProductExtra"."InvoiceDetailId" is N'Id chi tiết hóa đơn'
/

COMMENT ON COLUMN "Invoice02SpecificProductExtra"."InvoiceHeaderId" is N'Id hóa đơn'
/

COMMENT ON COLUMN "Invoice02SpecificProductExtra"."SpecificProductFieldId" is N'Id hàng hóa đặc trưng'
/

CREATE INDEX "IX_Invoice02SpecificProductExtra_InvoiceDetailId_SpecificProductFieldId_TenantId_IsDeleted" ON "Invoice02SpecificProductExtra" ("InvoiceDetailId", "SpecificProductFieldId", "TenantId", "IsDeleted")
/

CREATE INDEX "IX_Invoice02SpecificProductExtra_InvoiceHeaderId_TenantId_IsDeleted" ON "Invoice02SpecificProductExtra" ("InvoiceHeaderId", "TenantId", "IsDeleted")
/

DELETE FROM "__EFMigrationsHistory"
WHERE "MigrationId" = N'20250529024226_VCBINNB_2984_ALTER_Recover_Invoice02_Invoice03_Invoice04_Ticket_ADD_Type'
/

