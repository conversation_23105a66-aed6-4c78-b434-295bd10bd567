
BEGIN 
EXECUTE IMMEDIATE 'CREATE TABLE 
"PurchaseInvoiceHeaders" (
    "Id" NUMBER(19) GENERATED BY DEFAULT ON NULL AS IDENTITY NOT NULL,
    "ExtraProperties" VARCHAR2(3000),
    "ConcurrencyStamp" NVARCHAR2(40),
    "CreationTime" TIMESTAMP(7) NOT NULL,
    "CreatorId" RAW(16),
    "LastModificationTime" TIMESTAMP(7),
    "LastModifierId" RAW(16),
    "IsDeleted" NUMBER(1) DEFAULT 0 NOT NULL,
    "DeleterId" RAW(16),
    "DeletionTime" TIMESTAMP(7),
    "TenantId" RAW(16) NOT NULL,
    "SignatureDateBySeller" NVARCHAR2(2000),
    "SignatureDateByBuyer" NVARCHAR2(2000),
    "SignatureDateByGeneralDepartmentOfTaxation" NVARCHAR2(2000),
    "SignatureDateOther" NVARCHAR2(2000),
    "TotalAmount" DECIMAL(21,6),
    "TotalReduceAmountNotTax" DECIMAL(21,6),
    "TotalVatAmount" DECIMAL(21,6),
    "TotalDiscountAmountBeforeTax" DECIMAL(21,6),
    "TotalReduceOtherAmount" DECIMAL(21,6),
    "TotalPaymentAmount" DECIMAL(21,6),
    "PaymentAmountWords" NVARCHAR2(255),
    "BuyerFullName" NVARCHAR2(400),
    "BuyerTaxCode" NVARCHAR2(14),
    "BuyerAddressLine" NVARCHAR2(400),
    "BuyerCode" NVARCHAR2(50),
    "BuyerPhoneNumber" NVARCHAR2(20),
    "BuyerEmail" NVARCHAR2(50),
    "BuyerLegalName" NVARCHAR2(100),
    "BuyerBankAccount" NVARCHAR2(30),
    "BuyerBankName" NVARCHAR2(400),
    "SellerFullName" NVARCHAR2(400),
    "SellerTaxCode" NVARCHAR2(14),
    "SellerAddressLine" NVARCHAR2(400),
    "SellerPhoneNumber" NVARCHAR2(20),
    "SellerEmail" NVARCHAR2(50),
    "SellerBankAccount" NVARCHAR2(30),
    "SellerBankName" NVARCHAR2(400),
    "SellerFaxNumber" NVARCHAR2(20),
    "SellerWebsite" NVARCHAR2(100),
    "SellerExtraProperties" VARCHAR2(3000),
    "InvoiceStatus" NUMBER(10),
    "ReferenceInvoiceType" NUMBER(10),
    "TemplateNoReference" NVARCHAR2(11),
    "SerialNoReference" NVARCHAR2(8),
    "InvoiceNoReference" NVARCHAR2(8),
    "InvoiceDateReference" TIMESTAMP(7),
    "ReferenceInvoiceNote" NVARCHAR2(255),
    "Version" NVARCHAR2(6),
    "InvoiceName" NVARCHAR2(100),
    "TemplateNo" NUMBER(5),
    "SerialNo" NVARCHAR2(6),
    "InvoiceNo" NVARCHAR2(8),
    "InvoiceNumber" NUMBER(19),
    "ApplicationCode" NVARCHAR2(20),
    "InvoiceDate" TIMESTAMP(7),
    "NonTariffZones" NUMBER(5),
    "Bill" NVARCHAR2(50),
    "BillDate" TIMESTAMP(7),
    "UnitCurrency" NVARCHAR2(3),
    "ExchangeRate" DECIMAL(7,2),
    "PaymentMethod" NVARCHAR2(50),
    "IssuedTaxCode" NVARCHAR2(14),
    "AuthorizedTaxCode" NVARCHAR2(14),
    "AuthorizedFullName" NVARCHAR2(400),
    "AuthorizedAddressLine" NVARCHAR2(400),
    "PurchaseInvoiceStatus" NUMBER(5) NOT NULL,
    "Source" NUMBER(5) NOT NULL,
    "PurchaseInvoiceType" NUMBER(5) NOT NULL,
    "CodeGeneralDepartmentOfTaxation" NVARCHAR2(34),
    "Note" NVARCHAR2(500),
    CONSTRAINT "PK_PurchaseInvoiceHeaders" PRIMARY KEY ("Id")
)';
END;
/

BEGIN 
EXECUTE IMMEDIATE 'CREATE TABLE 
"PurchaseInvoiceMediaFiles" (
    "Id" NUMBER(19) GENERATED BY DEFAULT ON NULL AS IDENTITY NOT NULL,
    "InvoiceHeaderId" NUMBER(19) NOT NULL,
    "LastModificationTime" TIMESTAMP(7),
    "LastModifierId" RAW(16),
    "CreationTime" TIMESTAMP(7) NOT NULL,
    "CreatorId" RAW(16),
    "TenantId" RAW(16) NOT NULL,
    "PhysicalFileName" NVARCHAR2(250) NOT NULL,
    "ContentType" NVARCHAR2(50) NOT NULL,
    "FileName" NVARCHAR2(250) NOT NULL,
    "Length" NUMBER(19) NOT NULL,
    CONSTRAINT "PK_PurchaseInvoiceMediaFiles" PRIMARY KEY ("Id")
)';
END;
/

BEGIN 
EXECUTE IMMEDIATE 'CREATE TABLE 
"PurchaseInvoiceTemplates" (
    "Id" NUMBER(19) GENERATED BY DEFAULT ON NULL AS IDENTITY NOT NULL,
    "Name" NVARCHAR2(250) NOT NULL,
    "TemplateNo" NUMBER(5) NOT NULL,
    "SerialNo" NVARCHAR2(6) NOT NULL,
    "ExtraProperties" NVARCHAR2(2000),
    "ConcurrencyStamp" NVARCHAR2(40),
    "CreationTime" TIMESTAMP(7) NOT NULL,
    "CreatorId" RAW(16),
    "LastModificationTime" TIMESTAMP(7),
    "LastModifierId" RAW(16),
    "IsDeleted" NUMBER(1) DEFAULT 0 NOT NULL,
    "DeleterId" RAW(16),
    "DeletionTime" TIMESTAMP(7),
    "TenantId" RAW(16) NOT NULL,
    CONSTRAINT "PK_PurchaseInvoiceTemplates" PRIMARY KEY ("Id")
)';
END;
/

BEGIN 
EXECUTE IMMEDIATE 'CREATE TABLE 
"PurchaseInvoiceDetails" (
    "Id" NUMBER(19) GENERATED BY DEFAULT ON NULL AS IDENTITY NOT NULL,
    "ProductType" NUMBER(10),
    "Index" NUMBER(10),
    "ProductCode" NVARCHAR2(50),
    "ProductName" NVARCHAR2(500),
    "UnitName" NVARCHAR2(50),
    "Quantity" DECIMAL(21,6),
    "UnitPrice" DECIMAL(21,6),
    "DiscountPercentBeforeTax" DECIMAL(6,4),
    "DiscountAmountBeforeTax" DECIMAL(21,6),
    "Amount" DECIMAL(21,6),
    "VatPercent" NVARCHAR2(11),
    "InvoiceHeaderId" NUMBER(19),
    "ExtraProperties" VARCHAR2(3000),
    "ConcurrencyStamp" NVARCHAR2(40),
    "CreationTime" TIMESTAMP(7) NOT NULL,
    "CreatorId" RAW(16),
    "LastModificationTime" TIMESTAMP(7),
    "LastModifierId" RAW(16),
    "IsDeleted" NUMBER(1) DEFAULT 0 NOT NULL,
    "DeleterId" RAW(16),
    "DeletionTime" TIMESTAMP(7),
    "TenantId" RAW(16) NOT NULL,
    CONSTRAINT "PK_PurchaseInvoiceDetails" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_PurchaseInvoiceDetails_PurchaseInvoiceHeaders_InvoiceHeaderId" FOREIGN KEY ("InvoiceHeaderId") REFERENCES "PurchaseInvoiceHeaders" ("Id")
)';
END;
/

BEGIN 
EXECUTE IMMEDIATE 'CREATE TABLE 
"PurchaseInvoiceFees" (
    "Id" NUMBER(19) GENERATED BY DEFAULT ON NULL AS IDENTITY NOT NULL,
    "FeeName" NVARCHAR2(2000),
    "FeeAmout" DECIMAL(21,6),
    "InvoiceHeaderId" NUMBER(19),
    "ExtraProperties" VARCHAR2(3000),
    "ConcurrencyStamp" NVARCHAR2(40),
    "CreationTime" TIMESTAMP(7) NOT NULL,
    "CreatorId" RAW(16),
    "LastModificationTime" TIMESTAMP(7),
    "LastModifierId" RAW(16),
    "IsDeleted" NUMBER(1) DEFAULT 0 NOT NULL,
    "DeleterId" RAW(16),
    "DeletionTime" TIMESTAMP(7),
    "TenantId" RAW(16) NOT NULL,
    CONSTRAINT "PK_PurchaseInvoiceFees" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_PurchaseInvoiceFees_PurchaseInvoiceHeaders_InvoiceHeaderId" FOREIGN KEY ("InvoiceHeaderId") REFERENCES "PurchaseInvoiceHeaders" ("Id")
)';
END;
/

BEGIN 
EXECUTE IMMEDIATE 'CREATE TABLE 
"PurchaseInvoiceLogs" (
    "Id" NUMBER(19) GENERATED BY DEFAULT ON NULL AS IDENTITY NOT NULL,
    "PurchaseInvoiceStatus" NUMBER(5) NOT NULL,
    "InvoiceHeaderId" NUMBER(19),
    "ExtraProperties" VARCHAR2(3000),
    "ConcurrencyStamp" NVARCHAR2(40),
    "CreationTime" TIMESTAMP(7) NOT NULL,
    "CreatorId" RAW(16),
    "LastModificationTime" TIMESTAMP(7),
    "LastModifierId" RAW(16),
    "IsDeleted" NUMBER(1) DEFAULT 0 NOT NULL,
    "DeleterId" RAW(16),
    "DeletionTime" TIMESTAMP(7),
    "TenantId" RAW(16) NOT NULL,
    CONSTRAINT "PK_PurchaseInvoiceLogs" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_PurchaseInvoiceLogs_PurchaseInvoiceHeaders_InvoiceHeaderId" FOREIGN KEY ("InvoiceHeaderId") REFERENCES "PurchaseInvoiceHeaders" ("Id")
)';
END;
/

BEGIN 
EXECUTE IMMEDIATE 'CREATE TABLE 
"PurchaseInvoiceTaxBreakdowns" (
    "Id" NUMBER(19) GENERATED BY DEFAULT ON NULL AS IDENTITY NOT NULL,
    "VatPercent" NVARCHAR2(11),
    "Amount" DECIMAL(21,6),
    "VatAmount" DECIMAL(21,6),
    "InvoiceHeaderId" NUMBER(19),
    "ExtraProperties" VARCHAR2(3000),
    "ConcurrencyStamp" NVARCHAR2(40),
    "CreationTime" TIMESTAMP(7) NOT NULL,
    "CreatorId" RAW(16),
    "LastModificationTime" TIMESTAMP(7),
    "LastModifierId" RAW(16),
    "IsDeleted" NUMBER(1) DEFAULT 0 NOT NULL,
    "DeleterId" RAW(16),
    "DeletionTime" TIMESTAMP(7),
    "TenantId" RAW(16) NOT NULL,
    CONSTRAINT "PK_PurchaseInvoiceTaxBreakdowns" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_PurchaseInvoiceTaxBreakdowns_PurchaseInvoiceHeaders_InvoiceHeaderId" FOREIGN KEY ("InvoiceHeaderId") REFERENCES "PurchaseInvoiceHeaders" ("Id")
)';
END;
/

CREATE INDEX "IX_PurchaseInvoiceDetails_InvoiceHeaderId" ON "PurchaseInvoiceDetails" ("InvoiceHeaderId")
/

CREATE INDEX "IX_PurchaseInvoiceFees_InvoiceHeaderId" ON "PurchaseInvoiceFees" ("InvoiceHeaderId")
/

CREATE INDEX "IX_PurchaseInvoiceLogs_InvoiceHeaderId" ON "PurchaseInvoiceLogs" ("InvoiceHeaderId")
/

CREATE INDEX "IX_PurchaseInvoiceTaxBreakdowns_InvoiceHeaderId" ON "PurchaseInvoiceTaxBreakdowns" ("InvoiceHeaderId")
/

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES (N'20231114042328_them-purchase-invoice-theo-dung-quy-dinh-do-dai-cua-TCT', N'5.0.17')