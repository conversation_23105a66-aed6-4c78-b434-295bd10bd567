using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace VnisCore.Core.Oracle.EntityFrameworkCore.DbMigrations.Migrations
{
    public partial class VCBINNB_2984_ADD_SpecificeProduct : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "BudgetUnitCode",
                table: "Invoice02Header",
                type: "NVARCHAR2(7)",
                maxLength: 7,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BuyerIDNumber",
                table: "Invoice02Header",
                type: "NVARCHAR2(12)",
                maxLength: 12,
                nullable: false,
                defaultValue: " ");

            migrationBuilder.AddColumn<string>(
                name: "BuyerPassportNumber",
                table: "Invoice02Header",
                type: "NVARCHAR2(20)",
                maxLength: 20,
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "SpecificProductType",
                table: "Invoice02Detail",
                type: "NUMBER(5)",
                nullable: false,
                defaultValue: (short)0,
                comment: "<PERSON>ại hàng hóa đặc trưng");

            migrationBuilder.AlterColumn<decimal>(
                name: "ExchangeRate",
                table: "Invoice01TransactionDetail",
                type: "DECIMAL(18, 2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "DECIMAL(18,2)");

            migrationBuilder.AddColumn<string>(
                name: "BudgetUnitCode",
                table: "Invoice01Header",
                type: "NVARCHAR2(7)",
                maxLength: 7,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BuyerIDNumber",
                table: "Invoice01Header",
                type: "NVARCHAR2(12)",
                maxLength: 12,
                nullable: false,
                defaultValue: " ");

            migrationBuilder.AddColumn<string>(
                name: "BuyerPassportNumber",
                table: "Invoice01Header",
                type: "NVARCHAR2(20)",
                maxLength: 20,
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "SpecificProductType",
                table: "Invoice01Detail",
                type: "NUMBER(5)",
                nullable: false,
                defaultValue: (short)0,
                comment: "Loại hàng hóa đặc trưng");

            migrationBuilder.AlterColumn<decimal>(
                name: "VatPercent",
                table: "InputInvoice01TaxBreakdown",
                type: "DECIMAL(18, 2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "DECIMAL(18,2)");

            migrationBuilder.AlterColumn<decimal>(
                name: "VatPercent",
                table: "InputInvoice01Detail",
                type: "DECIMAL(18, 2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "DECIMAL(18,2)");

            migrationBuilder.AlterColumn<decimal>(
                name: "DiscountPercentBeforeTax",
                table: "InputInvoice01Detail",
                type: "DECIMAL(18, 2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "DECIMAL(18,2)");

            migrationBuilder.CreateTable(
                name: "Invoice01SpecificProductExtra",
                columns: table => new
                {
                    Id = table.Column<long>(type: "NUMBER(19)", nullable: false, defaultValueSql: "\"SEQ_Invoice01SpecificProductExtra_Id\".NEXTVAL"),
                    CreationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: false),
                    CreatorId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    IsDeleted = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true),
                    TenantId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    IsActive = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    InvoiceHeaderId = table.Column<long>(type: "NUMBER(19)", nullable: false, comment: "Id hóa đơn"),
                    InvoiceDetailId = table.Column<long>(type: "NUMBER(19)", nullable: false, comment: "Id chi tiết hóa đơn"),
                    SpecificProductFieldId = table.Column<long>(type: "NUMBER(19)", nullable: false, comment: "Id hàng hóa đặc trưng"),
                    FieldName = table.Column<string>(type: "NVARCHAR2(20)", maxLength: 20, nullable: false, comment: "Tên trường hàng hóa đặc trưng"),
                    FieldValue = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: false, comment: "Giá trị mô tả cho tên trường hàng hóa đặc trưng")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Invoice01SpecificProductExtra", x => x.Id);
                },
                comment: "Bảng lưu trữ các giá trị của thông tin hàng hóa đặc trưng của từng hàng hóa");

            migrationBuilder.CreateTable(
                name: "Invoice02SpecificProductExtra",
                columns: table => new
                {
                    Id = table.Column<long>(type: "NUMBER(19)", nullable: false, defaultValueSql: "\"SEQ_Invoice02SpecificProductExtra_Id\".NEXTVAL"),
                    CreationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: false),
                    CreatorId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    IsDeleted = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true),
                    TenantId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    IsActive = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    InvoiceHeaderId = table.Column<long>(type: "NUMBER(19)", nullable: false, comment: "Id hóa đơn"),
                    InvoiceDetailId = table.Column<long>(type: "NUMBER(19)", nullable: false, comment: "Id chi tiết hóa đơn"),
                    SpecificProductFieldId = table.Column<long>(type: "NUMBER(19)", nullable: false, comment: "Id hàng hóa đặc trưng"),
                    FieldName = table.Column<string>(type: "NVARCHAR2(20)", maxLength: 20, nullable: false, comment: "Tên trường hàng hóa đặc trưng"),
                    FieldValue = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: false, comment: "Giá trị mô tả cho tên trường hàng hóa đặc trưng")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Invoice02SpecificProductExtra", x => x.Id);
                },
                comment: "Bảng lưu trữ các giá trị của thông tin hàng hóa đặc trưng của từng hàng hóa");

            migrationBuilder.CreateTable(
                name: "SpecificProductField",
                columns: table => new
                {
                    Id = table.Column<long>(type: "NUMBER(19)", nullable: false, defaultValueSql: "\"SEQ_SpecificProductField_Id\".NEXTVAL"),
                    Type = table.Column<int>(type: "NUMBER(10)", nullable: false, comment: "Loại hàng hóa đặc trưng"),
                    FieldName = table.Column<string>(type: "NVARCHAR2(20)", maxLength: 20, nullable: false, comment: "Tên trường"),
                    DisplayName = table.Column<string>(type: "NVARCHAR2(100)", maxLength: 100, nullable: false, comment: "Tên hiển thị"),
                    Metadata = table.Column<string>(type: "NVARCHAR2(2000)", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: false),
                    CreatorId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    IsDeleted = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true),
                    TenantId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    IsActive = table.Column<bool>(type: "NUMBER(1)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SpecificProductField", x => x.Id);
                },
                comment: "Bảng lưu trữ các giá trị của thông tin loại hàng hóa đặc trưng");

            migrationBuilder.CreateIndex(
                name: "IX_Invoice01SpecificProductExtra_InvoiceDetailId_SpecificProductFieldId_TenantId_IsDeleted",
                table: "Invoice01SpecificProductExtra",
                columns: new[] { "InvoiceDetailId", "SpecificProductFieldId", "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_Invoice01SpecificProductExtra_InvoiceHeaderId_TenantId_IsDeleted",
                table: "Invoice01SpecificProductExtra",
                columns: new[] { "InvoiceHeaderId", "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_Invoice02SpecificProductExtra_InvoiceDetailId_SpecificProductFieldId_TenantId_IsDeleted",
                table: "Invoice02SpecificProductExtra",
                columns: new[] { "InvoiceDetailId", "SpecificProductFieldId", "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_Invoice02SpecificProductExtra_InvoiceHeaderId_TenantId_IsDeleted",
                table: "Invoice02SpecificProductExtra",
                columns: new[] { "InvoiceHeaderId", "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_SpecificProductField_Type_FieldName_TenantId_IsDeleted",
                table: "SpecificProductField",
                columns: new[] { "Type", "FieldName", "TenantId", "IsDeleted" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Invoice01SpecificProductExtra");

            migrationBuilder.DropTable(
                name: "Invoice02SpecificProductExtra");

            migrationBuilder.DropTable(
                name: "SpecificProductField");

            migrationBuilder.DropColumn(
                name: "BudgetUnitCode",
                table: "Invoice02Header");

            migrationBuilder.DropColumn(
                name: "BuyerIDNumber",
                table: "Invoice02Header");

            migrationBuilder.DropColumn(
                name: "BuyerPassportNumber",
                table: "Invoice02Header");

            migrationBuilder.DropColumn(
                name: "SpecificProductType",
                table: "Invoice02Detail");

            migrationBuilder.DropColumn(
                name: "BudgetUnitCode",
                table: "Invoice01Header");

            migrationBuilder.DropColumn(
                name: "BuyerIDNumber",
                table: "Invoice01Header");

            migrationBuilder.DropColumn(
                name: "BuyerPassportNumber",
                table: "Invoice01Header");

            migrationBuilder.DropColumn(
                name: "SpecificProductType",
                table: "Invoice01Detail");

            migrationBuilder.AlterColumn<decimal>(
                name: "ExchangeRate",
                table: "Invoice01TransactionDetail",
                type: "DECIMAL(18,2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "DECIMAL(18, 2)");

            migrationBuilder.AlterColumn<decimal>(
                name: "VatPercent",
                table: "InputInvoice01TaxBreakdown",
                type: "DECIMAL(18,2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "DECIMAL(18, 2)");

            migrationBuilder.AlterColumn<decimal>(
                name: "VatPercent",
                table: "InputInvoice01Detail",
                type: "DECIMAL(18,2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "DECIMAL(18, 2)");

            migrationBuilder.AlterColumn<decimal>(
                name: "DiscountPercentBeforeTax",
                table: "InputInvoice01Detail",
                type: "DECIMAL(18,2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "DECIMAL(18, 2)");
        }
    }
}
