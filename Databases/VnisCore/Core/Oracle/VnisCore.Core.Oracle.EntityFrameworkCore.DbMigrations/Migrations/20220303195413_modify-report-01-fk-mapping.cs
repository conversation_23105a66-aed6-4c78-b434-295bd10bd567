using Microsoft.EntityFrameworkCore.Migrations;

namespace VnisCore.Core.Oracle.EntityFrameworkCore.DbMigrations.Migrations
{
    public partial class modifyreport01fkmapping : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Detail01_TaxReportHeaderId",
                table: "TaxReport01Detail");

            migrationBuilder.AlterColumn<decimal>(
                name: "VatPercent",
                table: "InputInvoice01TaxBreakdown",
                type: "DECIMAL(18, 2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "DECIMAL(18,2)");

            migrationBuilder.AlterColumn<decimal>(
                name: "VatPercent",
                table: "InputInvoice01Detail",
                type: "DECIMAL(18, 2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "DECIMAL(18,2)");

            migrationBuilder.AlterColumn<decimal>(
                name: "DiscountPercentBeforeTax",
                table: "InputInvoice01Detail",
                type: "DECIMAL(18, 2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "DECIMAL(18,2)");

            migrationBuilder.AddForeignKey(
                name: "FK_Detail01Mapping_TRHeaderId",
                table: "TaxReport01DetailMapping",
                column: "TaxReportHeaderId",
                principalTable: "TaxReport01Header",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Detail01Mapping_TRHeaderId",
                table: "TaxReport01DetailMapping");

            migrationBuilder.AlterColumn<decimal>(
                name: "VatPercent",
                table: "InputInvoice01TaxBreakdown",
                type: "DECIMAL(18,2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "DECIMAL(18, 2)");

            migrationBuilder.AlterColumn<decimal>(
                name: "VatPercent",
                table: "InputInvoice01Detail",
                type: "DECIMAL(18,2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "DECIMAL(18, 2)");

            migrationBuilder.AlterColumn<decimal>(
                name: "DiscountPercentBeforeTax",
                table: "InputInvoice01Detail",
                type: "DECIMAL(18,2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "DECIMAL(18, 2)");

            migrationBuilder.AddForeignKey(
                name: "FK_Detail01_TaxReportHeaderId",
                table: "TaxReport01Detail",
                column: "TaxReportHeaderId",
                principalTable: "TaxReport01Header",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
