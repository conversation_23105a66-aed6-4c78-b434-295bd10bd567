using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace VnisCore.Core.Oracle.EntityFrameworkCore.DbMigrations.Migrations
{
    public partial class UpdateSettingG4 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<short>(
                name: "ApproveDeleteStatus",
                table: "SettingG4",
                type: "NUMBER(5)",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "ApprovedDeleteId",
                table: "SettingG4",
                type: "RAW(16)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ApprovedDeleteTime",
                table: "SettingG4",
                type: "TIMESTAMP(7)",
                nullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "VatPercent",
                table: "InputInvoice01TaxBreakdown",
                type: "DECIMAL(18, 2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "DECIMAL(18,2)");

            migrationBuilder.AlterColumn<decimal>(
                name: "VatPercent",
                table: "InputInvoice01Detail",
                type: "DECIMAL(18, 2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "DECIMAL(18,2)");

            migrationBuilder.AlterColumn<decimal>(
                name: "DiscountPercentBeforeTax",
                table: "InputInvoice01Detail",
                type: "DECIMAL(18, 2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "DECIMAL(18,2)");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ApproveDeleteStatus",
                table: "SettingG4");

            migrationBuilder.DropColumn(
                name: "ApprovedDeleteId",
                table: "SettingG4");

            migrationBuilder.DropColumn(
                name: "ApprovedDeleteTime",
                table: "SettingG4");

            migrationBuilder.AlterColumn<decimal>(
                name: "VatPercent",
                table: "InputInvoice01TaxBreakdown",
                type: "DECIMAL(18,2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "DECIMAL(18, 2)");

            migrationBuilder.AlterColumn<decimal>(
                name: "VatPercent",
                table: "InputInvoice01Detail",
                type: "DECIMAL(18,2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "DECIMAL(18, 2)");

            migrationBuilder.AlterColumn<decimal>(
                name: "DiscountPercentBeforeTax",
                table: "InputInvoice01Detail",
                type: "DECIMAL(18,2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "DECIMAL(18, 2)");
        }
    }
}
