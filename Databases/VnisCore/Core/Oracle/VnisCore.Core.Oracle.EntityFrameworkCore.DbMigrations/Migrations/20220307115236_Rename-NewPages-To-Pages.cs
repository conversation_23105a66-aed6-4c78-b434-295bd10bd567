using Microsoft.EntityFrameworkCore.Migrations;

namespace VnisCore.Core.Oracle.EntityFrameworkCore.DbMigrations.Migrations
{
    public partial class RenameNewPagesToPages : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "NewPages",
                table: "TicketExportJob",
                newName: "Pages");

            migrationBuilder.RenameColumn(
                name: "NewPages",
                table: "Invoice04ExportJob",
                newName: "Pages");

            migrationBuilder.RenameColumn(
                name: "NewPages",
                table: "Invoice03ExportJob",
                newName: "Pages");

            migrationBuilder.RenameColumn(
                name: "NewPages",
                table: "Invoice02ExportJob",
                newName: "Pages");

            migrationBuilder.RenameColumn(
                name: "NewPages",
                table: "Invoice01ExportJob",
                newName: "Pages");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Pages",
                table: "TicketExportJob",
                newName: "NewPages");

            migrationBuilder.RenameColumn(
                name: "Pages",
                table: "Invoice04ExportJob",
                newName: "NewPages");

            migrationBuilder.RenameColumn(
                name: "Pages",
                table: "Invoice03ExportJob",
                newName: "NewPages");

            migrationBuilder.RenameColumn(
                name: "Pages",
                table: "Invoice02ExportJob",
                newName: "NewPages");

            migrationBuilder.RenameColumn(
                name: "Pages",
                table: "Invoice01ExportJob",
                newName: "NewPages");
        }
    }
}
