DROP TABLE "ReportReconcileInvoiceMinioDetail"
/

DROP TABLE "ReportReconcileInvoiceMinio"
/

DELETE FROM "__EFMigrationsHistory"
WHERE "MigrationId" = N'20230916042953_AddReportReconcileInvoiceMinio'
/

ALTER TABLE "ReportReconcileInvoiceMinioDetail" ADD "ReportHeaderId" NUMBER(19) DEFAULT 0 NOT NULL
/

BEGIN 
EXECUTE IMMEDIATE 'CREATE TABLE 
"ReportReconcileInvoiceMinio" (
    "Id" NUMBER(19) GENERATED BY DEFAULT ON NULL AS IDENTITY NOT NULL,
    "CreationTime" TIMESTAMP(7) NOT NULL,
    "CreatorId" RAW(16),
    "DeleterId" RAW(16),
    "DeletionTime" TIMESTAMP(7),
    "DifferenceQuantity" NUMBER(19) NOT NULL,
    "InvoiceDate" TIMESTAMP(7) NOT NULL,
    "IsActive" NUMBER(1) NOT NULL,
    "IsDeleted" NUMBER(1) DEFAULT 0 NOT NULL,
    "LastModificationTime" TIMESTAMP(7),
    "LastModifierId" RAW(16),
    "TenantCode" NVARCHAR2(500),
    "TenantFullName" NVARCHAR2(400),
    "TenantId" RAW(16) NOT NULL,
    "TotalInvoiceSigned" NUMBER(19) NOT NULL,
    "TotalXmlInMinio" NUMBER(19) NOT NULL,
    "TotalXmlInMongo" NUMBER(19) NOT NULL,
    CONSTRAINT "PK_ReportReconcileInvoiceMinio" PRIMARY KEY ("Id")
)';
END;
/

CREATE INDEX "IX_ReportReconcileInvoiceMinioDetail_ReportHeaderId" ON "ReportReconcileInvoiceMinioDetail" ("ReportHeaderId")
/

ALTER TABLE "ReportReconcileInvoiceMinioDetail" ADD CONSTRAINT "FK_ReportReconcileInvoiceMinio" FOREIGN KEY ("ReportHeaderId") REFERENCES "ReportReconcileInvoiceMinio" ("Id") ON DELETE CASCADE
/

DELETE FROM "__EFMigrationsHistory"
WHERE "MigrationId" = N'20240410102043_update-invoice-reconciliation'
/

