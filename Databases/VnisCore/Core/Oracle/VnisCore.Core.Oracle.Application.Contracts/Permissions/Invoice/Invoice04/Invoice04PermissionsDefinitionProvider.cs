using Core.Authorization.Permissions;
using Core.Localization;
using Core.Localization.Resources.AbpLocalization;

namespace VnisCore.Core.Oracle.Application.Contracts.Permissions.Invoice.Invoice04
{
    public class Invoice04PermissionsDefinitionProvider : PermissionDefinitionProvider
    {
        public override void Define(IPermissionDefinitionContext context)
        {
            var settingGroup = context.AddGroup(Invoice04Permissions.GroupName, L("Permission:Invoice04Management"));

            if (settingGroup == null) return;

            var settingPermission = settingGroup.AddPermission(Invoice04Permissions.Invoice04.Default, L("Permission:Invoice04Management"));
            settingPermission.AddChild(Invoice04Permissions.Invoice04.Create, L("Permission:Create"));
            settingPermission.AddChild(Invoice04Permissions.Invoice04.Update, L("Permission:Edit"));
            settingPermission.AddChild(Invoice04Permissions.Invoice04.Approve, L("Permission:Approve"));
            settingPermission.AddChild(Invoice04Permissions.Invoice04.CancelApprove, L("Permission:CancelApprove"));
            settingPermission.AddChild(Invoice04Permissions.Invoice04.Cancel, L("Permission:Cancel"));
            settingPermission.AddChild(Invoice04Permissions.Invoice04.Delete, L("Permission:Delete"));
            settingPermission.AddChild(Invoice04Permissions.Invoice04.Replace, L("Permission:Replace"));
            settingPermission.AddChild(Invoice04Permissions.Invoice04.ImportExcel, L("Permission:ImportExcel"));
            settingPermission.AddChild(Invoice04Permissions.Invoice04.ExportExcel, L("Permission:ExportExcel"));
            settingPermission.AddChild(Invoice04Permissions.Invoice04.ExportXml, L("Permission:ExportXml"));
            settingPermission.AddChild(Invoice04Permissions.Invoice04.Sign, L("Permission:Sign"));
            settingPermission.AddChild(Invoice04Permissions.Invoice04.UnOfficial, L("Permission:UnOfficial"));
            settingPermission.AddChild(Invoice04Permissions.Invoice04.Official, L("Permission:Official"));
            settingPermission.AddChild(Invoice04Permissions.Invoice04.UploadDocument, L("Permission:UploadDocument"));
            settingPermission.AddChild(Invoice04Permissions.Invoice04.DeleteDocument, L("Permission:DeleteDocument"));
            settingPermission.AddChild(Invoice04Permissions.Invoice04.SendMail, L("Permission:SendMail"));
        }

        private static LocalizableString L(string name)
        {
            return LocalizableString.Create<CoreLocalizationResource>(name);
        }
    }
}