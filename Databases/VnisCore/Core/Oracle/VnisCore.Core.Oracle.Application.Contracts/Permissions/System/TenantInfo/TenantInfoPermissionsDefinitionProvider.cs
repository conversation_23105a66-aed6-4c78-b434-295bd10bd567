using Core.Authorization.Permissions;
using Core.Localization;
using Core.Localization.Resources.AbpLocalization;

namespace VnisCore.Core.Oracle.Application.Contracts.Permissions.System.TenantInfo
{
    public class TenantInfoPermissionsDefinitionProvider : PermissionDefinitionProvider
    {
        public override void Define(IPermissionDefinitionContext context)
        {
            var settingGroup = context.AddGroup(TenantInfoPermissions.GroupName, L("Permission:TenantInfoManagement"));

            if (settingGroup == null) return;

            var settingPermission = settingGroup.AddPermission(TenantInfoPermissions.TenantInfo.Default, L("Permission:TenantInfoManagement"));
            settingPermission.AddChild(TenantInfoPermissions.TenantInfo.Update, L("Permission:Edit"));
        }

        private static LocalizableString L(string name)
        {
            return LocalizableString.Create<CoreLocalizationResource>(name);
        }
    }
}