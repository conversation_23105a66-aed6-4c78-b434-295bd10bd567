using Core.Authorization.Permissions;
using Core.Localization;
using Core.Localization.Resources.AbpLocalization;

namespace VnisCore.Core.Oracle.Application.Contracts.Permissions.Report.DataFluctuationsReport
{
    public class DataFluctuationsReportPermissionsDefinitionProvider : PermissionDefinitionProvider
    {
        public override void Define(IPermissionDefinitionContext context)
        {
            var settingGroup = context.AddGroup(DataFluctuationsReportPermissions.GroupName, L("Permission:DataFluctuationsReportManagement"));

            if (settingGroup == null) return;

            var settingPermission = settingGroup.AddPermission(DataFluctuationsReportPermissions.DataFluctuationsReport.Default, L("Permission:DataFluctuationsReportManagement"));
            settingPermission.AddChild(DataFluctuationsReportPermissions.DataFluctuationsReport.ExportExcel, L("Permission:ExportExcel"));
        }

        private static LocalizableString L(string name)
        {
            return LocalizableString.Create<CoreLocalizationResource>(name);
        }
    }
}