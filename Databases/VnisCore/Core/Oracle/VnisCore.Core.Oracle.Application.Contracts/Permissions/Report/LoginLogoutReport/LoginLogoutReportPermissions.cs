using Core.Reflection;

namespace VnisCore.Core.Oracle.Application.Contracts.Permissions.Report.LoginLogoutReport
{
    public class LoginLogoutReportPermissions
    {
        public const string GroupName = "LoginLogoutReportManagement";

        public static class LoginLogoutReport
        {
            public const string Default = GroupName + ".LoginLogoutReport";
            public const string ExportExcel = Default + ".ExportExcel";
        }

        public static string[] GetAll()
        {
            return ReflectionHelper.GetPublicConstantsRecursively(typeof(LoginLogoutReportPermissions));
        }
    }
}