using Core.Authorization.Permissions;
using Core.Localization;
using Core.Localization.Resources.AbpLocalization;

namespace VnisCore.Core.Oracle.Application.Contracts.Permissions.Report.ReportReconcileInvoiceMinio
{
    public class ReportReconcileInvoiceMinioPermissionsDefinitionProvider : PermissionDefinitionProvider
    {
        public override void Define(IPermissionDefinitionContext context)
        {
            var settingGroup = context.AddGroup(ReportReconcileInvoiceMinioPermissions.GroupName, L("Permission:ReportReconcileInvoiceMinioManagement"));

            if (settingGroup == null) return;

            var settingPermission = settingGroup.AddPermission(ReportReconcileInvoiceMinioPermissions.ReportReconcileInvoiceMinio.Default, L("Permission:ReportReconcileInvoiceMinioManagement"));
            settingPermission.AddChild(ReportReconcileInvoiceMinioPermissions.ReportReconcileInvoiceMinio.Info, L("Permission:Info"));
        }

        private static LocalizableString L(string name)
        {
            return LocalizableString.Create<CoreLocalizationResource>(name);
        }
    }
}