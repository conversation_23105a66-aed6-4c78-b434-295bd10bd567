using Core.Authorization.Permissions;
using Core.Localization;
using Core.Localization.Resources.AbpLocalization;

namespace VnisCore.Core.Oracle.Application.Contracts.Permissions.Catalog.Certificate
{
    public class CertificatePermissionsDefinitionProvider : PermissionDefinitionProvider
    {
        public override void Define(IPermissionDefinitionContext context)
        {
            var settingGroup = context.AddGroup(CertificatePermissions.GroupName, L("Permission:CertificateManagement"));

            if (settingGroup == null) return;

            var settingPermission = settingGroup.AddPermission(CertificatePermissions.Certificate.Default, L("Permission:CertificateManagement"));
            settingPermission.AddChild(CertificatePermissions.Certificate.Sync, L("Permission:Sync"));
            settingPermission.AddChild(CertificatePermissions.Certificate.Claim, L("Permission:Claim"));
            settingPermission.AddChild(CertificatePermissions.Certificate.Delete, L("Permission:Delete"));
        }

        private static LocalizableString L(string name)
        {
            return LocalizableString.Create<CoreLocalizationResource>(name);
        }
    }
}