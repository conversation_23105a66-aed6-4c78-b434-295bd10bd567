using System;
using Core;
using Core.Domain.Entities.Auditing;
using Core.EntityFrameworkCore.Modeling;
using Core.Identity;
using Microsoft.EntityFrameworkCore;

namespace VnisCore.AuthDatabase.Oracle.Domain.Entities
{
    public class DefineEntity : FullAuditedEntity<Guid>
    {
        public Guid? ParentId { get; set; }
        public string GroupCode { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public short? DisplayOrder { get; set; }
        public Guid? TenantId { get; set; }
        public bool IsActive { get; set; }
    }

    public static class VnisCoreAuthDatabaseOracleDbContextModeDefineCreatingExtensions
    {
        public static void ConfigureVnisCoreAuthDatabaseOracleDefineEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<DefineEntity>(b =>
            {
                b.ToTable(AbpIdentityDbProperties.DbTablePrefix + "Define", AbpIdentityDbProperties.DbSchema);
                b.ConfigureByConvention(); //auto configure for the base class props
                b.Property(x => x.GroupCode).IsRequired().HasMaxLength(150);
                b.Property(x => x.Code).IsRequired().HasMaxLength(150);
                b.Property(x => x.Name).IsRequired().HasMaxLength(250);
                b.Property(x => x.Description).HasMaxLength(500);
                b.Property(x => x.IsActive).HasDefaultValue(false);
            });

        }
    }
}
