using Core.Domain;
using Core.EntityFrameworkCore;
using VnisCore.AuthDatabase.Oracle.Domain.Shared;
using VnisCore.AuthDatabase.Oracle.Domain.Shared.MultiTenancy;
using Core.FeatureManagement;
using Core.Identity;
using Core.IdentityServer;
using Core.Modularity;
using Core.MultiTenancy;
using Core.PermissionManagement.IdentityServer;
using Core.SettingManagement;
using Core.TenantManagement;
using Core.Users; //using Core.Emailing;
//using Core.EntityFrameworkCore;

namespace VnisCore.AuthDatabase.Oracle.Domain
{
    [DependsOn(
        typeof(AbpDddDomainModule),
        typeof(AbpEntityFrameworkCoreModule),
        typeof(VnisCoreAuthDatabaseOracleDomainSharedModule),
        typeof(AbpFeatureManagementDomainModule),
        typeof(AbpIdentityDomainModule),
        typeof(AbpIdentityServerDomainModule),
        typeof(AbpPermissionManagementDomainIdentityServerModule),
        typeof(AbpSettingManagementDomainModule),
        typeof(AbpTenantManagementDomainModule),
        typeof(AbpUsersDomainModule)
    )]
    public class VnisCoreAuthDatabaseOracleDomainModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            Configure<AbpMultiTenancyOptions>(options =>
            {
                options.IsEnabled = MultiTenancyConsts.IsEnabled;
            });

//#if DEBUG
//            context.Services.Replace(ServiceDescriptor.Singleton<IEmailSender, NullEmailSender>());
//#endif
        }
    }
}
