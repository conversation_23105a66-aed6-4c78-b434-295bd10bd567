// <auto-generated />
using System;
using Core.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Oracle.EntityFrameworkCore.Metadata;
using VnisCore.AuthDatabase.Oracle.EntityFrameworkCore.DbMigrations.EntityFrameworkCore;

namespace VnisCore.AuthDatabase.Oracle.EntityFrameworkCore.DbMigrations.Migrations
{
    [DbContext(typeof(VnisCoreAuthDatabaseOracleMigrationsDbContext))]
    [Migration("20230602025938_add_Abb AbbFullNameVi AbbAdress in table VnisTenants")]
    partial class add_AbbAbbFullNameViAbbAdressintableVnisTenants
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("_Abp_DatabaseProvider", EfCoreDatabaseProvider.Oracle)
                .HasAnnotation("Relational:MaxIdentifierLength", 128)
                .HasAnnotation("ProductVersion", "5.0.17")
                .HasAnnotation("Oracle:ValueGenerationStrategy", OracleValueGenerationStrategy.IdentityColumn);

            modelBuilder.Entity("Core.FeatureManagement.FeatureValue", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("RAW(16)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("NVARCHAR2(128)");

                    b.Property<string>("ProviderKey")
                        .HasMaxLength(64)
                        .HasColumnType("NVARCHAR2(64)");

                    b.Property<string>("ProviderName")
                        .HasMaxLength(64)
                        .HasColumnType("NVARCHAR2(64)");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("NVARCHAR2(128)");

                    b.HasKey("Id");

                    b.HasIndex("Name", "ProviderName", "ProviderKey");

                    b.ToTable("VnisFeatureValues");
                });

            modelBuilder.Entity("Core.Identity.IdentityClaimType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("RAW(16)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("NVARCHAR2(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<string>("Description")
                        .HasMaxLength(256)
                        .HasColumnType("NVARCHAR2(256)");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("NVARCHAR2(2000)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsStatic")
                        .HasColumnType("NUMBER(1)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("NVARCHAR2(256)");

                    b.Property<string>("Regex")
                        .HasMaxLength(512)
                        .HasColumnType("NVARCHAR2(512)");

                    b.Property<string>("RegexDescription")
                        .HasMaxLength(128)
                        .HasColumnType("NVARCHAR2(128)");

                    b.Property<bool>("Required")
                        .HasColumnType("NUMBER(1)");

                    b.Property<int>("ValueType")
                        .HasColumnType("NUMBER(10)");

                    b.HasKey("Id");

                    b.ToTable("VnisClaimTypes");
                });

            modelBuilder.Entity("Core.Identity.IdentityLinkUser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("RAW(16)");

                    b.Property<Guid?>("SourceTenantId")
                        .HasColumnType("RAW(16)");

                    b.Property<Guid>("SourceUserId")
                        .HasColumnType("RAW(16)");

                    b.Property<Guid?>("TargetTenantId")
                        .HasColumnType("RAW(16)");

                    b.Property<Guid>("TargetUserId")
                        .HasColumnType("RAW(16)");

                    b.HasKey("Id");

                    b.HasIndex("SourceUserId", "SourceTenantId", "TargetUserId", "TargetTenantId")
                        .IsUnique()
                        .HasFilter("\"SourceTenantId\" IS NOT NULL AND \"TargetTenantId\" IS NOT NULL");

                    b.ToTable("VnisLinkUsers");
                });

            modelBuilder.Entity("Core.Identity.IdentityRole", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("RAW(16)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("NVARCHAR2(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<string>("DisplayName")
                        .HasMaxLength(500)
                        .HasColumnType("NVARCHAR2(500)");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("NVARCHAR2(2000)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("NUMBER(1)")
                        .HasColumnName("IsDefault");

                    b.Property<bool>("IsPublic")
                        .HasColumnType("NUMBER(1)")
                        .HasColumnName("IsPublic");

                    b.Property<bool>("IsStatic")
                        .HasColumnType("NUMBER(1)")
                        .HasColumnName("IsStatic");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("NVARCHAR2(256)");

                    b.Property<string>("NormalizedName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("NVARCHAR2(256)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("RAW(16)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName");

                    b.ToTable("VnisRoles");
                });

            modelBuilder.Entity("Core.Identity.IdentityRoleClaim", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("ClaimType")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("NVARCHAR2(256)");

                    b.Property<string>("ClaimValue")
                        .HasMaxLength(1024)
                        .HasColumnType("NVARCHAR2(1024)");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("RAW(16)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("TenantId");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("VnisRoleClaims");
                });

            modelBuilder.Entity("Core.Identity.IdentitySecurityLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("RAW(16)");

                    b.Property<string>("Action")
                        .HasMaxLength(96)
                        .HasColumnType("NVARCHAR2(96)");

                    b.Property<string>("ApplicationName")
                        .HasMaxLength(96)
                        .HasColumnType("NVARCHAR2(96)");

                    b.Property<string>("BrowserInfo")
                        .HasMaxLength(512)
                        .HasColumnType("NVARCHAR2(512)");

                    b.Property<string>("ClientId")
                        .HasMaxLength(64)
                        .HasColumnType("NVARCHAR2(64)");

                    b.Property<string>("ClientIpAddress")
                        .HasMaxLength(64)
                        .HasColumnType("NVARCHAR2(64)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("NVARCHAR2(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<string>("CorrelationId")
                        .HasMaxLength(64)
                        .HasColumnType("NVARCHAR2(64)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("TIMESTAMP(7)");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("NVARCHAR2(2000)")
                        .HasColumnName("ExtraProperties");

                    b.Property<string>("Identity")
                        .HasMaxLength(96)
                        .HasColumnType("NVARCHAR2(96)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("TenantId");

                    b.Property<string>("TenantName")
                        .HasMaxLength(64)
                        .HasColumnType("NVARCHAR2(64)");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("NVARCHAR2(256)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId", "Action");

                    b.HasIndex("TenantId", "ApplicationName");

                    b.HasIndex("TenantId", "Identity");

                    b.HasIndex("TenantId", "UserId");

                    b.ToTable("VnisSecurityLogs");
                });

            modelBuilder.Entity("Core.Identity.IdentityUser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("RAW(16)");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("NUMBER(10)")
                        .HasColumnName("AccessFailedCount");

                    b.Property<string>("CashierCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("NVARCHAR2(50)")
                        .HasDefaultValue("0")
                        .HasColumnName("CashierCode");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("NVARCHAR2(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("DepartmentCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("NVARCHAR2(50)")
                        .HasDefaultValue("0")
                        .HasColumnName("DepartmentCode");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("NVARCHAR2(256)")
                        .HasColumnName("Email");

                    b.Property<bool>("EmailConfirmed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("EmailConfirmed");

                    b.Property<string>("EmployeeCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("NVARCHAR2(50)")
                        .HasDefaultValue("0")
                        .HasColumnName("EmployeeCode");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("NVARCHAR2(2000)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<bool>("IsExternal")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsExternal");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("LastModifierId");

                    b.Property<bool>("LockoutEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("LockoutEnabled");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("TIMESTAMP(7) WITH TIME ZONE");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("NVARCHAR2(256)")
                        .HasColumnName("Name");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("NVARCHAR2(256)")
                        .HasColumnName("NormalizedEmail");

                    b.Property<string>("NormalizedUserName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("NVARCHAR2(256)")
                        .HasColumnName("NormalizedUserName");

                    b.Property<string>("PasswordHash")
                        .HasMaxLength(128)
                        .HasColumnType("NVARCHAR2(128)")
                        .HasColumnName("PasswordHash");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(16)
                        .HasColumnType("NVARCHAR2(16)")
                        .HasColumnName("PhoneNumber");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("PhoneNumberConfirmed");

                    b.Property<string>("SecurityStamp")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("NVARCHAR2(256)")
                        .HasColumnName("SecurityStamp");

                    b.Property<string>("Surname")
                        .HasMaxLength(256)
                        .HasColumnType("NVARCHAR2(256)")
                        .HasColumnName("Surname");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("TenantId");

                    b.Property<bool>("TwoFactorEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("TwoFactorEnabled");

                    b.Property<short>("Type")
                        .HasColumnType("NUMBER(5)");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("NVARCHAR2(256)")
                        .HasColumnName("UserName");

                    b.HasKey("Id");

                    b.HasIndex("Email");

                    b.HasIndex("NormalizedEmail");

                    b.HasIndex("NormalizedUserName");

                    b.HasIndex("UserName");

                    b.ToTable("VnisUsers");
                });

            modelBuilder.Entity("Core.Identity.IdentityUserClaim", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("ClaimType")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("NVARCHAR2(256)");

                    b.Property<string>("ClaimValue")
                        .HasMaxLength(1024)
                        .HasColumnType("NVARCHAR2(1024)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("TenantId");

                    b.Property<Guid>("UserId")
                        .HasColumnType("RAW(16)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("VnisUserClaims");
                });

            modelBuilder.Entity("Core.Identity.IdentityUserLogin", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("LoginProvider")
                        .HasMaxLength(64)
                        .HasColumnType("NVARCHAR2(64)");

                    b.Property<string>("ProviderDisplayName")
                        .HasMaxLength(128)
                        .HasColumnType("NVARCHAR2(128)");

                    b.Property<string>("ProviderKey")
                        .IsRequired()
                        .HasMaxLength(196)
                        .HasColumnType("NVARCHAR2(196)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("TenantId");

                    b.HasKey("UserId", "LoginProvider");

                    b.HasIndex("LoginProvider", "ProviderKey");

                    b.ToTable("VnisUserLogins");
                });

            modelBuilder.Entity("Core.Identity.IdentityUserOrganizationUnit", b =>
                {
                    b.Property<Guid>("OrganizationUnitId")
                        .HasColumnType("RAW(16)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("RAW(16)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("TenantId");

                    b.HasKey("OrganizationUnitId", "UserId");

                    b.HasIndex("UserId", "OrganizationUnitId");

                    b.ToTable("VnisUserOrganizationUnits");
                });

            modelBuilder.Entity("Core.Identity.IdentityUserRole", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("RAW(16)");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("RAW(16)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("TenantId");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId", "UserId");

                    b.ToTable("VnisUserRoles");
                });

            modelBuilder.Entity("Core.Identity.IdentityUserToken", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("LoginProvider")
                        .HasMaxLength(64)
                        .HasColumnType("NVARCHAR2(64)");

                    b.Property<string>("Name")
                        .HasMaxLength(128)
                        .HasColumnType("NVARCHAR2(128)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("TenantId");

                    b.Property<string>("Value")
                        .HasColumnType("NVARCHAR2(2000)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("VnisUserTokens");
                });

            modelBuilder.Entity("Core.Identity.OrganizationUnit", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("RAW(16)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(95)
                        .HasColumnType("NVARCHAR2(95)")
                        .HasColumnName("Code");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("NVARCHAR2(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("NVARCHAR2(128)")
                        .HasColumnName("DisplayName");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("NVARCHAR2(2000)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("LastModifierId");

                    b.Property<Guid?>("ParentId")
                        .HasColumnType("RAW(16)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("RAW(16)");

                    b.HasKey("Id");

                    b.HasIndex("Code");

                    b.HasIndex("ParentId");

                    b.ToTable("VnisOrganizationUnits");
                });

            modelBuilder.Entity("Core.Identity.OrganizationUnitRole", b =>
                {
                    b.Property<Guid>("OrganizationUnitId")
                        .HasColumnType("RAW(16)");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("RAW(16)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("TenantId");

                    b.HasKey("OrganizationUnitId", "RoleId");

                    b.HasIndex("RoleId", "OrganizationUnitId");

                    b.ToTable("VnisOrganizationUnitRoles");
                });

            modelBuilder.Entity("Core.IdentityServer.ApiResources.ApiResource", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("RAW(16)");

                    b.Property<string>("AllowedAccessTokenSigningAlgorithms")
                        .HasMaxLength(100)
                        .HasColumnType("NVARCHAR2(100)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("NVARCHAR2(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("NVARCHAR2(1000)");

                    b.Property<string>("DisplayName")
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("NUMBER(1)");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("NVARCHAR2(2000)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.Property<bool>("ShowInDiscoveryDocument")
                        .HasColumnType("NUMBER(1)");

                    b.HasKey("Id");

                    b.ToTable("VnisApiResources");
                });

            modelBuilder.Entity("Core.IdentityServer.ApiResources.ApiResourceClaim", b =>
                {
                    b.Property<Guid>("ApiResourceId")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("Type")
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.HasKey("ApiResourceId", "Type");

                    b.ToTable("VnisApiResourceClaims");
                });

            modelBuilder.Entity("Core.IdentityServer.ApiResources.ApiResourceProperty", b =>
                {
                    b.Property<Guid>("ApiResourceId")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("Key")
                        .HasMaxLength(250)
                        .HasColumnType("NVARCHAR2(250)");

                    b.Property<string>("Value")
                        .HasMaxLength(300)
                        .HasColumnType("NVARCHAR2(300)");

                    b.HasKey("ApiResourceId", "Key", "Value");

                    b.ToTable("VnisApiResourceProperties");
                });

            modelBuilder.Entity("Core.IdentityServer.ApiResources.ApiResourceScope", b =>
                {
                    b.Property<Guid>("ApiResourceId")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("Scope")
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.HasKey("ApiResourceId", "Scope");

                    b.ToTable("VnisApiResourceScopes");
                });

            modelBuilder.Entity("Core.IdentityServer.ApiResources.ApiResourceSecret", b =>
                {
                    b.Property<Guid>("ApiResourceId")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("Type")
                        .HasMaxLength(250)
                        .HasColumnType("NVARCHAR2(250)");

                    b.Property<string>("Value")
                        .HasMaxLength(300)
                        .HasColumnType("NVARCHAR2(300)");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("NVARCHAR2(1000)");

                    b.Property<DateTime?>("Expiration")
                        .HasColumnType("TIMESTAMP(7)");

                    b.HasKey("ApiResourceId", "Type", "Value");

                    b.ToTable("VnisApiResourceSecrets");
                });

            modelBuilder.Entity("Core.IdentityServer.ApiScopes.ApiScope", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("RAW(16)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("NVARCHAR2(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("NVARCHAR2(1000)");

                    b.Property<string>("DisplayName")
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.Property<bool>("Emphasize")
                        .HasColumnType("NUMBER(1)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("NUMBER(1)");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("NVARCHAR2(2000)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.Property<bool>("Required")
                        .HasColumnType("NUMBER(1)");

                    b.Property<bool>("ShowInDiscoveryDocument")
                        .HasColumnType("NUMBER(1)");

                    b.HasKey("Id");

                    b.ToTable("VnisApiScopes");
                });

            modelBuilder.Entity("Core.IdentityServer.ApiScopes.ApiScopeClaim", b =>
                {
                    b.Property<Guid>("ApiScopeId")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("Type")
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.HasKey("ApiScopeId", "Type");

                    b.ToTable("VnisApiScopeClaims");
                });

            modelBuilder.Entity("Core.IdentityServer.ApiScopes.ApiScopeProperty", b =>
                {
                    b.Property<Guid>("ApiScopeId")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("Key")
                        .HasMaxLength(250)
                        .HasColumnType("NVARCHAR2(250)");

                    b.Property<string>("Value")
                        .HasMaxLength(300)
                        .HasColumnType("NVARCHAR2(300)");

                    b.HasKey("ApiScopeId", "Key", "Value");

                    b.ToTable("VnisApiScopeProperties");
                });

            modelBuilder.Entity("Core.IdentityServer.Clients.Client", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("RAW(16)");

                    b.Property<int>("AbsoluteRefreshTokenLifetime")
                        .HasColumnType("NUMBER(10)");

                    b.Property<int>("AccessTokenLifetime")
                        .HasColumnType("NUMBER(10)");

                    b.Property<int>("AccessTokenType")
                        .HasColumnType("NUMBER(10)");

                    b.Property<bool>("AllowAccessTokensViaBrowser")
                        .HasColumnType("NUMBER(1)");

                    b.Property<bool>("AllowOfflineAccess")
                        .HasColumnType("NUMBER(1)");

                    b.Property<bool>("AllowPlainTextPkce")
                        .HasColumnType("NUMBER(1)");

                    b.Property<bool>("AllowRememberConsent")
                        .HasColumnType("NUMBER(1)");

                    b.Property<string>("AllowedIdentityTokenSigningAlgorithms")
                        .HasMaxLength(100)
                        .HasColumnType("NVARCHAR2(100)");

                    b.Property<bool>("AlwaysIncludeUserClaimsInIdToken")
                        .HasColumnType("NUMBER(1)");

                    b.Property<bool>("AlwaysSendClientClaims")
                        .HasColumnType("NUMBER(1)");

                    b.Property<int>("AuthorizationCodeLifetime")
                        .HasColumnType("NUMBER(10)");

                    b.Property<bool>("BackChannelLogoutSessionRequired")
                        .HasColumnType("NUMBER(1)");

                    b.Property<string>("BackChannelLogoutUri")
                        .HasMaxLength(2000)
                        .HasColumnType("NVARCHAR2(2000)");

                    b.Property<string>("ClientClaimsPrefix")
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.Property<string>("ClientId")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.Property<string>("ClientName")
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.Property<string>("ClientUri")
                        .HasMaxLength(2000)
                        .HasColumnType("NVARCHAR2(2000)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("NVARCHAR2(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<int?>("ConsentLifetime")
                        .HasColumnType("NUMBER(10)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("NVARCHAR2(1000)");

                    b.Property<int>("DeviceCodeLifetime")
                        .HasColumnType("NUMBER(10)");

                    b.Property<bool>("EnableLocalLogin")
                        .HasColumnType("NUMBER(1)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("NUMBER(1)");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("NVARCHAR2(2000)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("FrontChannelLogoutSessionRequired")
                        .HasColumnType("NUMBER(1)");

                    b.Property<string>("FrontChannelLogoutUri")
                        .HasMaxLength(2000)
                        .HasColumnType("NVARCHAR2(2000)");

                    b.Property<int>("IdentityTokenLifetime")
                        .HasColumnType("NUMBER(10)");

                    b.Property<bool>("IncludeJwtId")
                        .HasColumnType("NUMBER(1)");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("LogoUri")
                        .HasMaxLength(2000)
                        .HasColumnType("NVARCHAR2(2000)");

                    b.Property<string>("PairWiseSubjectSalt")
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.Property<string>("ProtocolType")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.Property<int>("RefreshTokenExpiration")
                        .HasColumnType("NUMBER(10)");

                    b.Property<int>("RefreshTokenUsage")
                        .HasColumnType("NUMBER(10)");

                    b.Property<bool>("RequireClientSecret")
                        .HasColumnType("NUMBER(1)");

                    b.Property<bool>("RequireConsent")
                        .HasColumnType("NUMBER(1)");

                    b.Property<bool>("RequirePkce")
                        .HasColumnType("NUMBER(1)");

                    b.Property<bool>("RequireRequestObject")
                        .HasColumnType("NUMBER(1)");

                    b.Property<int>("SlidingRefreshTokenLifetime")
                        .HasColumnType("NUMBER(10)");

                    b.Property<bool>("UpdateAccessTokenClaimsOnRefresh")
                        .HasColumnType("NUMBER(1)");

                    b.Property<string>("UserCodeType")
                        .HasMaxLength(100)
                        .HasColumnType("NVARCHAR2(100)");

                    b.Property<int?>("UserSsoLifetime")
                        .HasColumnType("NUMBER(10)");

                    b.HasKey("Id");

                    b.HasIndex("ClientId");

                    b.ToTable("VnisClients");
                });

            modelBuilder.Entity("Core.IdentityServer.Clients.ClientClaim", b =>
                {
                    b.Property<Guid>("ClientId")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("Type")
                        .HasMaxLength(250)
                        .HasColumnType("NVARCHAR2(250)");

                    b.Property<string>("Value")
                        .HasMaxLength(250)
                        .HasColumnType("NVARCHAR2(250)");

                    b.HasKey("ClientId", "Type", "Value");

                    b.ToTable("VnisClientClaims");
                });

            modelBuilder.Entity("Core.IdentityServer.Clients.ClientCorsOrigin", b =>
                {
                    b.Property<Guid>("ClientId")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("Origin")
                        .HasMaxLength(150)
                        .HasColumnType("NVARCHAR2(150)");

                    b.HasKey("ClientId", "Origin");

                    b.ToTable("VnisClientCorsOrigins");
                });

            modelBuilder.Entity("Core.IdentityServer.Clients.ClientGrantType", b =>
                {
                    b.Property<Guid>("ClientId")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("GrantType")
                        .HasMaxLength(250)
                        .HasColumnType("NVARCHAR2(250)");

                    b.HasKey("ClientId", "GrantType");

                    b.ToTable("VnisClientGrantTypes");
                });

            modelBuilder.Entity("Core.IdentityServer.Clients.ClientIdPRestriction", b =>
                {
                    b.Property<Guid>("ClientId")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("Provider")
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.HasKey("ClientId", "Provider");

                    b.ToTable("VnisClientIdPRestrictions");
                });

            modelBuilder.Entity("Core.IdentityServer.Clients.ClientPostLogoutRedirectUri", b =>
                {
                    b.Property<Guid>("ClientId")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("PostLogoutRedirectUri")
                        .HasMaxLength(2000)
                        .HasColumnType("NVARCHAR2(2000)");

                    b.HasKey("ClientId", "PostLogoutRedirectUri");

                    b.ToTable("VnisClientPostLogoutRedirectUris");
                });

            modelBuilder.Entity("Core.IdentityServer.Clients.ClientProperty", b =>
                {
                    b.Property<Guid>("ClientId")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("Key")
                        .HasMaxLength(250)
                        .HasColumnType("NVARCHAR2(250)");

                    b.Property<string>("Value")
                        .HasMaxLength(2000)
                        .HasColumnType("NVARCHAR2(2000)");

                    b.HasKey("ClientId", "Key", "Value");

                    b.ToTable("VnisClientProperties");
                });

            modelBuilder.Entity("Core.IdentityServer.Clients.ClientRedirectUri", b =>
                {
                    b.Property<Guid>("ClientId")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("RedirectUri")
                        .HasMaxLength(2000)
                        .HasColumnType("NVARCHAR2(2000)");

                    b.HasKey("ClientId", "RedirectUri");

                    b.ToTable("VnisClientRedirectUris");
                });

            modelBuilder.Entity("Core.IdentityServer.Clients.ClientScope", b =>
                {
                    b.Property<Guid>("ClientId")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("Scope")
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.HasKey("ClientId", "Scope");

                    b.ToTable("VnisClientScopes");
                });

            modelBuilder.Entity("Core.IdentityServer.Clients.ClientSecret", b =>
                {
                    b.Property<Guid>("ClientId")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("Type")
                        .HasMaxLength(250)
                        .HasColumnType("NVARCHAR2(250)");

                    b.Property<string>("Value")
                        .HasMaxLength(300)
                        .HasColumnType("NVARCHAR2(300)");

                    b.Property<string>("Description")
                        .HasMaxLength(2000)
                        .HasColumnType("NVARCHAR2(2000)");

                    b.Property<DateTime?>("Expiration")
                        .HasColumnType("TIMESTAMP(7)");

                    b.HasKey("ClientId", "Type", "Value");

                    b.ToTable("VnisClientSecrets");
                });

            modelBuilder.Entity("Core.IdentityServer.Devices.DeviceFlowCodes", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("RAW(16)");

                    b.Property<string>("ClientId")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("NVARCHAR2(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("CreatorId");

                    b.Property<string>("Data")
                        .IsRequired()
                        .HasMaxLength(50000)
                        .HasColumnType("NCLOB");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.Property<string>("DeviceCode")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.Property<DateTime?>("Expiration")
                        .IsRequired()
                        .HasColumnType("TIMESTAMP(7)");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("NVARCHAR2(2000)")
                        .HasColumnName("ExtraProperties");

                    b.Property<string>("SessionId")
                        .HasMaxLength(100)
                        .HasColumnType("NVARCHAR2(100)");

                    b.Property<string>("SubjectId")
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.Property<string>("UserCode")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.HasKey("Id");

                    b.HasIndex("DeviceCode")
                        .IsUnique();

                    b.HasIndex("Expiration");

                    b.HasIndex("UserCode");

                    b.ToTable("VnisDeviceFlowCodes");
                });

            modelBuilder.Entity("Core.IdentityServer.Grants.PersistedGrant", b =>
                {
                    b.Property<string>("Key")
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.Property<string>("ClientId")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("NVARCHAR2(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime?>("ConsumedTime")
                        .HasColumnType("TIMESTAMP(7)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("TIMESTAMP(7)");

                    b.Property<string>("Data")
                        .IsRequired()
                        .HasMaxLength(50000)
                        .HasColumnType("NCLOB");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.Property<DateTime?>("Expiration")
                        .HasColumnType("TIMESTAMP(7)");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("NVARCHAR2(2000)")
                        .HasColumnName("ExtraProperties");

                    b.Property<Guid>("Id")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("SessionId")
                        .HasMaxLength(100)
                        .HasColumnType("NVARCHAR2(100)");

                    b.Property<string>("SubjectId")
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("NVARCHAR2(50)");

                    b.HasKey("Key");

                    b.HasIndex("Expiration");

                    b.HasIndex("SubjectId", "ClientId", "Type");

                    b.HasIndex("SubjectId", "SessionId", "Type");

                    b.ToTable("VnisPersistedGrants");
                });

            modelBuilder.Entity("Core.IdentityServer.IdentityResources.IdentityResource", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("RAW(16)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("NVARCHAR2(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("NVARCHAR2(1000)");

                    b.Property<string>("DisplayName")
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.Property<bool>("Emphasize")
                        .HasColumnType("NUMBER(1)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("NUMBER(1)");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("NVARCHAR2(2000)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.Property<bool>("Required")
                        .HasColumnType("NUMBER(1)");

                    b.Property<bool>("ShowInDiscoveryDocument")
                        .HasColumnType("NUMBER(1)");

                    b.HasKey("Id");

                    b.ToTable("VnisIdentityResources");
                });

            modelBuilder.Entity("Core.IdentityServer.IdentityResources.IdentityResourceClaim", b =>
                {
                    b.Property<Guid>("IdentityResourceId")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("Type")
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR2(200)");

                    b.HasKey("IdentityResourceId", "Type");

                    b.ToTable("VnisIdentityResourceClaims");
                });

            modelBuilder.Entity("Core.IdentityServer.IdentityResources.IdentityResourceProperty", b =>
                {
                    b.Property<Guid>("IdentityResourceId")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("Key")
                        .HasMaxLength(250)
                        .HasColumnType("NVARCHAR2(250)");

                    b.Property<string>("Value")
                        .HasMaxLength(300)
                        .HasColumnType("NVARCHAR2(300)");

                    b.HasKey("IdentityResourceId", "Key", "Value");

                    b.ToTable("VnisIdentityResourceProperties");
                });

            modelBuilder.Entity("Core.PermissionManagement.PermissionGrant", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("RAW(16)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("NVARCHAR2(128)");

                    b.Property<string>("ProviderKey")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("NVARCHAR2(64)");

                    b.Property<string>("ProviderName")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("NVARCHAR2(64)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("RAW(16)");

                    b.HasKey("Id");

                    b.HasIndex("Name", "ProviderName", "ProviderKey");

                    b.ToTable("VnisPermissionGrants");
                });

            modelBuilder.Entity("Core.SettingManagement.Setting", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("RAW(16)");

                    b.Property<string>("Code")
                        .HasMaxLength(128)
                        .HasColumnType("NVARCHAR2(128)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("NVARCHAR2(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("Description")
                        .HasColumnType("NVARCHAR2(2000)");

                    b.Property<string>("ExtraProperties")
                        .IsUnicode(false)
                        .HasColumnType("CLOB")
                        .HasColumnName("ExtraProperties");

                    b.Property<string>("GroupCode")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("NVARCHAR2(128)");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<bool>("IsReadOnly")
                        .HasColumnType("NUMBER(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("NVARCHAR2(128)");

                    b.Property<string>("Options")
                        .HasColumnType("NVARCHAR2(2000)");

                    b.Property<Guid?>("ParentId")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("ProviderKey")
                        .HasMaxLength(64)
                        .HasColumnType("NVARCHAR2(64)");

                    b.Property<string>("ProviderName")
                        .HasMaxLength(64)
                        .HasColumnType("NVARCHAR2(64)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("RAW(16)");

                    b.Property<int>("Type")
                        .HasColumnType("NUMBER(10)");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("NVARCHAR2(2000)");

                    b.HasKey("Id");

                    b.HasIndex("Name", "Code", "ProviderName", "ProviderKey");

                    b.ToTable("VnisSettings");
                });

            modelBuilder.Entity("Core.TenantManagement.Tenant", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("RAW(16)");

                    b.Property<string>("AbbAddress")
                        .HasMaxLength(3000)
                        .HasColumnType("VARCHAR2(3000)");

                    b.Property<string>("AbbFullNameVi")
                        .HasMaxLength(3000)
                        .HasColumnType("VARCHAR2(3000)");

                    b.Property<string>("Address")
                        .HasMaxLength(400)
                        .HasColumnType("NVARCHAR2(400)");

                    b.Property<string>("BankAccount")
                        .HasMaxLength(30)
                        .HasColumnType("NVARCHAR2(30)");

                    b.Property<string>("BankName")
                        .HasMaxLength(400)
                        .HasColumnType("NVARCHAR2(400)");

                    b.Property<string>("BusinessType")
                        .HasMaxLength(500)
                        .HasColumnType("NVARCHAR2(500)");

                    b.Property<string>("City")
                        .HasMaxLength(500)
                        .HasColumnType("NVARCHAR2(500)");

                    b.Property<string>("CodeTaxDepartment")
                        .HasMaxLength(5)
                        .HasColumnType("NVARCHAR2(5)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("NVARCHAR2(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<string>("Country")
                        .HasMaxLength(500)
                        .HasColumnType("NVARCHAR2(500)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("CreatorId");

                    b.Property<DateTime?>("DayOfUser")
                        .HasColumnType("TIMESTAMP(7)");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("District")
                        .HasMaxLength(500)
                        .HasColumnType("NVARCHAR2(500)");

                    b.Property<DateTime?>("EffectiveDeactiveDate")
                        .HasColumnType("TIMESTAMP(7)");

                    b.Property<string>("Emails")
                        .HasColumnType("NVARCHAR2(2000)");

                    b.Property<DateTime?>("ExpireDate")
                        .HasColumnType("TIMESTAMP(7)");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("NVARCHAR2(2000)")
                        .HasColumnName("ExtraProperties");

                    b.Property<string>("Fax")
                        .HasMaxLength(20)
                        .HasColumnType("NVARCHAR2(20)");

                    b.Property<string>("FullNameEn")
                        .HasMaxLength(400)
                        .HasColumnType("NVARCHAR2(400)");

                    b.Property<string>("FullNameVi")
                        .HasMaxLength(400)
                        .HasColumnType("NVARCHAR2(400)");

                    b.Property<bool>("IsCurrent")
                        .HasColumnType("NUMBER(1)");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<short>("IsEnable")
                        .HasColumnType("NUMBER(5)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("LegalName")
                        .HasMaxLength(500)
                        .HasColumnType("NVARCHAR2(500)");

                    b.Property<string>("Metadata")
                        .HasColumnType("NVARCHAR2(2000)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("NVARCHAR2(64)");

                    b.Property<Guid?>("ParentId")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("Phones")
                        .HasMaxLength(20)
                        .HasColumnType("NVARCHAR2(20)");

                    b.Property<string>("PlaceName")
                        .HasMaxLength(50)
                        .HasColumnType("NVARCHAR2(50)");

                    b.Property<string>("TaxCode")
                        .HasMaxLength(14)
                        .HasColumnType("NVARCHAR2(14)");

                    b.Property<string>("TaxDepartment")
                        .HasMaxLength(100)
                        .HasColumnType("NVARCHAR2(100)");

                    b.Property<string>("TenantCode")
                        .HasMaxLength(500)
                        .HasColumnType("NVARCHAR2(500)");

                    b.Property<string>("Website")
                        .HasMaxLength(100)
                        .HasColumnType("NVARCHAR2(100)");

                    b.HasKey("Id");

                    b.HasIndex("Name");

                    b.ToTable("VnisTenants");
                });

            modelBuilder.Entity("Core.TenantManagement.TenantConnectionString", b =>
                {
                    b.Property<Guid>("TenantId")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("Name")
                        .HasMaxLength(64)
                        .HasColumnType("NVARCHAR2(64)");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("NVARCHAR2(1024)");

                    b.HasKey("TenantId", "Name");

                    b.ToTable("VnisTenantConnectionStrings");
                });

            modelBuilder.Entity("VnisCore.AuthDatabase.Oracle.Domain.Entities.DefineEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("RAW(16)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("NVARCHAR2(150)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("NVARCHAR2(500)");

                    b.Property<short?>("DisplayOrder")
                        .HasColumnType("NUMBER(5)");

                    b.Property<string>("GroupCode")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("NVARCHAR2(150)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(1)")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("NVARCHAR2(250)");

                    b.Property<Guid?>("ParentId")
                        .HasColumnType("RAW(16)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("RAW(16)");

                    b.HasKey("Id");

                    b.ToTable("VnisDefine");
                });

            modelBuilder.Entity("VnisCore.AuthDatabase.Oracle.Domain.Entities.LanguageEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("RAW(16)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("NVARCHAR2(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("CreatorId");

                    b.Property<string>("CultureName")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("NVARCHAR2(10)");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(150)
                        .HasColumnType("NVARCHAR2(150)");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("NVARCHAR2(2000)")
                        .HasColumnName("ExtraProperties");

                    b.Property<string>("FlagIcon")
                        .HasMaxLength(50)
                        .HasColumnType("NVARCHAR2(50)");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<bool>("IsEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(1)")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("LastModifierId");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("UiCultureName")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("NVARCHAR2(10)");

                    b.HasKey("Id");

                    b.ToTable("VnisLanguage");
                });

            modelBuilder.Entity("VnisCore.AuthDatabase.Oracle.Domain.Entities.LanguageTextEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("RAW(16)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("DeletionTime");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<Guid>("LanguageId")
                        .HasColumnType("RAW(16)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("NVARCHAR2(500)");

                    b.Property<string>("ResourceName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("NVARCHAR2(500)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("Value")
                        .HasColumnType("NVARCHAR2(2000)");

                    b.HasKey("Id");

                    b.ToTable("VnisLanguageText");
                });

            modelBuilder.Entity("VnisCore.AuthDatabase.Oracle.Domain.Entities.SettingLogEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("RAW(16)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("NVARCHAR2(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("NVARCHAR2(2000)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("NewValue")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("NVARCHAR2(500)");

                    b.Property<string>("OldValue")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("NVARCHAR2(500)");

                    b.Property<Guid>("SettingId")
                        .HasColumnType("RAW(16)");

                    b.HasKey("Id");

                    b.ToTable("VnisSettingLogs");
                });

            modelBuilder.Entity("VnisCore.AuthDatabase.Oracle.Domain.Entities.UserLogEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("RAW(16)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("NVARCHAR2(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("NVARCHAR2(2000)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("NUMBER(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<string>("LabelName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("NVARCHAR2(500)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("TIMESTAMP(7)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("RAW(16)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("NewValue")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("NVARCHAR2(500)");

                    b.Property<string>("OldValue")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("NVARCHAR2(500)");

                    b.Property<string>("PropertyName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("NVARCHAR2(500)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("RAW(16)");

                    b.Property<string>("UserName")
                        .HasColumnType("NVARCHAR2(2000)");

                    b.HasKey("Id");

                    b.ToTable("VnisUserLogs");
                });

            modelBuilder.Entity("Core.Identity.IdentityRoleClaim", b =>
                {
                    b.HasOne("Core.Identity.IdentityRole", null)
                        .WithMany("Claims")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Core.Identity.IdentityUserClaim", b =>
                {
                    b.HasOne("Core.Identity.IdentityUser", null)
                        .WithMany("Claims")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Core.Identity.IdentityUserLogin", b =>
                {
                    b.HasOne("Core.Identity.IdentityUser", null)
                        .WithMany("Logins")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Core.Identity.IdentityUserOrganizationUnit", b =>
                {
                    b.HasOne("Core.Identity.OrganizationUnit", null)
                        .WithMany()
                        .HasForeignKey("OrganizationUnitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Core.Identity.IdentityUser", null)
                        .WithMany("OrganizationUnits")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Core.Identity.IdentityUserRole", b =>
                {
                    b.HasOne("Core.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Core.Identity.IdentityUser", null)
                        .WithMany("Roles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Core.Identity.IdentityUserToken", b =>
                {
                    b.HasOne("Core.Identity.IdentityUser", null)
                        .WithMany("Tokens")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Core.Identity.OrganizationUnit", b =>
                {
                    b.HasOne("Core.Identity.OrganizationUnit", null)
                        .WithMany()
                        .HasForeignKey("ParentId");
                });

            modelBuilder.Entity("Core.Identity.OrganizationUnitRole", b =>
                {
                    b.HasOne("Core.Identity.OrganizationUnit", null)
                        .WithMany("Roles")
                        .HasForeignKey("OrganizationUnitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Core.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Core.IdentityServer.ApiResources.ApiResourceClaim", b =>
                {
                    b.HasOne("Core.IdentityServer.ApiResources.ApiResource", null)
                        .WithMany("UserClaims")
                        .HasForeignKey("ApiResourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Core.IdentityServer.ApiResources.ApiResourceProperty", b =>
                {
                    b.HasOne("Core.IdentityServer.ApiResources.ApiResource", null)
                        .WithMany("Properties")
                        .HasForeignKey("ApiResourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Core.IdentityServer.ApiResources.ApiResourceScope", b =>
                {
                    b.HasOne("Core.IdentityServer.ApiResources.ApiResource", null)
                        .WithMany("Scopes")
                        .HasForeignKey("ApiResourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Core.IdentityServer.ApiResources.ApiResourceSecret", b =>
                {
                    b.HasOne("Core.IdentityServer.ApiResources.ApiResource", null)
                        .WithMany("Secrets")
                        .HasForeignKey("ApiResourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Core.IdentityServer.ApiScopes.ApiScopeClaim", b =>
                {
                    b.HasOne("Core.IdentityServer.ApiScopes.ApiScope", null)
                        .WithMany("UserClaims")
                        .HasForeignKey("ApiScopeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Core.IdentityServer.ApiScopes.ApiScopeProperty", b =>
                {
                    b.HasOne("Core.IdentityServer.ApiScopes.ApiScope", null)
                        .WithMany("Properties")
                        .HasForeignKey("ApiScopeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Core.IdentityServer.Clients.ClientClaim", b =>
                {
                    b.HasOne("Core.IdentityServer.Clients.Client", null)
                        .WithMany("Claims")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Core.IdentityServer.Clients.ClientCorsOrigin", b =>
                {
                    b.HasOne("Core.IdentityServer.Clients.Client", null)
                        .WithMany("AllowedCorsOrigins")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Core.IdentityServer.Clients.ClientGrantType", b =>
                {
                    b.HasOne("Core.IdentityServer.Clients.Client", null)
                        .WithMany("AllowedGrantTypes")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Core.IdentityServer.Clients.ClientIdPRestriction", b =>
                {
                    b.HasOne("Core.IdentityServer.Clients.Client", null)
                        .WithMany("IdentityProviderRestrictions")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Core.IdentityServer.Clients.ClientPostLogoutRedirectUri", b =>
                {
                    b.HasOne("Core.IdentityServer.Clients.Client", null)
                        .WithMany("PostLogoutRedirectUris")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Core.IdentityServer.Clients.ClientProperty", b =>
                {
                    b.HasOne("Core.IdentityServer.Clients.Client", null)
                        .WithMany("Properties")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Core.IdentityServer.Clients.ClientRedirectUri", b =>
                {
                    b.HasOne("Core.IdentityServer.Clients.Client", null)
                        .WithMany("RedirectUris")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Core.IdentityServer.Clients.ClientScope", b =>
                {
                    b.HasOne("Core.IdentityServer.Clients.Client", null)
                        .WithMany("AllowedScopes")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Core.IdentityServer.Clients.ClientSecret", b =>
                {
                    b.HasOne("Core.IdentityServer.Clients.Client", null)
                        .WithMany("ClientSecrets")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Core.IdentityServer.IdentityResources.IdentityResourceClaim", b =>
                {
                    b.HasOne("Core.IdentityServer.IdentityResources.IdentityResource", null)
                        .WithMany("UserClaims")
                        .HasForeignKey("IdentityResourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Core.IdentityServer.IdentityResources.IdentityResourceProperty", b =>
                {
                    b.HasOne("Core.IdentityServer.IdentityResources.IdentityResource", null)
                        .WithMany("Properties")
                        .HasForeignKey("IdentityResourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Core.TenantManagement.TenantConnectionString", b =>
                {
                    b.HasOne("Core.TenantManagement.Tenant", null)
                        .WithMany("ConnectionStrings")
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Core.Identity.IdentityRole", b =>
                {
                    b.Navigation("Claims");
                });

            modelBuilder.Entity("Core.Identity.IdentityUser", b =>
                {
                    b.Navigation("Claims");

                    b.Navigation("Logins");

                    b.Navigation("OrganizationUnits");

                    b.Navigation("Roles");

                    b.Navigation("Tokens");
                });

            modelBuilder.Entity("Core.Identity.OrganizationUnit", b =>
                {
                    b.Navigation("Roles");
                });

            modelBuilder.Entity("Core.IdentityServer.ApiResources.ApiResource", b =>
                {
                    b.Navigation("Properties");

                    b.Navigation("Scopes");

                    b.Navigation("Secrets");

                    b.Navigation("UserClaims");
                });

            modelBuilder.Entity("Core.IdentityServer.ApiScopes.ApiScope", b =>
                {
                    b.Navigation("Properties");

                    b.Navigation("UserClaims");
                });

            modelBuilder.Entity("Core.IdentityServer.Clients.Client", b =>
                {
                    b.Navigation("AllowedCorsOrigins");

                    b.Navigation("AllowedGrantTypes");

                    b.Navigation("AllowedScopes");

                    b.Navigation("Claims");

                    b.Navigation("ClientSecrets");

                    b.Navigation("IdentityProviderRestrictions");

                    b.Navigation("PostLogoutRedirectUris");

                    b.Navigation("Properties");

                    b.Navigation("RedirectUris");
                });

            modelBuilder.Entity("Core.IdentityServer.IdentityResources.IdentityResource", b =>
                {
                    b.Navigation("Properties");

                    b.Navigation("UserClaims");
                });

            modelBuilder.Entity("Core.TenantManagement.Tenant", b =>
                {
                    b.Navigation("ConnectionStrings");
                });
#pragma warning restore 612, 618
        }
    }
}
