using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace VnisCore.AuthDatabase.Oracle.EntityFrameworkCore.DbMigrations.Migrations
{
    public partial class initdatabase : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "VnisApiResources",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    Name = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: false),
                    DisplayName = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: true),
                    Description = table.Column<string>(type: "NVARCHAR2(1000)", maxLength: 1000, nullable: true),
                    Enabled = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    AllowedAccessTokenSigningAlgorithms = table.Column<string>(type: "NVARCHAR2(100)", maxLength: 100, nullable: true),
                    ShowInDiscoveryDocument = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    ExtraProperties = table.Column<string>(type: "NVARCHAR2(2000)", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "NVARCHAR2(40)", maxLength: 40, nullable: true),
                    CreationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: false),
                    CreatorId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    IsDeleted = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisApiResources", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "VnisApiScopes",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    Enabled = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    Name = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: false),
                    DisplayName = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: true),
                    Description = table.Column<string>(type: "NVARCHAR2(1000)", maxLength: 1000, nullable: true),
                    Required = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    Emphasize = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    ShowInDiscoveryDocument = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    ExtraProperties = table.Column<string>(type: "NVARCHAR2(2000)", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "NVARCHAR2(40)", maxLength: 40, nullable: true),
                    CreationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: false),
                    CreatorId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    IsDeleted = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisApiScopes", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "VnisClaimTypes",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    Name = table.Column<string>(type: "NVARCHAR2(256)", maxLength: 256, nullable: false),
                    Required = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    IsStatic = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    Regex = table.Column<string>(type: "NVARCHAR2(512)", maxLength: 512, nullable: true),
                    RegexDescription = table.Column<string>(type: "NVARCHAR2(128)", maxLength: 128, nullable: true),
                    Description = table.Column<string>(type: "NVARCHAR2(256)", maxLength: 256, nullable: true),
                    ValueType = table.Column<int>(type: "NUMBER(10)", nullable: false),
                    ExtraProperties = table.Column<string>(type: "NVARCHAR2(2000)", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "NVARCHAR2(40)", maxLength: 40, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisClaimTypes", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "VnisClients",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    ClientId = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: false),
                    ClientName = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: true),
                    Description = table.Column<string>(type: "NVARCHAR2(1000)", maxLength: 1000, nullable: true),
                    ClientUri = table.Column<string>(type: "NVARCHAR2(2000)", maxLength: 2000, nullable: true),
                    LogoUri = table.Column<string>(type: "NVARCHAR2(2000)", maxLength: 2000, nullable: true),
                    Enabled = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    ProtocolType = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: false),
                    RequireClientSecret = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    RequireConsent = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    AllowRememberConsent = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    AlwaysIncludeUserClaimsInIdToken = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    RequirePkce = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    AllowPlainTextPkce = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    RequireRequestObject = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    AllowAccessTokensViaBrowser = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    FrontChannelLogoutUri = table.Column<string>(type: "NVARCHAR2(2000)", maxLength: 2000, nullable: true),
                    FrontChannelLogoutSessionRequired = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    BackChannelLogoutUri = table.Column<string>(type: "NVARCHAR2(2000)", maxLength: 2000, nullable: true),
                    BackChannelLogoutSessionRequired = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    AllowOfflineAccess = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    IdentityTokenLifetime = table.Column<int>(type: "NUMBER(10)", nullable: false),
                    AllowedIdentityTokenSigningAlgorithms = table.Column<string>(type: "NVARCHAR2(100)", maxLength: 100, nullable: true),
                    AccessTokenLifetime = table.Column<int>(type: "NUMBER(10)", nullable: false),
                    AuthorizationCodeLifetime = table.Column<int>(type: "NUMBER(10)", nullable: false),
                    ConsentLifetime = table.Column<int>(type: "NUMBER(10)", nullable: true),
                    AbsoluteRefreshTokenLifetime = table.Column<int>(type: "NUMBER(10)", nullable: false),
                    SlidingRefreshTokenLifetime = table.Column<int>(type: "NUMBER(10)", nullable: false),
                    RefreshTokenUsage = table.Column<int>(type: "NUMBER(10)", nullable: false),
                    UpdateAccessTokenClaimsOnRefresh = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    RefreshTokenExpiration = table.Column<int>(type: "NUMBER(10)", nullable: false),
                    AccessTokenType = table.Column<int>(type: "NUMBER(10)", nullable: false),
                    EnableLocalLogin = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    IncludeJwtId = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    AlwaysSendClientClaims = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    ClientClaimsPrefix = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: true),
                    PairWiseSubjectSalt = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: true),
                    UserSsoLifetime = table.Column<int>(type: "NUMBER(10)", nullable: true),
                    UserCodeType = table.Column<string>(type: "NVARCHAR2(100)", maxLength: 100, nullable: true),
                    DeviceCodeLifetime = table.Column<int>(type: "NUMBER(10)", nullable: false),
                    ExtraProperties = table.Column<string>(type: "NVARCHAR2(2000)", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "NVARCHAR2(40)", maxLength: 40, nullable: true),
                    CreationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: false),
                    CreatorId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    IsDeleted = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisClients", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "VnisDefine",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    ParentId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    GroupCode = table.Column<string>(type: "NVARCHAR2(150)", maxLength: 150, nullable: false),
                    Code = table.Column<string>(type: "NVARCHAR2(150)", maxLength: 150, nullable: false),
                    Name = table.Column<string>(type: "NVARCHAR2(250)", maxLength: 250, nullable: false),
                    Description = table.Column<string>(type: "NVARCHAR2(500)", maxLength: 500, nullable: true),
                    DisplayOrder = table.Column<short>(type: "NUMBER(5)", nullable: true),
                    TenantId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    IsActive = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: false),
                    CreationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: false),
                    CreatorId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    IsDeleted = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisDefine", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "VnisDeviceFlowCodes",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    DeviceCode = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: false),
                    UserCode = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: false),
                    SubjectId = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: true),
                    SessionId = table.Column<string>(type: "NVARCHAR2(100)", maxLength: 100, nullable: true),
                    ClientId = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: true),
                    Expiration = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: false),
                    Data = table.Column<string>(type: "NCLOB", maxLength: 50000, nullable: false),
                    ExtraProperties = table.Column<string>(type: "NVARCHAR2(2000)", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "NVARCHAR2(40)", maxLength: 40, nullable: true),
                    CreationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: false),
                    CreatorId = table.Column<Guid>(type: "RAW(16)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisDeviceFlowCodes", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "VnisFeatureValues",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    Name = table.Column<string>(type: "NVARCHAR2(128)", maxLength: 128, nullable: false),
                    Value = table.Column<string>(type: "NVARCHAR2(128)", maxLength: 128, nullable: false),
                    ProviderName = table.Column<string>(type: "NVARCHAR2(64)", maxLength: 64, nullable: true),
                    ProviderKey = table.Column<string>(type: "NVARCHAR2(64)", maxLength: 64, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisFeatureValues", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "VnisIdentityResources",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    Name = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: false),
                    DisplayName = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: true),
                    Description = table.Column<string>(type: "NVARCHAR2(1000)", maxLength: 1000, nullable: true),
                    Enabled = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    Required = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    Emphasize = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    ShowInDiscoveryDocument = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    ExtraProperties = table.Column<string>(type: "NVARCHAR2(2000)", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "NVARCHAR2(40)", maxLength: 40, nullable: true),
                    CreationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: false),
                    CreatorId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    IsDeleted = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisIdentityResources", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "VnisLanguage",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    CultureName = table.Column<string>(type: "NVARCHAR2(10)", maxLength: 10, nullable: false),
                    UiCultureName = table.Column<string>(type: "NVARCHAR2(10)", maxLength: 10, nullable: false),
                    DisplayName = table.Column<string>(type: "NVARCHAR2(150)", maxLength: 150, nullable: false),
                    FlagIcon = table.Column<string>(type: "NVARCHAR2(50)", maxLength: 50, nullable: true),
                    IsEnabled = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: false),
                    TenantId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    ExtraProperties = table.Column<string>(type: "NVARCHAR2(2000)", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "NVARCHAR2(40)", maxLength: 40, nullable: true),
                    CreationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: false),
                    CreatorId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    IsDeleted = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisLanguage", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "VnisLanguageText",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    ResourceName = table.Column<string>(type: "NVARCHAR2(500)", maxLength: 500, nullable: false),
                    Name = table.Column<string>(type: "NVARCHAR2(500)", maxLength: 500, nullable: false),
                    Value = table.Column<string>(type: "NVARCHAR2(2000)", nullable: true),
                    LanguageId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    TenantId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    CreationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: false),
                    CreatorId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    IsDeleted = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisLanguageText", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "VnisLinkUsers",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    SourceUserId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    SourceTenantId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    TargetUserId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    TargetTenantId = table.Column<Guid>(type: "RAW(16)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisLinkUsers", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "VnisOrganizationUnits",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    TenantId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    ParentId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    Code = table.Column<string>(type: "NVARCHAR2(95)", maxLength: 95, nullable: false),
                    DisplayName = table.Column<string>(type: "NVARCHAR2(128)", maxLength: 128, nullable: false),
                    ExtraProperties = table.Column<string>(type: "NVARCHAR2(2000)", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "NVARCHAR2(40)", maxLength: 40, nullable: true),
                    CreationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: false),
                    CreatorId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    IsDeleted = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisOrganizationUnits", x => x.Id);
                    table.ForeignKey(
                        name: "FK_VnisOrganizationUnits_VnisOrganizationUnits_ParentId",
                        column: x => x.ParentId,
                        principalTable: "VnisOrganizationUnits",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "VnisPermissionGrants",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    TenantId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    Name = table.Column<string>(type: "NVARCHAR2(128)", maxLength: 128, nullable: false),
                    ProviderName = table.Column<string>(type: "NVARCHAR2(64)", maxLength: 64, nullable: false),
                    ProviderKey = table.Column<string>(type: "NVARCHAR2(64)", maxLength: 64, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisPermissionGrants", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "VnisPersistedGrants",
                columns: table => new
                {
                    Key = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: false),
                    Type = table.Column<string>(type: "NVARCHAR2(50)", maxLength: 50, nullable: false),
                    SubjectId = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: true),
                    SessionId = table.Column<string>(type: "NVARCHAR2(100)", maxLength: 100, nullable: true),
                    ClientId = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: true),
                    CreationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: false),
                    Expiration = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true),
                    ConsumedTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true),
                    Data = table.Column<string>(type: "NCLOB", maxLength: 50000, nullable: false),
                    Id = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    ExtraProperties = table.Column<string>(type: "NVARCHAR2(2000)", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "NVARCHAR2(40)", maxLength: 40, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisPersistedGrants", x => x.Key);
                });

            migrationBuilder.CreateTable(
                name: "VnisRoles",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    TenantId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    Name = table.Column<string>(type: "NVARCHAR2(256)", maxLength: 256, nullable: false),
                    NormalizedName = table.Column<string>(type: "NVARCHAR2(256)", maxLength: 256, nullable: false),
                    DisplayName = table.Column<string>(type: "NVARCHAR2(500)", maxLength: 500, nullable: true),
                    IsDefault = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    IsStatic = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    IsPublic = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    ExtraProperties = table.Column<string>(type: "NVARCHAR2(2000)", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "NVARCHAR2(40)", maxLength: 40, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisRoles", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "VnisSecurityLogs",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    TenantId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    ApplicationName = table.Column<string>(type: "NVARCHAR2(96)", maxLength: 96, nullable: true),
                    Identity = table.Column<string>(type: "NVARCHAR2(96)", maxLength: 96, nullable: true),
                    Action = table.Column<string>(type: "NVARCHAR2(96)", maxLength: 96, nullable: true),
                    UserId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    UserName = table.Column<string>(type: "NVARCHAR2(256)", maxLength: 256, nullable: true),
                    TenantName = table.Column<string>(type: "NVARCHAR2(64)", maxLength: 64, nullable: true),
                    ClientId = table.Column<string>(type: "NVARCHAR2(64)", maxLength: 64, nullable: true),
                    CorrelationId = table.Column<string>(type: "NVARCHAR2(64)", maxLength: 64, nullable: true),
                    ClientIpAddress = table.Column<string>(type: "NVARCHAR2(64)", maxLength: 64, nullable: true),
                    BrowserInfo = table.Column<string>(type: "NVARCHAR2(512)", maxLength: 512, nullable: true),
                    CreationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: false),
                    ExtraProperties = table.Column<string>(type: "NVARCHAR2(2000)", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "NVARCHAR2(40)", maxLength: 40, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisSecurityLogs", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "VnisSettings",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    ParentId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    GroupCode = table.Column<string>(type: "NVARCHAR2(128)", maxLength: 128, nullable: false),
                    Name = table.Column<string>(type: "NVARCHAR2(128)", maxLength: 128, nullable: false),
                    Code = table.Column<string>(type: "NVARCHAR2(128)", maxLength: 128, nullable: true),
                    Value = table.Column<string>(type: "NVARCHAR2(2000)", maxLength: 2000, nullable: false),
                    ProviderName = table.Column<string>(type: "NVARCHAR2(64)", maxLength: 64, nullable: true),
                    ProviderKey = table.Column<string>(type: "NVARCHAR2(64)", maxLength: 64, nullable: true),
                    TenantId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    Options = table.Column<string>(type: "NVARCHAR2(2000)", nullable: true),
                    Type = table.Column<int>(type: "NUMBER(10)", nullable: false),
                    Description = table.Column<string>(type: "NVARCHAR2(2000)", nullable: true),
                    IsReadOnly = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    ExtraProperties = table.Column<string>(type: "CLOB", unicode: false, nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "NVARCHAR2(40)", maxLength: 40, nullable: true),
                    CreationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: false),
                    CreatorId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    IsDeleted = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisSettings", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "VnisTenants",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    TenantCode = table.Column<string>(type: "NVARCHAR2(500)", maxLength: 500, nullable: true),
                    Name = table.Column<string>(type: "NVARCHAR2(64)", maxLength: 64, nullable: false),
                    ParentId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    IsEnable = table.Column<short>(type: "NUMBER(5)", nullable: false),
                    ExpireDate = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true),
                    IsCurrent = table.Column<bool>(type: "NUMBER(1)", nullable: false),
                    TaxCode = table.Column<string>(type: "NVARCHAR2(14)", maxLength: 14, nullable: true),
                    Address = table.Column<string>(type: "NVARCHAR2(400)", maxLength: 400, nullable: true),
                    City = table.Column<string>(type: "NVARCHAR2(500)", maxLength: 500, nullable: true),
                    Country = table.Column<string>(type: "NVARCHAR2(500)", maxLength: 500, nullable: true),
                    District = table.Column<string>(type: "NVARCHAR2(500)", maxLength: 500, nullable: true),
                    FullNameVi = table.Column<string>(type: "NVARCHAR2(400)", maxLength: 400, nullable: true),
                    FullNameEn = table.Column<string>(type: "NVARCHAR2(400)", maxLength: 400, nullable: true),
                    LegalName = table.Column<string>(type: "NVARCHAR2(500)", maxLength: 500, nullable: true),
                    Fax = table.Column<string>(type: "NVARCHAR2(20)", maxLength: 20, nullable: true),
                    BusinessType = table.Column<string>(type: "NVARCHAR2(500)", maxLength: 500, nullable: true),
                    Emails = table.Column<string>(type: "NVARCHAR2(2000)", nullable: true),
                    Phones = table.Column<string>(type: "NVARCHAR2(20)", maxLength: 20, nullable: true),
                    Website = table.Column<string>(type: "NVARCHAR2(100)", maxLength: 100, nullable: true),
                    Metadata = table.Column<string>(type: "NVARCHAR2(2000)", nullable: true),
                    ExtraProperties = table.Column<string>(type: "NVARCHAR2(2000)", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "NVARCHAR2(40)", maxLength: 40, nullable: true),
                    CreationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: false),
                    CreatorId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    IsDeleted = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisTenants", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "VnisUsers",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    TenantId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    UserName = table.Column<string>(type: "NVARCHAR2(256)", maxLength: 256, nullable: false),
                    NormalizedUserName = table.Column<string>(type: "NVARCHAR2(256)", maxLength: 256, nullable: false),
                    Name = table.Column<string>(type: "NVARCHAR2(256)", maxLength: 256, nullable: true),
                    Surname = table.Column<string>(type: "NVARCHAR2(256)", maxLength: 256, nullable: true),
                    EmployeeCode = table.Column<string>(type: "NVARCHAR2(50)", maxLength: 50, nullable: false, defaultValue: "0"),
                    DepartmentCode = table.Column<string>(type: "NVARCHAR2(50)", maxLength: 50, nullable: false, defaultValue: "0"),
                    CashierCode = table.Column<string>(type: "NVARCHAR2(50)", maxLength: 50, nullable: false, defaultValue: "0"),
                    Email = table.Column<string>(type: "NVARCHAR2(256)", maxLength: 256, nullable: false),
                    NormalizedEmail = table.Column<string>(type: "NVARCHAR2(256)", maxLength: 256, nullable: false),
                    EmailConfirmed = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: false),
                    PasswordHash = table.Column<string>(type: "NVARCHAR2(256)", maxLength: 256, nullable: true),
                    SecurityStamp = table.Column<string>(type: "NVARCHAR2(256)", maxLength: 256, nullable: false),
                    IsExternal = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: false),
                    PhoneNumber = table.Column<string>(type: "NVARCHAR2(16)", maxLength: 16, nullable: true),
                    PhoneNumberConfirmed = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: false),
                    TwoFactorEnabled = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: false),
                    LockoutEnd = table.Column<DateTimeOffset>(type: "TIMESTAMP(7) WITH TIME ZONE", nullable: true),
                    LockoutEnabled = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: false),
                    AccessFailedCount = table.Column<int>(type: "NUMBER(10)", nullable: false),
                    Type = table.Column<short>(type: "NUMBER(5)", nullable: false),
                    ExtraProperties = table.Column<string>(type: "NVARCHAR2(2000)", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "NVARCHAR2(40)", maxLength: 40, nullable: true),
                    CreationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: false),
                    CreatorId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    IsDeleted = table.Column<bool>(type: "NUMBER(1)", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisUsers", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "VnisApiResourceClaims",
                columns: table => new
                {
                    Type = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: false),
                    ApiResourceId = table.Column<Guid>(type: "RAW(16)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisApiResourceClaims", x => new { x.ApiResourceId, x.Type });
                    table.ForeignKey(
                        name: "FK_VnisApiResourceClaims_VnisApiResources_ApiResourceId",
                        column: x => x.ApiResourceId,
                        principalTable: "VnisApiResources",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VnisApiResourceProperties",
                columns: table => new
                {
                    ApiResourceId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    Key = table.Column<string>(type: "NVARCHAR2(250)", maxLength: 250, nullable: false),
                    Value = table.Column<string>(type: "NVARCHAR2(300)", maxLength: 300, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisApiResourceProperties", x => new { x.ApiResourceId, x.Key, x.Value });
                    table.ForeignKey(
                        name: "FK_VnisApiResourceProperties_VnisApiResources_ApiResourceId",
                        column: x => x.ApiResourceId,
                        principalTable: "VnisApiResources",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VnisApiResourceScopes",
                columns: table => new
                {
                    ApiResourceId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    Scope = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisApiResourceScopes", x => new { x.ApiResourceId, x.Scope });
                    table.ForeignKey(
                        name: "FK_VnisApiResourceScopes_VnisApiResources_ApiResourceId",
                        column: x => x.ApiResourceId,
                        principalTable: "VnisApiResources",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VnisApiResourceSecrets",
                columns: table => new
                {
                    Type = table.Column<string>(type: "NVARCHAR2(250)", maxLength: 250, nullable: false),
                    Value = table.Column<string>(type: "NVARCHAR2(300)", maxLength: 300, nullable: false),
                    ApiResourceId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    Description = table.Column<string>(type: "NVARCHAR2(1000)", maxLength: 1000, nullable: true),
                    Expiration = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisApiResourceSecrets", x => new { x.ApiResourceId, x.Type, x.Value });
                    table.ForeignKey(
                        name: "FK_VnisApiResourceSecrets_VnisApiResources_ApiResourceId",
                        column: x => x.ApiResourceId,
                        principalTable: "VnisApiResources",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VnisApiScopeClaims",
                columns: table => new
                {
                    Type = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: false),
                    ApiScopeId = table.Column<Guid>(type: "RAW(16)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisApiScopeClaims", x => new { x.ApiScopeId, x.Type });
                    table.ForeignKey(
                        name: "FK_VnisApiScopeClaims_VnisApiScopes_ApiScopeId",
                        column: x => x.ApiScopeId,
                        principalTable: "VnisApiScopes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VnisApiScopeProperties",
                columns: table => new
                {
                    ApiScopeId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    Key = table.Column<string>(type: "NVARCHAR2(250)", maxLength: 250, nullable: false),
                    Value = table.Column<string>(type: "NVARCHAR2(300)", maxLength: 300, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisApiScopeProperties", x => new { x.ApiScopeId, x.Key, x.Value });
                    table.ForeignKey(
                        name: "FK_VnisApiScopeProperties_VnisApiScopes_ApiScopeId",
                        column: x => x.ApiScopeId,
                        principalTable: "VnisApiScopes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VnisClientClaims",
                columns: table => new
                {
                    ClientId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    Type = table.Column<string>(type: "NVARCHAR2(250)", maxLength: 250, nullable: false),
                    Value = table.Column<string>(type: "NVARCHAR2(250)", maxLength: 250, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisClientClaims", x => new { x.ClientId, x.Type, x.Value });
                    table.ForeignKey(
                        name: "FK_VnisClientClaims_VnisClients_ClientId",
                        column: x => x.ClientId,
                        principalTable: "VnisClients",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VnisClientCorsOrigins",
                columns: table => new
                {
                    ClientId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    Origin = table.Column<string>(type: "NVARCHAR2(150)", maxLength: 150, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisClientCorsOrigins", x => new { x.ClientId, x.Origin });
                    table.ForeignKey(
                        name: "FK_VnisClientCorsOrigins_VnisClients_ClientId",
                        column: x => x.ClientId,
                        principalTable: "VnisClients",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VnisClientGrantTypes",
                columns: table => new
                {
                    ClientId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    GrantType = table.Column<string>(type: "NVARCHAR2(250)", maxLength: 250, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisClientGrantTypes", x => new { x.ClientId, x.GrantType });
                    table.ForeignKey(
                        name: "FK_VnisClientGrantTypes_VnisClients_ClientId",
                        column: x => x.ClientId,
                        principalTable: "VnisClients",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VnisClientIdPRestrictions",
                columns: table => new
                {
                    ClientId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    Provider = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisClientIdPRestrictions", x => new { x.ClientId, x.Provider });
                    table.ForeignKey(
                        name: "FK_VnisClientIdPRestrictions_VnisClients_ClientId",
                        column: x => x.ClientId,
                        principalTable: "VnisClients",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VnisClientPostLogoutRedirectUris",
                columns: table => new
                {
                    ClientId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    PostLogoutRedirectUri = table.Column<string>(type: "NVARCHAR2(2000)", maxLength: 2000, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisClientPostLogoutRedirectUris", x => new { x.ClientId, x.PostLogoutRedirectUri });
                    table.ForeignKey(
                        name: "FK_VnisClientPostLogoutRedirectUris_VnisClients_ClientId",
                        column: x => x.ClientId,
                        principalTable: "VnisClients",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VnisClientProperties",
                columns: table => new
                {
                    ClientId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    Key = table.Column<string>(type: "NVARCHAR2(250)", maxLength: 250, nullable: false),
                    Value = table.Column<string>(type: "NVARCHAR2(2000)", maxLength: 2000, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisClientProperties", x => new { x.ClientId, x.Key, x.Value });
                    table.ForeignKey(
                        name: "FK_VnisClientProperties_VnisClients_ClientId",
                        column: x => x.ClientId,
                        principalTable: "VnisClients",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VnisClientRedirectUris",
                columns: table => new
                {
                    ClientId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    RedirectUri = table.Column<string>(type: "NVARCHAR2(2000)", maxLength: 2000, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisClientRedirectUris", x => new { x.ClientId, x.RedirectUri });
                    table.ForeignKey(
                        name: "FK_VnisClientRedirectUris_VnisClients_ClientId",
                        column: x => x.ClientId,
                        principalTable: "VnisClients",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VnisClientScopes",
                columns: table => new
                {
                    ClientId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    Scope = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisClientScopes", x => new { x.ClientId, x.Scope });
                    table.ForeignKey(
                        name: "FK_VnisClientScopes_VnisClients_ClientId",
                        column: x => x.ClientId,
                        principalTable: "VnisClients",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VnisClientSecrets",
                columns: table => new
                {
                    Type = table.Column<string>(type: "NVARCHAR2(250)", maxLength: 250, nullable: false),
                    Value = table.Column<string>(type: "NVARCHAR2(300)", maxLength: 300, nullable: false),
                    ClientId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    Description = table.Column<string>(type: "NVARCHAR2(2000)", maxLength: 2000, nullable: true),
                    Expiration = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisClientSecrets", x => new { x.ClientId, x.Type, x.Value });
                    table.ForeignKey(
                        name: "FK_VnisClientSecrets_VnisClients_ClientId",
                        column: x => x.ClientId,
                        principalTable: "VnisClients",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VnisIdentityResourceClaims",
                columns: table => new
                {
                    Type = table.Column<string>(type: "NVARCHAR2(200)", maxLength: 200, nullable: false),
                    IdentityResourceId = table.Column<Guid>(type: "RAW(16)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisIdentityResourceClaims", x => new { x.IdentityResourceId, x.Type });
                    table.ForeignKey(
                        name: "FK_VnisIdentityResourceClaims_VnisIdentityResources_IdentityResourceId",
                        column: x => x.IdentityResourceId,
                        principalTable: "VnisIdentityResources",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VnisIdentityResourceProperties",
                columns: table => new
                {
                    IdentityResourceId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    Key = table.Column<string>(type: "NVARCHAR2(250)", maxLength: 250, nullable: false),
                    Value = table.Column<string>(type: "NVARCHAR2(300)", maxLength: 300, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisIdentityResourceProperties", x => new { x.IdentityResourceId, x.Key, x.Value });
                    table.ForeignKey(
                        name: "FK_VnisIdentityResourceProperties_VnisIdentityResources_IdentityResourceId",
                        column: x => x.IdentityResourceId,
                        principalTable: "VnisIdentityResources",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VnisOrganizationUnitRoles",
                columns: table => new
                {
                    RoleId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    OrganizationUnitId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    TenantId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: false),
                    CreatorId = table.Column<Guid>(type: "RAW(16)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisOrganizationUnitRoles", x => new { x.OrganizationUnitId, x.RoleId });
                    table.ForeignKey(
                        name: "FK_VnisOrganizationUnitRoles_VnisOrganizationUnits_OrganizationUnitId",
                        column: x => x.OrganizationUnitId,
                        principalTable: "VnisOrganizationUnits",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_VnisOrganizationUnitRoles_VnisRoles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "VnisRoles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VnisRoleClaims",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    RoleId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    TenantId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    ClaimType = table.Column<string>(type: "NVARCHAR2(256)", maxLength: 256, nullable: false),
                    ClaimValue = table.Column<string>(type: "NVARCHAR2(1024)", maxLength: 1024, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisRoleClaims", x => x.Id);
                    table.ForeignKey(
                        name: "FK_VnisRoleClaims_VnisRoles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "VnisRoles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VnisTenantConnectionStrings",
                columns: table => new
                {
                    TenantId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    Name = table.Column<string>(type: "NVARCHAR2(64)", maxLength: 64, nullable: false),
                    Value = table.Column<string>(type: "NVARCHAR2(1024)", maxLength: 1024, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisTenantConnectionStrings", x => new { x.TenantId, x.Name });
                    table.ForeignKey(
                        name: "FK_VnisTenantConnectionStrings_VnisTenants_TenantId",
                        column: x => x.TenantId,
                        principalTable: "VnisTenants",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VnisUserClaims",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    UserId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    TenantId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    ClaimType = table.Column<string>(type: "NVARCHAR2(256)", maxLength: 256, nullable: false),
                    ClaimValue = table.Column<string>(type: "NVARCHAR2(1024)", maxLength: 1024, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisUserClaims", x => x.Id);
                    table.ForeignKey(
                        name: "FK_VnisUserClaims_VnisUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "VnisUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VnisUserLogins",
                columns: table => new
                {
                    UserId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    LoginProvider = table.Column<string>(type: "NVARCHAR2(64)", maxLength: 64, nullable: false),
                    TenantId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    ProviderKey = table.Column<string>(type: "NVARCHAR2(196)", maxLength: 196, nullable: false),
                    ProviderDisplayName = table.Column<string>(type: "NVARCHAR2(128)", maxLength: 128, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisUserLogins", x => new { x.UserId, x.LoginProvider });
                    table.ForeignKey(
                        name: "FK_VnisUserLogins_VnisUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "VnisUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VnisUserOrganizationUnits",
                columns: table => new
                {
                    UserId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    OrganizationUnitId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    TenantId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "TIMESTAMP(7)", nullable: false),
                    CreatorId = table.Column<Guid>(type: "RAW(16)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisUserOrganizationUnits", x => new { x.OrganizationUnitId, x.UserId });
                    table.ForeignKey(
                        name: "FK_VnisUserOrganizationUnits_VnisOrganizationUnits_OrganizationUnitId",
                        column: x => x.OrganizationUnitId,
                        principalTable: "VnisOrganizationUnits",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_VnisUserOrganizationUnits_VnisUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "VnisUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VnisUserRoles",
                columns: table => new
                {
                    UserId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    RoleId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    TenantId = table.Column<Guid>(type: "RAW(16)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisUserRoles", x => new { x.UserId, x.RoleId });
                    table.ForeignKey(
                        name: "FK_VnisUserRoles_VnisRoles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "VnisRoles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_VnisUserRoles_VnisUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "VnisUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VnisUserTokens",
                columns: table => new
                {
                    UserId = table.Column<Guid>(type: "RAW(16)", nullable: false),
                    LoginProvider = table.Column<string>(type: "NVARCHAR2(64)", maxLength: 64, nullable: false),
                    Name = table.Column<string>(type: "NVARCHAR2(128)", maxLength: 128, nullable: false),
                    TenantId = table.Column<Guid>(type: "RAW(16)", nullable: true),
                    Value = table.Column<string>(type: "NVARCHAR2(2000)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VnisUserTokens", x => new { x.UserId, x.LoginProvider, x.Name });
                    table.ForeignKey(
                        name: "FK_VnisUserTokens_VnisUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "VnisUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_VnisClients_ClientId",
                table: "VnisClients",
                column: "ClientId");

            migrationBuilder.CreateIndex(
                name: "IX_VnisDeviceFlowCodes_DeviceCode",
                table: "VnisDeviceFlowCodes",
                column: "DeviceCode",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_VnisDeviceFlowCodes_Expiration",
                table: "VnisDeviceFlowCodes",
                column: "Expiration");

            migrationBuilder.CreateIndex(
                name: "IX_VnisDeviceFlowCodes_UserCode",
                table: "VnisDeviceFlowCodes",
                column: "UserCode");

            migrationBuilder.CreateIndex(
                name: "IX_VnisFeatureValues_Name_ProviderName_ProviderKey",
                table: "VnisFeatureValues",
                columns: new[] { "Name", "ProviderName", "ProviderKey" });

            migrationBuilder.CreateIndex(
                name: "IX_VnisLinkUsers_SourceUserId_SourceTenantId_TargetUserId_TargetTenantId",
                table: "VnisLinkUsers",
                columns: new[] { "SourceUserId", "SourceTenantId", "TargetUserId", "TargetTenantId" },
                unique: true,
                filter: "\"SourceTenantId\" IS NOT NULL AND \"TargetTenantId\" IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_VnisOrganizationUnitRoles_RoleId_OrganizationUnitId",
                table: "VnisOrganizationUnitRoles",
                columns: new[] { "RoleId", "OrganizationUnitId" });

            migrationBuilder.CreateIndex(
                name: "IX_VnisOrganizationUnits_Code",
                table: "VnisOrganizationUnits",
                column: "Code");

            migrationBuilder.CreateIndex(
                name: "IX_VnisOrganizationUnits_ParentId",
                table: "VnisOrganizationUnits",
                column: "ParentId");

            migrationBuilder.CreateIndex(
                name: "IX_VnisPermissionGrants_Name_ProviderName_ProviderKey",
                table: "VnisPermissionGrants",
                columns: new[] { "Name", "ProviderName", "ProviderKey" });

            migrationBuilder.CreateIndex(
                name: "IX_VnisPersistedGrants_Expiration",
                table: "VnisPersistedGrants",
                column: "Expiration");

            migrationBuilder.CreateIndex(
                name: "IX_VnisPersistedGrants_SubjectId_ClientId_Type",
                table: "VnisPersistedGrants",
                columns: new[] { "SubjectId", "ClientId", "Type" });

            migrationBuilder.CreateIndex(
                name: "IX_VnisPersistedGrants_SubjectId_SessionId_Type",
                table: "VnisPersistedGrants",
                columns: new[] { "SubjectId", "SessionId", "Type" });

            migrationBuilder.CreateIndex(
                name: "IX_VnisRoleClaims_RoleId",
                table: "VnisRoleClaims",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_VnisRoles_NormalizedName",
                table: "VnisRoles",
                column: "NormalizedName");

            migrationBuilder.CreateIndex(
                name: "IX_VnisSecurityLogs_TenantId_Action",
                table: "VnisSecurityLogs",
                columns: new[] { "TenantId", "Action" });

            migrationBuilder.CreateIndex(
                name: "IX_VnisSecurityLogs_TenantId_ApplicationName",
                table: "VnisSecurityLogs",
                columns: new[] { "TenantId", "ApplicationName" });

            migrationBuilder.CreateIndex(
                name: "IX_VnisSecurityLogs_TenantId_Identity",
                table: "VnisSecurityLogs",
                columns: new[] { "TenantId", "Identity" });

            migrationBuilder.CreateIndex(
                name: "IX_VnisSecurityLogs_TenantId_UserId",
                table: "VnisSecurityLogs",
                columns: new[] { "TenantId", "UserId" });

            migrationBuilder.CreateIndex(
                name: "IX_VnisSettings_Name_Code_ProviderName_ProviderKey",
                table: "VnisSettings",
                columns: new[] { "Name", "Code", "ProviderName", "ProviderKey" });

            migrationBuilder.CreateIndex(
                name: "IX_VnisTenants_Name",
                table: "VnisTenants",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_VnisUserClaims_UserId",
                table: "VnisUserClaims",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_VnisUserLogins_LoginProvider_ProviderKey",
                table: "VnisUserLogins",
                columns: new[] { "LoginProvider", "ProviderKey" });

            migrationBuilder.CreateIndex(
                name: "IX_VnisUserOrganizationUnits_UserId_OrganizationUnitId",
                table: "VnisUserOrganizationUnits",
                columns: new[] { "UserId", "OrganizationUnitId" });

            migrationBuilder.CreateIndex(
                name: "IX_VnisUserRoles_RoleId_UserId",
                table: "VnisUserRoles",
                columns: new[] { "RoleId", "UserId" });

            migrationBuilder.CreateIndex(
                name: "IX_VnisUsers_Email",
                table: "VnisUsers",
                column: "Email");

            migrationBuilder.CreateIndex(
                name: "IX_VnisUsers_NormalizedEmail",
                table: "VnisUsers",
                column: "NormalizedEmail");

            migrationBuilder.CreateIndex(
                name: "IX_VnisUsers_NormalizedUserName",
                table: "VnisUsers",
                column: "NormalizedUserName");

            migrationBuilder.CreateIndex(
                name: "IX_VnisUsers_UserName",
                table: "VnisUsers",
                column: "UserName");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "VnisApiResourceClaims");

            migrationBuilder.DropTable(
                name: "VnisApiResourceProperties");

            migrationBuilder.DropTable(
                name: "VnisApiResourceScopes");

            migrationBuilder.DropTable(
                name: "VnisApiResourceSecrets");

            migrationBuilder.DropTable(
                name: "VnisApiScopeClaims");

            migrationBuilder.DropTable(
                name: "VnisApiScopeProperties");

            migrationBuilder.DropTable(
                name: "VnisClaimTypes");

            migrationBuilder.DropTable(
                name: "VnisClientClaims");

            migrationBuilder.DropTable(
                name: "VnisClientCorsOrigins");

            migrationBuilder.DropTable(
                name: "VnisClientGrantTypes");

            migrationBuilder.DropTable(
                name: "VnisClientIdPRestrictions");

            migrationBuilder.DropTable(
                name: "VnisClientPostLogoutRedirectUris");

            migrationBuilder.DropTable(
                name: "VnisClientProperties");

            migrationBuilder.DropTable(
                name: "VnisClientRedirectUris");

            migrationBuilder.DropTable(
                name: "VnisClientScopes");

            migrationBuilder.DropTable(
                name: "VnisClientSecrets");

            migrationBuilder.DropTable(
                name: "VnisDefine");

            migrationBuilder.DropTable(
                name: "VnisDeviceFlowCodes");

            migrationBuilder.DropTable(
                name: "VnisFeatureValues");

            migrationBuilder.DropTable(
                name: "VnisIdentityResourceClaims");

            migrationBuilder.DropTable(
                name: "VnisIdentityResourceProperties");

            migrationBuilder.DropTable(
                name: "VnisLanguage");

            migrationBuilder.DropTable(
                name: "VnisLanguageText");

            migrationBuilder.DropTable(
                name: "VnisLinkUsers");

            migrationBuilder.DropTable(
                name: "VnisOrganizationUnitRoles");

            migrationBuilder.DropTable(
                name: "VnisPermissionGrants");

            migrationBuilder.DropTable(
                name: "VnisPersistedGrants");

            migrationBuilder.DropTable(
                name: "VnisRoleClaims");

            migrationBuilder.DropTable(
                name: "VnisSecurityLogs");

            migrationBuilder.DropTable(
                name: "VnisSettings");

            migrationBuilder.DropTable(
                name: "VnisTenantConnectionStrings");

            migrationBuilder.DropTable(
                name: "VnisUserClaims");

            migrationBuilder.DropTable(
                name: "VnisUserLogins");

            migrationBuilder.DropTable(
                name: "VnisUserOrganizationUnits");

            migrationBuilder.DropTable(
                name: "VnisUserRoles");

            migrationBuilder.DropTable(
                name: "VnisUserTokens");

            migrationBuilder.DropTable(
                name: "VnisApiResources");

            migrationBuilder.DropTable(
                name: "VnisApiScopes");

            migrationBuilder.DropTable(
                name: "VnisClients");

            migrationBuilder.DropTable(
                name: "VnisIdentityResources");

            migrationBuilder.DropTable(
                name: "VnisTenants");

            migrationBuilder.DropTable(
                name: "VnisOrganizationUnits");

            migrationBuilder.DropTable(
                name: "VnisRoles");

            migrationBuilder.DropTable(
                name: "VnisUsers");
        }
    }
}
