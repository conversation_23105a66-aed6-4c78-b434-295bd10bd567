using Microsoft.EntityFrameworkCore.Migrations;

namespace VnisCore.AuthDatabase.Oracle.EntityFrameworkCore.DbMigrations.Migrations
{
    public partial class changefieldtoactiontopropertyname : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Action",
                table: "VnisUseLogs");

            migrationBuilder.AddColumn<string>(
                name: "PropertyName",
                table: "VnisUseLogs",
                type: "NVARCHAR2(500)",
                maxLength: 500,
                nullable: false,
                defaultValue: "");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PropertyName",
                table: "VnisUseLogs");

            migrationBuilder.AddColumn<int>(
                name: "Action",
                table: "VnisUseLogs",
                type: "NUMBER(10)",
                nullable: false,
                defaultValue: 0);
        }
    }
}
