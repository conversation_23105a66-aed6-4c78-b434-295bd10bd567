ALTER TABLE "VnisUsers" DROP COLUMN "ApproveStatus"
/

ALTER TABLE "VnisUsers" DROP COLUMN "Status"
/

INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES (N'20240223061659_20240223_VCBINNB_982', N'5.0.17')
/

UPDATE 
"VnisSettings" vs
SET vs."ExtraProperties" = '{"key":"{\"Font\":\"Tahoma\",\"FontSize\":\"12\",\"Colums\":[{\"Field\":\"STT\",\"Label\":\"Vnis.FE.Setting.Lb.UserReport.ExtraProperties.STT\",\"Type\":\"Int32\",\"Value\":null,\"Alignment\":\"Left\",\"IsEnable\":true},{\"Field\":\"UserName\",\"Label\":\"Vnis.FE.Setting.Lb.UserReport.ExtraProperties.UserName\",\"Type\":\"String\",\"Value\":null,\"Alignment\":\"Left\",\"IsEnable\":true},{\"Field\":\"Name\",\"Label\":\"Vnis.FE.Setting.Lb.UserReport.ExtraProperties.Name\",\"Type\":\"String\",\"Value\":null,\"Alignment\":\"Left\",\"IsEnable\":true},{\"Field\":\"Email\",\"Label\":\"Vnis.FE.Setting.Lb.UserReport.ExtraProperties.Email\",\"Type\":\"String\",\"Value\":null,\"Alignment\":\"Center\",\"IsEnable\":true},{\"Field\":\"PhoneNumber\",\"Label\":\"Vnis.FE.Setting.Lb.UserReport.ExtraProperties.PhoneNumber\",\"Type\":\"String\",\"Value\":null,\"Alignment\":\"Right\",\"IsEnable\":true},{\"Field\":\"CashierCode\",\"Label\":\"Vnis.FE.Setting.Lb.UserReport.ExtraProperties.CashierCode\",\"Type\":\"String\",\"Value\":null,\"Alignment\":\"Right\",\"IsEnable\":true},{\"Field\":\"Status\",\"Label\":\"Vnis.FE.Setting.Lb.UserReport.ExtraProperties.Status\",\"Type\":\"String\",\"Value\":null,\"Alignment\":\"Left\",\"IsEnable\":true}]}"}'
WHERE vs."Id" = '793B681E052247419CC6FDBFF192FA49'
/

DELETE FROM "__EFMigrationsHistory"
WHERE "MigrationId" = N'20240223061659_20240223_VCBINNB_982'

DELETE FROM "__EFMigrationsHistory"
WHERE "MigrationId" = N'20240307095236_20240307_VCBINNB_982'
/

