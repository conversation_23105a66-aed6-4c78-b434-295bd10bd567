using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using VnisCore.AuthDatabase.Oracle.Domain.Data;
using Core.DependencyInjection;

namespace VnisCore.AuthDatabase.Oracle.EntityFrameworkCore.DbMigrations.EntityFrameworkCore
{
    public class AuthDatabaseEntityFrameworkCoreOracleEinvoiceDbSchemaMigrator
        : IVnisCoreAuthDatabaseOracleDbSchemaMigrator, ITransientDependency
    {
        private readonly IServiceProvider _serviceProvider;

        public AuthDatabaseEntityFrameworkCoreOracleEinvoiceDbSchemaMigrator(
            IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public async Task MigrateAsync()
        {
            /* We intentionally resolving the cmsMigrationsDbContext
             * from IServiceProvider (instead of directly injecting it)
             * to properly get the connection string of the current tenant in the
             * current scope.
             */

            await _serviceProvider
                .GetRequiredService<VnisCoreAuthDatabaseOracleMigrationsDbContext>()
                .Database
                .MigrateAsync();
        }
    }
}