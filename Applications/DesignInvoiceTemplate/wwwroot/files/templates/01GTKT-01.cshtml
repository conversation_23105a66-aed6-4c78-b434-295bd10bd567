@using DesignInvoiceTemplate.Extensions
@using RazorEngine.Text
@using System;
@using System.Linq

@model DesignInvoiceTemplate.Models.TypeModels._01GTKT.BaseDesign01Model

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- Core, khong duoc sua -->
    <style>
        *, :after, :before {
            box-sizing: border-box;
        }

        html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video {
            margin: 0;
            padding: 0;
        }

        article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
            display: block;
        }

        html, body {
            font-family: @Model.FontFamily;
            /*background-color: gray;*/
            font-size: @(Model.FontSize)px;
            /*width: 1240px;
            height: 1754px;*/
            margin: 0 auto;
            color: @Model.FontColor;
        }

        /***Always insert a page break before the element***/
        .pb_before {
            page-break-before: always !important;
        }

        /***Always insert a page break after the element***/
        .pb_after {
            page-break-after: always !important;
        }

        /***Avoid page break before the element (if possible)***/
        .pb_before_avoid {
            page-break-before: avoid !important;
        }

        /***Avoid page break after the element (if possible)***/
        .pb_after_avoid {
            page-break-after: avoid !important;
        }

        /* Avoid page break inside the element (if possible) */
        .pbi_avoid {
            page-break-inside: avoid !important;
        }

        table {
            word-break: break-word;
            width: 100%;
        }

            table > tbody > tr {
                page-break-inside: avoid;
            }

            /*Lap header 1 lan - footer 1 lan*/
            table.one-header-one-footer > thead {
                display: table-row-group;
            }

            table.one-header-one-footer > tfoot {
                display: table-row-group;
            }


            /*Lap header n lan - footer 1 lan*/
            table.many-header-one-footer > tbody > tr {
                page-break-inside: avoid;
            }

            table.many-header-one-footer > tfoot {
                display: table-row-group;
            }
    </style>

    <style>
        table {
            border-collapse: collapse;
        }

        /************* Seller *************/
        .seller {
            border-bottom: 1px solid black;
        }

            .seller p {
                line-height: 26px;
            }

        .logo {
            width: 25%;
        }

        .seller-full-name {
            color: @(Model.MetaData.SellerFullName.UseFontColor ? Model.MetaData.SellerFullName.FontColor : Model.FontColor);
            font-style: @Model.MetaData.SellerFullName.FontStyle;
            font-size: @(Model.MetaData.SellerFullName.UseFontSize ? Model.MetaData.SellerFullName.FontSize : Model.FontSize)px;
            font-weight: @Model.MetaData.SellerFullName.FontWeight;
            text-transform: uppercase;
            }

        .seller-address {
            color: @(Model.MetaData.SellerAddress.UseFontColor ? Model.MetaData.SellerAddress.FontColor : Model.FontColor);
            font-style: @Model.MetaData.SellerAddress.FontStyle;
            font-size: @(Model.MetaData.SellerAddress.UseFontSize ? Model.MetaData.SellerAddress.FontSize : Model.FontSize)px;
            font-weight: @Model.MetaData.SellerAddress.FontWeight;
        }

        .seller-phone-number {
            float: left;
            width: 40%;
            color: @(Model.MetaData.SellerPhoneNumber.UseFontColor ? Model.MetaData.SellerPhoneNumber.FontColor : Model.FontColor);
            font-style: @Model.MetaData.SellerPhoneNumber.FontStyle;
            font-size: @(Model.MetaData.SellerPhoneNumber.UseFontSize ? Model.MetaData.SellerPhoneNumber.FontSize : Model.FontSize)px;
            font-weight: @Model.MetaData.SellerPhoneNumber.FontWeight;
        }

        .seller-fax-number {
            width: 60%;
            float: left;
            color: @(Model.MetaData.SellerFaxNumber.UseFontColor ? Model.MetaData.SellerFaxNumber.FontColor : Model.FontColor);
            font-style: @Model.MetaData.SellerFaxNumber.FontStyle;
            font-size: @(Model.MetaData.SellerFaxNumber.UseFontSize ? Model.MetaData.SellerFaxNumber.FontSize : Model.FontSize)px;
            font-weight: @Model.MetaData.SellerFaxNumber.FontWeight;
        }

        .seller-tax-code {
            width: 100%;
            float: left;
            color: @(Model.MetaData.SellerTaxCode.UseFontColor ? Model.MetaData.SellerTaxCode.FontColor : Model.FontColor);
            font-style: @Model.MetaData.SellerTaxCode.FontStyle;
            font-size: @(Model.MetaData.SellerTaxCode.UseFontSize ? Model.MetaData.SellerTaxCode.FontSize : Model.FontSize)px;
            font-weight: @Model.MetaData.SellerTaxCode.FontWeight;
        }

        /************* Invoice Identity ***************/
        table.invoice-identity {
            margin-top: 20px;
        }

        table.invoice-identity tbody tr td {
            vertical-align: top;
        }

        .invoice-identity {
            border-bottom: 1px solid black;
        }

        .qr-code {
            width: 25%;
        }

        .qr-code-value {
            margin-left: 20px;
            width: 60%;
            height: 120px;
            object-fit: contain;
        }

        .invoice-name {
            text-align: center;
            width: 50%;
            padding: 10px;
        }

            .invoice-name p {
                line-height: 30px;
            }

        .invoice-template-name {
            color: @(Model.MetaData.InvoiceTemplateName.UseFontColor ? Model.MetaData.InvoiceTemplateName.FontColor : Model.FontColor);
            font-style: @Model.MetaData.InvoiceTemplateName.FontStyle;
            font-size: @(Model.MetaData.InvoiceTemplateName.UseFontSize ? Model.MetaData.InvoiceTemplateName.FontSize : Model.FontSize)px;
            font-weight: @Model.MetaData.InvoiceTemplateName.FontWeight;
            text-transform: uppercase;
        }

        .invoice-date {
            color: @(Model.MetaData.InvoiceDate.UseFontColor ? Model.MetaData.InvoiceDate.FontColor : Model.FontColor);
            font-style: @Model.MetaData.InvoiceDate.FontStyle;
            font-size: @(Model.MetaData.InvoiceDate.UseFontSize ? Model.MetaData.InvoiceDate.FontSize : Model.FontSize)px;
            font-weight: @Model.MetaData.InvoiceDate.FontWeight;
        }

        .id-transaction {
            color: @(Model.MetaData.TransactionId.UseFontColor ? Model.MetaData.TransactionId.FontColor : Model.FontColor);
            font-style: @Model.MetaData.TransactionId.FontStyle;
            font-size: @(Model.MetaData.TransactionId.UseFontSize ? Model.MetaData.TransactionId.FontSize : Model.FontSize)px;
            font-weight: @Model.MetaData.TransactionId.FontWeight;
        }

        .invoice-template td {
            line-height: 30px;
        }

        .template-no {
            color: @(Model.MetaData.TemplateNo.UseFontColor ? Model.MetaData.TemplateNo.FontColor : Model.FontColor);
            font-style: @Model.MetaData.TemplateNo.FontStyle;
            font-size: @(Model.MetaData.TemplateNo.UseFontSize ? Model.MetaData.TemplateNo.FontSize : Model.FontSize)px;
            font-weight: @Model.MetaData.TemplateNo.FontWeight;
        }

        .serial-no {
            color: @(Model.MetaData.SerialNo.UseFontColor ? Model.MetaData.SerialNo.FontColor : Model.FontColor);
            font-style: @Model.MetaData.SerialNo.FontStyle;
            font-size: @(Model.MetaData.SerialNo.UseFontSize ? Model.MetaData.SerialNo.FontSize : Model.FontSize)px;
            font-weight: @Model.MetaData.SerialNo.FontWeight;
        }

        .invoice-no {
            color: @(Model.MetaData.InvoiceNo.UseFontColor ? Model.MetaData.InvoiceNo.FontColor : Model.FontColor);
            font-style: @Model.MetaData.InvoiceNo.FontStyle;
            font-size: @(Model.MetaData.InvoiceNo.UseFontSize ? Model.MetaData.InvoiceNo.FontSize : Model.FontSize)px;
            font-weight: @Model.MetaData.InvoiceNo.FontWeight;
        }

        .invoice-no-value {
            color: red;
        }

        /************ Buyer ****************/
        .buyer {
            margin: 10px 0;
        }

            .buyer td {
                line-height: 26px;
            }

        .buyer-legal-name {
            color: @(Model.MetaData.BuyerLegalName.UseFontColor ? Model.MetaData.BuyerLegalName.FontColor : Model.FontColor);
            font-style: @Model.MetaData.BuyerLegalName.FontStyle;
            font-size: @(Model.MetaData.BuyerLegalName.UseFontSize ? Model.MetaData.BuyerLegalName.FontSize : Model.FontSize)px;
            font-weight: @Model.MetaData.BuyerLegalName.FontWeight;
        }

        .buyer-full-name {
            color: @(Model.MetaData.BuyerFullName.UseFontColor ? Model.MetaData.BuyerFullName.FontColor : Model.FontColor);
            font-style: @Model.MetaData.BuyerFullName.FontStyle;
            font-size: @(Model.MetaData.BuyerFullName.UseFontSize ? Model.MetaData.BuyerFullName.FontSize : Model.FontSize)px;
            font-weight: @Model.MetaData.BuyerFullName.FontWeight;
        }

        .buyer-address {
            color: @(Model.MetaData.BuyerAddress.UseFontColor ? Model.MetaData.BuyerAddress.FontColor : Model.FontColor);
            font-style: @Model.MetaData.BuyerAddress.FontStyle;
            font-size: @(Model.MetaData.BuyerAddress.UseFontSize ? Model.MetaData.BuyerAddress.FontSize : Model.FontSize)px;
            font-weight: @Model.MetaData.BuyerAddress.FontWeight;
        }

        .buyer-bank-account {
            color: @(Model.MetaData.BuyerBankAccount.UseFontColor ? Model.MetaData.BuyerBankAccount.FontColor : Model.FontColor);
            font-style: @Model.MetaData.BuyerBankAccount.FontStyle;
            font-size: @(Model.MetaData.BuyerBankAccount.UseFontSize ? Model.MetaData.BuyerBankAccount.FontSize : Model.FontSize)px;
            font-weight: @Model.MetaData.BuyerBankAccount.FontWeight;
        }

        .payment-method {
            color: @(Model.MetaData.PaymentMethod.UseFontColor ? Model.MetaData.PaymentMethod.FontColor : Model.FontColor);
            font-style: @Model.MetaData.PaymentMethod.FontStyle;
            font-size: @(Model.MetaData.PaymentMethod.UseFontSize ? Model.MetaData.PaymentMethod.FontSize : Model.FontSize)px;
            font-weight: @Model.MetaData.PaymentMethod.FontWeight;
        }

        .buyer-tax-code {
            color: @(Model.MetaData.BuyerTaxCode.UseFontColor ? Model.MetaData.BuyerTaxCode.FontColor : Model.FontColor);
            font-style: @Model.MetaData.BuyerTaxCode.FontStyle;
            font-size: @(Model.MetaData.BuyerTaxCode.UseFontSize ? Model.MetaData.BuyerTaxCode.FontSize : Model.FontSize)px;
            font-weight: @Model.MetaData.BuyerTaxCode.FontWeight;
        }

        /************* Invoice Header *************/
        tr.invoice-header th {
            border: 1px solid black;
            padding: 5px;
        }

        .detail-index {
            color: @Model.MetaData.InvoiceDetails.Index.FontColor;
            font-style: @Model.MetaData.InvoiceDetails.Index.FontStyle;
            font-size: @(Model.MetaData.InvoiceDetails.Index.FontSize)px;
            width: @(Model.MetaData.InvoiceDetails.Index.Width)px;
        }

        .detail-product-name {
            color: @Model.MetaData.InvoiceDetails.ProductName.FontColor;
            font-style: @Model.MetaData.InvoiceDetails.ProductName.FontStyle;
            font-size: @(Model.MetaData.InvoiceDetails.ProductName.FontSize)px;
            width: @(Model.MetaData.InvoiceDetails.ProductName.Width)px;
        }

        .detail-unit-name {
            color: @Model.MetaData.InvoiceDetails.UnitName.FontColor;
            font-style: @Model.MetaData.InvoiceDetails.UnitName.FontStyle;
            font-size: @(Model.MetaData.InvoiceDetails.UnitName.FontSize)px;
            width: @(Model.MetaData.InvoiceDetails.UnitName.Width)px;
        }

        .detail-quantity {
            color: @Model.MetaData.InvoiceDetails.Quantity.FontColor;
            font-style: @Model.MetaData.InvoiceDetails.Quantity.FontStyle;
            font-size: @(Model.MetaData.InvoiceDetails.Quantity.FontSize)px;
            width: @(Model.MetaData.InvoiceDetails.Quantity.Width)px;
        }

        .detail-unit-price {
            color: @Model.MetaData.InvoiceDetails.UnitPrice.FontColor;
            font-style: @Model.MetaData.InvoiceDetails.UnitPrice.FontStyle;
            font-size: @(Model.MetaData.InvoiceDetails.UnitPrice.FontSize)px;
            width: @(Model.MetaData.InvoiceDetails.UnitPrice.Width)px;
        }

        .detail-amount {
            color: @Model.MetaData.InvoiceDetails.Amount.FontColor;
            font-style: @Model.MetaData.InvoiceDetails.Amount.FontStyle;
            font-size: @(Model.MetaData.InvoiceDetails.Amount.FontSize)px;
            width: @(Model.MetaData.InvoiceDetails.Amount.Width)px;
        }

        /************** Invoice Body *****************/
        tbody.invoice-body tr td {
            border: 1px solid black;
            padding: 5px;
        }

        .text-center {
            text-align: center;
        }

        .text-right {
            text-align: right;
        }

        tbody.invoice-body tr.invoice-footer td {
            padding: 0px;
            border: none;
        }

        tbody.invoice-body tr.invoice-footer td table.invoice-foot tr td {
            border: 1px solid black;
            padding: 5px;
        }

        tbody.invoice-body tr.invoice-footer td table.invoice-foot tbody tr.detail-last td {
            border-top: none;
        }

        /************* Invoice Footer ***************/

        tbody.invoice-body tr.invoice-footer td table.invoice-foot tbody tr.payment-amount-word td {
            border: none;
        }

        tbody.invoice-body tr.invoice-footer td table.invoice-foot tbody tr.signature td {
            border: none;
        }

        tbody.invoice-body tr.invoice-footer td table.invoice-foot tbody tr td.border-side {
                border-left: hidden;
                border-right: hidden;
            }

        /************** Invoice Signature ***************/
        /*.invoice-signature {
                            margin-top: 40px;
                        }*/

        .sign-buyer {
            width: 30%;
            vertical-align: top;
            text-align: center;
        }

        .sign-tranformer {
            width: 40%;
            vertical-align: top;
            text-align: center;
        }

        .signed-seller {
            width: 30%;
            text-align: center;
        }

        .tranformer-date {
            margin-top: 100px;
        }

        .sign-seller {
            margin: 30px auto;
            font-size: 12px;
            line-height: 17px;
            border: 2px solid #00CC00;
            padding: 8px;
            color: #00CC00;
            text-decoration: underline;
            font-weight: bold;
            width: 80%;
        }

        .tick-signed {
            width: 30px;
        }

        .sign-seller-full-name {
            text-transform: uppercase;
        }
    </style>
</head>

<body>

    <table class="many-header-one-footer">
        <thead>
            <tr>
                <td colspan="1000">
                    <table>
                        <tbody>
                            <tr>
                                <td>
                                    <table class="seller">
                                        <tbody>
                                            <tr>
                                                <td class="logo">
                                                    <img class="logo-value" src="data:image/png;base64,@Model.MetaData.Logo.Value">
                                                </td>
                                                <td>
                                                    @if (Model.MetaData.SellerFullName.IsShow)
                                                    {
                                                        <h2 class="seller-full-name">@Model.MetaData.SellerFullName.Value</h2>
                                                    }
                                                    @if (Model.MetaData.SellerAddress.IsShow)
                                                    {
                                                        <p class="seller-address">@Model.MetaData.SellerAddress.Label: @Model.MetaData.SellerAddress.Value</p>
                                                    }
                                                    <p>
                                                        @if (Model.MetaData.SellerPhoneNumber.IsShow)
                                                        {
                                                            <span class="seller-phone-number">@Model.MetaData.SellerPhoneNumber.Label: @Model.MetaData.SellerPhoneNumber.Value</span>
                                                        }
                                                        @if (Model.MetaData.SellerFaxNumber.IsShow)
                                                        {
                                                            <span class="seller-fax-number">
                                                                @if (Model.MetaData.SellerPhoneNumber.IsShow && Model.MetaData.SellerFaxNumber.IsShow)
                                                                {<span>-</span>} @Model.MetaData.SellerFaxNumber.Label: @Model.MetaData.SellerFaxNumber.Value
                                                            </span>
                                                        }
                                                    </p>

                                                    @if (Model.MetaData.SellerTaxCode.IsShow)
                                                    {
                                                        <p class="seller-tax-code">@Model.MetaData.SellerTaxCode.Label: @Model.MetaData.SellerTaxCode.Value</p>
                                                    }
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <table class="invoice-identity">
                                        <tbody>
                                            <tr>
                                                <td class="qr-code">
                                                    <img class="qr-code-value" src="data:image/jpeg;base64,@Model.MetaData.QrCode.Value" />
                                                </td>
                                                <td class="invoice-name">
                                                    <h2 class="invoice-template-name">@HtmlExtension.Raw(Model.MetaData.InvoiceTemplateName.Value)</h2>
                                                    @{
                                                        DateTime printAt;
                                                    }
                                                    @if (Model.MetaData.PrintedTime != null && DateTime.TryParse(Model.MetaData.PrintedTime.Value, out printAt))
                                                    {
                                                        <p>(Hoá đơn chuyển đổi từ hoá đơn điện tử)</p>
                                                    }
                                                    else
                                                    {
                                                        <p>(Bản thể hiện của hóa đơn điện tử)</p>
                                                    }
                                                    <p>
                                                        @{
                                                            string[] date_label = Model.MetaData.InvoiceDate.Label.Split("|");
                                                            DateTime invoiceDate;
                                                        }

                                                        @if (DateTime.TryParse(Model.MetaData.InvoiceDate.Value, out invoiceDate))
                                                        {
                                                            <span class="invoice-date">@date_label[0] @invoiceDate.Day.ToString("00")</span>
                                                            <span class="invoice-date">@date_label[1] @invoiceDate.Month.ToString("00")</span>
                                                            <span class="invoice-date">@date_label[2] @invoiceDate.Year</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="invoice-date">@date_label[0] &nbsp;</span>
                                                            <span class="invoice-date">@date_label[1] &nbsp;</span>
                                                            <span class="invoice-date">@date_label[2] &nbsp;</span>
                                                        }
                                                    </p>
                                                    <p class="id-transaction">@Model.MetaData.TransactionId.Label: @Model.MetaData.TransactionId.Value</p>
                                                </td>
                                                <td>
                                                    <table class="invoice-template">
                                                        <tr>
                                                            <td class="template-no">@Model.MetaData.TemplateNo.Label :</td>
                                                            <td class="template-no">@Model.MetaData.TemplateNo.Value</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="serial-no">@Model.MetaData.SerialNo.Label :</td>
                                                            <td class="serial-no">@Model.MetaData.SerialNo.Value</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="invoice-no">@Model.MetaData.InvoiceNo.Label :</td>
                                                            <td class="invoice-no invoice-no-value">@Model.MetaData.InvoiceNo.Value</td>
                                                        </tr>
                                                        <tr>
                                                            <td colspan="2">
                                                                @if (Model.MetaData.InvoiceStatus == "ThayThe" && Model.MetaData.InvoiceReference != null)
                                                                {
                                                                    <p>Hóa đơn thay thế cho hóa đơn số @Model.MetaData.InvoiceReference.InvoiceNo - @Model.MetaData.InvoiceReference.SerialNo - @Model.MetaData.InvoiceReference.InvoiceDate.Day/@Model.MetaData.InvoiceReference.InvoiceDate.Month/@Model.MetaData.InvoiceReference.InvoiceDate.Year</p>
                                                                }
                                                                @if (Model.MetaData.InvoiceStatus == "BiThayThe" && Model.MetaData.InvoiceReference != null)
                                                                {
                                                                    <p>Hóa đơn bị thay thế bởi hóa đơn số @Model.MetaData.InvoiceReference.InvoiceNo - @Model.MetaData.InvoiceReference.SerialNo - @Model.MetaData.InvoiceReference.InvoiceDate.Day/@Model.MetaData.InvoiceReference.InvoiceDate.Month/@Model.MetaData.InvoiceReference.InvoiceDate.Year</p>
                                                                }
                                                                @if (Model.MetaData.InvoiceStatus == "DieuChinhDinhDanh" && Model.MetaData.InvoiceReference != null)
                                                                {
                                                                    <p>Hóa đơn điều chỉnh nội dung thông tin người mua cho hóa đơn số @Model.MetaData.InvoiceReference.InvoiceNo - @Model.MetaData.InvoiceReference.SerialNo - @Model.MetaData.InvoiceReference.InvoiceDate.Day/@Model.MetaData.InvoiceReference.InvoiceDate.Month/@Model.MetaData.InvoiceReference.InvoiceDate.Year</p>
                                                                }
                                                                @if (Model.MetaData.InvoiceStatus == "BiDieuChinhDinhDanh" && Model.MetaData.InvoiceReference != null)
                                                                {
                                                                    <p>Hóa đơn bị điều chỉnh nội dung thông tin người mua bởi hóa đơn số @Model.MetaData.InvoiceReference.InvoiceNo - @Model.MetaData.InvoiceReference.SerialNo - @Model.MetaData.InvoiceReference.InvoiceDate.Day/@Model.MetaData.InvoiceReference.InvoiceDate.Month/@Model.MetaData.InvoiceReference.InvoiceDate.Year</p>
                                                                }
                                                                @if (Model.MetaData.InvoiceStatus == "DieuChinhTangGiam" && Model.MetaData.InvoiceReference != null)
                                                                {
                                                                    <p>Hóa đơn điều chỉnh tăng giảm cho hóa đơn số @Model.MetaData.InvoiceReference.InvoiceNo - @Model.MetaData.InvoiceReference.SerialNo - @Model.MetaData.InvoiceReference.InvoiceDate.Day/@Model.MetaData.InvoiceReference.InvoiceDate.Month/@Model.MetaData.InvoiceReference.InvoiceDate.Year</p>
                                                                }
                                                                @if (Model.MetaData.InvoiceStatus == "BiDieuChinhTangGiam" && Model.MetaData.InvoiceReference != null)
                                                                {
                                                                    <p>Hóa đơn bị điều chỉnh tăng giảm bởi hóa đơn số @Model.MetaData.InvoiceReference.InvoiceNo - @Model.MetaData.InvoiceReference.SerialNo - @Model.MetaData.InvoiceReference.InvoiceDate.Day/@Model.MetaData.InvoiceReference.InvoiceDate.Month/@Model.MetaData.InvoiceReference.InvoiceDate.Year</p>
                                                                }
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <table class="buyer">
                                        <tbody>
                                            @if (Model.MetaData.BuyerLegalName.IsShow)
                                            {
                                                <tr>
                                                    <td class="buyer-legal-name">@Model.MetaData.BuyerLegalName.Label: @Model.MetaData.BuyerLegalName.Value</td>
                                                </tr>
                                            }
                                            @if (Model.MetaData.BuyerFullName.IsShow)
                                            {
                                                <tr>
                                                    <td class="buyer-full-name">@Model.MetaData.BuyerFullName.Label: @Model.MetaData.BuyerFullName.Value</td>
                                                </tr>
                                            }
                                            @if (Model.MetaData.BuyerAddress.IsShow)
                                            {
                                                <tr>
                                                    <td class="buyer-address">@Model.MetaData.BuyerAddress.Label: @Model.MetaData.BuyerAddress.Value</td>
                                                </tr>
                                            }
                                            @if (Model.MetaData.BuyerBankAccount.IsShow)
                                            {
                                                <tr>
                                                    <td class="buyer-bank-account">@Model.MetaData.BuyerBankAccount.Label : @Model.MetaData.BuyerBankAccount.Value</td>
                                                </tr>
                                            }
                                            @if (Model.MetaData.PaymentMethod.IsShow)
                                            {
                                                <tr>
                                                    <td class="payment-method">@Model.MetaData.PaymentMethod.Label : @Model.MetaData.PaymentMethod.Value</td>
                                                </tr>
                                            }
                                            @if (Model.MetaData.BuyerTaxCode.IsShow)
                                            {
                                                <tr>
                                                    <td class="buyer-tax-code">@Model.MetaData.BuyerTaxCode.Label : @Model.MetaData.BuyerTaxCode.Value</td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
            <tr class="invoice-header">
                @{
                    var detailOptions = Model.MetaData.InvoiceDetails;
                    int count = 0;
                }

                @if (detailOptions.Index != null && detailOptions.Index.IsShow)
                {
                    <th width="@(detailOptions.Index.Width)px"> @HtmlExtension.Raw(detailOptions.Index.Label) </th>
                    count++;
                }
                @if (detailOptions.ProductName != null && detailOptions.ProductName.IsShow)
                {
                    <th>  @HtmlExtension.Raw(detailOptions.ProductName.Label) </th>
                    count++;
                }
                @if (detailOptions.UnitName != null && detailOptions.UnitName.IsShow)
                {
                    <th width="@(detailOptions.UnitName.Width)px">@HtmlExtension.Raw(detailOptions.UnitName.Label)</th>
                    count++;
                }
                @if (detailOptions.Quantity != null && detailOptions.Quantity.IsShow)
                {
                    <th width="@(detailOptions.Quantity.Width)px">@HtmlExtension.Raw(detailOptions.Quantity.Label)</th>
                    count++;
                }
                @if (detailOptions.UnitPrice != null && detailOptions.UnitPrice.IsShow)
                {
                    <th width="@(detailOptions.UnitPrice.Width)px">@HtmlExtension.Raw(detailOptions.UnitPrice.Label)</th>
                    count++;
                }
                @if (detailOptions.DiscountPercent != null && detailOptions.DiscountPercent.IsShow)
                {
                    <th width="@(detailOptions.DiscountPercent.Width)px">@HtmlExtension.Raw(detailOptions.DiscountPercent.Label)</th>
                    count++;
                }
                @if (detailOptions.DiscountAmount != null && detailOptions.DiscountAmount.IsShow)
                {
                    <th class="detail-discount-amount" width="@(detailOptions.DiscountAmount.Width)px">@HtmlExtension.Raw(detailOptions.DiscountAmount.Label)</th>
                    count++;
                }
                @if (detailOptions.Amount != null && detailOptions.Amount.IsShow)
                {
                    <th width="@(detailOptions.Amount.Width)px">@HtmlExtension.Raw(detailOptions.Amount.Label)</th>
                    count++;
                }
            </tr>
            <tr class="invoice-header">
                @if (detailOptions.Index != null && detailOptions.Index.IsShow)
                {
                    <th>A</th>
                }
                @if (detailOptions.ProductName != null && detailOptions.ProductName.IsShow)
                {
                    <th>B</th>
                }
                @if (detailOptions.UnitName != null && detailOptions.UnitName.IsShow)
                {
                    <th>C</th>
                }
                @if (detailOptions.Quantity != null && detailOptions.Quantity.IsShow)
                {
                    <th>1</th>
                }
                @if (detailOptions.UnitPrice != null && detailOptions.UnitPrice.IsShow)
                {
                    <th>2</th>
                }
                @if (detailOptions.DiscountPercent != null && detailOptions.DiscountPercent.IsShow)
                {
                    <th>3</th>
                }
                @if (detailOptions.DiscountAmount != null && detailOptions.DiscountAmount.IsShow)
                {
                    @if (detailOptions.DiscountPercent != null && detailOptions.DiscountPercent.IsShow)
                    {
                        <th>4</th>
                    }
                    else
                    {
                        <th>3</th>
                    }
                }
                @if (detailOptions.Amount != null && detailOptions.Amount.IsShow)
                {
                    @if ((detailOptions.DiscountPercent != null && detailOptions.DiscountPercent.IsShow) && (detailOptions.DiscountAmount != null && detailOptions.DiscountAmount.IsShow))
                    {
                        <th>5 = (1 x 2) - 4</th>
                    }
                    else if ((detailOptions.DiscountPercent != null && detailOptions.DiscountPercent.IsShow) && (detailOptions.DiscountAmount == null || !detailOptions.DiscountAmount.IsShow))
                    {
                        <th>4 = 1 x 2</th>
                    }
                    else if ((detailOptions.DiscountPercent == null || !detailOptions.DiscountPercent.IsShow) && (detailOptions.DiscountAmount != null && detailOptions.DiscountAmount.IsShow))
                    {
                        <th>4 = (1 x 2) - 3</th>
                    }
                    else
                    {
                        <th>3 = 1 x 2</th>
                    }
                }
            </tr>
        </thead>
        <tbody class="invoice-body">
            @if (detailOptions.Values != null && detailOptions.Values.Any())
            {
                @if (Model.MetaData.InvoiceStatus == "DieuChinhTangGiam")
                {
                    @foreach (var detail in detailOptions.Values)
                    {
                        @if (detail != detailOptions.Values.Last())
                        {
                            var detailProduct = "";
                            var quantity = "";
                            var unitPrice = "";
                            var amount = "";

                            if (detail.Quantity.Value < 0)
                            {
                                quantity = "giảm số lượng";
                            }
                            else if (detail.Quantity.Value > 0)
                            {
                                quantity = "tăng số lượng";
                            }

                            if (detail.UnitPrice.Value < 0)
                            {
                                unitPrice = "giảm đơn giá";
                            }
                            else if (detail.UnitPrice.Value > 0)
                            {
                                unitPrice = "tăng đơn giá";
                            }

                            if (detail.Amount.Value < 0)
                            {
                                amount = "giảm thành tiền";
                            }
                            else if (detail.Amount.Value > 0)
                            {
                                amount = "tăng thành tiền";
                            }

                            @if (detail.Quantity.Value == 0 && detail.UnitPrice.Value == 0 && detail.Amount.Value == 0)
                            {
                                detailProduct = @detail.ProductName;
                            }
                            else
                            {
                                detailProduct = detail.ProductName + ": điều chỉnh " + quantity + " " + unitPrice + " " + amount;
                            }

                            <tr>
                                @if (detailOptions.Index != null && detailOptions.Index.IsShow)
                                {
                                    <td class="text-center">
                                        @if (detail.Index == null)
                                        {
                                            <span>&nbsp;</span>
                                        }
                                        else
                                        {
                                            @detail.Index.Value
                                        }
                                    </td>
                                }
                                @if (detailOptions.ProductName != null && detailOptions.ProductName.IsShow)
                                {
                                    <td>
                                        @if (detail.ProductName == null)
                                        {
                                            <span>&nbsp;</span>
                                        }
                                        else
                                        {
                                            @detailProduct
                                        }
                                    </td>
                                }
                                @if (detailOptions.UnitName != null && detailOptions.UnitName.IsShow)
                                {
                                    <td class="text-center">
                                        @if (detail.UnitName == null)
                                        {
                                            <span>&nbsp;</span>
                                        }
                                        else
                                        {
                                            @if (detail.HideUnit == true)
                                            { }
                                            else
                                            {
                                                @detail.UnitName
                                            }
                                        }
                                    </td>
                                }
                                @if (detailOptions.Quantity != null && detailOptions.Quantity.IsShow)
                                {
                                    <td class="text-right">
                                        @if (detail.Quantity == null)
                                        {
                                            <span>&nbsp;</span>
                                        }
                                        else
                                        {
                                            @if (detail.HideQuantity == true)
                                            { }
                                            else
                                            {
                                                @if (detail.Quantity.Value != 0)
                                                {
                                                    @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail.Quantity.Value), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                                }
                                                else
                                                {
                                                    @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail.OriginQuantity), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                                }
                                            }
                                        }
                                    </td>
                                }
                                @if (detailOptions.UnitPrice != null && detailOptions.UnitPrice.IsShow)
                                {
                                    <td class="text-right">
                                        @if (detail.UnitPrice == null)
                                        {
                                            <span>&nbsp;</span>
                                        }
                                        else
                                        {
                                            @if (detail.HideUnitPrice == true)
                                            { }
                                            else
                                            {
                                                @if (detail.UnitPrice.Value != 0)
                                                {
                                                    @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail.UnitPrice.Value), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                                }
                                                else
                                                {
                                                    @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail.OriginUnitPrice), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                                }
                                            }
                                        }
                                    </td>
                                }
                                @if (detailOptions.DiscountPercent != null && detailOptions.DiscountPercent.IsShow)
                                {
                                    <td class="text-center">
                                        @if (detail.DiscountPercentBeforeTax == null)
                                        {
                                            <span>&nbsp;</span>
                                        }
                                        else
                                        {
                                            @if (detail.DiscountPercentBeforeTax.Value != 0)
                                            {
                                                @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail.DiscountPercentBeforeTax.Value), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                            }
                                            else
                                            {
                                                @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail.OriginDiscountPercentBeforeTax), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                            }
                                        }
                                    </td>
                                }
                                @if (detailOptions.DiscountAmount != null && detailOptions.DiscountAmount.IsShow)
                                {
                                    <td class="text-right">
                                        @if (detail.DiscountAmountBeforeTax == null)
                                        {
                                            <span>&nbsp;</span>
                                        }
                                        else
                                        {
                                            @if (detail.DiscountAmountBeforeTax.Value != 0)
                                            {
                                                @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail.DiscountAmountBeforeTax.Value), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                            }
                                            else
                                            {
                                                @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail.OriginDiscountAmountBeforeTax), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                            }
                                        }
                                    </td>
                                }
                                @if (detailOptions.Amount != null && detailOptions.Amount.IsShow)
                                {
                                    <td class="text-right">
                                        @if (detail.Amount == null)
                                        {
                                            <span>&nbsp;</span>
                                        }
                                        else
                                        {
                                            @if (detail.Amount.Value != 0)
                                            {
                                                @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail.Amount.Value), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                            }
                                            else
                                            {
                                                @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail.OriginAmount), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                            }
                                        }
                                    </td>
                                }
                            </tr>
                        }
                    }
                }
                else
                {
                    @foreach (var detail in detailOptions.Values)
                    {
                        @if (detail != detailOptions.Values.Last())
                        {
                            <tr>
                                @if (detailOptions.Index != null && detailOptions.Index.IsShow)
                                {
                                    <td class="text-center">
                                        @if (detail.Index == null)
                                        {
                                            <span>&nbsp;</span>
                                        }
                                        else
                                        {
                                            @detail.Index.Value
                                        }
                                    </td>
                                }
                                @if (detailOptions.ProductName != null && detailOptions.ProductName.IsShow)
                                {
                                    <td>
                                        @if (detail.ProductName == null)
                                        {
                                            <span>&nbsp;</span>
                                        }
                                        else
                                        {
                                            @detail.ProductName
                                        }
                                    </td>
                                }
                                @if (detailOptions.UnitName != null && detailOptions.UnitName.IsShow)
                                {
                                    <td class="text-center">
                                        @if (detail.UnitName == null)
                                        {
                                            <span>&nbsp;</span>
                                        }
                                        else
                                        {
                                            @if (detail.HideUnit == true)
                                            { }
                                            else
                                            {
                                                @detail.UnitName
                                            }
                                        }
                                    </td>
                                }
                                @if (detailOptions.Quantity != null && detailOptions.Quantity.IsShow)
                                {
                                    <td class="text-right">
                                        @if (detail.Quantity == null)
                                        {
                                            <span>&nbsp;</span>
                                        }
                                        else
                                        {
                                            @if (detail.HideQuantity == true)
                                            { }
                                            else
                                            {
                                                @if (detail.Quantity.Value != 0)
                                                {
                                                    @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail.Quantity.Value), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                                }
                                                else
                                                {
                                                    <span>0</span>
                                                }
                                            }
                                        }
                                    </td>
                                }
                                @if (detailOptions.UnitPrice != null && detailOptions.UnitPrice.IsShow)
                                {
                                    <td class="text-right">
                                        @if (detail.UnitPrice == null)
                                        {
                                            <span>&nbsp;</span>
                                        }
                                        else
                                        {
                                            @if (detail.HideUnitPrice == true)
                                            { }
                                            else
                                            {
                                                @if (detail.UnitPrice.Value != 0)
                                                {
                                                    @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail.UnitPrice.Value), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                                }
                                                else
                                                {
                                                    <span>0</span>
                                                }
                                            }
                                        }
                                    </td>
                                }
                                @if (detailOptions.DiscountPercent != null && detailOptions.DiscountPercent.IsShow)
                                {
                                    <td class="text-center">
                                        @if (detail.DiscountPercentBeforeTax == null)
                                        {
                                            <span>&nbsp;</span>
                                        }
                                        else
                                        {
                                            @if (detail.DiscountPercentBeforeTax.Value != 0)
                                            {
                                                @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail.DiscountPercentBeforeTax.Value), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                            }
                                            else
                                            {
                                                <span>0</span>
                                            }
                                        }
                                    </td>
                                }
                                @if (detailOptions.DiscountAmount != null && detailOptions.DiscountAmount.IsShow)
                                {
                                    <td class="text-right">
                                        @if (detail.DiscountAmountBeforeTax == null)
                                        {
                                            <span>&nbsp;</span>
                                        }
                                        else
                                        {
                                            @if (detail.DiscountAmountBeforeTax.Value != 0)
                                            {
                                                @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail.DiscountAmountBeforeTax.Value), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                            }
                                            else
                                            {
                                                <span>0</span>
                                            }
                                        }
                                    </td>
                                }
                                @if (detailOptions.Amount != null && detailOptions.Amount.IsShow)
                                {
                                    <td class="text-right">
                                        @if (detail.Amount == null)
                                        {
                                            <span>&nbsp;</span>
                                        }
                                        else
                                        {
                                            @if (detail.Amount.Value != 0)
                                            {
                                                @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail.Amount.Value), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                            }
                                            else
                                            {
                                                <span>0</span>
                                            }
                                        }
                                    </td>
                                }
                            </tr>
                        }
                    }
                }
            }

            <tr class="invoice-footer">
                <td colspan="@count">
                    <table class="invoice-foot">
                        <tbody>
                            @if (detailOptions.Values != null && detailOptions.Values.Any())
                            {
                                var detail2 = detailOptions.Values.Last();

                                @if (Model.MetaData.InvoiceStatus == "DieuChinhTangGiam")
                                {
                                    var detailProduct = "";
                                    var quantity = "";
                                    var unitPrice = "";
                                    var amount = "";

                                    if (detail2.Quantity.Value < 0)
                                    {
                                        quantity = "giảm số lượng";
                                    }
                                    else if (detail2.Quantity.Value > 0)
                                    {
                                        quantity = "tăng số lượng";
                                    }

                                    if (detail2.UnitPrice.Value < 0)
                                    {
                                        unitPrice = "giảm đơn giá";
                                    }
                                    else if (detail2.UnitPrice.Value > 0)
                                    {
                                        unitPrice = "tăng đơn giá";
                                    }

                                    if (detail2.Amount.Value < 0)
                                    {
                                        amount = "giảm thành tiền";
                                    }
                                    else if (detail2.Amount.Value > 0)
                                    {
                                        amount = "tăng thành tiền";
                                    }

                                    @if (detail2.Quantity.Value == 0 && detail2.UnitPrice.Value == 0 && detail2.Amount.Value == 0)
                                    {
                                        detailProduct = detail2.ProductName;
                                    }
                                    else
                                    {
                                        detailProduct = detail2.ProductName + ": điều chỉnh " + quantity + " " + unitPrice + " " + amount;
                                    }

                                    <tr class="detail-last">
                                        @if (detailOptions.Index != null && detailOptions.Index.IsShow)
                                        {
                                            <td class="text-center" width="@(detailOptions.Index.Width)px">
                                                @if (detail2.Index == null)
                                                {
                                                    <span>&nbsp;</span>
                                                }
                                                else
                                                {
                                                    @detail2.Index.Value
                                                }
                                            </td>
                                        }
                                        @if (detailOptions.ProductName != null && detailOptions.ProductName.IsShow)
                                        {
                                            <td>
                                                @if (detail2.ProductName == null)
                                                {
                                                    <span>&nbsp;</span>
                                                }
                                                else
                                                {
                                                    @detailProduct
                                                }
                                            </td>
                                        }
                                        @if (detailOptions.UnitName != null && detailOptions.UnitName.IsShow)
                                        {
                                            <td class="text-center" width="@(detailOptions.UnitName.Width)px">
                                                @if (detail2.UnitName == null)
                                                {
                                                    <span>&nbsp;</span>
                                                }
                                                else
                                                {
                                                    @if (detail2.HideUnit == true)
                                                    { }
                                                    else
                                                    {
                                                        @detail2.UnitName
                                                    }
                                                }
                                            </td>
                                        }
                                        @if (detailOptions.Quantity != null && detailOptions.Quantity.IsShow)
                                        {
                                            <td class="text-right" width="@(detailOptions.Quantity.Width)px">
                                                @if (detail2.Quantity == null)
                                                {
                                                    <span>&nbsp;</span>
                                                }
                                                else
                                                {
                                                    @if (detail2.HideQuantity == true)
                                                    { }
                                                    else
                                                    {
                                                        @if (detail2.Quantity.Value != 0)
                                                        {
                                                            @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail2.Quantity.Value), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                                        }
                                                        else
                                                        {
                                                            @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail2.OriginQuantity), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                                        }
                                                    }
                                                }
                                            </td>
                                        }
                                        @if (detailOptions.UnitPrice != null && detailOptions.UnitPrice.IsShow)
                                        {
                                            <td class="text-right" width="@(detailOptions.UnitPrice.Width)px">
                                                @if (detail2.UnitPrice == null)
                                                {
                                                    <span>&nbsp;</span>
                                                }
                                                else
                                                {
                                                    @if (detail2.HideUnitPrice == true)
                                                    { }
                                                    else
                                                    {
                                                        @if (detail2.UnitPrice.Value != 0)
                                                        {
                                                            @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail2.UnitPrice.Value), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                                        }
                                                        else
                                                        {
                                                            @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail2.OriginUnitPrice), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                                        }
                                                    }
                                                }
                                            </td>
                                        }
                                        @if (detailOptions.DiscountPercent != null && detailOptions.DiscountPercent.IsShow)
                                        {
                                            <td class="text-center" width="@(detailOptions.DiscountPercent.Width)px">
                                                @if (detail2.DiscountPercentBeforeTax == null)
                                                {
                                                    <span>&nbsp;</span>
                                                }
                                                else
                                                {
                                                    @if (detail2.DiscountPercentBeforeTax.Value != 0)
                                                    {
                                                        @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail2.DiscountPercentBeforeTax.Value), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                                    }
                                                    else
                                                    {
                                                        @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail2.OriginDiscountPercentBeforeTax), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                                    }
                                                }
                                            </td>
                                        }
                                        @if (detailOptions.DiscountAmount != null && detailOptions.DiscountAmount.IsShow)
                                        {
                                            <td class="text-right" width="@(detailOptions.DiscountAmount.Width)px">
                                                @if (detail2.DiscountAmountBeforeTax == null)
                                                {
                                                    <span>&nbsp;</span>
                                                }
                                                else
                                                {
                                                    @if (detail2.DiscountAmountBeforeTax.Value != 0)
                                                    {
                                                        @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail2.DiscountAmountBeforeTax.Value), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                                    }
                                                    else
                                                    {
                                                        @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail2.OriginDiscountAmountBeforeTax), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                                    }
                                                }
                                            </td>
                                        }
                                        @if (detailOptions.Amount != null && detailOptions.Amount.IsShow)
                                        {
                                            <td class="text-right" width="@(detailOptions.Amount.Width)px">
                                                @if (detail2.Amount == null)
                                                {
                                                    <span>&nbsp;</span>
                                                }
                                                else
                                                {
                                                    @if (detail2.Amount.Value != 0)
                                                    {
                                                        @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail2.Amount.Value), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                                    }
                                                    else
                                                    {
                                                        @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail2.OriginAmount), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                                    }
                                                }
                                            </td>
                                        }
                                    </tr>
                                }
                                else
                                {
                                    <tr class="detail-last">
                                        @if (detailOptions.Index != null && detailOptions.Index.IsShow)
                                        {
                                            <td class="text-center" width="@(detailOptions.Index.Width)px">
                                                @if (detail2.Index == null)
                                                {
                                                    <span>&nbsp;</span>
                                                }
                                                else
                                                {
                                                    @detail2.Index.Value
                                                }
                                            </td>
                                        }
                                        @if (detailOptions.ProductName != null && detailOptions.ProductName.IsShow)
                                        {
                                            <td>
                                                @if (detail2.ProductName == null)
                                                {
                                                    <span>&nbsp;</span>
                                                }
                                                else
                                                {
                                                    @detail2.ProductName
                                                }
                                            </td>
                                        }
                                        @if (detailOptions.UnitName != null && detailOptions.UnitName.IsShow)
                                        {
                                            <td class="text-center" width="@(detailOptions.UnitName.Width)px">
                                                @if (detail2.UnitName == null)
                                                {
                                                    <span>&nbsp;</span>
                                                }
                                                else
                                                {
                                                    @if (detail2.HideUnit == true)
                                                    { }
                                                    else
                                                    {
                                                        @detail2.UnitName
                                                    }
                                                }
                                            </td>
                                        }
                                        @if (detailOptions.Quantity != null && detailOptions.Quantity.IsShow)
                                        {
                                            <td class="text-right" width="@(detailOptions.Quantity.Width)px">
                                                @if (detail2.Quantity == null)
                                                {
                                                    <span>&nbsp;</span>
                                                }
                                                else
                                                {
                                                    @if (detail2.HideQuantity == true)
                                                    { }
                                                    else
                                                    {
                                                        @if (detail2.Quantity.Value != 0)
                                                        {
                                                            @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail2.Quantity.Value), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                                        }
                                                        else
                                                        {
                                                            <span>0</span>
                                                        }
                                                    }
                                                }
                                            </td>
                                        }
                                        @if (detailOptions.UnitPrice != null && detailOptions.UnitPrice.IsShow)
                                        {
                                            <td class="text-right" width="@(detailOptions.UnitPrice.Width)px">
                                                @if (detail2.UnitPrice == null)
                                                {
                                                    <span>&nbsp;</span>
                                                }
                                                else
                                                {
                                                    @if (detail2.HideUnitPrice == true)
                                                    { }
                                                    else
                                                    {
                                                        @if (detail2.UnitPrice.Value != 0)
                                                        {
                                                            @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail2.UnitPrice.Value), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                                        }
                                                        else
                                                        {
                                                            <span>0</span>
                                                        }
                                                    }
                                                }
                                            </td>
                                        }
                                        @if (detailOptions.DiscountPercent != null && detailOptions.DiscountPercent.IsShow)
                                        {
                                            <td class="text-center" width="@(detailOptions.DiscountPercent.Width)px">
                                                @if (detail2.DiscountPercentBeforeTax == null)
                                                {
                                                    <span>&nbsp;</span>
                                                }
                                                else
                                                {
                                                    @if (detail2.DiscountPercentBeforeTax.Value != 0)
                                                    {
                                                        @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail2.DiscountPercentBeforeTax.Value), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                                    }
                                                    else
                                                    {
                                                        <span>0</span>
                                                    }
                                                }
                                            </td>
                                        }
                                        @if (detailOptions.DiscountAmount != null && detailOptions.DiscountAmount.IsShow)
                                        {
                                            <td class="text-right" width="@(detailOptions.DiscountAmount.Width)px">
                                                @if (detail2.DiscountAmountBeforeTax == null)
                                                {
                                                    <span>&nbsp;</span>
                                                }
                                                else
                                                {
                                                    @if (detail2.DiscountAmountBeforeTax.Value != 0)
                                                    {
                                                        @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail2.DiscountAmountBeforeTax.Value), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                                    }
                                                    else
                                                    {
                                                        <span>0</span>
                                                    }
                                                }
                                            </td>
                                        }
                                        @if (detailOptions.Amount != null && detailOptions.Amount.IsShow)
                                        {
                                            <td class="text-right" width="@(detailOptions.Amount.Width)px">
                                                @if (detail2.Amount == null)
                                                {
                                                    <span>&nbsp;</span>
                                                }
                                                else
                                                {
                                                    @if (detail2.Amount.Value != 0)
                                                    {
                                                        @NumberFormatExtension.FormatNumberPdf(Math.Abs(detail2.Amount.Value), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                                    }
                                                    else
                                                    {
                                                        <span>0</span>
                                                    }
                                                }
                                            </td>
                                        }
                                    </tr>
                                }
                            }

                            @if (Model.MetaData.TotalDiscountAmountBeforeTax != null && Model.MetaData.TotalDiscountAmountBeforeTax.IsShow)
                            {
                                <tr>
                                    <td colspan="2"></td>
                                    <td colspan="@(count-3)" class="border-side">@HtmlExtension.Raw(Model.MetaData.TotalDiscountAmountBeforeTax.Label):</td>
                                    <td class="text-right">
                                        @if (string.IsNullOrEmpty(Model.MetaData.TotalDiscountAmountBeforeTax.Value))
                                        {
                                            <span>&nbsp;</span>
                                        }
                                        else
                                        {
                                            decimal totalDiscountAmountBeforeTax;
                                            @if (Model.MetaData.TotalDiscountAmountBeforeTax != null && decimal.TryParse(@Model.MetaData.TotalDiscountAmountBeforeTax.Value, out totalDiscountAmountBeforeTax))
                                            {
                                                @NumberFormatExtension.FormatNumberPdf(Math.Abs(totalDiscountAmountBeforeTax), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                            }
                                            else
                                            {
                                                <span>0</span>
                                            }
                                        }
                                    </td>
                                </tr>
                            }
                            <tr>
                                <td colspan="2"></td>
                                <td colspan="@(count-3)" class="border-side">@HtmlExtension.Raw(Model.MetaData.TotalAmount.Label):</td>
                                <td class="text-right">
                                    @if (string.IsNullOrEmpty(Model.MetaData.TotalAmount.Value))
                                    {
                                        <span>&nbsp;</span>
                                    }
                                    else
                                    {
                                        decimal totalAmount;
                                        @if (Model.MetaData.TotalAmount != null && decimal.TryParse(@Model.MetaData.TotalAmount.Value, out totalAmount))
                                        {
                                            @NumberFormatExtension.FormatNumberPdf(Math.Abs(totalAmount), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                        }
                                        else
                                        {
                                            <span>0</span>
                                        }
                                    }
                                </td>
                            </tr>
                            @foreach (var tax in Model.MetaData.InvoiceTaxBreakDowns)
                            {
                                <tr>
                                    @if (string.IsNullOrEmpty(tax.VatPercentDisplay))
                                    {
                                        <td colspan="2"><span>Thuế suất @tax.Name</span></td>
                                    }
                                    else
                                    {
                                        <td colspan="2"><span>@tax.VatPercentDisplay</span></td>
                                    }
                                    <td colspan="@(count-3)" class="border-side"><span>Tiền thuế @tax.VatPercentDisplay:</span></td>
                                    <td class="text-right">
                                        @if (tax.VatPercent == null)
                                        {
                                            <span>&nbsp;</span>
                                        }
                                        else
                                        {
                                            @if (tax.VatPercent < 0)
                                            {
                                                @tax.VatPercentDisplay
                                            }
                                            else if (tax.VatPercent == 0)
                                            {
                                                <span>0</span>
                                            }
                                            else if (tax.VatPercent > 0)
                                            {
                                                @NumberFormatExtension.FormatNumberPdf(Math.Abs(tax.VatAmount.Value), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                            }
                                        }
                                    </td>

                                </tr>
                            }
                            @if (Model.MetaData.TotalDiscountAmountAfterTax != null && Model.MetaData.TotalDiscountAmountAfterTax.IsShow)
                            {
                                <tr>
                                    <td colspan="2"></td>
                                    <td colspan="@(count-3)" class="border-side">@HtmlExtension.Raw(Model.MetaData.TotalDiscountAmountAfterTax.Label):</td>
                                    <td class="text-right">
                                        @if (string.IsNullOrEmpty(Model.MetaData.TotalDiscountAmountAfterTax.Value))
                                        {
                                            <span>&nbsp;</span>
                                        }
                                        else
                                        {
                                            decimal totalDiscountAmountAfterTax;
                                            @if (Model.MetaData.TotalDiscountAmountAfterTax != null && decimal.TryParse(@Model.MetaData.TotalDiscountAmountAfterTax.Value, out totalDiscountAmountAfterTax))
                                            {
                                                @NumberFormatExtension.FormatNumberPdf(Math.Abs(totalDiscountAmountAfterTax), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                            }
                                            else
                                            {
                                                <span>0</span>
                                            }
                                        }
                                    </td>
                                </tr>
                            }
                            <tr>
                                <td colspan="2"></td>
                                <td colspan="@(count-3)" class="border-side">@HtmlExtension.Raw(Model.MetaData.TotalPaymentAmount.Label):</td>
                                <td class="text-right">
                                    @if (string.IsNullOrEmpty(Model.MetaData.TotalPaymentAmount.Value))
                                    {
                                        <span>&nbsp;</span>
                                    }
                                    else
                                    {
                                        decimal totalPaymentAmount;
                                        @if (Model.MetaData.TotalPaymentAmount != null && decimal.TryParse(@Model.MetaData.TotalPaymentAmount.Value, out totalPaymentAmount))
                                        {
                                            @NumberFormatExtension.FormatNumberPdf(Math.Abs(totalPaymentAmount), Model.NumberDecimalSeparator, Model.NumberGroupSeparator)
                                        }
                                        else
                                        {
                                            <span>0</span>
                                        }
                                    }
                                </td>
                            </tr>

                            <tr class="payment-amount-word">
                                <td colspan="1000">
                                    @HtmlExtension.Raw(Model.MetaData.PaymentAmountWords.Label):
                                    @if (Model.MetaData.PaymentAmountWords.Value == null)
                                    {
                                        <span>&nbsp;</span>
                                    }
                                    else
                                    {
                                        @Model.MetaData.PaymentAmountWords.Value
                                    }
                                </td>
                            </tr>

                            <tr class="signature">
                                <td colspan="1000">
                                    <table class="invoice-signature">
                                        <tbody>
                                            <tr>
                                                <td class="sign-buyer">
                                                    <p><b>Người mua hàng (Buyer)</b></p>
                                                    <p>(Ký, ghi rõ họ, tên)</p>
                                                    <p>(Signature & fullname)</p>
                                                </td>
                                                <td class="sign-tranformer">
                                                    @if (Model.MetaData.PrintedTime != null && DateTime.TryParse(Model.MetaData.PrintedTime.Value, out printAt))
                                                    {
                                                        <p><b>Người thực hiện chuyển đổi</b></p>
                                                        <p>(Ký, ghi rõ họ, tên)</p>
                                                        <p>(Signature & fullname)</p>
                                                        <p class="tranformer-date">Ngày chuyển đổi: @($"{DateTime.UtcNow.Day}/{DateTime.UtcNow.Month}/{DateTime.UtcNow.Year}")</p>
                                                    }
                                                    else
                                                    { }
                                                </td>
                                                <td class="signed-seller">
                                                    <p><b>Người bán hàng (Seller)</b></p>
                                                    <p>(Ký, ghi rõ họ, tên)</p>
                                                    <p>(Signature & fullname)</p>
                                                    @if (Model.MetaData.SellerSignedTime != null && !string.IsNullOrEmpty(Model.MetaData.SellerSignedTime.Value))
                                                    {
                                                        <div class="sign-seller">
                                                            <p>
                                                                <img class="tick-signed" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAcCAYAAAB2+A+pAAAACXBIWXMAABJ0AAASdAHeZh94AAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAKASURBVHjavJbfS1NhGMc/z7Z0TXNW1pYGJSgoQlGQEQRGEEYYVJIE4U1/QJCEEBUFQuZVEF10FXQdQjdRdJFJCTmpDBPrwiLD5tq05jY3z87O04UU+GNq7swXDgfO+zznw/d53vf7vqgq6/U8/N2tuz4cVlXFxToN/0i9hsYHwCyCPeBYD6i8cWvo21v2exrR43EByGtpG76eU3oKlCeiC+fyVmrPUK0mQ0EwMugJSxbO5wUsA2XKFJCeQZtMWSrG9h5Ln1uJOMFIoE2GZIuzFSy9TiW2GcwoejIpy8W67IO6lLgPjBB6OiMrxduiWF46lJgPUsFVQW0By6sNSmIHpH+gzSqrzcup1PK6WJksA2sKPbN6aE6KnYHdSsQDOsveTYf+O39N4IMjp9Qaj4LlBBMGj72QvIOvj3dp4NMzyBRBYgI9OyVrapOqAlDYX6XG+Ch1pUf4eLQn68/ksShGBaQj6PmUrLVVDgB5IGp8CUO6guHgezzdlbok9OlWZcYPGYOd7pqcdoMDQC+o+Ef9IAY4iklOTywKbOxrUSLmXEoszPfmQckZDBC89lkYCoNlgbMUuS/zVD8ffgRWESQmubHvVs6mM29xtdW1Q2waTAH10PnujgLI3VLFuR3SGVAvNw9ckZyN5+/i+vfhsig1fjAzeN0lXCxppWOsCzxeiE+gbSp22Oyi7dRS3QqJOJgOookoHYHbUFgCqVm2FdXad5ItVAwgl0Sp9IGl4JC5d+InetUetVkNpNxV3c+sAYZCyoJUmo0F5fZeGJZSDCDtriTeLW4USITRTvvULm+ZYxkTw4JfYXyFVbbfy7Iei/VVDa7AZC96z16lK5Y63+PPAM6uo2WH47I/AAAAAElFTkSuQmCC">
                                                                <span>Signature Valid</span><br>
                                                                <span>Được ký bởi</span>
                                                                <span class="sign-seller-full-name">@Model.MetaData.SellerFullName.Value</span>
                                                            </p>
                                                            @if (Model.MetaData.SellerSignedTime.IsShow)
                                                            {
                                                                <p>
                                                                    @{
                                                                        DateTime sellerSignedAt;
                                                                    }
                                                                    <span>@Model.MetaData.SellerSignedTime.Label: </span>
                                                                    @if (DateTime.TryParse(Model.MetaData.SellerSignedTime.Value, out sellerSignedAt))
                                                                    {
                                                                        <span>@sellerSignedAt.AddHours(7).ToString("dd/MM/yyyy")</span>
                                                                    }
                                                                    else
                                                                    {
                                                                        <span>.../.../...</span>
                                                                    }
                                                                </p>
                                                            }
                                                        </div>
                                                    }
                                                    else { }
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
        </tbody>

    </table>

</body>
</html>
