/*html {
    height: 100%;
}*/

body {
    /*font-family: Roboto,Helvetica Neue,Helvetica,Arial,sans-serif;*/
    font-family: Arial,x-locale-body,sans-serif;
    font-size: 13px;
    padding: 0;
    background-color: #eee;
    /*line-height: 1.5384616;
    color: #333;
    
    min-height: 100%;*/
    /*overflow-y: hidden;*/
}

a:hover {
    text-decoration: none;
    cursor: pointer;
}

.index-form {
    width: 100%;
    margin: 0 auto;
    padding: 5px 20px 20px 20px;
    /*width: 25%;*/
    margin: 0 auto;
    /*padding: 150px 20px 20px 20px;*/
}

.page-wrapper {
    height: 100%;
}

    .page-wrapper .page-content {
        /*height: unset;*/
        min-height: 799px;
        position: relative;
    }


.navbar {
    margin: 0;
    border-radius: inherit;
}

#page {
    height: 100%;
    overflow-x: hidden;
}

#page-right {
    display: block;
    position: relative;
    height: 100%;
    width: 100%;
    /*background-color: yellow;*/
}

.no-padding {
    padding: 0;
}

input::placeholder {
    font-size: 13px;
}

.panel {
    box-shadow: 0 1px 3px rgba(0,0,0,.12), 0 1px 2px rgba(0,0,0,.24);
}

.panel-cus {
    /*border: 1px solid #b9b9b9;*/
    margin-top: 10px;
    margin-bottom: 20px;
    /*box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.22);
    -moz-box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.22);
    -webkit-box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.22);*/
}

.navbar-right {
    margin-right: 0;
}

.bg-green {
    background-color: #8bc34a !important;
    border-color: #8bc34a !important;
}

    .bg-green a {
        color: hsla(0,0%,100%,.9) !important;
        font-size: 14px;
    }

/*.page-header {
    margin: 20px 0 0 0;
    position: relative;
}*/

/*.breadcrumb {
    margin-bottom: 0;
    background-color: #eeeded;
}*/

.btn-save {
    margin-top: 5px;
}

/*.btn-save:hover {
        box-shadow: 0 5px 5px rgba(0,0,0,.12), 0 1px 2px rgba(0,0,0,.24);
    }*/

/*.btn {
    padding: 9px 17px;
}*/

.panel-title {
    /*color: #f44336 !important;*/
    /*font-weight: bold;*/
}

/*.demo {
    margin-top: -19px;
    display: block;
    background-color: white;
    width: fit-content;
    text-align: center;
}*/

.border-bottom {
    font-weight: bold;
    font-style: italic;
    border-bottom: 2px solid #ddd;
    margin: 15px 0;
    font-size: 14px;
    padding-bottom: 3px;
    /*color: #2a88ad;*/
}
/*.border-top-warning {
    border-top: 2px solid #8bc34a;
}*/


/*ul {
    overflow: auto;
    list-style-type: none;
}

li {
    height: 25px;
    float: left;
    margin-right: 0px;
    border-right: 1px solid #aaa;
    padding: 0 20px;
}

    li:last-child {
        border-right: none;
    }

    li a {
        text-decoration: none;
        color: #ccc;
        font: 15px/1 Helvetica, Verdana, sans-serif;
        text-transform: uppercase;
        -webkit-transition: all 0.5s ease;
    }

        li a:hover {
            color: #666;
        }


    li.active a {
        font-weight: bold;
        color: #333;
    }*/

/*.uploader {
    width: 100%;
    position: relative;
    display: table;
}

.uploader input[type=file] {
    width: 100%;
    margin-top: 0;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    height: 36px;
    border: 0;
    cursor: pointer;
    z-index: 10;
    opacity: 0;
    filter: alpha(opacity=0);
}

input[type="file"] {
    display: block;
}*/

/*::-webkit-file-upload-button {
    color: white;
    background-color: #4caf50;
    font-family: 'Lato', sans-serif;
    text-transform: uppercase;
    border: 0;
    border-radius: 2px;
    font-size: 12px;
    line-height: 32px;
    height: 32px;
    font-weight: normal;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    white-space: nowrap;
    padding: 0 12px;
}*/

/* for poor sods using IE */
/*::-ms-browse {
    color: black;
    background-color: #2196f3;
    font-family: 'Lato', sans-serif;
    text-transform: uppercase;
    border: 0;
    border-radius: 2px;
    font-size: 12px;
    line-height: 32px;
    height: 32px;
    font-weight: normal;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    white-space: nowrap;
    padding: 0 12px;
}*/

.btn-custom {
    background-color: #f5f5f5;
    border-color: transparent;
    font-weight: 500;
    text-transform: uppercase;
    border-width: 0;
}

    .btn-custom:hover {
        background-color: white;
    }

button[data-toggle="popover"].btn-default {
    /*border: #f44336;*/
    /*color: #888;*/
    width: 100%;
    line-height: 30px;
    margin-bottom: 10px;
    /*background-color: whitesmoke;*/
    box-shadow: 0 1px 3px rgba(0,0,0,.12), 0 1px 2px rgba(0,0,0,.24);
    border: none;
}

    button[data-toggle="popover"].btn-default:hover {
        color: white;
        box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.22);
        /*background-color: #ccc;*/
        background-color: #4caf50;
    }

button[type="submit"].btn:focus, button[type="button"].btn:focus {
    outline: none;
    outline-offset: unset;
}

button[data-toggle="popover"] i {
    color: orange;
}

h6.panel-title {
    font-size: 20px;
    font-family: Arial;
    color: #3f51b5;
    /*font-weight: bold;*/
}

#page-left {
    width: 100%;
    height: 100%;
    overflow: auto;
    /*background-color: blanchedalmond;*/
}

.panel-right-heading a {
}

.panel-right-body {
    background-color: white;
    height: 85%;
}

footer {
    position: absolute;
    bottom: 0;
    left: 20px;
    right: 20px;
    text-align: center;
    padding: 7px 0;
}

/*.panel-right-footer {
    background-color: white;
}*/

.panel-right-footer p {
    margin: 0;
}

iframe.form-control {
    height: 100%;
    border-radius: unset;
    padding: 0;
}

form {
    height: 100%;
}

.popover {
    width: 400px;
    max-width: 1000px;
    box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.22);
}

.popover-content label.col-md-3, .popover-content div.col-md-9 {
    padding: 3px 0;
}

label.error {
    color: red;
}

.notify {
    width: 200px;
    height: 45px;
    background: #4caf50;
    color: white;
    line-height: 45px;
    padding-left: 10px;
    -webkit-animation: mymove 2s infinite; /* Safari 4.0 - 8.0 */
    animation: mymove ease 1s;
    position: absolute;
    top: 50px;
    right: 20px;
    font-size: 14px;
}

/* Safari 4.0 - 8.0 */
@-webkit-keyframes mymove {
    0% {
        top: 10px;
    }

    100% {
        top: 50px;
    }
}

@keyframes mymove {
    0% {
        top: 10px;
    }

    100% {
        top: 50px;
    }
}
/*https: //www.sanwebe.com/2014/08/css-html-forms-designs*/

/*popover hover-https://codepen.io/mihaeltomic/pen/PqxVaq */

