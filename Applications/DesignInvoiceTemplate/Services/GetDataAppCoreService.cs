using DesignInvoiceTemplate.Models;
using DesignInvoiceTemplate.Models.Core;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace DesignInvoiceTemplate.Services
{
    public interface IGetDataAppCoreService
    {
        /// <summary>
        /// lấy thông tin tenantInfo từ core
        /// </summary>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        Task<TenantInfoModel> GetTenantInfoByIdAsync(Guid tenantId);

        /// <summary>
        /// lấy thông tin mẫu từ core
        /// </summary>
        /// <param name="invoiceTemplateId"></param>
        /// <returns></returns>
        Task<InvoiceTemplateCore> GetInvoiceTemplateAsync(long invoiceTemplateId);

        /// <summary>
        /// update lại idDesign vào mẫu hóa đơn ở app core
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="invoiceTemplateId"></param>
        Task UpdateDesignTemplateCoreAsync(Guid tenantId, long invoiceTemplateId);
    }

    public class GetDataAppCoreService : IGetDataAppCoreService
    {
        private readonly AppCoreOption _appCoreOption;
        private readonly string _secretKey;

        public GetDataAppCoreService(IConfiguration configuration, IOptions<AppCoreOption> options)
        {
            _appCoreOption = options.Value;
            _secretKey = configuration.GetSection("Identity:SecretKey").Value;
        }

      
        public async Task<InvoiceTemplateCore> GetInvoiceTemplateAsync(long invoiceTemplateId)
        {
            //gọi api lấy thông tin từ app core
            using var client = new HttpClient
            {
                BaseAddress = new Uri(_appCoreOption.Endpoint),
                Timeout = TimeSpan.FromSeconds(_appCoreOption.Timeout)
            };
            client.DefaultRequestHeaders.Accept.Clear();
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            var response = await client.GetAsync($"design-template/template/{invoiceTemplateId}/{_secretKey}");
            if (!response.IsSuccessStatusCode)
                throw new Exception("Có lỗi trong quá trình in" + $"\n StatusCode: {response.IsSuccessStatusCode}. \n Message: {response.RequestMessage}");

            var responseBody = await response.Content.ReadAsStringAsync();
            var invoiceTemplate = JsonConvert.DeserializeObject<InvoiceTemplateCore>(responseBody);

            return invoiceTemplate;
        }

        public async Task<TenantInfoModel> GetTenantInfoByIdAsync(Guid tenantId)
        {
            //gọi api lấy thông tin từ app core
            using var client = new HttpClient
            {
                BaseAddress = new Uri(_appCoreOption.Endpoint),
                Timeout = TimeSpan.FromSeconds(_appCoreOption.Timeout)
            };
            client.DefaultRequestHeaders.Accept.Clear();
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            var response = await client.GetAsync($"design-template/tenant-info/{tenantId}/{_secretKey}");
            if (!response.IsSuccessStatusCode)
                throw new Exception("Có lỗi trong quá trình in" + $"\n StatusCode: {response.IsSuccessStatusCode}. \n Message: {response.RequestMessage}");

            var responseBody = await response.Content.ReadAsStringAsync();
            var tenantInfoModel = JsonConvert.DeserializeObject<TenantInfoModel>(responseBody);

            return tenantInfoModel;
        }

        public async Task UpdateDesignTemplateCoreAsync(Guid tenantId, long invoiceTemplateId)
        {
            //gọi api in
            using var client = new HttpClient
            {
                BaseAddress = new Uri(_appCoreOption.Endpoint),
                Timeout = TimeSpan.FromSeconds(_appCoreOption.Timeout)
            };
            
            client.DefaultRequestHeaders.Accept.Clear();
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            var updateDesignModel = new
            {
                TemplateId = invoiceTemplateId,
                TenantId = tenantId,
                SecretKey = _secretKey
            };

            string jsonToken = JsonConvert.SerializeObject(updateDesignModel);
            var httpContent = new StringContent(jsonToken, Encoding.UTF8, "application/json");

            var response = await client.PutAsync($"design-template/template/{invoiceTemplateId}", httpContent);
            if (!response.IsSuccessStatusCode)
                throw new Exception("Có lỗi trong quá trình kết nối tới app core" + $"\n StatusCode: {response.IsSuccessStatusCode}. \n Message: {response.RequestMessage}");
        }
    }
}
