using DesignInvoiceTemplate.Models.Core;
using HtmlToPdf;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace DesignInvoiceTemplate.Models.TypeModels._04HGDL
{
    public class BaseDesign04Model
    {
        /// <summary>
        /// id bản ghi
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// số thứ tự mẫu: từ 1 tới 17
        /// </summary>
        public int FileTemplateId { get; set; }

        /// <summary>
        /// code của mẫu hóa đơn ở core
        /// </summary>
        public long InvoiceTemplateId { get; set; }

        public Guid TenantId { get; set; }

        public DateTime CreationTime { get; set; }

        /// <summary>
        /// app id
        /// </summary>
        public string AppId { get; set; }

        /// <summary>
        /// hướng : ngang, dọc
        /// </summary>
        public PageOrientation Orientation { get; set; }

        /// <summary>
        /// size A3, A4, A5,..
        /// </summary>
        public PageSize Size { get; set; }

        public PageMargins Margins { get; set; }

        public string Header { get; set; }
        public string Footer { get; set; }
        public string FooterPosition { get; set; }
        public string HeaderPosition { get; set; }

        public int FontSize { get; set; }

        public string FontColor { get; set; }

        public string FontFamily { get; set; }

        public InvoiceMetaData MetaData { get; set; }
        public string FilePdfPath { get; set; }

        /// <summary>
        /// độ mờ của ảnh nền
        /// </summary>
        public int FillOpacity { get; set; }

        /// <summary>
        /// dấu ngăn cách phần nguyên và phần thập phân
        /// </summary>
        public string NumberDecimalSeparator { get; set; }

        /// <summary>
        /// dấu ngăn cách phần hàng trăm hàng chục
        /// </summary>
        public string NumberGroupSeparator { get; set; }


        public static explicit operator BaseDesign04Model(DesignTemplateModel designTemplate)
        {
            if (designTemplate == null)
                return null;

            var result = new BaseDesign04Model
            {
                Footer = designTemplate.Footer,
                CreationTime = designTemplate.CreationTime,
                Header = designTemplate.Header,
                Id = designTemplate.Id,
                InvoiceTemplateId = designTemplate.InvoiceTemplateId,
                FileTemplateId = designTemplate.FileTemplateId,
                TenantId = designTemplate.TenantId,
                Margins = designTemplate.Margins,
                Orientation = designTemplate.Orientation,
                Size = designTemplate.Size,
                FontColor = designTemplate.FontColor,
                FontFamily = designTemplate.FontFamily,
                FontSize = designTemplate.FontSize,
                FooterPosition = designTemplate.FooterPosition,
                HeaderPosition = designTemplate.HeaderPosition,
                FilePdfPath = designTemplate.FilePdfPath,
                FillOpacity = designTemplate.FillOpacity,
                NumberDecimalSeparator = designTemplate.NumberDecimalSeparator,
                NumberGroupSeparator = designTemplate.NumberGroupSeparator,
                AppId = designTemplate.AppId,
                MetaData = new InvoiceMetaData
                {
                    Logo = GetElement(ElementModel.Logo.ToString(), designTemplate),
                    BackGround = GetElement(ElementModel.BackGround.ToString(), designTemplate),
                    TemplateName = GetElement(ElementModel.InvoiceTemplateName.ToString(), designTemplate),
                    TemplateNo = GetElement(ElementModel.TemplateNo.ToString(), designTemplate),
                    SerialNo = GetElement(ElementModel.SerialNo.ToString(), designTemplate),
                    TransactionId = GetElement(ElementModel.TransactionId.ToString(), designTemplate),
                    
                    InvoiceDate = GetElement(ElementModel.InvoiceDate.ToString(), designTemplate),
                    InvoiceNo = GetElement(ElementModel.InvoiceNo.ToString(), designTemplate),
                    
                    SellerFullName = GetElement(ElementModel.SellerFullName.ToString(), designTemplate),
                    SellerTaxcode = GetElement(ElementModel.SellerTaxCode.ToString(), designTemplate),
                    SellerAddress = GetElement(ElementModel.SellerAddress.ToString(), designTemplate),
                    SellerPhoneNumber = GetElement(ElementModel.SellerPhoneNumber.ToString(), designTemplate),
                    SellerBankAccount = GetElement(ElementModel.SellerBankAccount.ToString(), designTemplate),
                    SellerBankName = GetElement(ElementModel.SellerBankName.ToString(), designTemplate),
                    SellerEmail = GetElement(ElementModel.SellerEmail.ToString(), designTemplate),
                    SellerFaxNumber = GetElement(ElementModel.SellerFaxNumber.ToString(), designTemplate),

                    BuyerFullName = GetElement(ElementModel.BuyerFullName.ToString(), designTemplate),
                    BuyerPhoneNumber = GetElement(ElementModel.BuyerPhoneNumber.ToString(), designTemplate),
                    BuyerCode = GetElement(ElementModel.BuyerCode.ToString(), designTemplate),
                    BuyerLegalName = GetElement(ElementModel.BuyerLegalName.ToString(), designTemplate),
                    BuyerTaxCode = GetElement(ElementModel.BuyerTaxCode.ToString(), designTemplate),
                    BuyerEmail = GetElement(ElementModel.BuyerEmail.ToString(), designTemplate),
                    BuyerAddress = GetElement(ElementModel.BuyerAddress.ToString(), designTemplate),
                    BuyerBankAccount = GetElement(ElementModel.BuyerBankAccount.ToString(), designTemplate),

                    ContractNumber = GetElement(ElementModel.ContractNumber.ToString(), designTemplate),
                    DeliveryBy = GetElement(ElementModel.DeliveryBy.ToString(), designTemplate),
                    FromWarehouseName = GetElement(ElementModel.FromWarehouseName.ToString(), designTemplate),
                    ToWarehouseName = GetElement(ElementModel.ToWarehouseName.ToString(), designTemplate),
                    TransportationMethod = GetElement(ElementModel.TransportationMethod.ToString(), designTemplate),
                    EconomicContractDate = GetElement(ElementModel.EconomicContractDate.ToString(), designTemplate),
                    EconomicContractNumber = GetElement(ElementModel.EconomicContractNumber.ToString(), designTemplate),
                    EconomicContractOf = GetElement(ElementModel.EconomicContractOf.ToString(), designTemplate),

                    PaymentMethod = GetElement(ElementModel.PaymentMethod.ToString(), designTemplate),
                    ExchangeRate = GetElement(ElementModel.ExchangeRate.ToString(), designTemplate),
                    PrintedTime = GetElement(ElementModel.PrintedTime.ToString(), designTemplate),

                    TotalPaymentAmount = GetElement(ElementModel.TotalPaymentAmount.ToString(), designTemplate),
                    PaymentAmountWords = GetElement(ElementModel.PaymentAmountWords.ToString(), designTemplate),
                    Note = GetElement(ElementModel.Note.ToString(), designTemplate),

                    QrCode = GetElement(ElementModel.QrCode.ToString(), designTemplate),
                    SellerFullNameSigned = GetElement(ElementModel.SellerFullNameSigned.ToString(), designTemplate).Value,
                    SellerSignedTime = GetElement(ElementModel.SellerSignedTime.ToString(), designTemplate),

                    InvoiceDetails = JsonConvert.DeserializeObject<Invoice04DetailOption>(GetElement(ElementModel.InvoiceDetails.ToString(), designTemplate).Value),
                    //InvoiceHeaderExtras = JsonConvert.DeserializeObject<OptionHeaderExtras>(GetElement(ElementModel.InvoiceHeaderExtras.ToString(), designTemplate).Value),
                }
            };

            if (designTemplate.MetaData.ContainsKey(ElementModel.FakeLineInvoiceDetail.ToString()))
                result.MetaData.FakeLineInvoiceDetail = int.Parse(designTemplate.MetaData[ElementModel.FakeLineInvoiceDetail.ToString()].Value);
            else
                result.MetaData.FakeLineInvoiceDetail = 3; //mặc định là 3 dòng

            return result;
        }


        private static ElementOption GetElement(string key, DesignTemplateModel designTemplate)
        {
            if (designTemplate.MetaData.ContainsKey(key))
                return designTemplate.MetaData[key];

            return new ElementOption(key.ToString(), key);
        }
    }

    public class InvoiceMetaData
    {
        /// <summary>
        /// đường dẫn file logo
        /// </summary>
        public ElementOption Logo { get; set; }

        /// <summary>
        ///đường dẫn file ảnh nền
        /// </summary>
        public ElementOption BackGround { get; set; }

        //thông tin hóa đơn

        /// <summary>
        /// tên mẫu
        /// </summary>
        public ElementOption TemplateName { get; set; }

        /// <summary>
        /// mẫu số
        /// </summary>
        public ElementOption TemplateNo { get; set; }

        /// <summary>
        /// ký hiệu
        /// </summary>
        public ElementOption SerialNo { get; set; }

        /// <summary>
        /// số bảo mật
        /// </summary>
        public ElementOption TransactionId { get; set; }

        public ElementOption InvoiceDate { get; set; }
        public ElementOption InvoiceNo { get; set; }


        /// <summary>
        /// trạng thái hóa đơn
        /// </summary>
        public string InvoiceStatus { get; set; }


        /// <summary>
        /// trạng thái ký của hóa đơn
        /// </summary>
        public string SignStatus { get; set; }


        #region thông tin người bán
        /// <summary>
        ///  tên người bán
        /// </summary>
        public ElementOption SellerFullName { get; set; }

        /// <summary>
        /// mst người bán
        /// </summary>
        public ElementOption SellerTaxcode { get; set; }

        /// <summary>
        /// địa chỉ người bán
        /// </summary>
        public ElementOption SellerAddress { get; set; }

        /// <summary>
        /// địên thoại người bán
        /// </summary>
        public ElementOption SellerPhoneNumber { get; set; }
        public string SellerFullNameSigned { get; set; }
        public ElementOption SellerSignedTime { get; set; }

        /// <summary>
        /// số tài khoản người bán
        /// </summary>
        public ElementOption SellerBankAccount { get; set; }
        public ElementOption SellerFaxNumber { get; set; }
        public ElementOption SellerEmail { get; set; }
        public ElementOption SellerBankName { get; set; }
        #endregion


        #region người mua
        public ElementOption BuyerCode { get; set; }

        /// <summary>
        /// tên đơn vị mua hàng
        /// </summary>
        public ElementOption BuyerFullName { get; set; }

        /// <summary>
        /// tên người mua
        /// </summary>
        public ElementOption BuyerLegalName { get; set; }

        /// <summary>
        ///  mã số thuế người mua
        /// </summary>
        public ElementOption BuyerTaxCode { get; set; }

        /// <summary>
        /// email người mua
        /// </summary>
        public ElementOption BuyerEmail { get; set; }

        /// <summary>
        /// địa chỉ người mua
        /// </summary>
        public ElementOption BuyerAddress { get; set; }
        public ElementOption BuyerBankAccount { get; set; }
        public ElementOption BuyerPhoneNumber { get; set; }
        #endregion 


        #region Thông tin Phiếu xuất kho hàng giao gửi đại lý
        /// <summary>
        /// Hợp đồng kinh tế số  
        /// MaxLength 250
        /// </summary>
        public ElementOption EconomicContractNumber { get; set; }

        /// <summary>
        /// Ngày hợp đồng kinh tế
        /// </summary>
        public ElementOption EconomicContractDate { get; set; }

        /// <summary>
        /// Trường "Của" hiển thị trên mẫu in
        /// Lấy dữ liệu theo trường "SellerFullName"
        /// </summary>
        public ElementOption EconomicContractOf { get; set; }


        /// <summary>
        /// Họ và Tên người vận truyển
        /// MaxLength 250
        /// </summary>
        public ElementOption DeliveryBy { get; set; }

        /// <summary>
        /// hợp đồng số
        /// MaxLength 250
        /// </summary>
        public ElementOption ContractNumber { get; set; }

        /// <summary>
        /// Phương tiện vận chuyển
        /// MaxLength 250
        /// </summary>
        public ElementOption TransportationMethod { get; set; }

        /// <summary>
        /// Kho xuất
        /// MaxLength 250
        /// </summary>
        public ElementOption FromWarehouseName { get; set; }

        /// <summary>
        /// Kho nhập
        /// MaxLength 250
        /// </summary>
        public ElementOption ToWarehouseName { get; set; }
        #endregion



        /// <summary>
        /// hình thức thanh toán
        /// </summary>
        public ElementOption PaymentMethod { get; set; }
        public ElementOption ExchangeRate { get; set; }

        public ElementOption PrintedTime { get; set; }

        public ElementOption TotalPaymentAmount { get; set; }
        public ElementOption PaymentAmountWords { get; set; }

        /// <summary>
        /// ghi chú hóa đơn
        /// </summary>
        public ElementOption Note { get; set; }

        public ElementOption QrCode { get; set; }

        public Invoice04DetailOption InvoiceDetails { get; set; }


        /// <summary>
        /// thông tin hóa đơn reference
        /// </summary>
        public InvoiceReferenceModel InvoiceReference { get; set; }

        public OptionHeaderExtras<PdfInvoiceHeaderExtraModel> InvoiceHeaderExtras { get; set; }

        public int FakeLineInvoiceDetail { get; set; }
    }
}
