using DesignInvoiceTemplate.Models.Core;
using HtmlToPdf;
using Newtonsoft.Json;
using System;

namespace DesignInvoiceTemplate.Models.TypeModels._01BLP
{
    public class BaseReceipt01Model
    {
        /// <summary>
        /// id bản ghi
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// số thứ tự mẫu: từ 1 tới 17
        /// </summary>
        public int FileTemplateId { get; set; }

        /// <summary>
        /// code của mẫu biên lai ở core
        /// </summary>
        public long InvoiceTemplateId { get; set; }

        public Guid TenantId { get; set; }

        public DateTime CreationTime { get; set; }

        /// <summary>
        /// app id
        /// </summary>
        public string AppId { get; set; }

        /// <summary>
        /// hướng : ngang, dọc
        /// </summary>
        public PageOrientation Orientation { get; set; }

        /// <summary>
        /// size A3, A4, A5,..
        /// </summary>
        public PageSize Size { get; set; }

        public PageMargins Margins { get; set; }

        public string Header { get; set; }
        public string Footer { get; set; }

        public ReceiptMetaData MetaData { get; set; }
        public string FilePdfPath { get; set; }

        public int FontSize { get; set; }

        public string FontColor { get; set; }

        public string FontFamily { get; set; }
        public string FooterPosition { get; set; }
        public string HeaderPosition { get; set; }

        /// <summary>
        /// độ mờ của ảnh nền
        /// </summary>
        public int FillOpacity { get; set; }

        /// <summary>
        /// dấu ngăn cách phần nguyên và phần thập phân
        /// </summary>
        public string NumberDecimalSeparator { get; set; }

        /// <summary>
        /// dấu ngăn cách phần hàng trăm hàng chục
        /// </summary>
        public string NumberGroupSeparator { get; set; }

        public static explicit operator BaseReceipt01Model(DesignTemplateModel designTemplate)
        {
            if (designTemplate == null)
                return null;

            var result = new BaseReceipt01Model
            {
                Footer = designTemplate.Footer,
                CreationTime = designTemplate.CreationTime,
                Header = designTemplate.Header,
                Id = designTemplate.Id,
                InvoiceTemplateId = designTemplate.InvoiceTemplateId,
                FileTemplateId = designTemplate.FileTemplateId,
                TenantId = designTemplate.TenantId,
                Margins = designTemplate.Margins,
                Orientation = designTemplate.Orientation,
                Size = designTemplate.Size,
                FilePdfPath = designTemplate.FilePdfPath,
                FontColor = designTemplate.FontColor,
                FontFamily = designTemplate.FontFamily,
                FontSize = designTemplate.FontSize,
                FooterPosition = designTemplate.FooterPosition,
                HeaderPosition = designTemplate.HeaderPosition,
                FillOpacity = designTemplate.FillOpacity,
                NumberDecimalSeparator = designTemplate.NumberDecimalSeparator,
                NumberGroupSeparator = designTemplate.NumberGroupSeparator,
                AppId = designTemplate.AppId,
                MetaData = new ReceiptMetaData
                {
                    Logo = GetElement(ElementModel.Logo.ToString(), designTemplate),
                    BackGround = GetElement(ElementModel.BackGround.ToString(), designTemplate),
                    InvoiceTemplateName = GetElement(ElementModel.InvoiceTemplateName.ToString(), designTemplate),
                    TemplateNo = GetElement(ElementModel.TemplateNo.ToString(), designTemplate),
                    SerialNo = GetElement(ElementModel.SerialNo.ToString(), designTemplate),
                    TransactionId = GetElement(ElementModel.TransactionId.ToString(), designTemplate),
                    ReceiptName = GetElement(ElementModel.ReceiptName.ToString(), designTemplate),

                    ReceiptDate = GetElement(ElementModel.InvoiceDate.ToString(), designTemplate),
                    ReceiptNo = GetElement(ElementModel.InvoiceNo.ToString(), designTemplate),
                    SellerFullName = GetElement(ElementModel.SellerFullName.ToString(), designTemplate),
                    SellerTaxCode = GetElement(ElementModel.SellerTaxCode.ToString(), designTemplate),
                    SellerAddress = GetElement(ElementModel.SellerAddress.ToString(), designTemplate),
                    SellerPhoneNumber = GetElement(ElementModel.SellerPhoneNumber.ToString(), designTemplate),
                    SellerFaxNumber = GetElement(ElementModel.SellerFaxNumber.ToString(), designTemplate),
                    SellerBankAccount = GetElement(ElementModel.SellerBankAccount.ToString(), designTemplate),
                    SellerEmail = GetElement(ElementModel.SellerEmail.ToString(), designTemplate),
                    SellerBankName = GetElement(ElementModel.SellerBankName.ToString(), designTemplate),
                    BuyerFullName = GetElement(ElementModel.BuyerFullName.ToString(), designTemplate),
                    BuyerPhoneNumber = GetElement(ElementModel.BuyerPhoneNumber.ToString(), designTemplate),
                    BuyerCode = GetElement(ElementModel.BuyerCode.ToString(), designTemplate),
                    BuyerLegalName = GetElement(ElementModel.BuyerLegalName.ToString(), designTemplate),
                    BuyerTaxCode = GetElement(ElementModel.BuyerTaxCode.ToString(), designTemplate),
                    BuyerEmail = GetElement(ElementModel.BuyerEmail.ToString(), designTemplate),
                    BuyerAddress = GetElement(ElementModel.BuyerAddress.ToString(), designTemplate),
                    BuyerBankAccount = GetElement(ElementModel.BuyerBankAccount.ToString(), designTemplate),
                    PaymentMethod = GetElement(ElementModel.PaymentMethod.ToString(), designTemplate),
                    ExchangeRate = GetElement(ElementModel.ExchangeRate.ToString(), designTemplate),
                    PrintedTime = GetElement(ElementModel.PrintedTime.ToString(), designTemplate),

                    TotalPaymentAmount = GetElement(ElementModel.TotalPaymentAmount.ToString(), designTemplate),
                    PaymentAmountWords = GetElement(ElementModel.PaymentAmountWords.ToString(), designTemplate),
                    PaymentAmountWordEns = GetElement(ElementModel.PaymentAmountWordsEn.ToString(), designTemplate),
                    Note = GetElement(ElementModel.Note.ToString(), designTemplate),

                    SellerFullNameSigned = GetElement(ElementModel.SellerFullNameSigned.ToString(), designTemplate).Value,
                    SellerSignedTime = DateTime.Now,

                    ReceiptDetails = JsonConvert.DeserializeObject<Receipt01DetailModel>(designTemplate.MetaData[ElementModel.ReceiptDetails.ToString()].Value),
                    //InvoiceHeaderExtras = JsonConvert.DeserializeObject<OptionHeaderExtras>(designTemplate.MetaData[ElementModel.InvoiceHeaderExtras.ToString()].Value),
                }
            };

            if (designTemplate.MetaData.ContainsKey(ElementModel.FakeLineReceiptDetail.ToString()))
                result.MetaData.FakeLineReceiptDetail = int.Parse(designTemplate.MetaData[ElementModel.FakeLineReceiptDetail.ToString()].Value);
            else
                result.MetaData.FakeLineReceiptDetail = 3; //mặc định là 3 dòng

            return result;
        }

        private static ElementOption GetElement(string key, DesignTemplateModel designTemplate)
        {
            if (designTemplate.MetaData.ContainsKey(key))
                return designTemplate.MetaData[key];

            return new ElementOption(key.ToString(), key);
        }
    }

    public class ReceiptMetaData
    {
        /// <summary>
        /// đường dẫn file logo
        /// </summary>
        public ElementOption Logo { get; set; }

        /// <summary>
        ///đường dẫn file ảnh nền
        /// </summary>
        public ElementOption BackGround { get; set; }

        //thông tin biên lai

        /// <summary>
        /// tên mẫu
        /// </summary>
        public ElementOption InvoiceTemplateName { get; set; }

        /// <summary>
        /// tên loại phí (tên của mẫu biên lai)
        /// </summary>
        public ElementOption ReceiptName { get; set; }

        /// <summary>
        /// mẫu số
        /// </summary>
        public ElementOption TemplateNo { get; set; }

        /// <summary>
        /// ký hiệu
        /// </summary>
        public ElementOption SerialNo { get; set; }

        /// <summary>
        /// số bảo mật
        /// </summary>
        public ElementOption TransactionId { get; set; }

        public ElementOption ReceiptDate { get; set; }
        public ElementOption ReceiptNo { get; set; }

        #region thông tin người bán
        /// <summary>
        ///  tên người bán
        /// </summary>
        public ElementOption SellerFullName { get; set; }

        /// <summary>
        /// mst người bán
        /// </summary>
        public ElementOption SellerTaxCode { get; set; }

        /// <summary>
        /// địa chỉ người bán
        /// </summary>
        public ElementOption SellerAddress { get; set; }

        /// <summary>
        /// địên thoại người bán
        /// </summary>
        public ElementOption SellerPhoneNumber { get; set; }

        /// <summary>
        /// fax người bán
        /// </summary>
        public ElementOption SellerFaxNumber { get; set; }

        /// <summary>
        /// số tài khoản người bán
        /// </summary>
        public ElementOption SellerBankAccount { get; set; }

        /// <summary>
        /// email người bán
        /// </summary>
        public ElementOption SellerEmail { get; set; }

        /// <summary>
        /// tên ngân hàng người bán
        /// </summary>
        public ElementOption SellerBankName { get; set; }

        public string SellerFullNameSigned { get; set; }
        public DateTime? SellerSignedTime { get; set; }


        #endregion

        #region người mua
        public ElementOption BuyerCode { get; set; }

        /// <summary>
        /// tên đơn vị mua hàng
        /// </summary>
        public ElementOption BuyerFullName { get; set; }

        /// <summary>
        /// tên người mua
        /// </summary>
        public ElementOption BuyerLegalName { get; set; }

        /// <summary>
        ///  mã số thuế người mua
        /// </summary>
        public ElementOption BuyerTaxCode { get; set; }

        /// <summary>
        /// email người mua
        /// </summary>
        public ElementOption BuyerEmail { get; set; }

        /// <summary>
        /// địa chỉ người mua
        /// </summary>
        public ElementOption BuyerAddress { get; set; }
        public ElementOption BuyerBankAccount { get; set; }
        public ElementOption BuyerPhoneNumber { get; set; }
        #endregion 

        /// <summary>
        /// hình thức thanh toán
        /// </summary>
        public ElementOption PaymentMethod { get; set; }
        public ElementOption ExchangeRate { get; set; }
        public ElementOption PrintedTime { get; set; }

        public ElementOption TotalPaymentAmount { get; set; }
        public ElementOption PaymentAmountWords { get; set; }
        public ElementOption PaymentAmountWordEns { get; set; }

        /// <summary>
        /// ghi chú biên lai
        /// </summary>
        public ElementOption Note { get; set; }

        public int FakeLineReceiptDetail { get; set; }

        public OptionHeaderExtras<PdfReceiptExtraModel> ReceiptExtras { get; set; }

        public Receipt01DetailModel ReceiptDetails { get; set; }
    }
}
