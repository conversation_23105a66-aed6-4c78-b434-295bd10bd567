using System.ComponentModel.DataAnnotations;

namespace DesignInvoiceTemplate.Constants
{
    public enum InvoiceStatus
    {
        [Display(Name = "Gốc")]
        Goc = 1,

        [Display(Name = "Xóa bỏ")]
        XoaBo = 2,

        [Display(Name = "Thay thế")]
        ThayThe = 3,

        [Display(Name = "Bị thay thế")]
        BiThayThe = 4,

        [Display(Name = "Điều chỉnh định danh")]
        DieuChinhDinhDanh = 5,

        [Display(Name = "Bị điều chỉnh định danh")]
        BiDieuChinhDinhDanh = 6,

        [Display(Name = "Điều chỉnh tăng/giảm")]
        DieuChinhTangGiam = 7,

        [Display(Name = "Bị điều chỉnh tăng/giảm")]
        BiDieuChinhTangGiam = 8,

        [Display(Name = "Xóa hủy")]
        XoaHuy = 9
    }
}
