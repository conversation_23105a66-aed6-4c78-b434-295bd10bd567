<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <Version>5.13.0</Version>
    <PreserveCompilationReferences>true</PreserveCompilationReferences>
    <PreserveCompilationContext>true</PreserveCompilationContext>
  </PropertyGroup>

  <ItemGroup>
    <Folder Include="Models\Base\" />
    <Folder Include="wwwroot\files\temps\" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="BouncyCastle.NetCore" Version="1.8.8" />
    <!--<PackageReference Include="Microsoft.AspNetCore.Antiforgery" Version="2.2.0" />-->
    <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
    <PackageReference Include="QRCoder" Version="1.3.5" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="4.10.0" />
    <PackageReference Include="RazorEngine" Version="4.5.1-alpha001" />
    <PackageReference Include="RazorLight" Version="2.0.0-rc.3" />
    <!--<PackageReference Include="RazorLight.NetCore3" Version="3.0.2" />-->
  </ItemGroup>
  
  <ItemGroup>
    <ProjectReference Include="..\..\Databases\VnisCore\Core\Oracle\VnisCore.Core.Oracle.EntityFrameworkCore\VnisCore.Core.Oracle.EntityFrameworkCore.csproj" />
    <ProjectReference Include="..\..\Framework\Shared\Core.Shared\Core.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="HtmlToPdf">
      <HintPath>..\..\lib\DesignTemplate\HtmlToPdf.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.Development.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Update="nlog.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Update="wwwroot\css\site.css">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Update="wwwroot\js\site.js">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

</Project>
