using Core.Application;
using Core.Caching;
using Core.Modularity;
using Core.Shared.HttpMessageHandler;
using Core.Tvan.MInvoice.Interfaces;
using Core.Tvan.MInvoice.Models;
using Core.Tvan.MInvoice.Services;

using Microsoft.Extensions.DependencyInjection;

using System;

namespace Core.Tvan.MInvoice
{
    [DependsOn(
         typeof(AbpDddApplicationModule),
         typeof(AbpCachingModule)
     )]
    public class TvanMInvoiceModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            var configuration = context.Services.GetConfiguration();
            context.Services.Configure<TvanMInvoiceOption>(configuration.GetSection("TvanMInvoice"));

            //tvan invoice
            context.Services.AddHttpClient("tvan-minvoice", (serviceProvider, client) =>
            {
                client.BaseAddress = new Uri(configuration.GetSection("TvanMInvoice:EndPoint").Value);
                client.Timeout = TimeSpan.FromMinutes(5);
            })
            .AddHttpMessageHandler<CorrelationIdHttpMessageHandler>();

            context.Services.AddScoped<ITvanMInvoiceService, TvanMInvoiceService>();
            context.Services.AddSingleton<ITvanMInvoiceInvoiceClient, TvanMInvoiceInvoiceClient>();
            context.Services.AddSingleton<ITvanMInvoiceWithoutCodeService, TvanMInvoiceWithoutCodeService>();
            context.Services.AddSingleton<ITvanInvoiceWithoutCodeClient, TvanMInvoiceWithoutCodeClient>();
        }
    }
}
