using Core.Shared.Attributes;
using Core.Shared.Constants;
using System;
using System.ComponentModel.DataAnnotations;
using VnisCore.Core.Oracle.Domain.Decree.DecreeNo70;

namespace Core.Shared.Invoice.Dtos
{
    public abstract class BaseInvoiceHeaderDto : IInvoiceHeaderDecreeNo70
    {
        /// <summary>
        /// Id hóa đơn
        /// </summary>
        public virtual long Id { get; set; }

        /// <summary>
        /// Id chi nhánh
        /// </summary>
        public virtual Guid TenantId { get; set; }

        /// <summary>
        /// Id người tạo
        /// </summary>
        public virtual Guid CreatorId { get; set; }

        /// <summary>
        /// InvoiceTemplateId
        /// </summary>
        public virtual long InvoiceTemplateId { get; set; }

        /// <summary>
        /// Mẫu số của hóa đơn
        /// </summary>
        //[TemplateNo(ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.TemplateNo.WrongFormat")]
        public virtual short TemplateNo { get; set; }


        /// <summary>
        /// K<PERSON> hiệu hóa đơn
        /// </summary>
        [SerialNo(ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.SerialNo.WrongFormat")]
        [AllowedCharacters()]
        public virtual string SerialNo { get; set; }

        /// <summary>
        /// ErpId
        /// </summary>
        [StringLength(50, ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.ErpId.StringLength")]
        [AllowedCharacters()]
        public virtual string ErpId { get; set; }

        /// <summary>
        /// Mã thanh toán viên
        /// </summary>
        [StringLength(4, ErrorMessage = "Mã thanh toán viên chỉ được nhập từ 1-4 ký tự")]
        [AllowedCharacters()]
        public virtual string CreatorErp
        {
            get { return creatorErp; }
            set
            {
                creatorErp = string.IsNullOrWhiteSpace(value) ? value : value.TrimStart('0');
            }
        }
        private string creatorErp;

        [StringLength(50, ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.TransactionId.StringLength")]
        [AllowedCharacters()]
        public string TransactionId { get; set; }

        /// <summary>
        /// Ngày hóa đơn
        /// </summary>
        [DataType(DataType.DateTime)]
        [Required(ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.InvoiceDate.Required")]
        [LessThanCurrentDate(ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.InvoiceDate.LessThanCurrentDate")]
        public virtual DateTime InvoiceDate { get; set; }

        /// <summary>
        /// Loại hóa đơn
        /// </summary>
        public virtual InvoiceStatus InvoiceStatus { get; set; }

        /// <summary>
        /// Trạng thái ký hóa đơn
        /// </summary>
        public virtual SignStatus SignStatus { get; set; }

        /// <summary>
        /// Trạng thái duyệt hóa đơn
        /// </summary>
        public virtual ApproveStatus ApproveStatus { get; set; }

        /// <summary>
        /// Trạng thái duyệt hóa đơn xóa hủy
        /// </summary>
        public virtual ApproveStatus ApproveCancelStatus { get; set; }

        /// <summary>
        /// Trạng thái duyệt hóa đơn xóa bỏ
        /// </summary>
        public virtual ApproveStatus ApproveDeleteStatus { get; set; }

        /// <summary>
        /// Ghi chú
        /// </summary>
        [MaxLength(500, ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDtoInvoiceStatus.Note.MaxLength")]
        [AllowedCharacters()]
        public virtual string Note { get; set; }

        #region Thông tin thanh toán
        /// <summary>
        /// Phương thức thanh toán 
        /// </summary>
        [Required(ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.PaymentMethod.Required")]
        [StringLength(50, ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.PaymentMethod.StringLength")]
        [AllowedCharacters()]
        public virtual string PaymentMethod { get; set; }

        /// <summary>
        /// Nguyên tệ
        /// </summary>
        public virtual string FromCurrency { get; set; }

        /// <summary>
        /// Chuyến đến tiền tệ
        /// </summary>
        [Required(ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.ToCurrency.Required")]
        [MaxLength(3, ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.ToCurrency.MaxLength")]
        [AllowedCharacters()]
        public virtual string ToCurrency { get; set; }
        public virtual int RoundingCurrency { get; set; }
        public virtual int CurrencyConversion { get; set; }

        /// <summary>
        /// Tỷ giá
        /// </summary>
        [Required(ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.ExchangeRate.Required")]
        [MoreThanValue("0", typeof(decimal), ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.ExchangeRate.MoreThanValueAttribute")]
        public virtual decimal ExchangeRate { get; set; }


        /// <summary>
        /// Tổng tiền hàng trước thuế
        /// </summary>
        //[Range(0, double.MaxValue, ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.TotalAmount.Range")]
        public virtual decimal TotalAmount { get; set; }

        /// <summary>
        /// Tổng tiền phải trả
        /// </summary>
        //[Range(0, double.MaxValue, ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.TotalPaymentAmount.Range")]
        public virtual decimal TotalPaymentAmount { get; set; }

        /// <summary>
        /// Số tiền bằng chữ (Tiếng Việt)
        /// </summary>
        public virtual string PaymentAmountWords { get; set; }

        /// <summary>
        /// Số tiến bằng chữ (Tiếng Anh)
        /// </summary>
        public virtual string PaymentAmountWordsEn { get; set; }

        #endregion Thông tin thanh toán

        #region Thông tin người mua
        /// <summary>
        /// Mã người mua
        /// </summary>
        [Required(ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.BuyerCode.Required")]
        [MaxLength(50, ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.BuyerCode.MaxLength")]
        [CustomerId(ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.BuyerCode.WrongFormat")]
        [AllowedCharacters()]
        public virtual string BuyerCode { get; set; }


        /// <summary>
        /// Email người mua
        /// </summary>
        [StringLength(500, ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.BuyerEmail.Required")]
        [Email(ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.BuyerEmail.WrongFormat")]
        [AllowedCharacters()]
        public virtual string BuyerEmail { get; set; }

        /// <summary>
        /// Họ tên người mua
        /// </summary>
        [Required(ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.BuyerFullName.Required")]
        [StringLength(250, ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.BuyerFullName.StringLength")]
        [AllowedCharacters()]
        public virtual string BuyerFullName { get; set; }


        /// <summary>
        /// Tên khách hàng
        /// </summary>
        [StringLength(250, ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.BuyerLegalName.StringLength")]
        [AllowedCharacters()]
        public virtual string BuyerLegalName { get; set; }


        /// <summary>
        /// Mã số thuế người mua
        /// </summary>
        [StringLength(50, ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.BuyerTaxCode.StringLength")]
        [TaxCode(ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.BuyerTaxCode.WrongFormat")]
        [AllowedCharacters()]
        public virtual string BuyerTaxCode { get; set; }


        /// <summary>
        /// Địa chỉ người mua
        /// </summary>
        [Required(ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.BuyerAddressLine.Required")]
        [StringLength(500, ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.BuyerAddressLine.StringLength")]
        [AllowedCharacters()]
        public virtual string BuyerAddressLine { get; set; }


        /// <summary>
        /// Quân
        /// </summary>
        [StringLength(250, ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.BuyerDistrictName.StringLength")]
        [AllowedCharacters()]
        public virtual string BuyerDistrictName { get; set; }


        /// <summary>
        /// Thành phố
        /// </summary>
        [StringLength(250, ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.BuyerCityName.StringLength")]
        [AllowedCharacters()]
        public virtual string BuyerCityName { get; set; }


        /// <summary>
        /// Mã quốc gia
        /// </summary>
        [StringLength(5, ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.BuyerCountryCode.StringLength")]
        [AllowedCharacters()]
        public virtual string BuyerCountryCode { get; set; }

        /// <summary>
        /// Số điện thoại người mua
        /// </summary>
        [StringLength(50, ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.BuyerPhoneNumber.StringLength")]
        [AllowedCharacters()]
        public virtual string BuyerPhoneNumber { get; set; }

        /// <summary>
        /// Số Fax
        /// </summary>
        [StringLength(50, ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.BuyerFaxNumber.StringLength")]
        [AllowedCharacters()]
        public virtual string BuyerFaxNumber { get; set; }


        /// <summary>
        /// Số tài khoản người mua
        /// </summary>
        [StringLength(250, ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.BuyerBankAccount.StringLength")]
        [AllowedCharacters()]
        public virtual string BuyerBankAccount { get; set; }


        /// <summary>
        /// Tên ngân hàng người mua
        /// </summary>
        [StringLength(250, ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.BuyerBankName.StringLength")]
        [AllowedCharacters()]
        public virtual string BuyerBankName { get; set; }


        /// <summary>
        /// Loại khách hàng: Cá nhân (I), Tổ chức (C)
        /// </summary>
        [Required(ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.BuyerType.Required")]
        [AllowedCharacters()]
        public virtual string BuyerType { get; set; }
        #endregion Thông tin người mua

        #region Thông tin người bán
        /// <summary>
        /// Id bản ghi người bán (Organization)
        /// </summary>
        public Guid SellerId { get; set; }

        /// <summary>
        /// Mã công ty/chi nhánh
        /// </summary>
        public string SellerCode { get; set; }

        /// <summary>
        /// Người đại diện pháp nhân bên bán
        /// </summary>
        public string SellerLegalName { get; set; }

        /// <summary>
        /// Mã số thuế bên bán
        /// </summary>
        public string SellerTaxCode { get; set; }

        /// <summary>
        /// Địa chỉ bên bán
        /// </summary>
        public string SellerAddressLine { get; set; }

        /// <summary>
        /// Mã quốc gia người bán (Việt Nam là VN)
        /// </summary>
        public string SellerCountryCode { get; set; }

        /// <summary>
        /// Tên phường/xã người bán
        /// </summary>
        public string SellerDistrictName { get; set; }

        /// <summary>
        /// Tên thành phố người bán
        /// </summary>
        public string SellerCityName { get; set; }

        /// <summary>
        /// Số điện thoại người bán
        /// </summary>
        public string SellerPhoneNumber { get; set; }

        /// <summary>
        /// Số FAX người bán
        /// </summary>
        public string SellerFaxNumber { get; set; }

        /// <summary>
        /// Email người bán
        /// </summary>
        public string SellerEmail { get; set; }

        /// <summary>
        /// Tên ngân hàng người bán
        /// </summary>
        public string SellerBankName { get; set; }

        /// <summary>
        /// Tài khoản ngân hàng người bán
        /// </summary>
        public string SellerBankAccount { get; set; }

        /// <summary>
        /// Tên công ty bán
        /// </summary>
        public string SellerFullName { get; set; }

        /// <summary>
        /// Thời điểm hóa đơn được ký
        /// </summary>
        public DateTime? SellerSignedTime { get; set; }

        /// <summary>
        /// Họ tên người ký hóa đơn (Sử dụng tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string SellerFullNameSigned { get; set; }

        /// <summary>
        /// Tài khoản người ký hóa đơn (Sử dụng tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public Guid? SellerSignedId { get; set; }
        #endregion
        
        /// <summary>
        /// mã DV
        /// </summary>
        [AllowedCharacters()]
        public virtual string DepartmentCode { get; set; }


        /// <summary>
        /// Nguồn tạo hóa đơn
        /// </summary>
        public InvoiceSource Resource { get; set; }

        /// <summary>
        /// chi tiết hóa đơn
        /// </summary>
        //[Required(ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.InvoiceDetails.Required")]
        //[MinLength(1, ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.InvoiceDetails.MinLength")]
        //public virtual List<BaseInvoiceDetailDto> InvoiceDetails { get; set; }


        /// <summary>
        /// chi tiết thuế
        /// </summary>
        //[Required(ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.InvoiceTaxBreakdowns.Required")]
        //[MinLength(1, ErrorMessage = "Vnis.BE.Invoice01.CreateInvoice01HeaderDto.InvoiceTaxBreakdowns.MinLength")]
        //public virtual List<BaseInvoiceTaxBreakdownDto> InvoiceTaxBreakdowns { get; set; }


        /// <summary>
        /// Thông tin trường mở rộng
        /// </summary>
        //public virtual List<BaseInvoiceExtraDto> InvoiceHeaderExtras { get; set; }


        /// <summary>
        /// Mã số đơn vị có quan hệ với ngân sách  
        /// </summary>
        public virtual string BudgetUnitCode { get; set; }

        /// <summary>
        /// Số CCCD/Định danh
        /// </summary>
        public virtual string BuyerIDNumber { get; set; }

        /// <summary>
        /// Số hộ chiếu
        /// </summary>
        public virtual string BuyerPassportNumber { get; set; }
    }
}
