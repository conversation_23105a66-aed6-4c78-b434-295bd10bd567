using System.Collections.Generic;

namespace Core.Shared.Invoice.Interfaces
{
    public interface IValidationXMLService<TInput>
    {
        /// <summary>
        /// Validate dữ liệu hợp lệ của file có định dạng XML
        /// </summary>
        /// <param name="inputs"></param>
        void Validators(List<TInput> inputs);


        /// <summary>
        /// Validate dữ liệu hợp lệ của file có định dạng XML
        /// </summary>
        /// <param name="input"></param>
        void Validator(TInput input);
    }
}
