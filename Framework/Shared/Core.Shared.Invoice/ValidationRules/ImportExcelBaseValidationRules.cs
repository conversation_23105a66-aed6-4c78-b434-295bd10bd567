using Core.ExceptionHandling.Localization;
using Core.Shared.Extensions;
using Core.Shared.Invoice.Dtos;
using FluentValidation;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using System;

namespace Core.Shared.Invoice.ValidationRules

{
    public class ImportExcelBaseValidationRules <TImportExcelBaseDto> : AbstractValidator<TImportExcelBaseDto>
        where TImportExcelBaseDto : ImportExcelBaseDto
    {
        public ImportExcelBaseValidationRules(
            IServiceProvider serviceProvider
        )
        {
            var localizierException = serviceProvider.GetRequiredService<IStringLocalizer<AbpExceptionHandlingResource>>();

            // GroupId
            RuleFor(rule => rule.GroupId)
                   .NotEmpty()
                   .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.GroupId.Required", new string[] { "A", p.RowNumber.ToString() }]);

            // TemplateNo
            RuleFor(rule => rule._TemplateNo)
                .NotEmpty()
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.TemplateNo.Required", new string[] { "C", p.RowNumber.ToString() }]);

            RuleFor(rule => rule.TemplateNo)
                .Must((p, pp) => pp.ToString().IsTemplateNo() && p.SerialNo.IsSerialNo())
                .When(p => !string.IsNullOrWhiteSpace(p._TemplateNo) || !string.IsNullOrWhiteSpace(p.SerialNo), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.TemplateNo.WrongFormat", new string[] { "C", p.RowNumber.ToString() }]);

            // PCTime
            RuleFor(rule => rule.PCTime)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.PCTime.Required", new string[] { "C", p.RowNumber.ToString() }])

                .Must((p, pp) => pp.IsHHMMSS())
                .When(p => !string.IsNullOrWhiteSpace(p.PCTime), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.PCTime.WrongFormat", new string[] { "C", p.RowNumber.ToString() }]);

            // InvoiceDate
            RuleFor(rule => rule._InvoiceDate)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.InvoiceDate.Required", new string[] { "D", p.RowNumber.ToString() }])
                .Must((p, pp) => pp.TryParseExactDDDashMMDashYYYY() != null)
                .When(p => !string.IsNullOrWhiteSpace(p._InvoiceDate), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.WrongFormat", new string[] { "E", p.RowNumber.ToString() }]);
                
                //.Must((p, pp) => commonInvoice01Service.CheckInvoiceDateAndSerialNo(p.GetInvoiceDate(), p.SerialNo))
                //.When(p => !string.IsNullOrWhiteSpace(p.InvoiceDate) && !string.IsNullOrEmpty(p.SerialNo), ApplyConditionTo.CurrentValidator)
                //.WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.WrongFormat", new string[] { "E", p.RowNumber.ToString() }]);

            RuleFor(rule => rule.InvoiceDate)
                .Must((p, pp) => pp <= DateTime.Now.Date)
                .When(p => !string.IsNullOrWhiteSpace(p._InvoiceDate), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.InvoiceDate.GreaterThanToDay", new string[] { "D", p.RowNumber.ToString() }]);

            // CreatorErp
            RuleFor(rule => rule.CreatorErp)
                .Must((p, pp) => pp.IsCreatorErp())
                .When(p => !string.IsNullOrWhiteSpace(p.CreatorErp), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.CreatorErp.WrongFormat", new string[] { "H", p.RowNumber.ToString() }]);

            // TellSeq
            RuleFor(rule => rule.TellSeq)
                .Must((p, pp) => pp.CheckTellSeqVCB())
                .When(p => !string.IsNullOrWhiteSpace(p.TellSeq), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.TellSeq.WrongFormat", new string[] { "I", p.RowNumber.ToString() }]);

            // PaymentMethod
            RuleFor(rule => rule.PaymentMethod)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.PaymentMethod.Required", new string[] { "J", p.RowNumber.ToString() }])

                .Must((p, pp) => pp == "1" || pp == "2")
                .When(p => !string.IsNullOrWhiteSpace(p.PaymentMethod), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.PaymentMethod.WrongFormat", new string[] { "J", p.RowNumber.ToString() }]);

            // Currency
            RuleFor(rule => rule.Currency)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.Currency.Required", new string[] { "K", p.RowNumber.ToString() }])

                .Must((p, pp) => pp.Length <= 3)
                .When(p => !string.IsNullOrWhiteSpace(p.Currency), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.Currency.WrongFormat", new string[] { "K", p.RowNumber.ToString() }]);

            // ExchangeRate
            RuleFor(rule => rule._ExchangeRate)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.ExchangeRate.Required", new string[] { "L", p.RowNumber.ToString() }])

                .Must((p, pp) => decimal.TryParse(pp, out var rs))
                .When(p => !string.IsNullOrWhiteSpace(p._ExchangeRate), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.ExchangeRate.WrongFormat", new string[] { "L", p.RowNumber.ToString() }]);

            RuleFor(rule => rule.ExchangeRate)
                .ScalePrecision(2, 7)
                .When(p => !string.IsNullOrWhiteSpace(p._ExchangeRate), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.ExchangeRate.ScalePrecision", new string[] { "L", p.RowNumber.ToString() }]);

            // BuyerCode
            RuleFor(rule => rule._BuyerCode)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerCode.Required", new string[] { "N", p.RowNumber.ToString() }])

                .Must((p, pp) => pp.IsCustomerCode())
                .When(p => !string.IsNullOrWhiteSpace(p._BuyerCode), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.BuyerCode.WrongFormat", new string[] { "N", p.RowNumber.ToString() }]);
                
            RuleFor(rule => rule.BuyerCode)
                .NotEmpty()
                .When(p => !string.IsNullOrWhiteSpace(p._BuyerCode), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoice.BuyerCode.Invalid", new string[] { "N", p.BuyerCode.ToString() }]);

            // BuyerFullName
            RuleFor(rule => rule.BuyerFullName)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerFullName.Required", new string[] { "O", p.RowNumber.ToString() }])

                .MaximumLength(400)
                .When(p => !string.IsNullOrWhiteSpace(p.BuyerFullName), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerFullName.Maxlength", new string[] { "O", p.RowNumber.ToString() }]);

            // BuyerTaxCode
            RuleFor(rule => rule.BuyerTaxCode)
                .MaximumLength(14)
                .When(p => !string.IsNullOrWhiteSpace(p.BuyerTaxCode), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerTaxCode.Maxlength", new string[] { "P", p.RowNumber.ToString() }])
                .Must((p, pp) => pp.IsTaxCode())
                .When(p => !string.IsNullOrWhiteSpace(p.BuyerTaxCode), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerTaxCode.InValid", new string[] { "P", p.RowNumber.ToString() }]);

            // BudgetUnitCode
            RuleFor(p => p.BudgetUnitCode)
                .MaximumLength(7)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BudgetUnitCode.Maxlength", new string[] { "Q", p.RowNumber.ToString() }]);

            // BuyerIDNumber
            RuleFor(p => p.BuyerIDNumber)
                .MaximumLength(12)
                .When(p => !string.IsNullOrWhiteSpace(p.BuyerIDNumber), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerIDNumber.Maxlength", new string[] { "R", p.RowNumber.ToString() }])
                .Must((p, pp) => pp.IsIDNumber())
                .When(p => !string.IsNullOrWhiteSpace(p.BuyerIDNumber), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerIDNumber.WrongFormat", new string[] { "R", p.RowNumber.ToString() }]);

            // BuyerPassportNumber
            RuleFor(p => p.BuyerPassportNumber)
                .MaximumLength(20)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerPassportNumber.Maxlength", new string[] { "S", p.RowNumber.ToString() }]);

            // BuyerAddressLine
            RuleFor(rule => rule.BuyerAddressLine)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerAddressLine.Required", new string[] { "T", p.RowNumber.ToString() }])

                .MaximumLength(400)
                .When(p => !string.IsNullOrWhiteSpace(p.BuyerAddressLine), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerAddressLine.Maxlength", new string[] { "T", p.RowNumber.ToString() }]);

            // BuyerBankAccount
            RuleFor(rule => rule.BuyerBankAccount)
                .MaximumLength(30)
                .When(p => !string.IsNullOrWhiteSpace(p.BuyerBankAccount), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerBankAccount.Maxlength", new string[] { "U", p.RowNumber.ToString() }]);

            // BuyerBankName
            RuleFor(rule => rule.BuyerBankName)
                .MaximumLength(400)
                .When(p => !string.IsNullOrWhiteSpace(p.BuyerBankName), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerBankName.Maxlength", new string[] { "V", p.RowNumber.ToString() }]);

            // BuyerEmail
            RuleFor(rule => rule.BuyerEmail)
                .MaximumLength(50)
                .When(p => !string.IsNullOrWhiteSpace(p.BuyerEmail), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerEmail.Maxlength", new string[] { "W", p.RowNumber.ToString() }])

                .Must((p, pp) => pp.IsEmail())
                .When(p => !string.IsNullOrWhiteSpace(p.BuyerEmail), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerEmail.WrongFormat", new string[] { "W", p.RowNumber.ToString() }]);

            // BuyerType
            RuleFor(rule => rule.BuyerType)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerType.Required", new string[] { "X", p.RowNumber.ToString() }])

                .Must((p, pp) => pp == "I" || pp == "C")
                .When(p => !string.IsNullOrWhiteSpace(p.BuyerType), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.BuyerType.WrongFormat", new string[] { "X", p.RowNumber.ToString() }]);

            // ProductName
            RuleFor(rule => rule.ProductName)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.ProductName.Required", new string[] { "Z", p.RowNumber.ToString() }])

                .MaximumLength(500)
                .When(p => !string.IsNullOrWhiteSpace(p.ProductName), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.ProductName.MaximumLength", new string[] { "Z", p.RowNumber.ToString() }]);

            // UnitName
            RuleFor(rule => rule.UnitName)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.UnitName.Required", new string[] { "AA", p.RowNumber.ToString() }]);

            // Quantity
            RuleFor(rule => rule._Quantity)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.Quantity.Required", new string[] { "AB", p.RowNumber.ToString() }])

                .Must((p, pp) => decimal.TryParse(pp, out var rs))
                .When(p => !string.IsNullOrWhiteSpace(p._Quantity), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.Quantity.WrongFormat", new string[] { "AB", p.RowNumber.ToString() }]);

            // UnitPrice
            RuleFor(rule => rule._UnitPrice)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.UnitPrice.Required", new string[] { "AC", p.RowNumber.ToString() }])

                .Must((p, pp) => decimal.TryParse(pp, out var rs))
                .When(p => !string.IsNullOrWhiteSpace(p._UnitPrice), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.UnitPrice.WrongFormat", new string[] { "AC", p.RowNumber.ToString() }]);

            // Amount
            RuleFor(rule => rule._Amount)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.Amount.Required", new string[] { "AD", p.RowNumber.ToString() }])

                .Must((p, pp) => decimal.TryParse(pp, out var rs))
                .When(p => !string.IsNullOrWhiteSpace(p._Amount), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.Amount.WrongFormat", new string[] { "AD", p.RowNumber.ToString() }]);

            // VatPercent
            RuleFor(rule => rule._VatPercent)
               .NotEmpty()
               .When(p => true, ApplyConditionTo.CurrentValidator)
               .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.VatPercent.Required", new string[] { "AE", p.RowNumber.ToString() }])

               .Must((p, pp) => decimal.TryParse(pp, out var rs))
               .When(p => !string.IsNullOrWhiteSpace(p._VatPercent), ApplyConditionTo.CurrentValidator)
               .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.VatPercent.WrongFormat", new string[] { "AE", p.RowNumber.ToString() }]);

            RuleFor(rule => rule._VatAmount)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.VatAmount.Required", new string[] { "AF", p.RowNumber.ToString() }])

                .Must((p, pp) => decimal.TryParse(pp, out var rs))
                .When(p => !string.IsNullOrWhiteSpace(p._VatAmount), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.VatAmount.WrongFormat", new string[] { "AF", p.RowNumber.ToString() }]);

            // PaymentAmount
            RuleFor(rule => rule._PaymentAmount)
                .NotEmpty()
                .When(p => true, ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.PaymentAmount.Required", new string[] { "AG", p.RowNumber.ToString() }])

                .Must((p, pp) => decimal.TryParse(pp, out var rs))
                .When(p => !string.IsNullOrWhiteSpace(p._PaymentAmount), ApplyConditionTo.CurrentValidator)
                .WithMessage(p => localizierException["Vnis.BE.Invoice01.ImportSyntheticInvoiceModel.Cell.PaymentAmount.WrongFormat", new string[] { "AG", p.RowNumber.ToString() }]);
        }
    }
}
