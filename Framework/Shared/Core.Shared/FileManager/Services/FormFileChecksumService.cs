using Core.DependencyInjection;
using Core.DistributedCacheRedis;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Cached;
using Core.Shared.FileManager.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Linq;
using System.Security.Cryptography;
using System.Threading.Tasks;

namespace Core.Shared.FileManager.Services
{
    public class FormFileChecksumService : IFormFileChecksumService, IScopedDependency
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly ICacheChecksum _cacheChecksum;
        private readonly IDistributedCacheRedis _distributedCacheRedis;
        private readonly IFormFileService _formFileService;

        public FormFileChecksumService(
            IServiceProvider serviceProvider
        )
        {
            _localizer = serviceProvider.GetRequiredService<IStringLocalizer<CoreLocalizationResource>>();
            _cacheChecksum = serviceProvider.GetRequiredService<ICacheChecksum>();
            _distributedCacheRedis = serviceProvider.GetRequiredService<IDistributedCacheRedis>();
            _formFileService = serviceProvider.GetRequiredService<IFormFileService>();

        }
        /// <summary>
        /// Kiểm tra checksum
        /// </summary>
        /// <returns></returns>
        public async Task CheckSumAsync()
        {
            Log.Information("-> Begin FormFileChecksumService.CheckSum");
            var hashKey = _cacheChecksum.CreateHashKey();
            string checksum = CalculateChecksum(SHA256.Create());
            _cacheChecksum.SetValue(checksum);

            var values = await _distributedCacheRedis.GetListAsync(hashKey);
            if (values != null && values.Any(p => p == checksum))
            {
                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice.ImportExcel.File.InValidChecksum"]);
            }

            await _distributedCacheRedis.AddItemToListAsync(hashKey, checksum);
            Log.Information("-> End FormFileChecksumService.CheckSum");
        }


        /// <summary>
        /// Tính Sum File
        /// </summary>
        /// <param name="algorithm"></param>
        /// <returns></returns>
        public string CalculateChecksum(HashAlgorithm algorithm)
        {
            Log.Information($"-> File Length: {_formFileService.GetSize()}");
            var stream = _formFileService.GetStream();
            byte[] hashBytes = algorithm.ComputeHash(stream);
            var hash = BitConverter.ToString(hashBytes).Replace("-", "").ToUpperInvariant();
            Log.Information($"-> CalculateChecksum = {hash}");
            return hash;
        }

        /// <summary>
        /// Remove Checksum
        /// </summary>
        /// <returns></returns>
        public async Task RemoveCheckSumAsync()
        {
            var value = _cacheChecksum.GetValue();
            await RemoveCheckSumAsync(value);

        }

        /// <summary>
        /// Remove Checksum by value
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public async Task RemoveCheckSumAsync(string value)
        {
            Log.Information("-> Begin RemoveCheckSumAsync");

            var checkSumKey = _cacheChecksum.CreateHashKey();
            var values = await _distributedCacheRedis.GetListAsync(checkSumKey);
            if (values.Any(p => p == _cacheChecksum.GetValue()))
            {
                await _distributedCacheRedis.RemoveItemFromListAsync(checkSumKey);
            }

            Log.Information("-> Start RemoveCheckSumAsync");

        }
    }
}
