using PdfSharpCore.Pdf.IO;
using System.Collections.Generic;
using System.IO;

namespace Core.Shared.Pdf.Utils
{
    public static class PdfUtils
    {
        public static byte[] MergeFiles(List<byte[]> bytes)
        {
            using (var ms = new MemoryStream())
            {
                using (var document = new PdfSharpCore.Pdf.PdfDocument())
                {
                    foreach (var data in bytes)
                    {
                        using (var reader = PdfReader.Open(new MemoryStream(data), PdfDocumentOpenMode.Import))
                        {
                            foreach (var page in reader.Pages)
                            {
                                document.AddPage(page);
                            }
                        }
                    }
                    document.Save(ms, false);
                }
                return ms.ToArray();
            }
        }
    }
}
