using Core.EventBus;
using Core.Shared.Messages;

namespace Core.Shared.MessageEventsData.SyncElasticProduct
{
    [EventName("catalog.product")]
    public class ProductEventSendData : ProductInfoRequest
    {
        public ProductEventSendData()
        {

        }

        public ProductEventSendData(ProductInfoRequest item)
        {
            Id = item.Id;
            ErpId = item.ErpId;
            ProductCode = item.ProductCode;
            Name = item.Name;
            NormalizedName = item.NormalizedName;
            Price = item.Price;
            TaxValue = item.TaxValue;
            ProductTypeId = item.ProductTypeId;
            UnitId = item.UnitId;
            IsDeleted = item.IsDeleted;

        }
    }
}
