using Core.EventBus;
using System;

namespace Core.Shared.MessageEventsData.SignInvoice
{
    [EventName("invoice01.signinvoice01result")]
    public class SignInvoice01EventResultData : InvoiceSignResult
    {
        public SignInvoice01EventResultData()
        {

        }

        public SignInvoice01EventResultData(InvoiceSignResult item)
        {
            TenantId = item.TenantId;
            InvoiceHeaderId = item.InvoiceHeaderId;
            SerialNo = item.SerialNo;
        }
    }

    public class InvoiceSignResult
    {
        public Guid TenantId { get; set; }
        public long InvoiceHeaderId { get; set; }
        public string SerialNo { get; set; }

    }
}
