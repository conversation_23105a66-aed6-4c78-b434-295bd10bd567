using Core.EventBus;
using Core.Shared.Constants;
using Core.Shared.Messages;
using System;

namespace Core.Shared.MessageEventsData.SyncCatalog
{
    [EventName("synccatalog.syncproduct")]
    public class SyncProductEventSendData : SyncProductRequestModel
    { 
        public SyncProductEventSendData()
        {

        }

        public SyncProductEventSendData(SyncProductRequestModel item)
        {
            IdInvoice = item.IdInvoice;
            TenantId = item.TenantId;
            BuyerGroupName = item.BuyerGroupName;
            BuyerGroupCode = item.BuyerGroupCode;
            IdBuyerGroupErp = item.IdBuyerGroupErp;
            Type = item.Type;
        }

        public InvoiceSource Resource { get; set; }

        //Context
        public Guid UserId { get; set; }
        public string UserName { get; set; }
        public string UserFullName { get; set; }

    }
}