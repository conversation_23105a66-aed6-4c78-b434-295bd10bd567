using Core.Shared.Extensions;
using System.ComponentModel.DataAnnotations;

namespace Core.Shared.Attributes
{
    public class EmailAttribute : ValidationAttribute
    {
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value == null)
                return ValidationResult.Success;

            var source = (string)value;

            if (!string.IsNullOrEmpty(source) && !source.IsEmails(';'))
                return new ValidationResult(ErrorMessage = this.ErrorMessage);

            return ValidationResult.Success;
        }
    }
}