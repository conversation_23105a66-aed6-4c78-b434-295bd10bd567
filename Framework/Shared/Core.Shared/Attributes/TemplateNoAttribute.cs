using System.ComponentModel.DataAnnotations;
using Core.Shared.Extensions;

namespace Core.Shared.Attributes
{
    public class TemplateNoAttribute : ValidationAttribute
    {
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value == null)
                return ValidationResult.Success;

            var source = value.ToString();

            if (source.Length > 1 || !string.IsNullOrEmpty(source) && !source.IsTemplateNo())
                return new ValidationResult(ErrorMessage = this.ErrorMessage);

            return ValidationResult.Success;
        }

    }
}
