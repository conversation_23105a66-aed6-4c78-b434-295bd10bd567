using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Shared.Attributes
{
    public class MoreThanAttribute : ValidationAttribute
    {
        private readonly Type _type;
        private readonly string _compareProperty;

        public MoreThanAttribute(Type type, string compareProperty)
        {
            _type = type;
            _compareProperty = compareProperty;
        }
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            try
            {
                if (value == null)
                {
                    return ValidationResult.Success;
                }

                if(_type != value.GetType())
                    return new ValidationResult("So sánh khác kiểu dữ liệu");


                var property = validationContext.ObjectType.GetProperty(_compareProperty);
                if (property == null)
                    throw new ArgumentException($"Không tìm thấy thuộc tính {_compareProperty}");

                var compareValue = property.GetValue(validationContext.ObjectInstance);
                if (compareValue == null)
                    return ValidationResult.Success;

                // compare
                if (_type == typeof(short) && (short)value < (short)compareValue)
                    return new ValidationResult(this.ErrorMessage ?? $"Không được nhỏ hơn");

                if (_type == typeof(int) && (int)value < (int)compareValue)
                    return new ValidationResult(this.ErrorMessage ?? $"Không được nhỏ hơn");

                if (_type == typeof(long) && (long)value < (long)compareValue)
                    return new ValidationResult(this.ErrorMessage ?? $"Không được nhỏ hơn");

                if (_type == typeof(float) && (float)value < (float)compareValue)
                    return new ValidationResult(this.ErrorMessage ?? $"Không được nhỏ hơn");

                if (_type == typeof(double) && (double)value < (double)compareValue)
                    return new ValidationResult(this.ErrorMessage ?? $"Không được nhỏ hơn");

                if (_type == typeof(decimal) && (decimal)value < (decimal)compareValue)
                    return new ValidationResult(this.ErrorMessage ?? $"Không được nhỏ hơn");

                if (_type == typeof(DateTime) && (DateTime)value < (DateTime)compareValue)
                    return new ValidationResult(this.ErrorMessage ?? $"Không được nhỏ hơn");

                return ValidationResult.Success;
            }
            catch (Exception ex)
            {
                return new ValidationResult(ex.Message);

            }
        }
    }
}
