using System.IO;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;


namespace Core.Shared.Extensions
{
    public static class HttpContextExtension
    {
        public static string ReadRequestBody(this HttpContext httpContext)
        {
            var reader = new StreamReader(httpContext.Request.Body);
            reader.BaseStream.Seek(0, SeekOrigin.Begin);
            return reader.ReadToEnd();
        }

        public static async Task<string> ReadRequestBodyAsync(this HttpContext httpContext)
        {
            string body = null;
            var request = httpContext.Request;
            // Allows using several time the stream in ASP.Net Core
            request.EnableBuffering();

            using (var reader = new StreamReader(httpContext.Request.Body, Encoding.UTF8, true, 1024, true))
            {
                body = await reader.ReadToEndAsync();
            }

            request.Body.Position = 0;
            return body;
        }
    }

}
