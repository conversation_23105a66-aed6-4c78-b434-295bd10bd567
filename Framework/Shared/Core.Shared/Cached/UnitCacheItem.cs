using System;
using System.Collections.Generic;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;

namespace Core.Shared.Cached
{
    public class UnitCacheItem
    {
        private const string CacheKeyFormat = "i:{0},n:{1}";

        public List<UnitEntity> Value { get; set; } = new List<UnitEntity>();
        public TimeSpan Expired { get; set; } = TimeSpan.FromDays(1);
        public UnitCacheItem()
        {
        }

        public UnitCacheItem(List<UnitEntity> value, TimeSpan? expiry = null)
        {
            Value = value;
            Expired = expiry ??= TimeSpan.FromDays(1);
        }

        public static string CalculateCacheKey(Guid? tenantId)
        {
            var args = new string[] {
                tenantId.GetValueOrDefault().ToString(),
                "Unit"
            };

            return string.Format(CacheKeyFormat, args);
        }
    }
}
