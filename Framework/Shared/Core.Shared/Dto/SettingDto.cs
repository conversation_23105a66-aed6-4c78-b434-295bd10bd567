using Core.Application.Dtos;
using System;
using System.Collections.Generic;

namespace Core.Shared.Dto
{
    public class SettingDto : EntityDto<Guid>
    {
        public Guid? ParentId { get; set; }
        public string GroupCode { get; set; }
        public string Name { get; set; }
        public string Code { get; set; }
        public string Value { get; set; }
        public string ProviderName { get; set; }
        public string ProviderKey { get; set; }
        public Guid TenantId { get; set; }
        public string Options { get; set; }
        public int Type { get; set; }
        public string Description { get; set; }
        public bool IsReadOnly { get; set; }
        public Dictionary<string, string> ExtraProperties { get; set; }
    }
}
