using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.TenantManagement;
using Core.Tvan.Constants;
using Core.Tvan.Interfaces.MS01TBKTDL.InvoiceWithoutCode;
using Core.Tvan.Models;
using Core.Tvan.Models.Xmls;
using Core.Tvan.Models.Xmls.TCTResponse;
using Core.Tvan.StoredProcedure.Procedures;

using Dapper;
using Dapper.Oracle;

using Microsoft.EntityFrameworkCore;

using Newtonsoft.Json;

using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.BaseEntities;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Invoice03;

namespace Core.Tvan.Services.MS01TBKTDL.InvoiceWithoutCode
{
    public class MS01TBKTDLInvoice03WithoutCodeService : IMS01TBKTDLInvoiceWithoutCodeService
    {
        private readonly IAppFactory _appFactory;
        private readonly IFileService _fileService;

        public MS01TBKTDLInvoice03WithoutCodeService(IAppFactory appFactory,
                                                     IFileService fileService)
        {
            _appFactory = appFactory;
            _fileService = fileService;
        }

        public async Task HandleResponseTvan(string xml, IEnumerable<DLieuMS01TBaoModel.HDonModel> dsHDon, TTChungModel ttChung)
        {
            var repoTenant = _appFactory.Repository<Tenant, Guid>();
            var tenant = await repoTenant.Where(x => x.TaxCode == ttChung.MST).FirstOrDefaultAsync();
            var fileName = $"{ttChung.MST}.xml".Replace("/", "_");
            var tvanInvoice03WithoutCodeXmlEntity = new TvanInvoice03WithoutCodeXmlEntity
            {
                TenantId = tenant.Id,
                FileName = fileName,
                ContentType = ContentType.Xml,
                Length = Encoding.UTF8.GetBytes(xml).Length,
                PhysicalFileName = $"{ttChung.MST}_{DateTime.Now.Ticks}.xml".Replace("/", "_")
            };

            var pathFileMinio = $"{MediaFileType.Invoice03WithoutCodeTvanXml}/{tenant.Id}/{DateTime.Now.Year}/{DateTime.Now.Month:00}/{DateTime.Now.Day:00}/{tvanInvoice03WithoutCodeXmlEntity.CreationTime.Hour:00}/{tvanInvoice03WithoutCodeXmlEntity.PhysicalFileName}";
            await _fileService.UploadAsync(pathFileMinio, Encoding.UTF8.GetBytes(xml));
            var reposTvanInvoice03WithoutCodeXml = _appFactory.Repository<TvanInvoice03WithoutCodeXmlEntity, long>();
            await reposTvanInvoice03WithoutCodeXml.InsertAsync(tvanInvoice03WithoutCodeXmlEntity);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            var reposInvoice03WithoutCodeError = _appFactory.Repository<TvanInfoInvoice03WithoutCodeEntity, long>();
            var reposInvoice03Header = _appFactory.Repository<Invoice03HeaderEntity, long>();

            if (!dsHDon.Any())
                return;

            var hdons = dsHDon.Select(x => new InvoiceInfoModel
            {
                SerialNo = x.KHHDon,
                InvoiceNo = int.Parse(x.SHDon),
                TemplateNo = short.Parse(x.KHMSHDon)
            }).ToList();

            var invoice03Headers = await GetInvoiceAsync(tenant.Id, hdons);
            foreach (var invoice03Header in invoice03Headers)
            {
                var sqlUpdate = $@"UPDATE ""Invoice03Header"" SET  ""StatusTvan"" = {(short)TvanStatus.TCTReject} WHERE ""Id"" = {invoice03Header.Id}";
                await _appFactory.VnisCoreOracle.Connection.QueryAsync<Invoice03HeaderEntity>(sqlUpdate);

                var hdon = dsHDon.Where(x => x.KHHDon == invoice03Header.SerialNo && x.KHMSHDon == invoice03Header.TemplateNo.ToString() && x.SHDon == invoice03Header.Number.ToString()).FirstOrDefault();
                var invoice03WithoutCodeErrorEntity = new TvanInfoInvoice03WithoutCodeEntity
                {
                    InvoiceHeaderId = invoice03Header.Id,
                    FileId = tvanInvoice03WithoutCodeXmlEntity.Id,
                    IsActive = true,
                    TenantId = tenant.Id,
                    MessageTypeCode = ttChung.MLTDiep.ToString(),
                    MessageCode = ttChung.MTDiep,
                    MessageCodeReference = ttChung.MTDTChieu,
                    Reason = hdon == null ? null : JsonConvert.SerializeObject(hdon.DSLDo.LDo)
                };
                await reposInvoice03WithoutCodeError.InsertAsync(invoice03WithoutCodeErrorEntity);
            }
        }

        private async Task<List<BaseInvoiceHeader>> GetInvoiceAsync(Guid tenantId, List<InvoiceInfoModel> invoiceInfo)
        {
            var param = new OracleDynamicParameters();
            param.Add("json_conditions", JsonConvert.SerializeObject(invoiceInfo), OracleMappingType.NClob, ParameterDirection.Input);
            param.Add("tenantRawId", OracleExtension.ConvertGuidToRaw(tenantId), OracleMappingType.Varchar2, ParameterDirection.Input);
            param.Add(name: "output_data", value: DBNull.Value, dbType: OracleMappingType.RefCursor, direction: ParameterDirection.Output);

            var data = await _appFactory.VnisCoreOracle.Connection.QueryAsync<BaseInvoiceHeader>(
                TvanInvoiceProcedureName.Invoice03GetListInvoiceTvanResponse,
                param,
                null,
                null,
                CommandType.StoredProcedure);

            return data.ToList();
        }
    }
}
