using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.TenantManagement;
using Core.Tvan.Abstractions;
using Core.Tvan.Constants;
using Core.Tvan.Enums;
using Core.Tvan.Interfaces.MS01TBKTDL.InvoiceHasCode;
using Core.Tvan.Models.Xmls;
using Core.Tvan.Models.Xmls.TCTResponse;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;

using System;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Ticket;

namespace Core.Tvan.Services.MS01TBKTDL.InvoiceHasCode
{
    public class MS01TBKTDLTicketHasCodeService : BaseMS01TBKTDLInvoiceHasCodeService, IMS01TBKTDLInvoiceHasCodeService
    {
        private readonly IAppFactory _appFactory;
        private readonly IFileService _fileService;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public MS01TBKTDLTicketHasCodeService(IAppFactory appFactory,
                                                 IFileService fileService,
                                                 IStringLocalizer<CoreLocalizationResource> localizier)
        {
            _appFactory = appFactory;
            _fileService = fileService;
            _localizier = localizier;
        }

        public async Task HandleResponseTvan(string xml, TransmissionPartnerEnum transmissionPartner)
        {
            await ReceiveXmlFromTvanAsync<TDiepModel<TTChungModel, DLieuMS01TBaoModel>>(xml, transmissionPartner);
        }

        public override async Task<object> UpdateInvoiceHeader<T>(object tvanResponse)
        {
            var notificationTDiep = (TDiepModel<TTChungModel, DLieuMS01TBaoModel>)tvanResponse;
            var reposTicketHeader = _appFactory.Repository<TicketHeaderEntity, long>();
            var repoTenant = _appFactory.Repository<Tenant, Guid>();

            var tenant = await repoTenant.Where(x => x.TaxCode == notificationTDiep.DLieu.TBao.DLTBao.MST).FirstOrDefaultAsync();

            //lấy thông tin thông điệp
            var repoTvanInfoHasCode = _appFactory.Repository<TvanInfoTicketHasCodeEntity, long>();
            var tvanInfo = await repoTvanInfoHasCode.FirstOrDefaultAsync(x => x.MessageCode == notificationTDiep.TTChung.MTDTChieu);

            if (tvanInfo == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.TvanInvoice.TBKTDL.TvanHasCode", new string[] { notificationTDiep.TTChung.MTDTChieu }]);

            var ticketHeader = await reposTicketHeader.FirstOrDefaultAsync(x => x.TenantId == tenant.Id && x.Id == tvanInfo.InvoiceHeaderId);

            if (ticketHeader == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.TvanInvoice.TBKTDL.InvoiceNotFound", new string[] { notificationTDiep.TTChung.MTDTChieu }]);

            ticketHeader.StatusTvan = (short)TvanStatus.TCTReject;
            await reposTicketHeader.UpdateAsync(ticketHeader);

            return ticketHeader;
        }

        public override async Task UploadMinio<T>(string xml, object invoiceHeader, object responseInvoiceHasCode, TransmissionPartnerEnum transmissionPartner)
        {
            var ticketHeader = (TicketHeaderEntity)invoiceHeader;
            var fileName = $"{ticketHeader.SellerTaxCode}-{ticketHeader.TemplateNo}-{ticketHeader.SerialNo}-{ticketHeader.InvoiceNo}.xml".Replace("/", "_");
            var tvanTicketHasCodeXmlEntity = new TvanTicketHasCodeXmlEntity
            {
                TicketHeaderId = ticketHeader.Id,
                PhysicalFileName = fileName,
                ContentType = ContentType.Xml,
                FileName = fileName,
                TenantId = ticketHeader.TenantId,
                Length = Encoding.UTF8.GetBytes(xml).Length,
            };

            var pathFileMinio = $"{MediaFileType.TicketHasCodeTvanXml}/{ticketHeader.TenantId}/{DateTime.Now.Year}/{DateTime.Now.Month:00}/{DateTime.Now.Day:00}/{DateTime.Now.Hour:00}/{fileName}";

            await _fileService.UploadAsync(pathFileMinio, Encoding.UTF8.GetBytes(xml));
            var repoXml = _appFactory.Repository<TvanTicketHasCodeXmlEntity, long>();
            await repoXml.InsertAsync(tvanTicketHasCodeXmlEntity);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            var notificationTDiep = (TDiepModel<TTChungModel, DLieuMS01TBaoModel>)responseInvoiceHasCode;
            var ticketHasCodeTvanInfoEntity = new TvanInfoTicketHasCodeEntity
            {
                InvoiceHeaderId = ticketHeader.Id,
                MessageTypeCode = notificationTDiep.TTChung.MLTDiep.ToString(),
                MessageCode = notificationTDiep.TTChung.MTDiep,
                MessageCodeReference = notificationTDiep.TTChung.MTDTChieu,
                FileId = tvanTicketHasCodeXmlEntity.Id,
                IsActive = true,
                TenantId = ticketHeader.TenantId,
                Reason = notificationTDiep.DLieu.TBao.DLTBao.LCMa?.DSLDo?.LDo == null ? null : JsonConvert.SerializeObject(notificationTDiep.DLieu.TBao.DLTBao.LCMa.DSLDo.LDo),
                Title = MLTDiep._204.ToDisplayName(),
                TransmissionPartner = (short)transmissionPartner
            };

            var reposTicketHasCodeTvanInfo = _appFactory.Repository<TvanInfoTicketHasCodeEntity, long>();
            await reposTicketHasCodeTvanInfo.InsertAsync(ticketHasCodeTvanInfoEntity);
        }
    }
}
