using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.Tvan.Abstractions;
using Core.Tvan.Constants;
using Core.Tvan.Interfaces.MS01TBKTDL.InvoiceError;
using Core.Tvan.Models.Xmls;
using Core.Tvan.Models.Xmls.TCTResponse;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Tvan.Invoice01;

namespace Core.Tvan.Services.MS01TBKTDL.InvoiceError
{
    public class MS01TBKTDLInvoice01ErrorService : BaseMS01TBKTDLInvoiceErrorService, IMS01TBKTDLInvoiceErrorService
    {
        private readonly IAppFactory _appFactory;
        private readonly IFileService _fileService;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public MS01TBKTDLInvoice01ErrorService(IAppFactory appFactory,
                                                 IFileService fileService,
                                                 IStringLocalizer<CoreLocalizationResource> localizier)
        {
            _appFactory = appFactory;
            _fileService = fileService;
            _localizier = localizier;
        }

        public async Task HandleResponseTvan(string xml)
        {
            await ReceiveXmlFromTvanAsync<TDiepModel<TTChungModel, DLieuMS01TBaoModel>>(xml);
        }

        public override async Task UploadMinio<T>(string xml, object invoiceError, object responseInvoiceError)
        {
            var tvanInvoiceErrorSends = (List<TvanInfoInvoice01ErrorEntity>)invoiceError;
            var tenantId = tvanInvoiceErrorSends.FirstOrDefault().TenantId;

            var notificationTDiep = (TDiepModel<TTChungModel, DLieuMS01TBaoModel>)responseInvoiceError;
            var fileName = $"{notificationTDiep.TTChung.MST}-{notificationTDiep.TTChung.MLTDiep}-{notificationTDiep.TTChung.MTDiep}-{notificationTDiep.TTChung.MTDTChieu}_{DateTime.Now}.xml".Replace("/", "_");
            var tvanInvoice01ErrorXmlEntity = new TvanInvoice01ErrorXmlEntity
            {
                PhysicalFileName = fileName,
                ContentType = ContentType.Xml,
                FileName = fileName,
                TenantId = tenantId,
                Length = Encoding.UTF8.GetBytes(xml).Length,
            };

            var pathFileMinio = $"{MediaFileType.Invoice01ErrorTvanXml}/{tenantId}/{DateTime.Now.Year}/{DateTime.Now.Month:00}/{DateTime.Now.Day:00}/{DateTime.Now.Hour:00}/{fileName}";

            await _fileService.UploadAsync(pathFileMinio, Encoding.UTF8.GetBytes(xml));
            var repoXml = _appFactory.Repository<TvanInvoice01ErrorXmlEntity, long>();
            await repoXml.InsertAsync(tvanInvoice01ErrorXmlEntity);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            var invoice01ErrorTvanInfoEntites = new List<TvanInfoInvoice01ErrorEntity>();
            foreach (var item in tvanInvoiceErrorSends)
            {
                invoice01ErrorTvanInfoEntites.Add(new TvanInfoInvoice01ErrorEntity
                {
                    MessageTypeCode = notificationTDiep.TTChung.MLTDiep.ToString(),
                    MessageCode = notificationTDiep.TTChung.MTDiep,
                    MessageCodeReference = notificationTDiep.TTChung.MTDTChieu,
                    FileId = tvanInvoice01ErrorXmlEntity.Id,
                    IsActive = true,
                    TenantId = item.TenantId,
                    Action = item.Action,
                    InvoiceHeaderId = item.InvoiceHeaderId,
                    Status = (short)TTTNCCQT.Compare.GetHashCode(), 
                });
            }

            var repoInvoice01ErrorTvanInfo = _appFactory.Repository<TvanInfoInvoice01ErrorEntity, long>();
            await repoInvoice01ErrorTvanInfo.InsertManyAsync(invoice01ErrorTvanInfoEntites);
        }

        protected override async Task<object> GetInvoiceErrorAsync<T>(object response) 
        {
            var notificationTDiep = (TDiepModel<TTChungModel, DLieuMS01TBaoModel>)response;
            var repoInvoiceErrorTvanInfo = _appFactory.Repository<TvanInfoInvoice01ErrorEntity, long>();

            var invoiceErrors = await repoInvoiceErrorTvanInfo.Where(x => x.MessageCode == notificationTDiep.TTChung.MTDTChieu).ToListAsync();
            if (!invoiceErrors.Any())
                throw new UserFriendlyException(_localizier["Vnis.BE.TvanInvoice.InvoiceErrorTvanResponse.InvoiceErrorNotFoundWithMessageCode", new string[] { notificationTDiep.TTChung.MTDTChieu}]);

            return invoiceErrors;
        }
    }
}
