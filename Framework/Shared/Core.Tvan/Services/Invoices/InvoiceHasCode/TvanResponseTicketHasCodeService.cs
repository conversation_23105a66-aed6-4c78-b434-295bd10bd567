using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.TenantManagement;
using Core.Tvan.Abstractions;
using Core.Tvan.Constants;
using Core.Tvan.Enums;
using Core.Tvan.Interfaces.InvoiceHasCode;
using Core.Tvan.Models.Xmls.Invoices.Base;
using Core.Tvan.Models.Xmls.Invoices.Ticket;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

using System;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Ticket;

namespace Core.Tvan.Services.Invoices.InvoiceHasCode
{
    public class TvanResponseTicketHasCodeService : BaseTvanResponseInvoiceHasCodeService, IInvoiceHasCodeService
    {
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly IFileService _fileService;

        public TvanResponseTicketHasCodeService(IAppFactory appFactory,
                                                   IStringLocalizer<CoreLocalizationResource> localizier,
                                                   IFileService fileService) : base(appFactory)
        {
            _appFactory = appFactory;
            _localizier = localizier;
            _fileService = fileService;
        }

        public async Task HandleResponseTvan(string xml, TransmissionPartnerEnum transmissionPartner)
        {
            await ReceiveXmlFromTvanAsync<HDonModel<DLHDonInvoiceModel<TTChungDLHDonTicketModel, NDHDonTicketModel>>>(xml, transmissionPartner);
        }

        public override async Task<object> UpdateInvoiceHeader<T>(object tvanResponse)
        {
            var response = (HDonModel<DLHDonInvoiceModel<TTChungDLHDonTicketModel, NDHDonTicketModel>>)tvanResponse;

            var repoTenant = _appFactory.Repository<Tenant, Guid>();
            var tenant = await repoTenant.Where(x => x.TaxCode == response.DLHDon.NDHDon.NBan.MST).FirstOrDefaultAsync();
            if (tenant == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.TvanInvoice.TicketTvanHasCode.TaxcodeNotFound"]);

            var ticketHeaderRepos = _appFactory.Repository<TicketHeaderEntity, long>();
            var ticketHeaderEntity = await ticketHeaderRepos.FirstOrDefaultAsync(x => x.TemplateNo == response.DLHDon.TTChung.KHMSHDon
                                                                                            && x.SerialNo == response.DLHDon.TTChung.KHHDon
                                                                                            && x.Number == int.Parse(response.DLHDon.TTChung.SHDon)
                                                                                            && x.TenantId == tenant.Id
                                                                                            && !x.IsDeleted);

            if (ticketHeaderEntity == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.TvanInvoice.TicketTvanHasCode.InvoiceNotFound", new string[] { tenant.TaxCode, response.DLHDon.TTChung.KHHDon, response.DLHDon.TTChung.SHDon, response.DLHDon.TTChung.SHDon }]);
            else
            {
                ticketHeaderEntity.VerificationCode = response.MCCQT;
                ticketHeaderEntity.StatusTvan = (short)TvanStatus.TCTAccept;

                await ticketHeaderRepos.UpdateAsync(ticketHeaderEntity);

                return ticketHeaderEntity;
            }
        }

        public override async Task UploadMinio<T>(string xml, object invoiceHeader, object responseInvoiceHasCode, TransmissionPartnerEnum transmissionPartner)
        {
            //lưu xml
            //up lên minio trước
            //lưu file vào minio trước rồi mới lưu vào db

            var ticketHeaderEntity = (TicketHeaderEntity)invoiceHeader;

            var fileName = $"{ticketHeaderEntity.SellerTaxCode}-{ticketHeaderEntity.TemplateNo}-{ticketHeaderEntity.SerialNo}-{ticketHeaderEntity.InvoiceNo}.xml".Replace("/", "_");
            var tvanTicketHasCodeXmlEntity = new TvanTicketHasCodeXmlEntity
            {
                ContentType = ContentType.Xml,
                FileName = fileName,
                PhysicalFileName = $"{ticketHeaderEntity.SellerTaxCode}-{ticketHeaderEntity.TemplateNo}-{ticketHeaderEntity.SerialNo}-{ticketHeaderEntity.InvoiceNo}_{DateTime.Now.Ticks}.xml".Replace("/", "_"),
                Length = Encoding.UTF8.GetBytes(xml).Length,
                TenantId = ticketHeaderEntity.TenantId,
                TicketHeaderId = ticketHeaderEntity.Id
            };

            var pathFileMinio = $"{MediaFileType.TicketHasCodeTvanXml}/{ticketHeaderEntity.TenantId}/{DateTime.Now.Year}/{DateTime.Now.Month:00}/{DateTime.Now.Day:00}/{DateTime.Now.Hour:00}/{tvanTicketHasCodeXmlEntity.PhysicalFileName}";
            await _fileService.UploadAsync(pathFileMinio, Encoding.UTF8.GetBytes(xml));

            var repoXml = _appFactory.Repository<TvanTicketHasCodeXmlEntity, long>();
            await repoXml.InsertAsync(tvanTicketHasCodeXmlEntity);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            // lưu thông tin phản hồi của TCT vào bảng TicketHasCodeTvanInfo
            XmlDocument doc = new XmlDocument();
            doc.LoadXml(xml);
            var mLTDiepNode = doc.SelectSingleNode(@"//TDiep/TTChung/MLTDiep");
            var mTDiepNode = doc.SelectSingleNode(@"//TDiep/TTChung/MTDiep");
            var mTDTChieuNode = doc.SelectSingleNode(@"//TDiep/TTChung/MTDTChieu");

            var reposTicketHasCodeTvanInfo = _appFactory.Repository<TvanInfoTicketHasCodeEntity, long>();
            var ticketHasCodeTvanInfoEntity = new TvanInfoTicketHasCodeEntity
            {
                InvoiceHeaderId = ticketHeaderEntity.Id,
                MessageTypeCode = mLTDiepNode.InnerText,
                MessageCode = mTDiepNode.InnerText,
                MessageCodeReference = mTDTChieuNode.InnerText,
                FileId = tvanTicketHasCodeXmlEntity.Id,
                IsActive = true,
                TenantId = ticketHeaderEntity.TenantId,
                Title = MLTDiep._202.ToDisplayName(),
                TransmissionPartner = (short)transmissionPartner
            };

            await reposTicketHasCodeTvanInfo.InsertAsync(ticketHasCodeTvanInfoEntity);
        }
    }
}
