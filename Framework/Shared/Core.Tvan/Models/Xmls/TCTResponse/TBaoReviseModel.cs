using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace Core.Tvan.Models.Xmls.TCTResponse
{
    [XmlType("TBao")]
    public class TBaoReviseModel
    {
        [XmlElement("DLTBao")]
        public DLTBaoReviseModel DLTBao { get; set; }

        [XmlElement("DSCKS")]
        public DSCKSReviseModel DSCKS { get; set; }
    }

    [XmlType("DLTBao")]
    public class DLTBaoReviseModel
    {

    }

    [XmlType("DSCKS")]
    public class DSCKSReviseModel
    {
        public object TTCQT { get; set; }

        public object CQT { get; set; }

        public object CCKSKhac { get; set; }
    }
}
