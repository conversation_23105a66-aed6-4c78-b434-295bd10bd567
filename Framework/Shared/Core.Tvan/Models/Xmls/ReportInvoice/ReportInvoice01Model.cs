using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Xml.Serialization;

namespace Core.Tvan.Models.Xmls.ReportInvoice
{
    [XmlRoot(ElementName = "TDiep")]
    public class ReportInvoice01Model : TDiepModel<TTChungModel, DLieuReportInvoice01Model>
    {
    }

    [XmlRoot(ElementName = "BTHDLieu")]
    public class BTHDLieuReportInvoice01
    {
        [XmlElement(ElementName = "DLBTHop")]
        public DLBTHopReportInvoice01Model DLBTHop { get; set; }

        [XmlElement(ElementName = "DSCKS")]
        public DSCKSReportInvoice01Model DSCKS { get; set; }
    }

    [XmlRoot(ElementName = "DLBTHop")]
    public class DLBTHopReportInvoice01Model
    {
        [XmlAttribute("Id")]
        public string Data { get; set; }

        [XmlElement(ElementName = "TTChung")]
        public TTChungDLBTHopReportInvoice01Model TTChung { get; set; }

        [XmlElement(ElementName = "NDBTHDLieu")]
        public NDBTHDLieuReportInvoice01Model NDBTHDLieu { get; set; }
    }


    [XmlType("NDBTHDLieu")]
    public class NDBTHDLieuReportInvoice01Model
    {
        /// <summary>
        /// DSDLieu
        /// </summary>
        [XmlElement(ElementName = "DSDLieu")]
        public DSDLieuTaxReport01Model DSDLieu { get; set; }

    }

    [XmlRoot(ElementName = "DSDLieu")]
    public class DSDLieuTaxReport01Model
    {
        [XmlElement(ElementName = "DLieu")]
        public List<DLieuTaxReport01Model> DLieu { get; set; }
    }

    [XmlRoot(ElementName = "DLieu")]
    public class DLieuTaxReport01Model
    {
        /// <summary>
        /// Số thứ tự, Không bắt buộc
        /// maxLength = 6
        /// số
        /// </summary>
        [XmlElement(ElementName = "STT")]
        [MaxLength(6, ErrorMessage = "Vnis.BE.Tvan.DLieuTaxReport01Model.STT.MaxLength")]
        public long STT { get; set; }

        /// <summary>
        /// Ký hiệu mẫu số hóa đơn 
        /// bắt buộc (nếu có)
        /// maxLength = 11
        /// </summary>
        [XmlElement(ElementName = "KHMSHDon")]
        [MaxLength(11, ErrorMessage = "Vnis.BE.Tvan.DLieuTaxReport01Model.KHMSHDon.MaxLength")]
        public string KHMSHDon { get; set; }

        /// <summary>
        /// Ký hiệu hóa đơn
        /// max length = 8
        /// Chuỗi ký tự
        /// </summary>
        [XmlElement(ElementName = "KHHDon")]
        [MaxLength(8, ErrorMessage = "Vnis.BE.Tvan.DLieuTaxReport01Model.KHHDon.MaxLength")]
        public string KHHDon { get; set; }

        /// <summary>
        /// maxLength = 8
        /// bắt buộc(nếu có)
        /// </summary>
        [XmlElement(ElementName = "SHDon")]
        [MaxLength(8, ErrorMessage = "Vnis.BE.Tvan.DLieuTaxReport01Model.SHDon.MaxLength")]
        public string SHDon { get; set; }

        /// <summary>
        /// ngày lập
        /// Bắt buộc (Trừ trường hợp Loại hàng hóa, dịch vụ kinh doanh là 1- Xăng dầu)
        /// </summary>
        [XmlElement(ElementName = "NLap")]
        public string NLap { get; set; }

        /// <summary>
        /// Tên người mua
        /// maxLength = 400
        /// </summary>
        [XmlElement(ElementName = "TNMua")]
        [MaxLength(400, ErrorMessage = "Vnis.BE.Tvan.DLieuTaxReport01Model.TNMua.MaxLength")]
        public string TNMua { get; set; }

        /// <summary>
        /// Mã số thuế người mua 
        /// maxLength = 14
        /// </summary>
        [XmlElement(ElementName = "MSTNMua")]
        [MaxLength(14, ErrorMessage = "Vnis.BE.Tvan.DLieuTaxReport01Model.MSTNMua.MaxLength")]
        public string MSTNMua { get; set; }

        /// <summary>
        /// Mã đơn vị quan hệ ngân sách (Mã số đơn vị có quan hệ với ngân sách) 
        /// </summary>
        [XmlElement("MDVQHNSach")]
        [MaxLength(7, ErrorMessage = "Vnis.BE.Tvan.DLieuTaxReport01Model.MDVQHNSach.MaxLength")]
        public string MDVQHNSach { get; set; }

        /// <summary>
        /// Mã khách hàng
        /// maxlength = 50
        /// </summary>
        [XmlElement(ElementName = "MKHang")]
        [MaxLength(50, ErrorMessage = "Vnis.BE.Tvan.DLieuTaxReport01Model.MKHang.MaxLength")]
        public string MKHang { get; set; }

        [XmlElement(ElementName = "MHHDVu")]
        [MaxLength(50, ErrorMessage = "Vnis.BE.Tvan.DLieuTaxReport01Model.MHHDVu.MaxLength")]
        public string MHHDVu { get; set; }

        /// <summary>
        /// Tên hàng hóa, dịch vụ (Mặt hàng)
        /// maxlength = 500
        /// </summary>
        [XmlElement(ElementName = "THHDVu")]
        [MaxLength(500, ErrorMessage = "Vnis.BE.Tvan.DLieuTaxReport01Model.THHDVu.MaxLength")]
        public string THHDVu { get; set; }

        /// <summary>
        /// Đơn vị tính
        /// maxLength = 50
        /// </summary>
        [XmlElement(ElementName = "DVTinh")]
        [MaxLength(50, ErrorMessage = "Vnis.BE.Tvan.DLieuTaxReport01Model.DVTinh.MaxLength")]
        public string DVTinh { get; set; }

        /// <summary>
        /// Số lượng hàng hóa
        /// </summary>
        [XmlElement(ElementName = "SLuong")]
        public decimal? SLuong { get; set; }

        /// <summary>
        /// Tổng giá trị hàng hóa, dịch vụ bán ra chưa có thuế GTGT
        /// </summary>
        [XmlElement(ElementName = "TTCThue")]
        public decimal? TTCThue { get; set; }

        /// <summary>
        /// Thuế suất (Thuế suất thuế GTGT)
        /// maxLength = 11
        /// </summary>
        [XmlElement(ElementName = "TSuat")]
        [MaxLength(11, ErrorMessage = "Vnis.BE.Tvan.DLieuTaxReport01Model.TSuat.MaxLength")]
        public string? TSuat { get; set; }

        /// <summary>
        /// Tổng tiền thuế (Tổng tiền thuế GTGT)
        /// </summary>
        [XmlElement(ElementName = "TgTThue")]
        public decimal? TgTThue { get; set; }

        /// <summary>
        /// Tổng tiền phí
        /// Độ dài 21,6
        /// Số
        /// Bắt buộc (Nếu có)
        /// </summary>
        [XmlElement(ElementName = "TTPhi")]
        [MaxLength(21, ErrorMessage = "Vnis.BE.Tvan.DLieuTaxReport01Model.TTPhi.MaxLength")]
        public decimal? TTPhi { get; set; }

        /// <summary>
        /// Tổng giảm trừ khác
        /// Độ dài 21,6
        /// Số
        /// Không bắt buộc
        /// </summary>
        [XmlElement(ElementName = "TGTKhac")]
        [MaxLength(21, ErrorMessage = "Vnis.BE.Tvan.DLieuTaxReport01Model.TGTKhac.MaxLength")]
        public decimal? TGTKhac { get; set; }

        /// <summary>
        /// Tổng tiền 
        /// bắt buộc
        /// </summary>
        [XmlElement(ElementName = "TgTTToan")]
        [Required(ErrorMessage = "Vnis.BE.Tvan.DLieuTaxReport01Model.TgTTToan.Required")]
        public decimal? TgTTToan { get; set; }

        /// <summary>
        /// Tỷ giá
        /// Độ dài 21,6
        /// Số
        /// Bắt buộc (Trừ trường hợp Đơn vị tiền tệ là VND)
        /// </summary>
        [XmlElement(ElementName = "TGia")]
        [MaxLength(21, ErrorMessage = "Vnis.BE.Tvan.DLieuTaxReport01Model.TGia.MaxLength")]
        public decimal? TGia { get; set; }

        /// <summary>
        /// Trạng thái
        /// bắt buộc
        /// </summary>
        [XmlElement(ElementName = "TThai")]
        [MaxLength(1, ErrorMessage = "Vnis.BE.Tvan.DLieuTaxReport01Model.TThai.MaxLength")]
        public int TThai { get; set; }

        /// <summary>
        /// Loại hóa đơn có liên quan (Loại hóa đơn bị thay thế/điều chỉnh)
        /// </summary>
        [XmlElement(ElementName = "LHDCLQuan")]
        [MaxLength(1, ErrorMessage = "Vnis.BE.Tvan.DLieuTaxReport01Model.LHDCLQuan.MaxLength")]
        public string LHDCLQuan { get; set; }

        /// <summary>
        /// Ký hiệu mẫu số hóa đơn có liên quan (Ký hiệu mẫu số hóa đơn bị thay thế/điều chỉnh)
        /// maxlength = 11
        /// </summary>
        [XmlElement(ElementName = "KHMSHDCLQuan")]
        [MaxLength(11, ErrorMessage = "Vnis.BE.Tvan.DLieuTaxReport01Model.KHMSHDCLQuan.MaxLength")]
        public string KHMSHDCLQuan { get; set; }

        /// <summary>
        /// Ký hiệu hóa đơn có liên quan (Ký hiệu hóa đơn bị thay thế/điều chỉnh)
        /// maxlength = 8
        /// </summary>
        [XmlElement(ElementName = "KHHDCLQuan")]
        [MaxLength(8, ErrorMessage = "Vnis.BE.Tvan.DLieuTaxReport01Model.KHHDCLQuan.MaxLength")]
        public string KHHDCLQuan { get; set; }

        /// <summary>
        /// Số hóa đơn có liên quan (Số hóa đơn bị thay thế/điều chỉnh)
        /// maxLength = 8
        /// </summary>
        [XmlElement(ElementName = "SHDCLQuan")]
        [MaxLength(8, ErrorMessage = "Vnis.BE.Tvan.DLieuTaxReport01Model.SHDCLQuan.MaxLength")]
        public string SHDCLQuan { get; set; }

        /// <summary>
        /// Loại kỳ dữ liệu điều chỉnh
        /// maxLength = 1
        /// </summary>
        [XmlElement(ElementName = "LKDLDChinh")]
        [MaxLength(1, ErrorMessage = "Vnis.BE.Tvan.DLieuTaxReport01Model.LKDLDChinh.MaxLength")]
        public string LKDLDChinh { get; set; }

        /// <summary>
        /// Kỳ dữ liệu điều chỉnh
        /// maxlength = 10
        /// </summary>
        [XmlElement(ElementName = "KDLDChinh")]
        [MaxLength(10, ErrorMessage = "Vnis.BE.Tvan.DLieuTaxReport01Model.KDLDChinh.MaxLength")]
        public string KDLDChinh { get; set; }

        /// <summary>
        /// Số thông báo (Số thông báo của CQT về hóa đơn điện tử cần rà soát)
        /// maxlength = 30
        /// </summary>
        [XmlElement(ElementName = "STBao")]
        [MaxLength(30, ErrorMessage = "Vnis.BE.Tvan.DLieuTaxReport01Model.STBao.MaxLength")]
        public string STBao { get; set; }

        /// <summary>
        /// Ngày thông báo (Ngày thông báo của CQT về hóa đơn điện tử cần rà soát)
        /// </summary>
        [XmlElement(ElementName = "NTBao")]
        public string NTBao { get; set; }

        /// <summary>
        /// maxlength = 255
        /// ghi chú
        /// </summary>
        [XmlElement(ElementName = "GChu")]
        [MaxLength(255, ErrorMessage = "Vnis.BE.Tvan.DLieuTaxReport01Model.GChu.MaxLength")]
        public string GChu { get; set; }
    }


    [XmlRoot(ElementName = "DSCKS")]
    public class DSCKSReportInvoice01Model
    {
        [XmlElement(ElementName = "NNT")]
        public CKSNNTModel NNT { get; set; }

        [XmlElement(ElementName = "CCKSKhac")]
        public object CCKSKhac { get; set; }
    }


    [XmlRoot(ElementName = "DLieu")]
    public class DLieuReportInvoice01Model
    {
        [XmlElement(ElementName = "BTHDLieu")]
        public List<BTHDLieuReportInvoice01> BTHDLieu { get; set; }
    }

    [XmlRoot(ElementName = "TTChung")]
    public class TTChungDLBTHopReportInvoice01Model
    {
        /// <summary>
        /// Phiên bản XML (Trong Quy định này có giá trị là 2.0.0)
        /// maxLength = 6 - bat buoc
        /// </summary>
        [XmlElement(ElementName = "PBan")]
        [Required(ErrorMessage = "Vnis.BE.Tvan.TTChungDLBTHopReportInvoice01Model.PBan.Required")]
        [MaxLength(6, ErrorMessage = "Vnis.BE.Tvan.TTChungDLBTHopReportInvoice01Model.PBan.MaxLength")]
        public string PBan { get; set; }

        /// <summary>
        /// Mẫu số (mẫu số bảng tổng hợp)
        /// maxLength = 15 - bat buoc
        /// </summary>
        [XmlElement(ElementName = "MSo")]
        [Required(ErrorMessage = "Vnis.BE.Tvan.TTChungDLBTHopReportInvoice01Model.MSo.Required")]
        [MaxLength(15, ErrorMessage = "Vnis.BE.Tvan.TTChungDLBTHopReportInvoice01Model.MSo.MaxLength")]
        public string MSo { get; set; }

        /// <summary>
        /// Tên (tên bảng tổng hợp)
        /// maxLength = 100 - bat buoc
        /// </summary>
        [XmlElement(ElementName = "Ten")]
        [Required(ErrorMessage = "Vnis.BE.Tvan.TTChungDLBTHopReportInvoice01Model.Ten.Required")]
        [MaxLength(100, ErrorMessage = "Vnis.BE.Tvan.TTChungDLBTHopReportInvoice01Model.Ten.MaxLength")]
        public string Ten { get; set; }

        /// <summary>
        /// Số bảng tổng hợp dữ liệu (Số thứ tự bảng tổng hợp dữ liệu)
        /// maxLenght = 5 
        /// số
        /// </summary>
        [XmlElement(ElementName = "SBTHDLieu")]
        [Required(ErrorMessage = "Vnis.BE.Tvan.TTChungDLBTHopReportInvoice01Model.SBTHDLieu.Required")]
        [MaxLength(5, ErrorMessage = "Vnis.BE.Tvan.TTChungDLBTHopReportInvoice01Model.SBTHDLieu.MaxLength")]
        public long SBTHDLieu { get; set; }

        /// <summary>
        /// Loại kỳ dữ liệu
        /// maxLenght = 1, bắt buộc
        /// </summary>
        [XmlElement(ElementName = "LKDLieu")]
        [Required(ErrorMessage = "Vnis.BE.Tvan.TTChungDLBTHopReportInvoice01Model.LKDLieu.Required")]
        [MaxLength(1, ErrorMessage = "Vnis.BE.Tvan.TTChungDLBTHopReportInvoice01Model.LKDLieu.MaxLength")]
        public string LKDLieu { get; set; }

        /// <summary>
        /// Kỳ dữ liệu
        /// maxlenght = 10
        /// bắt buộc
        /// </summary>
        [XmlElement(ElementName = "KDLieu")]
        [Required(ErrorMessage = "Vnis.BE.Tvan.TTChungDLBTHopReportInvoice01Model.KDLieu.Required")]
        [MaxLength(10, ErrorMessage = "Vnis.BE.Tvan.TTChungDLBTHopReportInvoice01Model.KDLieu.MaxLength")]
        public string KDLieu { get; set; }

        /// <summary>
        /// Lần đầu
        /// Số (1: lần đầu, 0: bổ sung)
        /// </summary>
        [XmlElement(ElementName = "LDau")]
        [Required(ErrorMessage = "Vnis.BE.Tvan.TTChungDLBTHopReportInvoice01Model.LDau.Required")]
        [MaxLength(1, ErrorMessage = "Vnis.BE.Tvan.TTChungDLBTHopReportInvoice01Model.LDau.MaxLength")]
        public int LDau { get; set; }

        /// <summary>
        /// Bổ sung lần thứ
        /// maxLength = 3
        /// Bắt buộc (Đối với trường hợp  LDau = 0)
        /// </summary>
        [XmlElement(ElementName = "BSLThu")]
        [Required(ErrorMessage = "Vnis.BE.Tvan.TTChungDLBTHopReportInvoice01Model.BSLThu.Required")]
        [MaxLength(3, ErrorMessage = "Vnis.BE.Tvan.TTChungDLBTHopReportInvoice01Model.BSLThu.MaxLength")]
        public int BSLThu { get; set; }

        /// <summary>
        /// bắt buộc
        /// </summary>
        [XmlElement(ElementName = "NLap")]
        [Required(ErrorMessage = "Vnis.BE.Tvan.TTChungDLBTHopReportInvoice01Model.NLap.Required")]
        public string NLap { get; set; }

        /// <summary>
        /// Tên người nộp thuế
        /// bắt buộc - maxlength = 400
        /// </summary>
        [XmlElement(ElementName = "TNNT")]
        [Required(ErrorMessage = "Vnis.BE.Tvan.TTChungDLBTHopReportInvoice01Model.TNNT.Required")]
        [MaxLength(400, ErrorMessage = "Vnis.BE.Tvan.TTChungDLBTHopReportInvoice01Model.TNNT.MaxLength")]
        public string TNNT { get; set; }

        /// <summary>
        /// Mã số thuế NNT
        /// maxLenght = 14 - bắt buộc
        /// </summary>
        [XmlElement(ElementName = "MST")]
        [Required(ErrorMessage = "Vnis.BE.Tvan.TTChungDLBTHopReportInvoice01Model.MST.Required")]
        [MaxLength(14, ErrorMessage = "Vnis.BE.Tvan.TTChungDLBTHopReportInvoice01Model.MST.MaxLength")]
        public string MST { get; set; }

        /// <summary>
        /// Đơn vị tiền tệ
        /// maxLenght = 3
        /// Chuỗi ký tự (Chi tiết tại Khoản 2, Mục IV, Phần I Quyết định số 1450/QĐ-TCT ngày 7/10/2021)
        /// Bắt buộc
        /// </summary>
        [XmlElement(ElementName = "DVTTe")]
        [Required(ErrorMessage = "Vnis.BE.Tvan.TTChungDLBTHopReportInvoice01Model.DVTTe.Required")]
        [MaxLength(3, ErrorMessage = "Vnis.BE.Tvan.TTChungDLBTHopReportInvoice01Model.DVTTe.MaxLength")]
        public string DVTTe { get; set; }

        /// <summary>
        /// Hóa đơn đặt in
        /// Số (0: Hóa đơn điện tử, 1: Hóa đơn đặt in)
        /// </summary>
        [XmlElement(ElementName = "HDDIn")]
        [Required(ErrorMessage = "Vnis.BE.Tvan.TTChungDLBTHopReportInvoice01Model.HDDIn.Required")]
        [MaxLength(1, ErrorMessage = "Vnis.BE.Tvan.TTChungDLBTHopReportInvoice01Model.HDDIn.MaxLength")]
        public int HDDIn { get; set; }

        /// <summary>
        /// Loại hàng hóa (Loại hàng hóa, dịch vụ kinh doanh)
        /// Số (1: Xăng dầu, 2: Vận tải hàng không, 9: Khác)
        /// bắt buộc
        /// </summary>
        [XmlElement(ElementName = "LHHoa")]
        [Required(ErrorMessage = "Vnis.BE.Tvan.TTChungDLBTHopReportInvoice01Model.LHHoa.Required")]
        [MaxLength(1, ErrorMessage = "Vnis.BE.Tvan.TTChungDLBTHopReportInvoice01Model.LHHoa.MaxLength")]
        public int LHHoa { get; set; }
    }
}
