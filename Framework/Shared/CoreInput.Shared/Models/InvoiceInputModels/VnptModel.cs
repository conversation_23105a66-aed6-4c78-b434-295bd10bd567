using System.Collections.Generic;
using System.Xml.Serialization;

namespace CoreInput.Shared.Models.InvoiceInputModels
{
    [XmlRoot(ElementName = "Invoice")]
	public class VnptModel
    {
		[XmlElement(ElementName = "TvanData")]
		public TvanData TvanData { get; set; }
		[XmlElement(ElementName = "qrCodeData")]
		public string QrCodeData { get; set; }
		[XmlElement(ElementName = "key")]
		public string Key { get; set; }
		[XmlElement(ElementName = "Fkey")]
		public string Fkey { get; set; }
	}

	[XmlRoot(ElementName = "Product")]
	public class Product
	{
		[XmlElement(ElementName = "Code")]
		public string Code { get; set; }
		[XmlElement(ElementName = "Remark")]
		public string Remark { get; set; }
		[XmlElement(ElementName = "ProdName")]
		public string ProdName { get; set; }
		[XmlElement(ElementName = "ProdUnit")]
		public string ProdUnit { get; set; }
		[XmlElement(ElementName = "ProdQuantity")]
		public string ProdQuantity { get; set; }
		[XmlElement(ElementName = "Extra1")]
		public string Extra1 { get; set; }
		[XmlElement(ElementName = "ProdPrice")]
		public string ProdPrice { get; set; }
		[XmlElement(ElementName = "Total")]
		public string Total { get; set; }
		[XmlElement(ElementName = "Discount")]
		public string Discount { get; set; }
		[XmlElement(ElementName = "DiscountAmount")]
		public string DiscountAmount { get; set; }
		[XmlElement(ElementName = "VATRate")]
		public string VATRate { get; set; }
		[XmlElement(ElementName = "VATAmount")]
		public string VATAmount { get; set; }
		[XmlElement(ElementName = "Amount")]
		public string Amount { get; set; }
		[XmlElement(ElementName = "IsSum")]
		public string IsSum { get; set; }
	}

	[XmlRoot(ElementName = "Products")]
	public class VnptProducts
	{
		[XmlElement(ElementName = "Product")]
		public List<Product> Product { get; set; }
	}

	[XmlRoot(ElementName = "Content")]
	public class VnptContent
	{
		[XmlElement(ElementName = "InvoiceName")]
		public string InvoiceName { get; set; }
		[XmlElement(ElementName = "InvoicePattern")]
		public string InvoicePattern { get; set; }
		[XmlElement(ElementName = "SerialNo")]
		public string SerialNo { get; set; }
		[XmlElement(ElementName = "InvoiceNo")]
		public string InvoiceNo { get; set; }
		[XmlElement(ElementName = "ArisingDate")]
		public string ArisingDate { get; set; }
		[XmlElement(ElementName = "Kind_of_Payment")]
		public string Kind_of_Payment { get; set; }
		[XmlElement(ElementName = "ComName")]
		public string ComName { get; set; }
		[XmlElement(ElementName = "ComTaxCode")]
		public string ComTaxCode { get; set; }
		[XmlElement(ElementName = "ComAddress")]
		public string ComAddress { get; set; }
		[XmlElement(ElementName = "ComPhone")]
		public string ComPhone { get; set; }
		[XmlElement(ElementName = "ComBankNo")]
		public string ComBankNo { get; set; }
		[XmlElement(ElementName = "ComBankName")]
		public string ComBankName { get; set; }
		[XmlElement(ElementName = "CusCode")]
		public string CusCode { get; set; }
		[XmlElement(ElementName = "CusName")]
		public string CusName { get; set; }
		[XmlElement(ElementName = "Buyer")]
		public string Buyer { get; set; }
		[XmlElement(ElementName = "CusTaxCode")]
		public string CusTaxCode { get; set; }
		[XmlElement(ElementName = "CusPhone")]
		public string CusPhone { get; set; }
		[XmlElement(ElementName = "CusAddress")]
		public string CusAddress { get; set; }
		[XmlElement(ElementName = "CusBankName")]
		public string CusBankName { get; set; }
		[XmlElement(ElementName = "CusBankNo")]
		public string CusBankNo { get; set; }
		[XmlElement(ElementName = "Total")]
		public string Total { get; set; }
		[XmlElement(ElementName = "Amount")]
		public string Amount { get; set; }
		[XmlElement(ElementName = "Amount_words")]
		public string Amount_words { get; set; }
		[XmlElement(ElementName = "ProcessInvNote")]
		public string ProcessInvNote { get; set; }
		[XmlElement(ElementName = "KindOfService")]
		public string KindOfService { get; set; }
		[XmlElement(ElementName = "VAT_Amount")]
		public string VAT_Amount { get; set; }
		[XmlElement(ElementName = "VAT_Rate")]
		public string VAT_Rate { get; set; }
		[XmlElement(ElementName = "VATAmount0")]
		public string VATAmount0 { get; set; }
		[XmlElement(ElementName = "GrossValue0")]
		public string GrossValue0 { get; set; }
		[XmlElement(ElementName = "VATAmount5")]
		public string VATAmount5 { get; set; }
		[XmlElement(ElementName = "GrossValue5")]
		public string GrossValue5 { get; set; }
		[XmlElement(ElementName = "VATAmount10")]
		public string VATAmount10 { get; set; }
		[XmlElement(ElementName = "GrossValue10")]
		public string GrossValue10 { get; set; }
		[XmlElement(ElementName = "GrossValue")]
		public string GrossValue { get; set; }
		[XmlElement(ElementName = "GrossValueNonTax")]
		public string GrossValueNonTax { get; set; }
		[XmlElement(ElementName = "Discount_Amount")]
		public string Discount_Amount { get; set; }
		[XmlElement(ElementName = "Extra")]
		public string Extra { get; set; }
		[XmlElement(ElementName = "CurrencyUnit")]
		public string CurrencyUnit { get; set; }
		[XmlElement(ElementName = "ExchangeRate")]
		public string ExchangeRate { get; set; }
		[XmlElement(ElementName = "ConvertedAmount")]
		public string ConvertedAmount { get; set; }
		[XmlElement(ElementName = "Extra1")]
		public string Extra1 { get; set; }
		[XmlElement(ElementName = "Extra2")]
		public string Extra2 { get; set; }
		[XmlElement(ElementName = "EmailDeliver")]
		public string EmailDeliver { get; set; }
		[XmlElement(ElementName = "Products")]
		public VnptProducts Products { get; set; }
		[XmlElement(ElementName = "SignDate")]
		public string SignDate { get; set; }
		[XmlAttribute(AttributeName = "Id")]
		public string Id { get; set; }
	}

	[XmlRoot(ElementName = "TvanData")]
	public class TvanData
	{
		[XmlElement(ElementName = "Content")]
		public VnptContent Content { get; set; }
		[XmlElement(ElementName = "Date")]
		public string Date { get; set; }
		[XmlAttribute(AttributeName = "Id")]
		public string Id { get; set; }
	}
}
