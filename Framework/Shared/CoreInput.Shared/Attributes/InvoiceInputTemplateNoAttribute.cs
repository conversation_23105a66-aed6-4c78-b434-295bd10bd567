using System.ComponentModel.DataAnnotations;
using Core.Shared.Extensions;

namespace CoreInput.Shared.Attributes
{
    public class InvoiceInputTemplateNoAttribute : ValidationAttribute
    {
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value == null)
                return ValidationResult.Success;

            var source = value.ToString();

            if (!string.IsNullOrEmpty(source) && (source.IsTemplateNo() || source.IsTemplateNo32()))
                return ValidationResult.Success;

            return new ValidationResult(ErrorMessage = $"{source} định dạng dữ liệu mẫu số hóa đơn không đúng");
        }

    }
}
