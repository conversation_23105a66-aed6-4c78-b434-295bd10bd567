using Core.Shared.Constants;
using CoreInput.Shared.Abstractions;
using CoreInput.Shared.Interfaces;
using CoreInput.Shared.Models;
using CoreInput.Shared.Models.BaseInvoiceModels;
using CoreInput.Shared.Models.InvoiceInputModels;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Threading.Tasks;

namespace CoreInput.Shared.Services.EasyServices
{
    public class Easy01Service : BaseInvoiceInputService, IConvertToModelService
    {
        public async Task<ResponseModel<object>> ConvertXml(IFormFile file)
        {
            return await ConvertXmlAsync<EasyModel>(file);
        }

        public override ResponseModel<object> ConvertToModel<T>(object result)
        {
            var base01 = new InvoiceInput01Model();

            EasyModel easyModel = (EasyModel)result;

            base01.InvoiceDate = DateTime.Parse(easyModel.Content.ArisingDate);
            base01.TemplateNo = easyModel.Content.InvoicePattern;
            base01.SerialNo = easyModel.Content.SerialNo;
            base01.InvoiceNo = easyModel.Content.InvoiceNo;
            base01.Note = easyModel.Content.Note;

            if (!string.IsNullOrEmpty(easyModel.Content.InvoiceNo))
                base01.Number = int.Parse(easyModel.Content.InvoiceNo);

            // --- Thông tin người bán ---
            base01.SellerPhoneNumber = easyModel.Content.ComPhone;
            base01.SellerFaxNumber = easyModel.Content.ComFax;
            base01.SellerEmail = easyModel.Content.ComEmail;
            base01.SellerBankName = easyModel.Content.ComBankName;
            base01.SellerBankAccount = easyModel.Content.ComBankNo;
            base01.SellerFullName = easyModel.Content.ComName;
            base01.SellerAddressLine = easyModel.Content.ComAddress;
            base01.SellerTaxCode = easyModel.Content.ComTaxCode;
            base01.SellerSignedTime = DateTime.Parse(easyModel.Content.SignDate);

            // --- Thông tin người mua ---
            base01.BuyerFullNameSigned = easyModel.Content.Buyer;
            base01.BuyerFullName = easyModel.Content.CusName;
            base01.BuyerTaxCode = easyModel.Content.CusTaxCode;
            base01.BuyerAddressLine = easyModel.Content.CusAddress;
            base01.BuyerPhoneNumber = easyModel.Content.CusPhone;
            base01.BuyerEmail = easyModel.Content.CusEmails;
            base01.BuyerBankName = easyModel.Content.CusBankName;
            base01.BuyerBankAccount = easyModel.Content.CusBankNo;
            base01.BuyerSignedAt = DateTime.ParseExact(easyModel.Content.SignDate, "dd/MM/yyyy", CultureInfo.InvariantCulture);
            base01.BuyerSignedAtUtc = DateTime.SpecifyKind((DateTime)base01.BuyerSignedAt, DateTimeKind.Utc);

            // --- Thông tin thanh toán ---
            base01.FromCurrency = easyModel.Content.CurrencyUnit;
            base01.ToCurrency = easyModel.Content.CurrencyUnit;
            base01.ExchangeRate = decimal.Parse(easyModel.Content.ExchangeRate);
            base01.PaymentAmountWords = easyModel.Content.AmountInWords;
            base01.PaymentAmountWordsEn = easyModel.Content.AmountInWordsL2;
            base01.TotalAmount = Decimal.Parse(easyModel.Content.Total);
            base01.TotalPaymentAmount = Decimal.Parse(easyModel.Content.Amount);
            base01.TotalVatAmount = Decimal.Parse(easyModel.Content.VATAmount);
            base01.PaymentMethod = easyModel.Content.PaymentMethod;

            // --- Items ---
            var invoiceDetails = new List<BaseDetailModel>();
            foreach (var item in easyModel.Content.Products.Product)
            {
                var invoiceDetail = new BaseDetailModel
                {
                    Index = int.Parse(item.Pos),
                    ProductName = item.ProdName,
                    Quantity = int.Parse(item.ProdQuantity),
                    UnitName = item.ProdUnit,
                    UnitPrice = decimal.Parse(item.ProdPrice),
                    Amount = decimal.Parse(item.Amount),
                    PaymentAmount = decimal.Parse(item.Total),
                    ProductCode = item.Code
                };
                invoiceDetails.Add(invoiceDetail);
            }

            base01.InvoiceDetails = invoiceDetails;

            // --- TaxBreakDowns ---
            var invoiceTaxBreakdowns = new List<BaseTaxBreakdownModel>();
            var vatRate = int.Parse(easyModel.Content.VATRate);
            if (vatRate == 0)
            {
                var invoiceTaxBreakdown = new BaseTaxBreakdownModel
                {
                    VatPercent = vatRate,
                    VatAmount = decimal.Parse(easyModel.Content.GrossValue0)
                };

                invoiceTaxBreakdowns.Add(invoiceTaxBreakdown);
            }

            if (vatRate == 5)
            {
                var invoiceTaxBreakdown = new BaseTaxBreakdownModel
                {
                    VatPercent = vatRate,
                    VatAmount = decimal.Parse(easyModel.Content.GrossValue5)
                };

                invoiceTaxBreakdowns.Add(invoiceTaxBreakdown);
            }

            if (vatRate == 10)
            {
                var invoiceTaxBreakdown = new BaseTaxBreakdownModel
                {
                    VatPercent = vatRate,
                    VatAmount = decimal.Parse(easyModel.Content.GrossValue10)
                };

                invoiceTaxBreakdowns.Add(invoiceTaxBreakdown);
            }

            base01.InvoiceTaxBreakdowns = invoiceTaxBreakdowns;

            return new ResponseModel<object>(base01, StaticData._01GTKT, true);
        }
    }
}
