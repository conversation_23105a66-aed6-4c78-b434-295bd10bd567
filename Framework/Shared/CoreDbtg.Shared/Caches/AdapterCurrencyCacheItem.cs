using System;
using System.Collections.Generic;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;

namespace CoreDbtg.Shared.Caches
{
    public class AdapterCurrencyCacheItem
    {
        private const string CacheKeyFormat = "i:{0},n:{1}";

        public Dictionary<string, CurrencyEntity> Value { get; set; }

        public DateTime ExpiredDateTime { get; set; }

        public AdapterCurrencyCacheItem()
        {

        }

        public AdapterCurrencyCacheItem(Dictionary<string, CurrencyEntity> value, DateTime expiredDateTime)
        {
            Value = value;
            ExpiredDateTime = expiredDateTime;
        }

        public static string CalculateCacheKey()
        {
            return string.Format(CacheKeyFormat, "Adapter", $"Currencies");
        }
    }
}
