using System;

namespace CoreDbtg.Shared.Models
{
    public class TenantModel
    {
        public Guid Id { get; set; }

        public Guid? ParentId { get; set; }

        public string TenantCode { get; set; }

        public string Name { get; set; }

        public string LegalName { get; set; }

        public string Address { get; set; }

        public string FullNameVi { get; set; }

        public string FullNameEn { get; set; }

        public string City { get; set; }

        public string Country { get; set; }

        public string District { get; set; }

        public string Emails { get; set; }

        public string TaxCode { get; set; }

        public string Phones { get; set; }

        public string Fax { get; set; }

        public DateTime? EffectiveDeactiveDate { get; set; }

        public string Metadata { get; set; }
        public string Website { get; set; }
        public string BankName { get; set; }
        public string BankAccount { get; set; }
        public DateTime? DayOfUser { get; set; }
    }
}
