using System;
using System.Collections.Generic;

namespace CoreDbtg.Shared.Models
{
    public class ESInvoiceModel : ESBaseInvoiceHeaderModel
    {
        /// <summary>
        /// Id (int) của invoice
        /// </summary>
        public int IdInvoice { get; set; }

        #region Thông tin thanh toán
        /// <summary>
        /// Nguyên tệ
        /// </summary>
        public string FromCurrency { get; set; }

        /// <summary>
        /// Ngoại tệ
        /// </summary>
        public string ToCurrency { get; set; }

        public int RoundingCurrency { get; set; }

        /// <summary>
        ///  lưu giá trị chuyển đổi từ đơn vị cao sang đơn vị thấp. vd 1 đô la = 100 cents
        /// </summary>
        public int CurrencyConversion { get; set; }

        /// <summary>
        /// Tỷ giá (Bằng 1 nếu Nguyên tệ = Ngoại tệ)
        /// </summary>
        public decimal ExchangeRate { get; set; }

        /// <summary>
        /// Phương thức thanh toán
        /// </summary>
        public string PaymentMethod { get; set; }

        /// <summary>
        /// Ngày thanh toán
        /// </summary>
        public DateTime? PaymentDate { get; set; }

        /// <summary>
        /// Số tiền bằng chữ (Đọc tiếng việt)
        /// </summary>
        public string PaymentAmountWords { get; set; }

        /// <summary>
        /// Số tiền bằng chữ (Đọc tiếng anh)
        /// </summary>
        public string PaymentAmountWordsEn { get; set; }

        /// <summary>
        /// Tổng tiền chưa thuế, chưa chiết khấu
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// Tổng tiền phải trả
        /// </summary>
        public decimal TotalPaymentAmount { get; set; }
        #endregion

        #region Thông tin tạo/sửa/xóa/duyệt/in
        /// <summary>
        /// Họ tên người insert bản ghi vào DB (lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string FullNameCreator { get; set; }

        /// <summary>
        /// Họ tên người insert bản ghi vào DB (lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string UserNameCreator { get; set; }

        /// <summary>
        /// Ngày in chuyển đổi, khác null tức là đã in chuyển đổi
        /// </summary>
        public DateTime? PrintedTime { get; set; }

        /// <summary>
        /// Họ tên người in chuyển đổi (lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string FullNamePrinter { get; set; }

        /// <summary>
        /// Tài khoản đăng nhập người in chuyển đôi (lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public Guid? PrintedBy { get; set; }

        /// <summary>
        /// Thời điểm duyệt hóa đơn
        /// </summary>
        public DateTime? ApprovedTime { get; set; }

        /// <summary>
        /// Họ tên người duyệt hóa đơn (lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string FullNameApprover { get; set; }

        /// <summary>
        /// Tài khoản đăng nhập người duyệt hóa đơn (lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public Guid? ApprovedBy { get; set; }
        #endregion

        //#region Thông tin biên bản
        ///// <summary>
        ///// Số biên bản
        ///// </summary>
        //public string DocumentNo { get; set; }

        ///// <summary>
        ///// Ngày biên bản
        ///// </summary>
        //public DateTime? DocumentDate { get; set; }

        ///// <summary>
        ///// Lý do biên bản
        ///// </summary>
        //public string DocumentReason { get; set; }
        //#endregion

        #region Thông tin người mua
        /// <summary>
        /// code bản ghi người mua (Customer), có thể null nếu là loại hóa đơn 03XKNB hoặc chưa đồng bộ dữ liệu danh mục
        /// </summary>
        public string BuyerCode { get; set; }

        /// <summary>
        /// Mã khách hàng
        /// </summary>
        public long? BuyerId { get; set; }

        /// <summary>
        /// Họ tên người mua (nếu là khách lẻ không thuộc công ty nào thì đây là tên công ty)
        /// </summary>
        public string BuyerFullName { get; set; }

        /// <summary>
        /// Đại diện pháp nhân người mua (trường hợp là công ty)
        /// </summary>
        public string BuyerLegalName { get; set; }

        /// <summary>
        /// Mã số thuế người mua
        /// </summary>
        public string BuyerTaxCode { get; set; }

        /// <summary>
        /// Địa chỉ người mua
        /// </summary>
        public string BuyerAddressLine { get; set; }

        /// <summary>
        /// Tên Quận/Huyện người mua
        /// </summary>
        public string BuyerDistrictName { get; set; }

        /// <summary>
        /// Tên thành phố người mua
        /// </summary>
        public string BuyerCityName { get; set; }

        /// <summary>
        /// Mã quốc gia người mua (Việt Nam = VN)
        /// </summary>
        public string BuyerCountryCode { get; set; }

        /// <summary>
        /// Số điện thoại người mua
        /// </summary>
        public string BuyerPhoneNumber { get; set; }

        /// <summary>
        /// Số FAX người mua
        /// </summary>
        public string BuyerFaxNumber { get; set; }

        /// <summary>
        /// Email người mua
        /// </summary>
        public string BuyerEmail { get; set; }

        /// <summary>
        /// Tên ngân hàng người mua
        /// </summary>
        public string BuyerBankName { get; set; }

        /// <summary>
        /// Tài khoản ngân hàng người mua
        /// </summary>
        public string BuyerBankAccount { get; set; }

        /// <summary>
        /// Thời điểm người mua ký
        /// </summary>
        public DateTime? BuyerSignedTime { get; set; }

        /// <summary>
        /// Họ tên người mua ký (Sử dụng tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string BuyerFullNameSigned { get; set; }

        /// <summary>
        /// Tài khoản người mua ký hóa đơn (Sử dụng tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public Guid? BuyerSignedBy { get; set; }
        #endregion

        /// <summary>
        /// Tổng tiền chiết khấu trước thuế, là lượng điều chỉnh nếu là hóa đơn tăng/giảm
        /// </summary>
        public decimal TotalDiscountAmountBeforeTax { get; set; }

        /// <summary>
        /// phần trăm chiết khấu sau thuế
        /// </summary>
        public double TotalDiscountPercentAfterTax { get; set; }

        /// <summary>
        /// tổng tiền chiết khấu sau thuế
        /// </summary>
        public decimal TotalDiscountAmountAfterTax { get; set; }

        /// <summary>
        /// Tổng tiền thuế
        /// </summary>
        public decimal TotalVatAmount { get; set; }

        /// <summary>
        /// Id bản ghi người mua bên ERP
        /// </summary>
        public string BuyerErpId { get; set; }

        public long InvoiceReferenceId { get; set; }

        public string TransactionData { get; set; }

        public Dictionary<string, string> ExtraProperties { get; set; }
        public List<ESInvoiceTaxBreakdownModel> InvoiceTaxBreakdowns { get; set; }

        public List<ESInvoiceHeaderExtraModel> InvoiceHeaderExtras { get; set; }

        public List<ESInvoiceDetailModel> InvoiceDetails { get; set; }

        public class ESInvoiceTaxBreakdownModel
        {
            public long Id { get; set; }

            public string Name { get; set; }

            public decimal VatAmount { get; set; }

            public decimal VatPercent { get; set; }

            public decimal VatAmountBackUp { get; set; }
        }

        public class ESInvoiceHeaderExtraModel
        {
            public long InvoiceHeaderFieldId { get; set; }

            public long Id { get; set; }

            public string FieldValue { get; set; }

            public string FieldName { get; set; }
        }

        public class ESInvoiceDetailModel
        {
            public long Id { get; set; }

            public int Index { get; set; }

            public decimal DiscountAmountBeforeTax { get; set; }

            public decimal DiscountPercentBeforeTax { get; set; }

            public decimal PaymentAmount { get; set; }

            public string ProductCode { get; set; }

            public long ProductId { get; set; }

            public string ProductName { get; set; }

            public string UnitName { get; set; }

            public long UnitId { get; set; }

            public decimal UnitPrice { get; set; }

            public int RoundingUnit { get; set; }

            public decimal Quantity { get; set; }

            public decimal Amount { get; set; }

            public decimal VatPercent { get; set; }

            public decimal VatAmount { get; set; }

            public string Note { get; set; }

            public bool HideUnitPrice { get; set; }

            public bool HideUnit { get; set; }

            public bool HideQuantity { get; set; }

            public decimal TotalAmount { get; set; }

            public Dictionary<string, string> ExtraProperties { get; set; }
            public List<ESInvoiceDetailExtraModel> InvoiceDetailExtras { get; set; }
        }

        public class ESInvoiceDetailExtraModel
        {
            public long Id { get; set; }

            public string FieldValue { get; set; }

            public string FieldName { get; set; }

            public long InvoiceDetailFieldId { get; set; }
        }
    }

    public class ESBaseInvoiceHeaderModel
    {
        public long Id { get; set; }

        public long Partition { get; set; }

        public Guid TenantId { get; set; }

        public DateTime CreationTime { get; set; }

        public Guid CreatedBy { get; set; }

        public DateTime? UpdatedTime { get; set; }

        public Guid? LastModifierId { get; set; }

        public DateTime? CancelTime { get; set; }

        public Guid? CancelId { get; set; }

        public DateTime? DeletionTime { get; set; }

        public Guid? DeleteId { get; set; }

        /// <summary>
        /// Key dùng để index, không được trùng (MST|TemplateNo|SerialNo|InvoiceNo)
        /// </summary>
        public string Indexing { get; set; }

        /// <summary>
        /// Nguồn dữ liệu từ đâu: API, Form, Excel, ShareDb
        /// </summary>
        public int Source { get; set; }

        /// <summary>
        /// Gói tạo hóa đơn
        /// </summary>
        public Guid BatchId { get; set; }

        /// <summary>
        /// Mã tra cứu. Các hóa đơn thay thế/điều chỉnh sẽ cùng mã này
        /// </summary>
        public string TransactionId { get; set; }

        /// <summary>
        /// code bản ghi mẫu hóa đơn (Template)
        /// </summary>
        public long TemplateId { get; set; }

        /// <summary>
       
        /// </summary>
        public short TemplateNo { get; set; }

        /// <summary>
        /// Ký hiệu (PT/17E)
        /// </summary>
        public string SerialNo { get; set; }

        /// <summary>
        /// Số hóa đơn gồm 7 ký tự số (0000001)
        /// </summary>
        public string InvoiceNo { get; set; }

        public int? Number { get; set; }

        /// <summary>
        /// Ghi chú
        /// </summary>
        public string Note { get; set; }

        /// <summary>
        /// Ngày hóa đơn NSD nhập vào. Chỉ lưu ngày, tháng, năm. Không lưu thời gian. Lưu dạng UTC
        /// </summary>
        public DateTime InvoiceDate { get; set; }

        /// <summary>
        /// Trạng thái hóa đơn (int)
        /// </summary>
        public int InvoiceStatus { get; set; }

        /// <summary>
        /// Trạng thái hóa đơn (name)
        /// </summary>
        public string InvoiceStatusName { get; set; }

        /// <summary>
        /// Trạng thái ký hóa đơn
        /// </summary>
        public int SignStatus { get; set; }

        /// <summary>
        /// Trạng thái duyệt hóa đơn
        /// </summary>
        public int ApproveStatus { get; set; }

        public int ApproveCancelStatus { get; set; }

        public int ApproveDeleteStatus { get; set; }

        /// <summary>
        /// Ghi bản ghi đăng ký phát hành hóa đơn, lưu lại để xác định hóa đơn được tạo theo thông báo phát hành nào
        /// </summary>
        public long? RegistrationHeaderId { get; set; }

        public long? RegistrationDetailId { get; set; }

        /// <summary>
        /// Id bản ghi bên ERP
        /// </summary>
        public string ErpId { get; set; }

        /// <summary>
        /// Tài khoản người tạo hóa đơn bên ERP
        /// </summary>
        public string CreatorErp { get; set; }

        ///// <summary>
        ///// id file bản in thể hiện
        ///// </summary>
        //public Guid? IdUnOffical { get; set; }

        ///// <summary>
        ///// id file xml trong bảng media
        ///// </summary>
        //public Guid? IdXml { get; set; }

        ///// <summary>
        ///// id file biên bản
        ///// </summary>
        //public Guid? IdFileDocument { get; set; }

        public DateTime? IssuedTime { get; set; }

        /// <summary>
        /// kiểm tra thông tin hóa đơn đã được tra cứu ở portal chưa
        /// </summary>
        public bool IsViewed { get; set; }

        /// <summary>
        /// thời gian xem lần cuối cùng theo giờ local
        /// </summary>
        public DateTime? ViewedTime { get; set; }

        /// <summary>
        /// kiểm tra thông tin hóa đơn đã được xem chưa
        /// </summary>
        public bool IsOpened { get; set; }

        /// <summary>
        /// thời gian xem hóa đơn lần đầu theo giờ local
        /// </summary>
        public DateTime? OpenedTime { get; set; }

        #region Thông tin người bán
        public Guid SellerId { get; set; }

        /// <summary>
        /// Người đại diện pháp nhân bên bán
        /// </summary>
        public string SellerLegalName { get; set; }

        /// <summary>
        /// Mã số thuế bên bán
        /// </summary>
        public string SellerTaxCode { get; set; }

        /// <summary>
        /// Địa chỉ bên bán
        /// </summary>
        public string SellerAddressLine { get; set; }

        /// <summary>
        /// Mã quốc gia người bán (Việt Nam là VN)
        /// </summary>
        public string SellerCountryCode { get; set; }

        /// <summary>
        /// Tên phường/xã người bán
        /// </summary>
        public string SellerDistrictName { get; set; }

        /// <summary>
        /// Tên thành phố người bán
        /// </summary>
        public string SellerCityName { get; set; }

        /// <summary>
        /// Số điện thoại người bán
        /// </summary>
        public string SellerPhoneNumber { get; set; }

        /// <summary>
        /// Số FAX người bán
        /// </summary>
        public string SellerFaxNumber { get; set; }

        /// <summary>
        /// Email người bán
        /// </summary>
        public string SellerEmail { get; set; }

        /// <summary>
        /// Tên ngân hàng người bán
        /// </summary>
        public string SellerBankName { get; set; }

        /// <summary>
        /// Tài khoản ngân hàng người bán
        /// </summary>
        public string SellerBankAccount { get; set; }

        /// <summary>
        /// Tên công ty bán
        /// </summary>
        public string SellerFullName { get; set; }

        /// <summary>
        /// Thời điểm hóa đơn được ký
        /// </summary>
        public DateTime? SellerSignedTime { get; set; }

        /// <summary>
        /// Họ tên người ký hóa đơn (Sử dụng tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string SellerFullNameSigned { get; set; }

        /// <summary>
        /// Tài khoản người ký hóa đơn (Sử dụng tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public Guid? SellerSignedId { get; set; }
        #endregion
    }
}
