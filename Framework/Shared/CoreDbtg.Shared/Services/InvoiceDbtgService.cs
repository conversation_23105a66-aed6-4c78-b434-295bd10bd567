using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using CoreDbtg.Shared.Interfaces;
using CoreDbtg.Shared.Models;
using CoreDbtg.Shared.Models.Base;
using CoreDbtg.Shared.Models.Invoices;
using CoreDbtg.Shared.Models.Requests;
using Dapper;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using VCB.Integrated.Oracle.Domain.BaseEntities;
using VCB.Integrated.Oracle.Domain.Entities.G4;
using static CoreDbtg.Shared.Constants.DbtgSharedConst;

namespace CoreDbtg.Shared.Services
{
    public class InvoiceDbtgService : IInvoiceDbtgService
    {
        private readonly IAppFactory _appFactory;

        public InvoiceDbtgService(IAppFactory appFactory)
        {
            _appFactory = appFactory;
        }

        #region MAIN

        /// <summary>
        /// Valid invoice
        /// </summary>
        /// <param name="tblName"></param>
        /// <param name="dateNum"></param>
        /// <returns></returns>
        public async Task<bool> AnyGxValidInvoiceAsync(string tblName, int dateNum)
        {
            var sql = $@"
                SELECT 1 
                FROM DUAL 
                WHERE EXISTS (
                    SELECT 1 
                    FROM ""{tblName}"" 
                    WHERE  ""CeationDateNumber"" = :CeationDateNumber
                        AND ""CreatorStatus"" = :CreatorStatus
                        AND ""InvoiceStatus"" = :InvoiceStatus )
            ";

            var dicParameter = new Dictionary<string, object>
            {
                {":CeationDateNumber", dateNum },
                {":CreatorStatus", CreatorStatus.ValidateSuccess },
                {":InvoiceStatus", InvoiceStatusDbtg.Create }
            };
            return (
                await _appFactory.VcbIntegratedOracle.Connection.ExecuteScalarAsync<bool>(sql, new DynamicParameters(dicParameter)));
        }

        /// <summary>
        /// Sẽ xóa đi khi hoàn thành task VCBINNB-2656
        /// </summary>
        /// <param name="tblName"></param>
        /// <param name="dateNum"></param>
        /// <returns></returns>
        public async Task<bool> AnyGxValidInvoiceOldAsync(string tblName, int dateNum)
        {
            return (
                await _appFactory.VcbIntegratedOracle.Connection.ExecuteScalarAsync<bool>(
                    $@" SELECT 1 
                        FROM DUAL 
                        WHERE EXISTS (
                            SELECT 1 
                            FROM ""{tblName}"" 
                            WHERE  ""CeationDateNumber"" = {dateNum} 
                                AND ""CreatorStatus"" = {CreatorStatus.ValidateSuccess} 
                                AND ""InvoiceStatus"" = {InvoiceStatusDbtg.Create} ) "
                    ));
        }  

        /// <summary>
        /// Lấy mẫu hóa đơn đang sử dụng của 1 Tenant
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="templateNo"></param>
        /// <returns></returns>
        public async Task<List<RegisterAvaibilitiesByTenantsModel>> GetRegisterAvailabilities(Guid tenantId, short templateNo)
        {
            var query = GenerateDrawRegisterAvailabilitiesQuery(tenantId, templateNo);

            var data = await _appFactory.VnisCoreOracle.Connection.QueryAsync<RegisterAvaibilitiesByTenantsModel>(query);

            return (data != null && data.Any()) ? data.ToList() : null;
        }

        /// <summary>
        /// Lấy mẫu hóa đơn đang sử dụng theo ds TenantIds
        /// </summary>
        /// <param name="tenantIds"></param>
        /// <param name="templateNo"></param>
        /// <returns></returns>
        public async Task<List<RegisterAvaibilitiesByTenantsModel>> GetRegisterAvailabilities(IEnumerable<Guid> tenantIds, short templateNo, InvoiceTemplateSource? templateSource)
        {
            var query = GenerateDrawRegisterAvailabilitiesQuery(tenantIds, templateNo, templateSource);

            var data = await _appFactory.VnisCoreOracle.Connection.QueryAsync<RegisterAvaibilitiesByTenantsModel>(query);

            return (data != null && data.Any()) ? data.ToList() : null;
        }

        /// <summary>
        /// Sử dụng với case hóa đơn gộp. Đưa các thông tin của Header thành ExtraProps của Details
        /// </summary>
        /// <param name="headerEntity"></param>
        /// <returns></returns>
        public List<InvoiceDbtg01DetailExtraModel> ConvertMergeInvoiceDetailExtraToRequestModel<THeader>(THeader headerEntity)
            where THeader : BaseGxInvoiceModel
        {
            var result = new List<InvoiceDbtg01DetailExtraModel>
            {
                new InvoiceDbtg01DetailExtraModel
                {
                    FieldName = HeaderExtra.TransactionAmount,
                    FieldValue = headerEntity.TransactionAmount.ToString()
                }
            };

            if (!headerEntity.AppName.IsNullOrEmpty())
            {
                result.Add(new InvoiceDbtg01DetailExtraModel
                {
                    FieldName = HeaderExtra.AppName,
                    FieldValue = headerEntity.AppName
                });
            }

            if (!headerEntity.OperationName.IsNullOrEmpty())
            {
                result.Add(new InvoiceDbtg01DetailExtraModel
                {
                    FieldName = HeaderExtra.OperationName,
                    FieldValue = headerEntity.OperationName
                });
            }

            if (!headerEntity.TellSeq.IsNullOrEmpty())
            {
                result.Add(new InvoiceDbtg01DetailExtraModel
                {
                    FieldName = HeaderExtra.TellSeq,
                    FieldValue = headerEntity.TellSeq
                });
            }

            if (!headerEntity.MerNo.IsNullOrEmpty())
            {
                result.Add(new InvoiceDbtg01DetailExtraModel
                {
                    FieldName = HeaderExtra.MerNo,
                    FieldValue = headerEntity.MerNo
                });
            }

            if (!headerEntity.BidNo.IsNullOrEmpty())
            {
                result.Add(new InvoiceDbtg01DetailExtraModel
                {
                    FieldName = HeaderExtra.BidNo,
                    FieldValue = headerEntity.BidNo
                });
            }

            if (!headerEntity.ErpId.IsNullOrEmpty())
            {
                result.Add(new InvoiceDbtg01DetailExtraModel
                {
                    FieldName = DetailExtra.ErpId,
                    FieldValue = headerEntity.ErpId
                });
            }

            if (!headerEntity.CreatorErp.IsNullOrEmpty())
            {
                result.Add(new InvoiceDbtg01DetailExtraModel
                {
                    FieldName = DetailExtra.CreatorErp,
                    FieldValue = headerEntity.CreatorErp
                });
            }

            if (!headerEntity.Note.IsNullOrEmpty())
            {
                result.Add(new InvoiceDbtg01DetailExtraModel
                {
                    FieldName = DetailExtra.Note,
                    FieldValue = headerEntity.Note
                });
            }

            result.Add(new InvoiceDbtg01DetailExtraModel
            {
                FieldName = DetailExtra.ExchangeRate,
                FieldValue = headerEntity.ExchangeRate.ToString()
            });

            if (!headerEntity.BuyerCode.IsNullOrEmpty())
            {
                result.Add(new InvoiceDbtg01DetailExtraModel
                {
                    FieldName = DetailExtra.BuyerCode,
                    FieldValue = headerEntity.BuyerCode
                });
            }

            if (!headerEntity.BuyerFullName.IsNullOrEmpty())
            {
                result.Add(new InvoiceDbtg01DetailExtraModel
                {
                    FieldName = DetailExtra.BuyerFullName,
                    FieldValue = headerEntity.BuyerFullName
                });
            }

            if (!headerEntity.BuyerLegalName.IsNullOrEmpty())
            {
                result.Add(new InvoiceDbtg01DetailExtraModel
                {
                    FieldName = DetailExtra.BuyerLegalName,
                    FieldValue = headerEntity.BuyerLegalName
                });
            }

            if (!headerEntity.BuyerTaxCode.IsNullOrEmpty())
            {
                result.Add(new InvoiceDbtg01DetailExtraModel
                {
                    FieldName = DetailExtra.BuyerTaxCode,
                    FieldValue = headerEntity.BuyerTaxCode
                });
            }

            if (!headerEntity.BuyerAddressLine.IsNullOrEmpty())
            {
                result.Add(new InvoiceDbtg01DetailExtraModel
                {
                    FieldName = DetailExtra.BuyerAddressLine,
                    FieldValue = headerEntity.BuyerAddressLine
                });
            }

            if (!headerEntity.BuyerPhoneNumber.IsNullOrEmpty())
            {
                result.Add(new InvoiceDbtg01DetailExtraModel
                {
                    FieldName = DetailExtra.BuyerPhoneNumber,
                    FieldValue = headerEntity.BuyerPhoneNumber
                });
            }

            if (!headerEntity.BuyerEmail.IsNullOrEmpty())
            {
                result.Add(new InvoiceDbtg01DetailExtraModel
                {
                    FieldName = DetailExtra.BuyerEmail,
                    FieldValue = headerEntity.BuyerEmail
                });
            }

            if (!headerEntity.BuyerBankName.IsNullOrEmpty())
            {
                result.Add(new InvoiceDbtg01DetailExtraModel
                {
                    FieldName = DetailExtra.BuyerBankName,
                    FieldValue = headerEntity.BuyerBankName
                });
            }

            if (!headerEntity.BuyerBankAccount.IsNullOrEmpty())
            {
                result.Add(new InvoiceDbtg01DetailExtraModel
                {
                    FieldName = DetailExtra.BuyerBankAccount,
                    FieldValue = headerEntity.BuyerBankAccount
                });
            }

            try
            {
                if (!headerEntity.ExtraProperties.IsNullOrEmpty())
                {
                    var fieldNames = result.Select(x => x.FieldName);
                    var extra = JsonConvert.DeserializeObject<List<InvoiceDbtg01DetailExtraModel>>(headerEntity.ExtraProperties)
                                            .Where(x => !x.FieldName.IsNullOrEmpty() && !x.FieldValue.IsNullOrEmpty() && !fieldNames.Contains(x.FieldName));

                    result.AddRange(extra);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
            }

            return result;
        }

        public List<InvoiceDbtg01DetailModel> ConvertMergeInvoiceDetailToRequestModel<TDetail>(List<TDetail> entities, List<InvoiceDbtg01DetailExtraModel> extraDetails, Dictionary<short, string> prodTypes)
            where TDetail : BaseGxInvoiceDetailModel
        {
            var result = new List<InvoiceDbtg01DetailModel>();

            var i = 1;
            foreach (var itemDetail in entities)
            {
                var detail = new InvoiceDbtg01DetailModel
                {
                    Amount = itemDetail.Amount,
                    ProductName = prodTypes.ContainsKey(itemDetail.ProductType) ? prodTypes[itemDetail.ProductType] : InvoiceProperty.ProdNameDefault,
                    DiscountAmountBeforeTax = 0,
                    DiscountPercentBeforeTax = 0,
                    Index = i,
                    Note = itemDetail.Note,
                    PaymentAmount = itemDetail.PaymentAmount,
                    VatAmount = itemDetail.VatAmount,
                    VatPercent = itemDetail.VatPercent,
                    ProductCode = InvoiceProperty.ProductCode,
                    UnitName = InvoiceProperty.UnitName,
                    UnitPrice = itemDetail.Amount,
                    Quantity = InvoiceProperty.Quantity
                };

                detail.InvoiceDetailExtras = new List<InvoiceDbtg01DetailExtraModel>();
                if (!itemDetail.GlNo.IsNullOrEmpty())
                {
                    detail.InvoiceDetailExtras.Add(new InvoiceDbtg01DetailExtraModel
                    {
                        FieldName = DetailExtra.GlNo,
                        FieldValue = itemDetail.GlNo
                    });
                }

                if (!itemDetail.RefNo.IsNullOrEmpty())
                {
                    detail.InvoiceDetailExtras.Add(new InvoiceDbtg01DetailExtraModel
                    {
                        FieldName = DetailExtra.RefNo,
                        FieldValue = itemDetail.RefNo
                    });
                }

                if (!itemDetail.Note2.IsNullOrEmpty())
                {
                    detail.InvoiceDetailExtras.Add(new InvoiceDbtg01DetailExtraModel
                    {
                        FieldName = DetailExtra.Note2,
                        FieldValue = itemDetail.Note2
                    });
                }

                if (!itemDetail.ExtraProperties.IsNullOrEmpty())
                {
                    try
                    {
                        var fieldNames = detail.InvoiceDetailExtras.Select(x => x.FieldName);
                        var extra = JsonConvert.DeserializeObject<List<InvoiceDbtg01DetailExtraModel>>(itemDetail.ExtraProperties)
                                                .Where(x => !x.FieldName.IsNullOrEmpty() && !x.FieldValue.IsNullOrEmpty() && !fieldNames.Contains(x.FieldName));

                        detail.InvoiceDetailExtras.AddRange(extra);
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, ex.Message);
                    }
                }

                if (extraDetails != null && extraDetails.Any())
                {
                    detail.InvoiceDetailExtras.AddRange(extraDetails);
                }

                result.Add(detail);

                i++;
            }

            return result;
        }

        public CreateInvoice01RequestModel ConvertInvoiceToRequestModel<THeader, TDetail>(THeader headerEntity, DateTime invoiceDate, List<TDetail> detailEntities, string serialNo, int integratedGroup, InvoiceSource source, Dictionary<short, string> prodTypes)
            where THeader : BaseInvoiceHeader
            where TDetail : BaseInvoiceDetail
        {
            detailEntities = detailEntities.OrderBy(x => x.Index).ToList();

            var request = new CreateInvoice01RequestModel
            {
                TenantCode = headerEntity.TenantCode,
                Id = headerEntity.Id,
                IntegratedGroup = integratedGroup,
                ErpId = headerEntity.ErpId,
                //TransactionId = item.TransactionId,
                InvoiceDate = invoiceDate,
                InvoiceDateYear = (short)invoiceDate.Year,
                InvoiceDateQuater = (short)invoiceDate.GetQuarter(),
                InvoiceDateMonth = (short)invoiceDate.Month,
                InvoiceDateWeek = (short)invoiceDate.GetWeek(),
                InvoiceDateNumber = (short)invoiceDate.Day,
                CreatorErp = headerEntity.CreatorErp,
                Source = (short)source,
                TemplateNo = 1,
                SerialNo = serialNo,
                Note = headerEntity.Note,
                ToCurrency = headerEntity.ToCurrency,
                ExchangeRate = headerEntity.ExchangeRate,
                PaymentMethod = headerEntity.PaymentMethod,
                TotalAmount = headerEntity.TotalAmount,
                TotalPaymentAmount = headerEntity.TotalPaymentAmount,
                BuyerCode = headerEntity.BuyerCode,
                BuyerFullName = headerEntity.BuyerFullName,
                BuyerLegalName = headerEntity.BuyerLegalName,
                BuyerTaxCode = headerEntity.BuyerTaxCode,
                BuyerAddressLine = headerEntity.BuyerAddressLine,
                BuyerPhoneNumber = headerEntity.BuyerPhoneNumber,
                BuyerEmail = headerEntity.BuyerEmail,
                BuyerBankName = headerEntity.BuyerBankName,
                BuyerBankAccount = headerEntity.BuyerBankAccount,
                TotalDiscountAmountBeforeTax = 0,
                TotalVatAmount = headerEntity.TotalVatAmount,
                TotalDiscountAmountAfterTax = 0,
                TotalDiscountPercentAfterTax = 0,

                InvoiceTaxBreakdowns = detailEntities.GroupBy(x => x.VatPercent).Select(x => new InvoiceDbtg01TaxBreakdownModel
                {
                    VatPercent = x.Key,
                    VatAmount = x.Sum(y => y.VatAmount),
                    VatAmountBackUp = x.Sum(y => y.VatAmount),
                }).ToList()
            };

            request.InvoiceDetails = ConvertInvoiceDetailToRequestModel(detailEntities, prodTypes);
            request.InvoiceHeaderExtras = ConvertInvoiceHeaderExtraToRequestModel(headerEntity);

            return request;
        }

        public List<InvoiceDbtg01HeaderExtraModel> ConvertInvoiceHeaderExtraToRequestModel<THeader>(THeader headerEntity)
            where THeader : BaseInvoiceHeader
        {
            var result = new List<InvoiceDbtg01HeaderExtraModel>
            {
                new InvoiceDbtg01HeaderExtraModel
                {
                    FieldName = HeaderExtra.TransactionAmount,
                    FieldValue = headerEntity.TransactionAmount.ToString()
                }
            };

            if (!headerEntity.AppName.IsNullOrEmpty())
            {
                result.Add(new InvoiceDbtg01HeaderExtraModel
                {
                    FieldName = HeaderExtra.AppName,
                    FieldValue = headerEntity.AppName
                });
            }

            if (!headerEntity.OperationName.IsNullOrEmpty())
            {
                result.Add(new InvoiceDbtg01HeaderExtraModel
                {
                    FieldName = HeaderExtra.OperationName,
                    FieldValue = headerEntity.OperationName
                });
            }

            if (!headerEntity.TellSeq.IsNullOrEmpty())
            {
                result.Add(new InvoiceDbtg01HeaderExtraModel
                {
                    FieldName = HeaderExtra.TellSeq,
                    FieldValue = headerEntity.TellSeq
                });
            }

            if (!headerEntity.MerNo.IsNullOrEmpty())
            {
                result.Add(new InvoiceDbtg01HeaderExtraModel
                {
                    FieldName = HeaderExtra.MerNo,
                    FieldValue = headerEntity.MerNo
                });
            }

            if (!headerEntity.BidNo.IsNullOrEmpty())
            {
                result.Add(new InvoiceDbtg01HeaderExtraModel
                {
                    FieldName = HeaderExtra.BidNo,
                    FieldValue = headerEntity.BidNo
                });
            }

            try
            {
                if (!headerEntity.ExtraProperties.IsNullOrEmpty())
                {
                    var fieldNames = result.Select(x => x.FieldName);
                    var extra = JsonConvert.DeserializeObject<List<InvoiceDbtg01HeaderExtraModel>>(headerEntity.ExtraProperties)
                                            .Where(x => !x.FieldName.IsNullOrEmpty()
                                                        && !x.FieldValue.IsNullOrEmpty()
                                                        && !fieldNames.Contains(x.FieldName));

                    result.AddRange(extra);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
            }

            return result;
        }

        public List<InvoiceDbtg01DetailModel> ConvertInvoiceDetailToRequestModel<TDetail>(List<TDetail> detailEntities, Dictionary<short, string> prodTypes)
            where TDetail : BaseInvoiceDetail
        {
            var details = new List<InvoiceDbtg01DetailModel>();

            var i = 1;
            foreach (var item in detailEntities)
            {
                var detail = new InvoiceDbtg01DetailModel
                {
                    Amount = item.Amount,
                    ProductName = prodTypes.ContainsKey(item.ProductType) ? prodTypes[item.ProductType] : InvoiceProperty.ProdNameDefault,
                    DiscountAmountBeforeTax = 0,
                    DiscountPercentBeforeTax = 0,
                    Index = i,
                    Note = item.Note,
                    PaymentAmount = item.PaymentAmount,
                    VatAmount = item.VatAmount,
                    VatPercent = item.VatPercent,
                    ProductCode = InvoiceProperty.ProductCode,
                    UnitName = InvoiceProperty.UnitName,
                    UnitPrice = item.Amount,
                    Quantity = InvoiceProperty.Quantity
                };

                detail.InvoiceDetailExtras = new List<InvoiceDbtg01DetailExtraModel>();
                if (!item.GlNo.IsNullOrEmpty())
                {
                    detail.InvoiceDetailExtras.Add(new InvoiceDbtg01DetailExtraModel
                    {
                        FieldName = DetailExtra.GlNo,
                        FieldValue = item.GlNo
                    });
                }

                if (!item.RefNo.IsNullOrEmpty())
                {
                    detail.InvoiceDetailExtras.Add(new InvoiceDbtg01DetailExtraModel
                    {
                        FieldName = DetailExtra.RefNo,
                        FieldValue = item.RefNo
                    });
                }

                if (!item.Note2.IsNullOrEmpty())
                {
                    detail.InvoiceDetailExtras.Add(new InvoiceDbtg01DetailExtraModel
                    {
                        FieldName = DetailExtra.Note2,
                        FieldValue = item.Note2
                    });
                }

                if (!item.ExtraProperties.IsNullOrEmpty())
                {
                    try
                    {
                        var fieldNames = detail.InvoiceDetailExtras.Select(x => x.FieldName);
                        var extra = JsonConvert.DeserializeObject<List<InvoiceDbtg01DetailExtraModel>>(item.ExtraProperties)
                                                .Where(x => !x.FieldName.IsNullOrEmpty()
                                                            && !x.FieldValue.IsNullOrEmpty()
                                                            && !fieldNames.Contains(x.FieldName));

                        detail.InvoiceDetailExtras.AddRange(extra);
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, ex.Message);
                    }
                }

                details.Add(detail);

                i++;
            }

            return details;
        }

        public Invoice01IntegratedHeaderModel MappingToInvoiceModel<THeader, TDetail>(THeader headerEntity, DateTime invoiceDate, List<TDetail> detailEntities, RegisterAvaibilitiesByTenantsModel template, int integratedGroup, InvoiceSource source, Dictionary<short, string> prodTypes)
            where THeader : BaseInvoiceHeader
            where TDetail : BaseInvoiceDetail
        {
            detailEntities = detailEntities.OrderBy(x => x.Index).ToList();

            var request = new Invoice01IntegratedHeaderModel
            {
                TenantCode = headerEntity.TenantCode,
                Id = headerEntity.Id,
                ErpId = headerEntity.ErpId,
                InvoiceDate = invoiceDate.ToUniversalTime(),
                CreatorErp = headerEntity.CreatorErp,
                Source = (short)source,
                TemplateNo = template.TemplateNo,
                SerialNo = template.SerialNo,
                InvoiceTemplateId = template.Id,
                Note = headerEntity.Note,
                ToCurrency = headerEntity.ToCurrency,
                ExchangeRate = headerEntity.ExchangeRate,
                PaymentMethod = headerEntity.PaymentMethod,
                TotalAmount = headerEntity.TotalAmount,
                TotalPaymentAmount = headerEntity.TotalPaymentAmount,
                BuyerType = headerEntity.BuyerType,
                BuyerCode = headerEntity.BuyerCode,
                BuyerFullName = headerEntity.BuyerFullName,
                BuyerLegalName = headerEntity.BuyerLegalName,
                BuyerTaxCode = headerEntity.BuyerTaxCode,
                BuyerAddressLine = headerEntity.BuyerAddressLine,
                BuyerPhoneNumber = headerEntity.BuyerPhoneNumber,
                BuyerEmail = headerEntity.BuyerEmail,
                BuyerBankName = headerEntity.BuyerBankName,
                BuyerBankAccount = headerEntity.BuyerBankAccount,
                TotalDiscountAmountBeforeTax = 0,
                TotalVatAmount = headerEntity.TotalVatAmount,
                TotalDiscountAmountAfterTax = 0,
                TotalDiscountPercentAfterTax = 0,
                BudgetUnitCode = headerEntity.BudgetUnitCode,
                BuyerIDNumber = headerEntity.BuyerIDNumber,
                BuyerPassportNumber = headerEntity.BuyerPassportNumber,

                TemplateCode = headerEntity.TemplateCode,
                TaxBreakdowns = detailEntities.GroupBy(x => x.VatPercent).Select(x => new Invoice01IntegratedTaxBreakdownModel
                {
                    VatPercent = x.Key,
                    VatAmount = x.Sum(y => y.VatAmount),
                    VatAmountBackUp = x.Sum(y => y.VatAmount),
                }).ToList()
            };

            request.Details = MappingToInvoiceDetailModel(detailEntities, prodTypes);
            request.HeaderExtras = MappingToInvoiceHeaderExtraModel(headerEntity);

            return request;
        }

        public List<Invoice01IntegratedDetailModel> MappingToInvoiceDetailModel<TDetail>(List<TDetail> detailEntities, Dictionary<short, string> prodTypes)
            where TDetail : BaseInvoiceDetail
        {
            var details = new List<Invoice01IntegratedDetailModel>();

            var i = 1;
            foreach (var item in detailEntities)
            {
                var unitName = !string.IsNullOrWhiteSpace(item.UnitName)
                    ? item.UnitName
                    : InvoiceProperty.UnitName;
                var unitPrice = item.UnitPrice.HasValue
                    ? item.UnitPrice
                    : item.Amount;
                var quantity = item.Quantity.HasValue
                    ? item.Quantity
                    : InvoiceProperty.Quantity;

                var detail = new Invoice01IntegratedDetailModel
                {
                    Amount = item.Amount,
                    ProductName = prodTypes.ContainsKey(item.ProductType) ? prodTypes[item.ProductType] : InvoiceProperty.ProdNameDefault,
                    DiscountAmountBeforeTax = 0,
                    DiscountPercentBeforeTax = 0,
                    Index = i,
                    Note = item.Note,
                    PaymentAmount = item.PaymentAmount,
                    VatAmount = item.VatAmount,
                    VatPercent = item.VatPercent,
                    ProductCode = InvoiceProperty.ProductCode,
                    UnitName = unitName,
                    UnitPrice = unitPrice,
                    Quantity = quantity
                };

                detail.DetailExtras = new List<Invoice01IntegratedDetailExtraModel>();
                if (!item.GlNo.IsNullOrEmpty())
                {
                    detail.DetailExtras.Add(new Invoice01IntegratedDetailExtraModel
                    {
                        FieldName = DetailExtra.GlNo,
                        FieldValue = item.GlNo
                    });
                }

                if (!item.RefNo.IsNullOrEmpty())
                {
                    detail.DetailExtras.Add(new Invoice01IntegratedDetailExtraModel
                    {
                        FieldName = DetailExtra.RefNo,
                        FieldValue = item.RefNo
                    });
                }

                if (!item.Note2.IsNullOrEmpty())
                {
                    detail.DetailExtras.Add(new Invoice01IntegratedDetailExtraModel
                    {
                        FieldName = DetailExtra.Note2,
                        FieldValue = item.Note2
                    });
                }

                if (!item.ExtraProperties.IsNullOrEmpty())
                {
                    try
                    {
                        var fieldNames = detail.DetailExtras.Select(x => x.FieldName);
                        var extra = JsonConvert.DeserializeObject<List<Invoice01IntegratedDetailExtraModel>>(item.ExtraProperties)
                                                .Where(x => !x.FieldName.IsNullOrEmpty()
                                                            && !x.FieldValue.IsNullOrEmpty()
                                                            && !fieldNames.Contains(x.FieldName));

                        detail.DetailExtras.AddRange(extra);
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, ex.Message);
                    }
                }

                details.Add(detail);

                i++;
            }

            return details;
        }

        public List<Invoice01IntegratedHeaderExtraModel> MappingToInvoiceHeaderExtraModel<THeader>(THeader headerEntity)
            where THeader : BaseInvoiceHeader
        {
            var result = new List<Invoice01IntegratedHeaderExtraModel>
            {
                new Invoice01IntegratedHeaderExtraModel
                {
                    FieldName = HeaderExtra.TransactionAmount,
                    FieldValue = headerEntity.TransactionAmount.ToString()
                }
            };

            if (!headerEntity.AppName.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedHeaderExtraModel
                {
                    FieldName = HeaderExtra.AppName,
                    FieldValue = headerEntity.AppName
                });
            }

            if (!headerEntity.OperationName.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedHeaderExtraModel
                {
                    FieldName = HeaderExtra.OperationName,
                    FieldValue = headerEntity.OperationName
                });
            }

            if (!headerEntity.TellSeq.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedHeaderExtraModel
                {
                    FieldName = HeaderExtra.TellSeq,
                    FieldValue = headerEntity.TellSeq
                });
            }

            if (!headerEntity.MerNo.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedHeaderExtraModel
                {
                    FieldName = HeaderExtra.MerNo,
                    FieldValue = headerEntity.MerNo
                });
            }

            if (!headerEntity.BidNo.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedHeaderExtraModel
                {
                    FieldName = HeaderExtra.BidNo,
                    FieldValue = headerEntity.BidNo
                });
            }

            try
            {
                if (!headerEntity.ExtraProperties.IsNullOrEmpty())
                {
                    var fieldNames = result.Select(x => x.FieldName);
                    var extra = JsonConvert.DeserializeObject<List<Invoice01IntegratedHeaderExtraModel>>(headerEntity.ExtraProperties)
                                            .Where(x => !x.FieldName.IsNullOrEmpty()
                                                        && !x.FieldValue.IsNullOrEmpty()
                                                        && !fieldNames.Contains(x.FieldName));

                    result.AddRange(extra);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
            }

            return result;
        }

        public List<Invoice01IntegratedDetailExtraModel> MappingToMergedInvoiceDetailExtraModel<THeader>(THeader headerEntity)
            where THeader : BaseGxInvoiceModel
        {
            var result = new List<Invoice01IntegratedDetailExtraModel>
            {
                new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = HeaderExtra.TransactionAmount,
                    FieldValue = headerEntity.TransactionAmount.ToString()
                }
            };

            if (!headerEntity.AppName.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = HeaderExtra.AppName,
                    FieldValue = headerEntity.AppName
                });
            }

            if (!headerEntity.OperationName.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = HeaderExtra.OperationName,
                    FieldValue = headerEntity.OperationName
                });
            }

            if (!headerEntity.TellSeq.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = HeaderExtra.TellSeq,
                    FieldValue = headerEntity.TellSeq
                });
            }

            if (!headerEntity.MerNo.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = HeaderExtra.MerNo,
                    FieldValue = headerEntity.MerNo
                });
            }

            if (!headerEntity.BidNo.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = HeaderExtra.BidNo,
                    FieldValue = headerEntity.BidNo
                });
            }

            if (!headerEntity.ErpId.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = DetailExtra.ErpId,
                    FieldValue = headerEntity.ErpId
                });
            }

            if (!headerEntity.CreatorErp.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = DetailExtra.CreatorErp,
                    FieldValue = headerEntity.CreatorErp
                });
            }

            if (!headerEntity.Note.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = DetailExtra.Note,
                    FieldValue = headerEntity.Note
                });
            }

            result.Add(new Invoice01IntegratedDetailExtraModel
            {
                FieldName = DetailExtra.ExchangeRate,
                FieldValue = headerEntity.ExchangeRate.ToString()
            });

            if (!headerEntity.BuyerCode.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = DetailExtra.BuyerCode,
                    FieldValue = headerEntity.BuyerCode
                });
            }

            if (!headerEntity.BuyerFullName.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = DetailExtra.BuyerFullName,
                    FieldValue = headerEntity.BuyerFullName
                });
            }

            if (!headerEntity.BuyerLegalName.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = DetailExtra.BuyerLegalName,
                    FieldValue = headerEntity.BuyerLegalName
                });
            }

            if (!headerEntity.BuyerTaxCode.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = DetailExtra.BuyerTaxCode,
                    FieldValue = headerEntity.BuyerTaxCode
                });
            }

            if (!headerEntity.BuyerAddressLine.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = DetailExtra.BuyerAddressLine,
                    FieldValue = headerEntity.BuyerAddressLine
                });
            }

            if (!headerEntity.BuyerPhoneNumber.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = DetailExtra.BuyerPhoneNumber,
                    FieldValue = headerEntity.BuyerPhoneNumber
                });
            }

            if (!headerEntity.BuyerEmail.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = DetailExtra.BuyerEmail,
                    FieldValue = headerEntity.BuyerEmail
                });
            }

            if (!headerEntity.BuyerBankName.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = DetailExtra.BuyerBankName,
                    FieldValue = headerEntity.BuyerBankName
                });
            }

            if (!headerEntity.BuyerBankAccount.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = DetailExtra.BuyerBankAccount,
                    FieldValue = headerEntity.BuyerBankAccount
                });
            }

            try
            {
                if (!headerEntity.ExtraProperties.IsNullOrEmpty())
                {
                    var fieldNames = result.Select(x => x.FieldName);
                    var extra = JsonConvert.DeserializeObject<List<Invoice01IntegratedDetailExtraModel>>(headerEntity.ExtraProperties)
                                            .Where(x => !x.FieldName.IsNullOrEmpty() && !x.FieldValue.IsNullOrEmpty() && !fieldNames.Contains(x.FieldName));

                    result.AddRange(extra);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
            }

            return result;
        }

        public List<Invoice01IntegratedDetailExtraModel> MappingToMergedInvoiceDetailExtraModel<THeader>(THeader headerEntity, int integratedGroup)
            where THeader : BaseGxInvoiceModel
        {
            var result = new List<Invoice01IntegratedDetailExtraModel>
            {
                new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = HeaderExtra.TransactionAmount,
                    FieldValue = headerEntity.TransactionAmount.ToString()
                }
            };

            if (!headerEntity.AppName.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = HeaderExtra.AppName,
                    FieldValue = headerEntity.AppName
                });
            }

            if (!headerEntity.OperationName.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = HeaderExtra.OperationName,
                    FieldValue = headerEntity.OperationName
                });
            }

            if (!headerEntity.TellSeq.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = HeaderExtra.TellSeq,
                    FieldValue = headerEntity.TellSeq
                });
            }

            if (!headerEntity.MerNo.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = HeaderExtra.MerNo,
                    FieldValue = headerEntity.MerNo
                });
            }

            if (!headerEntity.BidNo.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = HeaderExtra.BidNo,
                    FieldValue = headerEntity.BidNo
                });
            }

            if (!headerEntity.ErpId.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = DetailExtra.ErpId,
                    FieldValue = headerEntity.ErpId
                });
            }

            if (!headerEntity.CreatorErp.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = DetailExtra.CreatorErp,
                    FieldValue = headerEntity.CreatorErp
                });
            }

            if (!headerEntity.Note.IsNullOrEmpty())
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = DetailExtra.Note,
                    FieldValue = headerEntity.Note
                });
            }

            if (integratedGroup == IntegratedGroup.G5)
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = DetailExtra.ExchangeRate,
                    FieldValue = headerEntity.ExchangeRate.ToString()
                });
            }

            if (!headerEntity.BuyerCode.IsNullOrEmpty() && integratedGroup == IntegratedGroup.G5)
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = DetailExtra.BuyerCode,
                    FieldValue = headerEntity.BuyerCode
                });
            }

            if (!headerEntity.BuyerFullName.IsNullOrEmpty() && integratedGroup == IntegratedGroup.G5)
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = DetailExtra.BuyerFullName,
                    FieldValue = headerEntity.BuyerFullName
                });
            }

            if (!headerEntity.BuyerLegalName.IsNullOrEmpty() && integratedGroup == IntegratedGroup.G5)
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = DetailExtra.BuyerLegalName,
                    FieldValue = headerEntity.BuyerLegalName
                });
            }

            if (!headerEntity.BuyerTaxCode.IsNullOrEmpty() && integratedGroup == IntegratedGroup.G5)
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = DetailExtra.BuyerTaxCode,
                    FieldValue = headerEntity.BuyerTaxCode
                });
            }

            if (!headerEntity.BuyerAddressLine.IsNullOrEmpty() && integratedGroup == IntegratedGroup.G5)
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = DetailExtra.BuyerAddressLine,
                    FieldValue = headerEntity.BuyerAddressLine
                });
            }

            if (!headerEntity.BuyerPhoneNumber.IsNullOrEmpty() && integratedGroup == IntegratedGroup.G5)
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = DetailExtra.BuyerPhoneNumber,
                    FieldValue = headerEntity.BuyerPhoneNumber
                });
            }

            if (!headerEntity.BuyerEmail.IsNullOrEmpty() && integratedGroup == IntegratedGroup.G5)
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = DetailExtra.BuyerEmail,
                    FieldValue = headerEntity.BuyerEmail
                });
            }

            if (!headerEntity.BuyerBankName.IsNullOrEmpty() && integratedGroup == IntegratedGroup.G5)
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = DetailExtra.BuyerBankName,
                    FieldValue = headerEntity.BuyerBankName
                });
            }

            if (!headerEntity.BuyerBankAccount.IsNullOrEmpty() && integratedGroup == IntegratedGroup.G5)
            {
                result.Add(new Invoice01IntegratedDetailExtraModel
                {
                    FieldName = DetailExtra.BuyerBankAccount,
                    FieldValue = headerEntity.BuyerBankAccount
                });
            }

            try
            {
                if (!headerEntity.ExtraProperties.IsNullOrEmpty())
                {
                    var fieldNames = result.Select(x => x.FieldName);
                    var extra = JsonConvert.DeserializeObject<List<Invoice01IntegratedDetailExtraModel>>(headerEntity.ExtraProperties)
                                            .Where(x => !x.FieldName.IsNullOrEmpty() && !x.FieldValue.IsNullOrEmpty() && !fieldNames.Contains(x.FieldName));

                    result.AddRange(extra);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
            }

            return result;
        }

        public List<Invoice01IntegratedDetailModel> MappingToMergedInvoiceDetailModel<TDetail>(List<TDetail> entities, List<Invoice01IntegratedDetailExtraModel> extraDetails, Dictionary<short, string> prodTypes)
            where TDetail : BaseGxInvoiceDetailModel
        {
            var result = new List<Invoice01IntegratedDetailModel>();

            foreach (var itemDetail in entities)
            {
                var detail = new Invoice01IntegratedDetailModel
                {
                    Amount = itemDetail.Amount,
                    ProductName = prodTypes.ContainsKey(itemDetail.ProductType) ? prodTypes[itemDetail.ProductType] : InvoiceProperty.ProdNameDefault,
                    DiscountAmountBeforeTax = 0,
                    DiscountPercentBeforeTax = 0,
                    Index = 0,
                    Note = itemDetail.Note,
                    PaymentAmount = itemDetail.PaymentAmount,
                    VatAmount = itemDetail.VatAmount,
                    VatPercent = itemDetail.VatPercent,
                    ProductCode = InvoiceProperty.ProductCode,
                    UnitName = InvoiceProperty.UnitName,
                    UnitPrice = itemDetail.Amount,
                    Quantity = InvoiceProperty.Quantity
                };

                detail.DetailExtras = new List<Invoice01IntegratedDetailExtraModel>();
                if (!itemDetail.GlNo.IsNullOrEmpty())
                {
                    detail.DetailExtras.Add(new Invoice01IntegratedDetailExtraModel
                    {
                        FieldName = DetailExtra.GlNo,
                        FieldValue = itemDetail.GlNo
                    });
                }

                if (!itemDetail.RefNo.IsNullOrEmpty())
                {
                    detail.DetailExtras.Add(new Invoice01IntegratedDetailExtraModel
                    {
                        FieldName = DetailExtra.RefNo,
                        FieldValue = itemDetail.RefNo
                    });
                }

                if (!itemDetail.Note2.IsNullOrEmpty())
                {
                    detail.DetailExtras.Add(new Invoice01IntegratedDetailExtraModel
                    {
                        FieldName = DetailExtra.Note2,
                        FieldValue = itemDetail.Note2
                    });
                }

                if (!itemDetail.ExtraProperties.IsNullOrEmpty())
                {
                    try
                    {
                        var fieldNames = detail.DetailExtras.Select(x => x.FieldName);
                        var extra = JsonConvert.DeserializeObject<List<Invoice01IntegratedDetailExtraModel>>(itemDetail.ExtraProperties)
                                                .Where(x => !x.FieldName.IsNullOrEmpty() && !x.FieldValue.IsNullOrEmpty() && !fieldNames.Contains(x.FieldName));

                        detail.DetailExtras.AddRange(extra);
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, ex.Message);
                    }
                }

                if (extraDetails != null && extraDetails.Any())
                {
                    detail.DetailExtras.AddRange(extraDetails);
                }

                result.Add(detail);
            }

            return result;
        }

        public async Task UpdateDataToDbtgAsync(bool isSuccess, int group, GIntegratedInvoiceModel model)
        {
            var tableName = GetGroupDbtgName(group);

            if (isSuccess)
            {
                if (model != null)
                {
                    var query = $@" UPDATE ""{tableName}""
                                    SET ""CreatorStatus"" = {model.CreatorStatus},
                                        ""Message"" = N'{model.Message}',
                                        ""InvoiceDate"" = '{model.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                        ""InvoiceNo"" = N'{model.InvoiceNo}',
                                        ""Number"" = {model.Number},
                                        ""SignStatus"" = {model.SignStatus}
                                    WHERE ""Id"" = {model.Id} ";

                    await _appFactory.VcbIntegratedOracle.Connection.ExecuteScalarAsync(query);
                }
            }
            else
            {
                if (model != null)
                {
                    var query = $@" UPDATE ""{tableName}""
                                SET ""CreatorStatus"" = {model.CreatorStatus},
                                    ""Message"" = N'{model.Message}'
                                WHERE ""Id"" = {model.Id} ";

                    await _appFactory.VcbIntegratedOracle.Connection.ExecuteScalarAsync(query);
                }
            }
        }

        public async Task UpdateDatasToDbtgAsync(bool isSuccess, int group, List<GIntegratedInvoiceModel> models)
        {
            var tableName = GetGroupDbtgName(group);

            if (isSuccess)
            {
                if (models != null && models.Any())
                {
                    var query = new StringBuilder(" BEGIN ");
                    foreach (var model in models)
                    {
                        query.Append($@" UPDATE ""{tableName}""
                                    SET ""CreatorStatus"" = {model.CreatorStatus},
                                        ""Message"" = N'{model.Message}',
                                        ""InvoiceDate"" = '{model.InvoiceDate.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
                                        ""InvoiceNo"" = N'{model.InvoiceNo}',
                                        ""Number"" = {model.Number},
                                        ""SignStatus"" = {model.SignStatus}
                                    WHERE ""Id"" = {model.Id} ;");
                    }
                    query.Append(" END; ");

                    await _appFactory.VcbIntegratedOracle.Connection.ExecuteScalarAsync(query.ToString());
                }
            }
            else
            {
                if (models != null && models.Any())
                {
                    var query = new StringBuilder(" BEGIN ");
                    foreach (var model in models)
                    {
                        query.Append($@"    UPDATE ""{tableName}""
                                            SET ""CreatorStatus"" = {model.CreatorStatus},
                                                ""Message"" = N'{model.Message}'
                                            WHERE ""Id"" = {model.Id} ;");
                    }
                    query.Append(" END; ");

                    await _appFactory.VcbIntegratedOracle.Connection.ExecuteScalarAsync(query.ToString());
                }
            }
        }

        public string GetGroupDbtgName(int group)
        {
            switch (group)
            {
                case IntegratedGroup.G1: return IntegratedGroup.G1TableName;

                case IntegratedGroup.G2: return IntegratedGroup.G2TableName;

                case IntegratedGroup.G3: return IntegratedGroup.G3TableName;

                case IntegratedGroup.G4: return IntegratedGroup.G4TableName;

                case IntegratedGroup.G5: return IntegratedGroup.G5TableName;

                default: return string.Empty;
            }
        }

        public List<THeader> RemarkBuyerCode<THeader>(List<THeader> headers)
            where THeader : BaseInvoiceHeader
        {

            var results = headers.Select(p =>
            {
                p.BuyerCode = new Func<string>(() =>
                {
                    var buyerCode = p.BuyerCode;
                    var cif = buyerCode.DeleteZeroAtFirstChar();
                    if (string.IsNullOrWhiteSpace(cif))
                    {
                        throw new Exception($"Số CIF: {buyerCode} không hợp lệ");
                    }
                    return cif;
                })();
                return p;
            }).ToList();

            return results;
        }

        public CreateAdjustmentDetailInvoice01IntegratedModel MappingToInvoiceAdjustmentDetailModel(G4InvoiceHeaderEntity item, DateTime invoiceDate, List<G4InvoiceDetailEntity> lstDetail, RegisterAvaibilitiesByTenantsModel serialNo, int g4, InvoiceSource shareAdapterG4, Dictionary<short, string> prodTypes)
        {
            //TODO: mapping hóa đơn điều chỉnh
            throw new NotImplementedException();
        }

        #endregion


        #region PRIVATE FUNCTION

        /// <summary>
        /// Script Lấy mẫu hóa đơn đang sử dụng theo ds TenantIds
        /// </summary>
        /// <param name="tenantIds"></param>
        /// <param name="templateNo"></param>
        /// <returns></returns>
        private string GenerateDrawRegisterAvailabilitiesQuery(IEnumerable<Guid> tenantIds, short templateNo, InvoiceTemplateSource? templateSource)
        {
            var rawTenantIds = String.Join(",", tenantIds.Distinct().Select(x => $"'{OracleExtension.ConvertGuidToRaw(x)}'"));
            var dateNow = DateTime.Now.Date;
            var formatDate = dateNow.AddDays(1).ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")); //thêm 1 ngày vì thời gian lưu của tct có giờ

            var currentMonth = dateNow.Month;
            var currentYear = dateNow.ToString("yy", CultureInfo.GetCultureInfo("en-US"));
            var conditionSerialNo = new StringBuilder();
            if (currentMonth == 1)
            {
                var firstYear = dateNow.AddYears(-1).ToString("yy", CultureInfo.GetCultureInfo("en-US"));
                conditionSerialNo.Append($@" (T.""SerialNo"" LIKE 'K{firstYear}%' OR T.""SerialNo"" LIKE 'K{currentYear}%') ");
            }
            else
            {
                conditionSerialNo.Append($@" T.""SerialNo"" LIKE 'K{currentYear}%' ");
            }

            var sql = new StringBuilder();

            sql.Append($@"      
                        
                        SELECT T.""Id"", 
                                T.""Name"", 
                                T.""TemplateNo"",
                                T.""SerialNo"", 
                                T.""TenantId"",
                                T.""TemplateNo"" || SUBSTR(T.""SerialNo"", 1, 5) || 'x' ""GroupTemplateNoSerialNo"",
                                SUBSTR(T.""SerialNo"", 2, 2) ""Year""
                        FROM ""InvoiceTemplate"" T
                        INNER JOIN(
                            SELECT ""Id"", 
                                    ""CurrentNumber"", 
                                    ""EndNumber"", 
                                    ""RegistrationHeaderId""
                            FROM ""MonitorInvoiceTemplate""
                            WHERE ""CurrentNumber"" < ""EndNumber"" AND ""TenantId"" IN ( {rawTenantIds} )
                        ) M ON T.""Id"" = M.""Id""
                        INNER JOIN(
                            SELECT ""Id"", 
                                    ""TenantId"", 
                                    ""InvoiceTypes"", 
                                    ""InvoiceHasCode""
                            FROM ""NewRegistrationHeader""
                            WHERE ""Status"" = {RegistrationTvanStatus.GDTAccepted.GetHashCode()} AND
                                ""InvoiceTypes"" IS NOT NULL AND 
                                ""InvoiceTypes"" LIKE '%1%' AND
                                ""GDTResponseTime"" IS NOT NULL AND
                                ""GDTResponseTime"" < '{formatDate}' AND
                                ""TenantId"" IN ( {rawTenantIds} ) AND
                                ""IsDeleted"" = 0
                            GROUP BY ""TenantId"", ""Id"", ""InvoiceTypes"", ""InvoiceHasCode""
                        ) R ON T.""TenantId"" = R.""TenantId"" AND M.""RegistrationHeaderId"" = R.""Id""
                        WHERE
                            {(templateSource == null ? "" : $@" T.""SourceType"" = {templateSource.Value.GetHashCode()} AND ")}
                            T.""IsDeleted"" = 0 AND
                            T.""FileId"" IS NOT NULL AND
                            T.""TemplateNo"" = {templateNo} AND
                            R.""InvoiceHasCode"" = 0 AND {conditionSerialNo}
            ");

            return sql.ToString();
        }

        /// <summary>
        /// Script Lấy mẫu hóa đơn đang sử dụng của 1 Tenant
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="templateNo"></param>
        /// <returns></returns>
        private string GenerateDrawRegisterAvailabilitiesQuery(Guid tenantId, short templateNo)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var formatDate = DateTime.Now.Date.AddDays(1).ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US")); //thêm 1 ngày vì thời gian lưu của tct có giờ

            var sql = new StringBuilder();

            sql.Append($@"      
                        SELECT  T.""Id"", T.""Name"", T.""TemplateNo"", T.""SerialNo""
                        FROM ""InvoiceTemplate"" T
                        INNER JOIN (
                            SELECT ""Id"", ""CurrentNumber"", ""EndNumber""
                            FROM ""MonitorInvoiceTemplate""
                            WHERE ""CurrentNumber"" < ""EndNumber"" AND ""TenantId"" = '{rawTenantId}'
                        )
                        M ON M.""Id"" = T.""Id""
                        INNER JOIN (
                            SELECT ""InvoiceTypes"", ""InvoiceHasCode"" 
                            FROM 
                                (
                                    SELECT ""InvoiceTypes"", ""InvoiceHasCode""
                                    FROM ""NewRegistrationHeader""
                                    WHERE
                                        ""NewRegistrationHeader"".""Status"" = {RegistrationTvanStatus.GDTAccepted.GetHashCode()} 
                                        AND ""InvoiceTypes"" IS NOT NULL 
                                        AND LENGTH(TRIM(""InvoiceTypes"")) > 1
                                        AND ""NewRegistrationHeader"".""GDTResponseTime"" IS NOT NULL
                                        AND ""NewRegistrationHeader"".""GDTResponseTime"" < '{formatDate}' 
                                        AND ""NewRegistrationHeader"".""TenantId"" = '{rawTenantId}'
                                        AND ""IsDeleted"" = 0
                                    ORDER BY ""Id"" DESC
                                    FETCH FIRST 1 ROWS ONLY
                                )
                            WHERE ""InvoiceTypes"" LIKE '%1%' 
                        ) 
                        R ON 1 = 1
                        WHERE T.""TenantId"" = '{rawTenantId}' AND T.""IsDeleted"" = 0 
                        AND T.""FileId"" IS NOT NULL 
                        AND T.""TemplateNo"" = {templateNo}
                        AND (
                                (R.""InvoiceHasCode"" = 0 AND T.""SerialNo"" Like 'K%')
                                OR (R.""InvoiceHasCode"" = 1 AND T.""SerialNo"" Like 'C%')
                            )
            ");

            return sql.ToString();
        }

        public async Task<bool> AnyGxValidInvoiceBackDateAsync(string tblName)
        {
            return
                 await _appFactory.VcbIntegratedOracle.Connection.ExecuteScalarAsync<bool>(
                     $@" SELECT 1 
                        FROM DUAL 
                        WHERE EXISTS (
                            SELECT 1 
                            FROM ""{tblName}"" 
                            WHERE ""CreatorStatus"" = :CreatorStatus 
                                AND ""InvoiceStatus"" = :InvoiceStatus) "
                     , new
                     {
                         CreatorStatus = CreatorStatus.WaittingToCreateBackDate,
                         InvoiceStatus = InvoiceStatusDbtg.Create,
                     });
        }

        #endregion
    }
}
