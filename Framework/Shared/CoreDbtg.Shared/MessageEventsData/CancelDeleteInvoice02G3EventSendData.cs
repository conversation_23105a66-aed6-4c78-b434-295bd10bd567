using Core.EventBus;
using System;
using System.Collections.Generic;

namespace CoreDbtg.Shared.MessageEventsData
{
    [EventName("invoice-DBTG-02.G3.canceldeleteinvoice02")]
    public class CancelDeleteInvoice02G3EventSendData
    {
        public List<string> ErpIds { get; set; }

        //// additinal field
        //public string ErrorMessages { get; set; }

        //public InvoiceSource Resource { get; set; }

        ////Context
        public Guid TenantId { get; set; }
        //public Guid UserId { get; set; }
        //public string UserName { get; set; }
        //public string UserFullName { get; set; }
    }
}
