using Core.EventBus;
using CoreDbtg.Shared.Models;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CoreDbtg.Shared.MessageEventsData
{
    [EventName("InvoiceDBTG01.SyncBatchInvoiceToG4DBTG")]
    public class SyncBatchInvoiceToG4DBTGEvent
    {
        public SyncBatchInvoiceToG4DBTGEvent()
        {
        }

        public SyncBatchInvoiceToG4DBTGEvent(bool isSuccess, int integratedGroup, List<GIntegratedInvoiceModel> invoices)
        {
            IsSuccess = isSuccess;
            IntegratedGroup = integratedGroup;

            if (invoices != null && invoices.Any())
                Invoices = invoices;
        }

        public int IntegratedGroup { get; set; }

        public bool IsSuccess { get; set; }

        public List<GIntegratedInvoiceModel> Invoices { get; set; }

        //public Guid TenantId { get; set; }

        public Guid CreatorId { get; set; }

        public string FullNameCreator { get; set; }

        public string UserNameCreator { get; set; }
    }
}