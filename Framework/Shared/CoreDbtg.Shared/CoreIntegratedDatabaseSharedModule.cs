using Core.Application;
using Core.AutoMapper;
using Core.Modularity;
using CoreDbtg.Shared.Models;
using Microsoft.Extensions.DependencyInjection;

namespace CoreDbtg.Shared
{
    [DependsOn(
        typeof(AbpDddApplicationModule),
        typeof(AbpAutoMapperModule)
    )]
    public class CoreIntegratedDatabaseSharedModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            var configuration = context.Services.GetConfiguration();
            context.Services.Configure<SerialNoByTemplateTypeOption>(configuration.GetSection("SerialNoByTemplateType"));
        }
    }
}
