using Core.Application;
using Core.Caching;
using Core.Modularity;
using Core.Shared.HttpMessageHandler;
using Core.Tvan.Vnpay.Interfaces;
using Core.Tvan.Vnpay.Models;
using Core.Tvan.Vnpay.Services;

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using System;
using System.Net.Http;

namespace Core.Tvan.Vnpay
{
    [DependsOn(
         typeof(AbpDddApplicationModule),
         typeof(AbpCachingModule)
     )]
    public class TvanVnpayModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            var configuration = context.Services.GetConfiguration();
            context.Services.Configure<TvanVnpayOption>(configuration.GetSection("TvanVnpayInvoice"));

            ////tvan vnpay invoice
            //context.Services.AddHttpClient("tvan-vnpay-invoice", (serviceProvider, client) =>
            //{
            //    var tvanOptions = serviceProvider.GetService<IOptions<TvanVnpayOption>>().Value;
            //    client.BaseAddress = new Uri(tvanOptions.EndPoint);
            //    client.DefaultRequestHeaders.TryAddWithoutValidation("X-API-KEY", tvanOptions.ApiKey);
            //    client.Timeout = TimeSpan.FromMinutes(tvanOptions.Timeout);
            //});

            //tvan vnpay invoice
            context.Services.AddHttpClient("tvan-vnpay-invoice", (serviceProvider, client) =>
            {
                var tvanOptions = serviceProvider.GetService<IOptions<TvanVnpayOption>>().Value;
                client.BaseAddress = new Uri(tvanOptions.EndPoint);
                client.DefaultRequestHeaders.TryAddWithoutValidation("X-API-KEY", tvanOptions.ApiKey);
                client.Timeout = TimeSpan.FromMinutes(tvanOptions.Timeout);
            }).ConfigurePrimaryHttpMessageHandler(() =>
            {
                var handler = new HttpClientHandler();
                handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => { return true; };
                return handler;
            })
            .AddHttpMessageHandler<CorrelationIdHttpMessageHandler>();

            context.Services.AddScoped<ITvanVnpayService, TvanVnpayService>();
            context.Services.AddSingleton<ITvanVnpayInvoiceClient, TvanVnpayInvoiceClient>();
        }
    }
}
