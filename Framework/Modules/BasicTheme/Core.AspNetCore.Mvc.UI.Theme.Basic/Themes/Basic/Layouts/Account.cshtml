@using Core.AspNetCore.Mvc.UI.MultiTenancy.Localization
@using Core.AspNetCore.Mvc.UI.Theme.Basic
﻿@using Core.Localization.Resources.AbpLocalization
@using Microsoft.Extensions.Localization
@using Microsoft.Extensions.Options
@using Core.AspNetCore.MultiTenancy
@using Core.AspNetCore.Mvc.UI.Components.LayoutHook
@using Core.AspNetCore.Mvc.UI.Theme.Basic.Bundling
@using Core.AspNetCore.Mvc.UI.Theme.Basic.Themes.Basic.Components.MainNavbar
@using Core.AspNetCore.Mvc.UI.Theme.Basic.Themes.Basic.Components.PageAlerts
@using Core.AspNetCore.Mvc.UI.Theme.Basic.Themes.Basic.Components.Toolbar
@using Core.AspNetCore.Mvc.UI.Theming
@using Core.AspNetCore.Mvc.UI.Widgets.Components.WidgetScripts
@using Core.AspNetCore.Mvc.UI.Widgets.Components.WidgetStyles
@using Core.MultiTenancy
@using Core.Localization
@using Core.Ui.Branding
@using IdentityServer4.Models
@using IdentityServer4.Services
@inject IBrandingProvider BrandingProvider
@inject IOptions<AbpMultiTenancyOptions> MultiTenancyOptions
@inject ICurrentTenant CurrentTenant
@inject ITenantResolveResultAccessor TenantResolveResultAccessor
@inject IStringLocalizer<AbpUiMultiTenancyResource> MultiTenancyStringLocalizer

@{
    Layout = null;
    var containerClass = ViewBag.FluidLayout == true ? "container-fluid" : "container"; //TODO: Better and type-safe options
    var rtl = CultureHelper.IsRtl ? "rtl" : string.Empty;

    var host = Context.Request;
    var isVnis = true;
    var title = "VN-Invoice - Giải pháp hóa đơn điện tử VN";
    if (host.QueryString.HasValue)
    {
        if (host.QueryString.Value != null && host.QueryString.Value.Contains(".vnpayinvoice.vn"))
        {
            isVnis = false;
            title = "VNPAY-Invoice - Giải pháp hóa đơn điện tử VN";
        }
    }
}

<!DOCTYPE html>

<html lang="@CultureInfo.CurrentCulture.Name" dir="@rtl">
<head>
    @await Component.InvokeLayoutHookAsync(LayoutHooks.Head.First, StandardLayouts.Account)

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    @if (isVnis)
    {
        <title>@title</title>
        <link rel="icon" type="image/x-icon" href="~/images/favicon.ico" />
    }
    else
    {
        <title>@title</title>
        <link rel="icon" type="image/x-icon" href="~/images/favicon-vnpay.ico" />
    }

    @if (ViewBag.Description != null)
    {
        <meta name="description" content="@(ViewBag.Description as string)" />
    }
    <abp-style-bundle name="@BasicThemeBundles.Styles.Global" />

    @await RenderSectionAsync("styles", false)

    @await Component.InvokeAsync(typeof(WidgetStylesViewComponent))

    @await Component.InvokeLayoutHookAsync(LayoutHooks.Head.Last, StandardLayouts.Account)
</head>
<body class="abp-account-layout bg-light @rtl">
    @await Component.InvokeLayoutHookAsync(LayoutHooks.Body.First, StandardLayouts.Account)

    @(await Component.InvokeAsync<MainNavbarViewComponent>())

    <div class="@containerClass" style="height: 100vh;">
        <abp-row style="height: 100vh;">
            <abp-column class="col mx-auto" style="max-width: 440px; margin: auto">
                @if (MultiTenancyOptions.Value.IsEnabled &&
                (TenantResolveResultAccessor.Result?.AppliedResolvers?.Contains(CookieTenantResolveContributor.ContributorName) == true ||
                TenantResolveResultAccessor.Result?.AppliedResolvers?.Contains(QueryStringTenantResolveContributor.ContributorName) == true))
                {
                //var authContext = ViewData["authorizationRequest"] as AuthorizationRequest;
                    //var client = authContext?.Client;
                    //if (client?.ClientName != "einvoiceportal_app")
                    //{
                    <div class="card shadow-sm rounded mb-3">
                        <div class="card-body px-5">
                            <div class="row">
                                <div class="col">
                                    <span style="font-size: .8em;" class="text-uppercase text-muted">@MultiTenancyStringLocalizer["Language"]</span>
                                </div>
                                <div class="col-auto d-flex">
                                    @(await Component.InvokeAsync<MainNavbarToolbarViewComponent>())
                                </div>
                            </div>
                            <div class="row">
                                <div class="col">
                                    <span style="font-size: .8em;" class="text-uppercase text-muted">@MultiTenancyStringLocalizer["Tenant"]</span><br />
                                    <h6 class="m-0 d-inline-block">
                                        @if (CurrentTenant.Id == null)
                                        {
                                            <span>
                                                @MultiTenancyStringLocalizer["NotSelected"]
                                            </span>
                                        }
                                        else
                                        {
                                            <strong>@(CurrentTenant.Name ?? CurrentTenant.Id.Value.ToString())</strong>
                                        }
                                    </h6>
                                </div>
                                <div class="col-auto">
                                    <a id="AbpTenantSwitchLink" href="javascript:;" class="btn btn-sm mt-3 btn-outline-primary">@MultiTenancyStringLocalizer["Switch"]</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    //}

                }
                @(await Component.InvokeAsync<PageAlertsViewComponent>())
                @await Component.InvokeLayoutHookAsync(LayoutHooks.PageContent.First, StandardLayouts.Account)
                @RenderBody()
                @await Component.InvokeLayoutHookAsync(LayoutHooks.PageContent.Last, StandardLayouts.Account)
            </abp-column>
        </abp-row>
    </div>

    <abp-script-bundle name="@BasicThemeBundles.Scripts.Global" />

    <script src="~/Abp/ApplicationConfigurationScript"></script>
    <script src="~/Abp/ServiceProxyScript"></script>

    @await RenderSectionAsync("scripts", false)

    @await Component.InvokeAsync(typeof(WidgetScriptsViewComponent))

    @await Component.InvokeLayoutHookAsync(LayoutHooks.Body.Last, StandardLayouts.Account)
</body>
</html>
 