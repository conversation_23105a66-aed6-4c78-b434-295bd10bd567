@using Microsoft.Extensions.Localization
@using Core.Users
@using Core.MultiTenancy
@using global::Localization.Resources.AbpUi
@inherits AbpComponentBase
@inject ICurrentUser CurrentUser
@inject ICurrentTenant CurrentTenant
@inject IJSRuntime JsRuntime
@inject NavigationManager Navigation
@inject IStringLocalizer<AbpUiResource> UiLocalizer
<AuthorizeView>
    <Authorized>
        <Dropdown>
            <DropdownToggle Color="Color.None">
                @if (CurrentTenant.Name != null)
                {
                    <span><i>@CurrentTenant.Name</i>\@CurrentUser.UserName</span>
                }
                else
                {
                    <span>@CurrentUser.UserName</span>
                }
            </DropdownToggle>
            <DropdownMenu>
                @if (Menu != null)
                {
                    @foreach (var menuItem in Menu.Items)
                    {
                        <DropdownItem Clicked="@(() => NavigateToAsync(menuItem.Url, menuItem.Target))">@menuItem.DisplayName</DropdownItem>
                    }
                }
                <DropdownDivider />
                <DropdownItem Clicked="BeginSignOut">@UiLocalizer["Logout"]</DropdownItem>
            </DropdownMenu>
        </Dropdown>
    </Authorized>
    <NotAuthorized>
        <a class="nav-link" href="authentication/login">@UiLocalizer["Login"]</a>
    </NotAuthorized>
</AuthorizeView>