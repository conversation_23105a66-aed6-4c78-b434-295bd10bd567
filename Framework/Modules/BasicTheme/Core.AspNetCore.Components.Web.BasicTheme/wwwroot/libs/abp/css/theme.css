
#main-navbar-tools a.dropdown-toggle {
    text-decoration: none;
    color: #fff;
}

.navbar .dropdown-submenu {
    position: relative;
}
.navbar .dropdown-menu {
    margin: 0;
    padding: 0;
}
    .navbar .dropdown-menu a {
        font-size: .9em;
        padding: 10px 15px;
        display: block;
        min-width: 210px;
        text-align: left;
        border-radius: 0.25rem;
        min-height: 44px;
    }
.navbar .dropdown-submenu a::after {
    transform: rotate(-90deg);
    position: absolute;
    right: 16px;
    top: 18px;
}
.navbar .dropdown-submenu .dropdown-menu {
    top: 0;
    left: 100%;
}

.card-header .btn {
    padding: 2px 6px;
}
.card-header h5 {
    margin: 0;
}
.container > .card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

@media screen and (min-width: 768px) {
    .navbar .dropdown:hover > .dropdown-menu {
        display: block;
    }

    .navbar .dropdown-submenu:hover > .dropdown-menu {
        display: block;
    }
}
.input-validation-error {
    border-color: #dc3545;
}
.field-validation-error {
    font-size: 0.8em;
}

.dataTables_scrollBody {
    min-height: 248px;
}

div.dataTables_wrapper div.dataTables_info {
    padding-top: 11px;
    white-space: nowrap;
}

div.dataTables_wrapper div.dataTables_length label {
    padding-top: 10px;
    margin-bottom: 0;
}

.rtl .dropdown-menu-right {
    right: auto;
    left: 0;
}

    .rtl .dropdown-menu-right a {
        text-align: right;
    }

.rtl .navbar .dropdown-menu a {
    text-align: right;
}
.rtl .navbar .dropdown-submenu .dropdown-menu {
    top: 0;
    left: auto;
    right: 100%;
}

/* TEMP */

.navbar-dark .navbar-nav .nav-link {
    color: #000 !important;
}

.navbar-nav > .nav-item > .nav-link,
.navbar-nav > .nav-item > .dropdown > .nav-link {
    color: #fff !important;
}

.navbar-nav>.nav-item>div>button{
    color:#fff;
}

.btn span.spinner-border {
    margin-right: .5rem;
}

.radar-spinner, .radar-spinner * {
    box-sizing: border-box;
}

.radar-spinner {
    height: 60px;
    width: 60px;
    position: relative;
}

    .radar-spinner .circle {
        position: absolute;
        height: 100%;
        width: 100%;
        top: 0;
        left: 0;
        animation: radar-spinner-animation 2s infinite;
    }

        .radar-spinner .circle:nth-child(1) {
            padding: calc(60px * 5 * 2 * 0 / 110);
            animation-delay: 300ms;
        }

        .radar-spinner .circle:nth-child(2) {
            padding: calc(60px * 5 * 2 * 1 / 110);
            animation-delay: 300ms;
        }

        .radar-spinner .circle:nth-child(3) {
            padding: calc(60px * 5 * 2 * 2 / 110);
            animation-delay: 300ms;
        }

        .radar-spinner .circle:nth-child(4) {
            padding: calc(60px * 5 * 2 * 3 / 110);
            animation-delay: 0ms;
        }

    .radar-spinner .circle-inner, .radar-spinner .circle-inner-container {
        height: 100%;
        width: 100%;
        border-radius: 50%;
        border: calc(60px * 5 / 110) solid transparent;
    }

    .radar-spinner .circle-inner {
        border-left-color: var(--secondary, #ff1d5e);
        border-right-color: var(--secondary, #ff1d5e);
    }

@keyframes radar-spinner-animation {
    50% {
        transform: rotate(180deg);
    }

    100% {
        transform: rotate(0deg);
    }
}


