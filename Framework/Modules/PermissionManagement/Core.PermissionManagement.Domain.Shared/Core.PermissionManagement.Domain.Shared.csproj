<Project Sdk="Microsoft.NET.Sdk">

  
  

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <AssemblyName>Core.PermissionManagement.Domain.Shared</AssemblyName>
    <PackageId>Core.PermissionManagement.Domain.Shared</PackageId>
    <AssetTargetFallback>portable-net45+win8+wp8+wpa81;</AssetTargetFallback>
    <GenerateAssemblyConfigurationAttribute>false</GenerateAssemblyConfigurationAttribute>
    <GenerateAssemblyCompanyAttribute>false</GenerateAssemblyCompanyAttribute>
    <GenerateAssemblyProductAttribute>false</GenerateAssemblyProductAttribute>
    <GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>
    <RootNamespace />
  </PropertyGroup>

  <ItemGroup>
    <content Remove="Volo\Abp\PermissionManagement\Localization\Domain\*.json" />
    <EmbeddedResource Include="Volo\Abp\PermissionManagement\Localization\Domain\*.json" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\Core\Core.Validation\Core.Validation.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.FileProviders.Embedded" Version="5.0.*" />
  </ItemGroup>

</Project>
