<Project Sdk="Microsoft.NET.Sdk.Web">

  
  

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <AssemblyName>Core.Account.Web.IdentityServer</AssemblyName>
    <PackageId>Core.Account.Web.IdentityServer</PackageId>
    <IsPackable>true</IsPackable>
    <AssetTargetFallback>portable-net45+win8+wp8+wpa81;</AssetTargetFallback>
    <GenerateAssemblyConfigurationAttribute>false</GenerateAssemblyConfigurationAttribute>
    <GenerateAssemblyCompanyAttribute>false</GenerateAssemblyCompanyAttribute>
    <GenerateAssemblyProductAttribute>false</GenerateAssemblyProductAttribute>
    <GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>
    <RootNamespace>Core.Account.Web</RootNamespace>
    <OutputType>Library</OutputType>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <EmbeddedResource Include="Pages\**\*.css" />
    <EmbeddedResource Include="Pages\**\*.js" />
    <EmbeddedResource Include="Components\**\*.js" />
    <EmbeddedResource Include="Components\**\*.css" />
    <Content Remove="Pages\**\*.css" />
    <Content Remove="Pages\**\*.js" />
    <Content Remove="Components\**\*.js" />
    <Content Remove="Components\**\*.css" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\Core\Core.Ldap\Core.Ldap.csproj" />
    <ProjectReference Include="..\..\..\Shared\Core.Shared\Core.Shared.csproj" />
    <ProjectReference Include="..\..\IdentityServer\Core.IdentityServer.Domain\Core.IdentityServer.Domain.csproj" />
    <ProjectReference Include="..\..\TenantManagement\Core.TenantManagement.Domain\Core.TenantManagement.Domain.csproj" />
    <ProjectReference Include="..\Core.Account.Application\Core.Account.Application.csproj" />
    <ProjectReference Include="..\Core.Account.Web\Core.Account.Web.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="System.Security.Principal.Windows" Version="5.0.*" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.FileProviders.Embedded" Version="5.0.*" />
  </ItemGroup>

</Project>
