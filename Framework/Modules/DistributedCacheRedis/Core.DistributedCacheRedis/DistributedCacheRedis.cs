using Core.Caching.StackExchangeRedis;
using Microsoft.Extensions.Caching.StackExchangeRedis;
using Microsoft.Extensions.Options;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Core.DistributedCacheRedis
{
    public class DistributedCacheRedis : AbpRedisCache, IDistributedCacheRedis
    {
        private readonly IConnectionMultiplexer _reids;
        private readonly TimeSpan _expiry;
        public DistributedCacheRedis(
            IOptions<RedisCacheOptions> optionsAccessor,
            IConnectionMultiplexer reids
        ) : base(optionsAccessor)
        {
            _reids = reids;
            _expiry = TimeSpan.FromMinutes(30);
        }

        public async Task<List<string>> GetListAsync(string key)
        {
            var db =  _reids.GetDatabase();
            var results = await db.ListRangeAsync(key);
            return results.Select(p => p.ToString()).ToList();
        }

        public async Task AddItemToListAsync(string key, string value, TimeSpan? expiry = null)
        {
            var db = _reids.GetDatabase();
            await db.ListRightPushAsync(key, value);
            await AddKeyExpireAsync(db, key, expiry);
        }

        public async Task RemoveItemFromListAsync(string key)
        {
            var db = _reids.GetDatabase();
            await db.ListLeftPopAsync(key);
        }

        public async Task RemoveItemByValueFromListAsync(string key, string value)
        {
            var db = _reids.GetDatabase();
            await db.ListRemoveAsync(key, value);
        }

        public async Task<long> IncrementAsync(string key, TimeSpan? expiry = null)
        {
            var db = _reids.GetDatabase();
            var result = await db.StringIncrementAsync(key);
            await AddKeyExpireAsync(db, key, expiry);
            return result;
        }

        public async Task<long> IncrementByValueAsync(string key, long value, TimeSpan? expiry = null)
        {
            var db = _reids.GetDatabase();
            var result = await db.StringIncrementAsync(key, value);
            await AddKeyExpireAsync(db, key, expiry);
            return result;
        }

        public async Task<long> DecrementAsync(string key, TimeSpan? expiry = null)
        {
            var db = _reids.GetDatabase();
            var result = await db.StringDecrementAsync(key);
            await AddKeyExpireAsync(db, key, expiry);
            return result;

        }
        public async Task<long> DecrementByValueAsync(string key, long value, TimeSpan? expiry = null)
        {
            var db = _reids.GetDatabase();
            var result = await db.StringDecrementAsync(key, value);
            await AddKeyExpireAsync(db, key, expiry);
            return result;
        }

        public async Task<long> GetCurrentValueAsync(string key)
        {
            var db = _reids.GetDatabase();
            var value = await db.StringGetAsync(key);
            
            if(string.IsNullOrWhiteSpace(value))
            {
                return default(long);
            }    

            if(!long.TryParse(value, out long result))
            {
                return default(long);
            }    

            return result;
        }

        private async Task AddKeyExpireAsync(IDatabase db, string key, TimeSpan? expiry = null)
        {
            if(expiry == null)
            {
                expiry = _expiry;
            }
            await db.KeyExpireAsync(key, expiry);
        }
    }
}
