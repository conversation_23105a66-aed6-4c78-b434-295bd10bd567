using System;
using System.Threading.Tasks;
using Core.Application.Dtos;
using Core.Application.Services;

namespace Core.Identity
{
    public interface IIdentityRoleAppService
        : ICrudAppService<
            IdentityRoleDto,
            Guid,
            GetIdentityRolesInput,
            IdentityRoleCreateDto,
            IdentityRoleUpdateDto>
    {
        Task<ListResultDto<IdentityRoleDto>> GetAllListAsync();
    }
}
