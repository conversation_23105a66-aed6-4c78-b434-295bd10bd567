using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Core.Domain.Repositories.EntityFrameworkCore;
using Core.EntityFrameworkCore;

namespace Core.Identity.EntityFrameworkCore
{
    public class EfCoreIdentityUserRepository : EfCoreRepository<IIdentityDbContext, IdentityUser, Guid>, IIdentityUserRepository
    {
        public EfCoreIdentityUserRepository(IDbContextProvider<IIdentityDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }

        public virtual async Task<IdentityUser> FindByNormalizedUserNameAsync(
            string normalizedUserName,
            Guid? tenantId,
            bool includeDetails = true,
            CancellationToken cancellationToken = default)
        {
            return await (await GetDbSetAsync())
                .IncludeDetails(includeDetails)
                .OrderBy(x => x.Id)
                .WhereIf(tenantId.HasValue, x=>x.TenantId == tenantId)
                .FirstOrDefaultAsync(
                    u => u.NormalizedUserName == normalizedUserName,
                    GetCancellationToken(cancellationToken)
                );
        }

        public virtual async Task<List<string>> GetRoleNamesAsync(
            Guid id,
            CancellationToken cancellationToken = default)
        {
            var dbContext = await GetDbContextAsync();
            var query = from userRole in dbContext.Set<IdentityUserRole>()
                        join role in dbContext.Roles on userRole.RoleId equals role.Id
                        where userRole.UserId == id
                        select role.Name;
            var organizationUnitIds = dbContext.Set<IdentityUserOrganizationUnit>().Where(q => q.UserId == id).Select(q => q.OrganizationUnitId).ToArray();

            var organizationRoleIds = await (
                from ouRole in dbContext.Set<OrganizationUnitRole>()
                join ou in dbContext.Set<OrganizationUnit>() on ouRole.OrganizationUnitId equals ou.Id
                where organizationUnitIds.Contains(ouRole.OrganizationUnitId)
                select ouRole.RoleId
            ).ToListAsync(GetCancellationToken(cancellationToken));

            var orgUnitRoleNameQuery = dbContext.Roles.Where(r => organizationRoleIds.Contains(r.Id)).Select(n => n.Name);
            var resultQuery = query.Union(orgUnitRoleNameQuery);
            return await resultQuery.ToListAsync(GetCancellationToken(cancellationToken));
        }

        public virtual async Task<List<string>> GetRoleNamesInOrganizationUnitAsync(
            Guid id,
            CancellationToken cancellationToken = default)
        {
            var dbContext = await GetDbContextAsync();
            var query = from userOu in dbContext.Set<IdentityUserOrganizationUnit>()
                        join roleOu in dbContext.Set<OrganizationUnitRole>() on userOu.OrganizationUnitId equals roleOu.OrganizationUnitId
                        join ou in dbContext.Set<OrganizationUnit>() on roleOu.OrganizationUnitId equals ou.Id
                        join userOuRoles in dbContext.Roles on roleOu.RoleId equals userOuRoles.Id
                        where userOu.UserId == id
                        select userOuRoles.Name;

            var result = await query.ToListAsync(GetCancellationToken(cancellationToken));

            return result;
        }

        public virtual async Task<IdentityUser> FindByLoginAsync(
            Guid? tenantId,
            string loginProvider,
            string providerKey,
            bool includeDetails = true,
            CancellationToken cancellationToken = default)
        {
            return await (await GetDbSetAsync())
                .IncludeDetails(includeDetails)
                .Where(u => u.Logins.Any(login => login.LoginProvider == loginProvider && login.ProviderKey == providerKey))
                .WhereIf(tenantId.HasValue, x => x.TenantId == tenantId)
                .OrderBy(x=>x.Id)
                .FirstOrDefaultAsync(GetCancellationToken(cancellationToken));
        }

        public virtual async Task<IdentityUser> FindByNormalizedEmailAsync(
            Guid? tenantId,
            string normalizedEmail,
            bool includeDetails = true,
            CancellationToken cancellationToken = default)
        {
            return await (await GetDbSetAsync())
                .IncludeDetails(includeDetails)
                .WhereIf(tenantId.HasValue, x => x.TenantId == tenantId)
                .OrderBy(x => x.Id)
                .FirstOrDefaultAsync(u => u.NormalizedEmail == normalizedEmail, GetCancellationToken(cancellationToken));
        }

        public virtual async Task<List<IdentityUser>> GetListByClaimAsync(
            Guid? tenantId,
            Claim claim,
            bool includeDetails = false,
            CancellationToken cancellationToken = default)
        {
            return await (await GetDbSetAsync())
                .IncludeDetails(includeDetails)
                .Where(u => u.Claims.Any(c => c.ClaimType == claim.Type && c.ClaimValue == claim.Value))
                .WhereIf(tenantId.HasValue, x => x.TenantId == tenantId)
                .ToListAsync(GetCancellationToken(cancellationToken));
        }

        public virtual async Task<List<IdentityUser>> GetListByNormalizedRoleNameAsync(
            Guid? tenantId,
            string normalizedRoleName,
            bool includeDetails = false,
            CancellationToken cancellationToken = default)
        {
            var dbContext = await GetDbContextAsync();

            var role = await dbContext.Roles
                .Where(x => x.NormalizedName == normalizedRoleName)
                .WhereIf(tenantId.HasValue, x => x.TenantId == tenantId)
                .OrderBy(x => x.Id)
                .FirstOrDefaultAsync(GetCancellationToken(cancellationToken));

            if (role == null)
            {
                return new List<IdentityUser>();
            }

            return await dbContext.Users
                .IncludeDetails(includeDetails)
                .Where(u => u.Roles.Any(r => r.RoleId == role.Id))
                .ToListAsync(GetCancellationToken(cancellationToken));
        }

        public virtual async Task<List<IdentityUser>> GetListAsync(
            Guid? tenantId,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            string filter = null,
            short type = 1,
            bool includeDetails = false,
            CancellationToken cancellationToken = default)
        {
            return await (await GetDbSetAsync())
                .IncludeDetails(includeDetails)
                .WhereIf(
                    !filter.IsNullOrWhiteSpace(),
                    u =>
                        u.UserName == filter ||
                        u.Email == filter ||
                        u.CashierCode == filter ||
                        u.EmployeeCode == filter ||
                        (u.Name != null && u.Name == filter) ||
                        (u.Surname != null && u.Surname == filter) ||
                        (u.PhoneNumber != null && u.PhoneNumber == filter)
                )
                .Where(x => x.Type == type && x.IsDeleted == false)
                .WhereIf(type == 1 && tenantId.HasValue, x => x.TenantId == tenantId)
                .OrderBy(sorting.IsNullOrWhiteSpace() ? nameof(IdentityUser.UserName) : sorting)
                .PageBy(skipCount, maxResultCount)
                .ToListAsync(GetCancellationToken(cancellationToken));
        }

        public virtual async Task<List<IdentityRole>> GetRolesAsync(
            Guid id,
            bool includeDetails = false,
            CancellationToken cancellationToken = default)
        {
            var dbContext = await GetDbContextAsync();

            var query = from userRole in dbContext.Set<IdentityUserRole>()
                        join role in dbContext.Roles.IncludeDetails(includeDetails) on userRole.RoleId equals role.Id
                        where userRole.UserId == id
                        select role;

            //TODO: Needs improvement
            var userOrganizationsQuery = from userOrg in dbContext.Set<IdentityUserOrganizationUnit>()
                                         join ou in dbContext.OrganizationUnits.IncludeDetails(includeDetails) on userOrg.OrganizationUnitId equals ou.Id
                                         where userOrg.UserId == id
                                         select ou;

            var orgUserRoleQuery = dbContext.Set<OrganizationUnitRole>()
                .Where(q => userOrganizationsQuery
                .Select(t => t.Id)
                .Contains(q.OrganizationUnitId))
                .Select(t => t.RoleId);

            var orgRoles = dbContext.Roles.Where(q => orgUserRoleQuery.Contains(q.Id));
            var resultQuery = query.Union(orgRoles);

            return await resultQuery.ToListAsync(GetCancellationToken(cancellationToken));
        }

        public virtual async Task<long> GetCountAsync(
            Guid? tenantId,
            string filter = null,
            short type = 1,
            CancellationToken cancellationToken = default)
        {
            return await (await GetDbSetAsync())
                .WhereIf(
                    !filter.IsNullOrWhiteSpace(),
                    u =>
                        u.UserName == filter ||
                        u.Email == filter ||
                        u.CashierCode == filter ||
                        u.EmployeeCode == filter ||
                        (u.Name != null && u.Name == filter) ||
                        (u.Surname != null && u.Surname == filter) ||
                        (u.PhoneNumber != null && u.PhoneNumber == filter)
                )
                .Where(x => x.Type == type && x.IsDeleted == false)
                .WhereIf(type == 1 && tenantId.HasValue, x => x.TenantId == tenantId)
                .LongCountAsync(GetCancellationToken(cancellationToken));
        }

        public virtual async Task<List<OrganizationUnit>> GetOrganizationUnitsAsync(
            Guid id,
            bool includeDetails = false,
            CancellationToken cancellationToken = default)
        {
            var dbContext = await GetDbContextAsync();

            var query = from userOu in dbContext.Set<IdentityUserOrganizationUnit>()
                        join ou in dbContext.OrganizationUnits.IncludeDetails(includeDetails) on userOu.OrganizationUnitId equals ou.Id
                        where userOu.UserId == id
                        select ou;

            return await query.ToListAsync(GetCancellationToken(cancellationToken));
        }

        public virtual async Task<List<IdentityUser>> GetUsersInOrganizationUnitAsync(
            Guid organizationUnitId,
            CancellationToken cancellationToken = default
            )
        {
            var dbContext = await GetDbContextAsync();

            var query = from userOu in dbContext.Set<IdentityUserOrganizationUnit>()
                        join user in dbContext.Users on userOu.UserId equals user.Id
                        where userOu.OrganizationUnitId == organizationUnitId
                        select user;

            return await query.ToListAsync(GetCancellationToken(cancellationToken));
        }

        public virtual async Task<List<IdentityUser>> GetUsersInOrganizationsListAsync(
            List<Guid> organizationUnitIds,
            CancellationToken cancellationToken = default
            )
        {
            var dbContext = await GetDbContextAsync();

            var query = from userOu in dbContext.Set<IdentityUserOrganizationUnit>()
                        join user in dbContext.Users on userOu.UserId equals user.Id
                        where organizationUnitIds.Contains(userOu.OrganizationUnitId)
                        select user;

            return await query.ToListAsync(GetCancellationToken(cancellationToken));
        }

        public virtual async Task<List<IdentityUser>> GetUsersInOrganizationUnitWithChildrenAsync(
            string code,
            CancellationToken cancellationToken = default
            )
        {
            var dbContext = await GetDbContextAsync();

            var query = from userOu in dbContext.Set<IdentityUserOrganizationUnit>()
                        join user in dbContext.Users on userOu.UserId equals user.Id
                        join ou in dbContext.Set<OrganizationUnit>() on userOu.OrganizationUnitId equals ou.Id
                        where ou.Code.StartsWith(code)
                        select user;

            return await query.ToListAsync(GetCancellationToken(cancellationToken));
        }

        [Obsolete("Use WithDetailsAsync method.")]
        public override IQueryable<IdentityUser> WithDetails()
        {
            return GetQueryable().IncludeDetails();
        }

        public override async Task<IQueryable<IdentityUser>> WithDetailsAsync()
        {
            return (await GetQueryableAsync()).IncludeDetails();
        }
    }
}
