{"culture": "en-GB", "texts": {"Menu:IdentityManagement": "Identity management", "Users": "Users", "NewUser": "New user", "UserName": "Username", "EmailAddress": "Email address", "PhoneNumber": "Phone number", "UserInformations": "User information", "DisplayName:IsDefault": "<PERSON><PERSON><PERSON>", "DisplayName:IsStatic": "Static", "DisplayName:IsPublic": "Public", "Roles": "Roles", "Password": "Password", "PersonalInfo": "My Profile", "PersonalSettings": "Personal settings", "UserDeletionConfirmationMessage": "User '{0}' will be deleted. Please confirm?", "RoleDeletionConfirmationMessage": "Role '{0}' will be deleted. Please confirm?", "DisplayName:RoleName": "Role name", "DisplayName:UserName": "Username", "DisplayName:Name": "Name", "DisplayName:Surname": "Surname", "DisplayName:Password": "Password", "DisplayName:Email": "Email Address", "DisplayName:PhoneNumber": "Phone Number", "DisplayName:TwoFactorEnabled": "Two factor verification", "DisplayName:LockoutEnabled": "Lock account after failed login attempts", "NewRole": "New role", "RoleName": "Role name", "CreationTime": "Creation time", "Permissions": "Permissions", "DisplayName:CurrentPassword": "Current password", "DisplayName:NewPassword": "New password", "DisplayName:NewPasswordConfirm": "Confirm new password", "PasswordChangedMessage": "Your password has been changed successfully.", "PersonalSettingsSavedMessage": "Your personal settings has been saved successfully.", "Core.Identity:DefaultError": "An unknown failure has occurred.", "Core.Identity:ConcurrencyFailure": "Optimistic concurrency failure, object has been modified.", "Core.Identity:DuplicateEmail": "Email '{0}' is already taken.", "Core.Identity:DuplicateRoleName": "Role name '{0}' has already been taken.", "Core.Identity:DuplicateUserName": "Username '{0}' has already been taken.", "Core.Identity:InvalidEmail": "Email '{0}' is invalid.", "Core.Identity:InvalidPasswordHasherCompatibilityMode": "The provided PasswordHasherCompatibilityMode is invalid.", "Core.Identity:InvalidPasswordHasherIterationCount": "The iteration count must be a positive integer.", "Core.Identity:InvalidRoleName": "Role name '{0}' is invalid.", "Core.Identity:InvalidToken": "Invalid token.", "Core.Identity:InvalidUserName": "Username '{0}' is invalid, it should only contain letters or digits.", "Core.Identity:LoginAlreadyAssociated": "A user with this login already exists.", "Core.Identity:PasswordMismatch": "Incorrect password.", "Core.Identity:PasswordRequiresDigit": "Passwords must have at least one digit ('0'-'9').", "Core.Identity:PasswordRequiresLower": "Passwords must have at least one lowercase ('a'-'z').", "Core.Identity:PasswordRequiresNonAlphanumeric": "Passwords must have at least one non alphanumeric character.", "Core.Identity:PasswordRequiresUpper": "Passwords must have at least one uppercase ('A'-'Z').", "Core.Identity:PasswordTooShort": "Passwords must be at least {0} characters.", "Core.Identity:RoleNotFound": "Role {0} does not exist.", "Core.Identity:UserAlreadyHasPassword": "User already has a password set.", "Core.Identity:UserAlreadyInRole": "User is already in role '{0}'.", "Core.Identity:UserLockedOut": "User is locked out.", "Core.Identity:UserLockoutNotEnabled": "Lockout is not enabled for this user.", "Core.Identity:UserNameNotFound": "User {0} does not exist.", "Core.Identity:UserNotInRole": "User is not in role '{0}'.", "Core.Identity:PasswordConfirmationFailed": "Password does not match the confirm password.", "Core.Identity:010001": "You cannot delete your own account!", "Core.Identity:010002": "You cannot set more than {MaxUserMembershipCount} organisation unit(s) for a user!", "Core.Identity:010003": "You cannot change the password of an externally logged in user!", "Core.Identity:010004": "There is already an organisation unit with name {0}. Two units with same name can not be created in same level.", "Core.Identity:010005": "Static roles cannot be renamed.", "Core.Identity:010006": "Static roles cannot be deleted.", "Core.Identity:010007": "You can't change your two factor setting.", "Core.Identity:010008": "Changing the two factor setting is not allowed.", "Identity.OrganizationUnit.MaxUserMembershipCount": "Maximum allowed organisation unit membership count for a user", "Permission:IdentityManagement": "Identity management", "Permission:RoleManagement": "Role management", "Permission:Create": "Create", "Permission:Edit": "Edit", "Permission:Delete": "Delete", "Permission:ChangePermissions": "Change permissions", "Permission:UserManagement": "User management", "Permission:UserLookup": "User lookup", "Feature:IdentityGroup": "Identity", "Feature:TwoFactor": "Two factor behaviour", "Feature:TwoFactorDescription": "Set two factor behaviour. Optional values: Optional, Disabled, Forced", "Feature:TwoFactor.Optional": "Optional", "Feature:TwoFactor.Disabled": "Disabled", "Feature:TwoFactor.Forced": "forced", "DisplayName:Abp.Identity.Password.RequiredLength": "Required length", "DisplayName:Abp.Identity.Password.RequiredUniqueChars": "Required unique characters number", "DisplayName:Abp.Identity.Password.RequireNonAlphanumeric": "Required non-alphanumeric character", "DisplayName:Abp.Identity.Password.RequireLowercase": "Required lower case character", "DisplayName:Abp.Identity.Password.RequireUppercase": "Required upper case character", "DisplayName:Abp.Identity.Password.RequireDigit": "Required digit", "DisplayName:Abp.Identity.Lockout.AllowedForNewUsers": "Enabled for new users", "DisplayName:Abp.Identity.Lockout.LockoutDuration": "Lockout duration(in seconds)", "DisplayName:Abp.Identity.Lockout.MaxFailedAccessAttempts": "<PERSON> failed access attempts", "DisplayName:Abp.Identity.SignIn.RequireConfirmedEmail": "Require confirmed email", "DisplayName:Abp.Identity.SignIn.EnablePhoneNumberConfirmation": "Allow users to confirm their phone number", "DisplayName:Abp.Identity.SignIn.RequireConfirmedPhoneNumber": "Require confirmed phone number", "DisplayName:Abp.Identity.User.IsUserNameUpdateEnabled": "Allow users to change their usernames", "DisplayName:Abp.Identity.User.IsEmailUpdateEnabled": "Allow users to change their email addresses", "Description:Abp.Identity.Password.RequiredLength": "The minimum length a password must be.", "Description:Abp.Identity.Password.RequiredUniqueChars": "The minimum number of unique characters which a password must contain.", "Description:Abp.Identity.Password.RequireNonAlphanumeric": "If passwords must contain a non-alphanumeric character.", "Description:Abp.Identity.Password.RequireLowercase": "If passwords must contain a lower case ASCII character.", "Description:Abp.Identity.Password.RequireUppercase": "If passwords must contain a upper case ASCII character.", "Description:Abp.Identity.Password.RequireDigit": "If passwords must contain a digit.", "Description:Abp.Identity.Lockout.AllowedForNewUsers": "Whether a new user can be locked out.", "Description:Abp.Identity.Lockout.LockoutDuration": "The duration a user is locked out for when a lockout occurs.", "Description:Abp.Identity.Lockout.MaxFailedAccessAttempts": "The number of failed access attempts allowed before a user is locked out, assuming lock out is enabled.", "Description:Abp.Identity.SignIn.RequireConfirmedEmail": "Whether a confirmed email address is required to sign in.", "Description:Abp.Identity.SignIn.EnablePhoneNumberConfirmation": "Whether the phoneNumber can be confirmed by the user.", "Description:Abp.Identity.SignIn.RequireConfirmedPhoneNumber": "Whether a confirmed telephone number is required to sign in.", "Description:Abp.Identity.User.IsUserNameUpdateEnabled": "Whether the username can be updated by the user.", "Description:Abp.Identity.User.IsEmailUpdateEnabled": "Whether the email can be updated by the user.", "DisplayName:Abp.Identity.TwoFactorBehaviour": "Two Factor behaviour", "Description:Abp.Identity.TwoFactorBehaviour": "Two Factor behaviour", "DisplayName:Abp.Identity.UsersCanChange": "Allow users to change their Two Factor.", "Description:Abp.Identity.UsersCanChange": "Allow users to change their Two Factor."}}