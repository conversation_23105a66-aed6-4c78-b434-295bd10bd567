<Project Sdk="Microsoft.NET.Sdk">

  
  

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <AssemblyName>Core.Users.EntityFrameworkCore</AssemblyName>
    <PackageId>Core.Users.EntityFrameworkCore</PackageId>
    <AssetTargetFallback>portable-net45+win8+wp8+wpa81;</AssetTargetFallback>
    <GenerateAssemblyConfigurationAttribute>false</GenerateAssemblyConfigurationAttribute>
    <GenerateAssemblyCompanyAttribute>false</GenerateAssemblyCompanyAttribute>
    <GenerateAssemblyProductAttribute>false</GenerateAssemblyProductAttribute>
    <RootNamespace />
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\Core\Core.EntityFrameworkCore\Core.EntityFrameworkCore.csproj" />
    <ProjectReference Include="..\Core.Users.Domain\Core.Users.Domain.csproj" />
  </ItemGroup>

</Project>
