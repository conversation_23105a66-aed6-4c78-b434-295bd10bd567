using MongoDB.Driver;
using Core.Data;
using Core.MongoDB;
using Core.MultiTenancy;

namespace Core.BackgroundJobs.MongoDB
{
    [IgnoreMultiTenancy]
    [ConnectionStringName(BackgroundJobsDbProperties.ConnectionStringName)]
    public class BackgroundJobsMongoDbContext : AbpMongoDbContext, IBackgroundJobsMongoDbContext
    {
        public IMongoCollection<BackgroundJobRecord> BackgroundJobs { get; set; }

        protected override void CreateModel(IMongoModelBuilder modelBuilder)
        {
            base.CreateModel(modelBuilder);

            modelBuilder.ConfigureBackgroundJobs();
        }
    }
}
