using Microsoft.Extensions.DependencyInjection;
using Core.Modularity;
using Core.MongoDB;

namespace Core.FeatureManagement.MongoDB
{
    [DependsOn(
        typeof(AbpFeatureManagementDomainModule),
        typeof(AbpMongoDbModule)
        )]
    public class AbpFeatureManagementMongoDbModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            context.Services.AddMongoDbContext<FeatureManagementMongoDbContext>(options =>
            {
                options.AddDefaultRepositories<IFeatureManagementMongoDbContext>();

                options.AddRepository<FeatureValue, MongoFeatureValueRepository>();
            });
        }
    }
}
