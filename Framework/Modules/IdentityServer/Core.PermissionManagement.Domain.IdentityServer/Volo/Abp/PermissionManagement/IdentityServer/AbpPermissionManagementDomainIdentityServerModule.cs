using Core.Authorization.Permissions;
using Core.IdentityServer;
using Core.Modularity;

namespace Core.PermissionManagement.IdentityServer
{
    [DependsOn(
        typeof(AbpIdentityServerDomainSharedModule),
        typeof(AbpPermissionManagementDomainModule)
    )]
    public class AbpPermissionManagementDomainIdentityServerModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            Configure<PermissionManagementOptions>(options =>
            {
                options.ManagementProviders.Add<ClientPermissionManagementProvider>();

                //options.ProviderPolicies[ClientPermissionValueProvider.ProviderName] = "IdentityServer.Client.ManagePermissions";
                options.ProviderPolicies[RolePermissionValueProvider.ProviderName] = "Identity.Roles.ManagePermissions";
            });
        }
    }
}
