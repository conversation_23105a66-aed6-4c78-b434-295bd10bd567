<Project Sdk="Microsoft.NET.Sdk">

  
  

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <AssemblyName>Core.IdentityServer.EntityFrameworkCore</AssemblyName>
    <PackageId>Core.IdentityServer.EntityFrameworkCore</PackageId>
    <AssetTargetFallback>portable-net45+win8+wp8+wpa81;</AssetTargetFallback>
    <GenerateAssemblyConfigurationAttribute>false</GenerateAssemblyConfigurationAttribute>
    <GenerateAssemblyCompanyAttribute>false</GenerateAssemblyCompanyAttribute>
    <GenerateAssemblyProductAttribute>false</GenerateAssemblyProductAttribute>
    <RootNamespace />
    <LangVersion>latest</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Core.IdentityServer.Domain\Core.IdentityServer.Domain.csproj" />
    <ProjectReference Include="..\..\..\Core\Core.EntityFrameworkCore\Core.EntityFrameworkCore.csproj" />
  </ItemGroup>

</Project>
