using MongoDB.Driver;
using Core.Data;
using Core.IdentityServer.ApiResources;
using Core.IdentityServer.ApiScopes;
using Core.IdentityServer.Clients;
using Core.IdentityServer.Devices;
using Core.IdentityServer.Grants;
using Core.IdentityServer.IdentityResources;
using Core.MongoDB;
using Core.MultiTenancy;

namespace Core.IdentityServer.MongoDB
{
    [IgnoreMultiTenancy]
    [ConnectionStringName(AbpIdentityServerDbProperties.ConnectionStringName)]
    public interface IAbpIdentityServerMongoDbContext : IAbpMongoDbContext
    {
        IMongoCollection<ApiResource> ApiResources { get; }

        IMongoCollection<ApiScope> ApiScopes { get; }

        IMongoCollection<Client> Clients { get; }

        IMongoCollection<IdentityResource> IdentityResources { get; }

        IMongoCollection<PersistedGrant> PersistedGrants { get; }

        IMongoCollection<DeviceFlowCodes> DeviceFlowCodes { get; }
    }
}
