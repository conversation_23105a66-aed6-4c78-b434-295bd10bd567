using System;
using Core.ObjectExtending.Modularity;

namespace Core.ObjectExtending
{
    public class TenantManagementModuleExtensionConfiguration : ModuleExtensionConfiguration
    {
        public TenantManagementModuleExtensionConfiguration ConfigureTenant(
            Action<EntityExtensionConfiguration> configureAction)
        {
            return this.ConfigureEntity(
                TenantManagementModuleExtensionConsts.EntityNames.Tenant,
                configureAction
            );
        }
    }
}