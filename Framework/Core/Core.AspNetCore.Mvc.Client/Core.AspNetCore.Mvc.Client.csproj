<Project Sdk="Microsoft.NET.Sdk.Web">

  
  

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <AssemblyName>Core.AspNetCore.Mvc.Client</AssemblyName>
    <PackageId>Core.AspNetCore.Mvc.Client</PackageId>
    <AssetTargetFallback>portable-net45+win8+wp8+wpa81;</AssetTargetFallback>
    <GenerateAssemblyConfigurationAttribute>false</GenerateAssemblyConfigurationAttribute>
    <GenerateAssemblyCompanyAttribute>false</GenerateAssemblyCompanyAttribute>
    <GenerateAssemblyProductAttribute>false</GenerateAssemblyProductAttribute>
    <IsPackable>true</IsPackable>
    <OutputType>Library</OutputType>
    <RootNamespace />
    <LangVersion>latest</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Core.AspNetCore.Mvc.Client.Common\Core.AspNetCore.Mvc.Client.Common.csproj" />
    <ProjectReference Include="..\Core.EventBus\Core.EventBus.csproj" />
  </ItemGroup>

</Project>
