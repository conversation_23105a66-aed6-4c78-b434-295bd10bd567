using System;
using Gateway.Core.Configuration;
using Gateway.Core.Configuration.Repository;
using Microsoft.Extensions.Options;

namespace Gateway.Core.Configuration.ChangeTracking
{
    public class CoreConfigurationMonitor : IOptionsMonitor<IInternalConfiguration>
    {
        private readonly ICoreConfigurationChangeTokenSource _changeTokenSource;
        private readonly IInternalConfigurationRepository _repo;

        public CoreConfigurationMonitor(IInternalConfigurationRepository repo, ICoreConfigurationChangeTokenSource changeTokenSource)
        {
            _changeTokenSource = changeTokenSource;
            _repo = repo;
        }

        public IInternalConfiguration Get(string name)
        {
            return _repo.Get().Data;
        }

        public IDisposable OnChange(Action<IInternalConfiguration, string> listener)
        {
            return _changeTokenSource.ChangeToken.RegisterChangeCallback(_ => listener(CurrentValue, ""), null);
        }

        public IInternalConfiguration CurrentValue => _repo.Get().Data;
    }
}
