using System.Collections.Generic;
using System.Security.Claims;
using Gateway.Core.DownstreamRouteFinder.UrlMatcher;
using Gateway.Core.Responses;

namespace Gateway.Core.Authorization
{
    public interface IClaimsAuthorizer
    {
        Response<bool> Authorize(
            ClaimsPrincipal claimsPrincipal,
            Dictionary<string, string> routeClaimsRequirement,
            List<PlaceholderNameAndValue> urlPathPlaceholderNameAndValues
        );
    }
}
