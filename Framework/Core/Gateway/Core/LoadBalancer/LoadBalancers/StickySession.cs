using Gateway.Core.Values;
using System;

namespace Gateway.Core.LoadBalancer.LoadBalancers
{
    public class StickySession
    {
        public StickySession(ServiceHostAndPort hostAndPort, DateTime expiry, string key)
        {
            HostAndPort = hostAndPort;
            Expiry = expiry;
            Key = key;
        }

        public ServiceHostAndPort HostAndPort { get; }

        public DateTime Expiry { get; }

        public string Key { get; }
    }
}
