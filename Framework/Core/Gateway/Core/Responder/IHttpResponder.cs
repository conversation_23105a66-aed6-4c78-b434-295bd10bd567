namespace Gateway.Core.Responder
{
    using Microsoft.AspNetCore.Http;
    using Gateway.Core.Middleware;
    using System.Threading.Tasks;

    public interface IHttpResponder
    {
        Task SetResponseOnHttpContext(HttpContext context, DownstreamResponse response);

        void SetErrorResponseOnContext(HttpContext context, int statusCode);

        Task SetErrorResponseOnContext(HttpContext context, DownstreamResponse response);
    }
}
