<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net5.0</TargetFramework>
		<NoPackageAnalysis>true</NoPackageAnalysis>
		<Description>API Gateway</Description>
		<AssemblyTitle>Gateway</AssemblyTitle>
		<VersionPrefix>0.0.0</VersionPrefix>
		<AssemblyName>Gateway</AssemblyName>
		<PackageId>Gateway</PackageId>
		<PackageTags>API Gateway;.NET core</PackageTags>
		<GenerateAssemblyConfigurationAttribute>false</GenerateAssemblyConfigurationAttribute>
		<GenerateAssemblyCompanyAttribute>false</GenerateAssemblyCompanyAttribute>
		<GeneratePackageOnBuild>false</GeneratePackageOnBuild>
		<GenerateAssemblyProductAttribute>false</GenerateAssemblyProductAttribute>
		<Authors>TrongChien</Authors>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="FluentValidation" Version="9.3.0" />
		<PackageReference Include="Microsoft.AspNetCore.MiddlewareAnalysis" Version="5.0.0" />
		<PackageReference Include="Microsoft.Extensions.DiagnosticAdapter" Version="3.1.10">
			<NoWarn>NU1701</NoWarn>
		</PackageReference>
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="5.0.0" />
		<PackageReference Include="StyleCop.Analyzers" Version="1.2.0-beta.164">
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="IdentityServer4.AccessTokenValidation" Version="3.0.1" />
		<PackageReference Include="IdentityServer4" Version="4.1.1" />
		<PackageReference Include="CacheManager.Core" Version="2.0.0-beta-1629" />
		<PackageReference Include="CacheManager.Microsoft.Extensions.Configuration" Version="2.0.0-beta-1629" />
		<PackageReference Include="CacheManager.Microsoft.Extensions.Logging" Version="2.0.0-beta-1629" />
		<PackageReference Include="Consul" Version="1.6.1.1" />
		<PackageReference Include="Steeltoe.Discovery.ClientCore" Version="3.0.1" />
		<PackageReference Include="Steeltoe.Discovery.Eureka" Version="3.0.1" />
		<PackageReference Include="KubeClient" Version="2.3.15" />
		<PackageReference Include="KubeClient.Extensions.DependencyInjection" Version="2.3.15" />
		<PackageReference Include="Polly" Version="7.2.1" />
		<PackageReference Include="Butterfly.Client" Version="0.0.8" />
		<PackageReference Include="Butterfly.Client.AspNetCore" Version="0.0.8" />
		<PackageReference Include="OpenTracing" Version="0.12.1" />
	</ItemGroup>

</Project>