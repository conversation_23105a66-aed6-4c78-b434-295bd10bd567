<Project Sdk="Microsoft.NET.Sdk">

  
  

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <AssemblyName>Core.AspNetCore.Mvc.Contracts</AssemblyName>
    <PackageId>Core.AspNetCore.Mvc.Contracts</PackageId>
    <AssetTargetFallback>portable-net45+win8+wp8+wpa81;</AssetTargetFallback>
    <GenerateAssemblyConfigurationAttribute>false</GenerateAssemblyConfigurationAttribute>
    <GenerateAssemblyCompanyAttribute>false</GenerateAssemblyCompanyAttribute>
    <GenerateAssemblyProductAttribute>false</GenerateAssemblyProductAttribute>
    <RootNamespace />
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Core.Ddd.Application.Contracts\Core.Ddd.Application.Contracts.csproj" />
  </ItemGroup>

</Project>
