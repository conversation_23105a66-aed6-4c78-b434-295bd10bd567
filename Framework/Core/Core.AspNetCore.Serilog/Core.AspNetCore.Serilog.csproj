<Project Sdk="Microsoft.NET.Sdk.Web">

  
    

    <PropertyGroup>
        <TargetFramework>net5.0</TargetFramework>
        <AssemblyName>Core.AspNetCore.Serilog</AssemblyName>
        <PackageId>Core.AspNetCore.Serilog</PackageId>
        <AssetTargetFallback>portable-net45+win8+wp8+wpa81;</AssetTargetFallback>
        <GenerateAssemblyConfigurationAttribute>false</GenerateAssemblyConfigurationAttribute>
        <GenerateAssemblyCompanyAttribute>false</GenerateAssemblyCompanyAttribute>
        <GenerateAssemblyProductAttribute>false</GenerateAssemblyProductAttribute>
        <IsPackable>true</IsPackable>
        <OutputType>Library</OutputType>
        <RootNamespace />
        <LangVersion>latest</LangVersion>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Core.AspNetCore\Core.AspNetCore.csproj" />
        <ProjectReference Include="..\Core.MultiTenancy\Core.MultiTenancy.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Serilog" Version="2.10.0" />
    </ItemGroup>

</Project>
