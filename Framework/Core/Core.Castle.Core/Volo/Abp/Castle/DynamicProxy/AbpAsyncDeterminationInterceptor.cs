using Castle.DynamicProxy;
using Core.DynamicProxy;

namespace Core.Castle.DynamicProxy
{
    public class AbpAsyncDeterminationInterceptor<TInterceptor> : AsyncDeterminationInterceptor
        where TInterceptor : IAbpInterceptor
    {
        public AbpAsyncDeterminationInterceptor(TInterceptor abpInterceptor)
            : base(new CastleAsyncAbpInterceptorAdapter<TInterceptor>(abpInterceptor))
        {

        }
    }
}