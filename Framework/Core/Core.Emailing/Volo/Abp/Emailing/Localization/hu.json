{"culture": "hu", "texts": {"DisplayName:Abp.Mailing.DefaultFromAddress": "Alapértlemezett fela<PERSON> c<PERSON>me", "DisplayName:Abp.Mailing.DefaultFromDisplayName": "<PERSON><PERSON><PERSON>lmezett megjelenő neve", "DisplayName:Abp.Mailing.Smtp.Host": "Kiszolg<PERSON><PERSON><PERSON>", "DisplayName:Abp.Mailing.Smtp.Port": "Port", "DisplayName:Abp.Mailing.Smtp.UserName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DisplayName:Abp.Mailing.Smtp.Password": "Je<PERSON><PERSON><PERSON>", "DisplayName:Abp.Mailing.Smtp.Domain": "Domain", "DisplayName:Abp.Mailing.Smtp.EnableSsl": "SSL engedélyezése", "DisplayName:Abp.Mailing.Smtp.UseDefaultCredentials": "Használja az alapértelmezett hitelesítő adatokat", "Description:Abp.Mailing.DefaultFromAddress": "<PERSON><PERSON> alap<PERSON>rt<PERSON>ezett fela<PERSON> címe", "Description:Abp.Mailing.DefaultFromDisplayName": "A feladó alapértelmezett megjelenő neve", "Description:Abp.Mailing.Smtp.Host": "Az SMTP kiszolgáló neve vagy IP címe.", "Description:Abp.Mailing.Smtp.Port": "Az SMTP forgalomhoz használt port.", "Description:Abp.Mailing.Smtp.UserName": "A hitelesítő adatokhoz társított felhasználói név..", "Description:Abp.Mailing.Smtp.Password": "A hitelesítő adatokhoz társított felhasználói név jelszava.", "Description:Abp.Mailing.Smtp.Domain": "A hitelesítő adatokat igazoló tartomány vagy számítógép neve.", "Description:Abp.Mailing.Smtp.EnableSsl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ha a levelező programja Secure Sockets Layer (SSL) protokollt használ a kapcsolat titkosításához.", "Description:Abp.Mailing.Smtp.UseDefaultCredentials": "Alkal<PERSON><PERSON>, hogy a kérésekhez az alapértelmezett hitelesítést használja.", "TextTemplate:StandardEmailTemplates.Layout": "Alapértelmezett e-mail elrendezési sablon", "TextTemplate:StandardEmailTemplates.Message": "Egyszerű üzenet sablon az e-mailekhez"}}