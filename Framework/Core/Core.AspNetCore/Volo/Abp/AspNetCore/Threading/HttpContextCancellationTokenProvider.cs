using System.Threading;
using Microsoft.AspNetCore.Http;
using Core.DependencyInjection;
using Core.Threading;

namespace Core.AspNetCore.Threading
{
    [Dependency(ReplaceServices = true)]
    public class HttpContextCancellationTokenProvider : ICancellationTokenProvider, ITransientDependency
    {
        public CancellationToken Token => _httpContextAccessor.HttpContext?.RequestAborted ?? CancellationToken.None;

        private readonly IHttpContextAccessor _httpContextAccessor;

        public HttpContextCancellationTokenProvider(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }
    }
}
