<Project Sdk="Microsoft.NET.Sdk">

  
  

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <AssemblyName>Core.AutoMapper</AssemblyName>
    <PackageId>Core.AutoMapper</PackageId>
    <AssetTargetFallback>portable-net45+win8+wp8+wpa81;</AssetTargetFallback>
    <GenerateAssemblyConfigurationAttribute>false</GenerateAssemblyConfigurationAttribute>
    <GenerateAssemblyCompanyAttribute>false</GenerateAssemblyCompanyAttribute>
    <GenerateAssemblyProductAttribute>false</GenerateAssemblyProductAttribute>
    <RootNamespace />
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Core.Auditing\Core.Auditing.csproj" />
    <ProjectReference Include="..\Core.ObjectExtending\Core.ObjectExtending.csproj" />
    <ProjectReference Include="..\Core.ObjectMapping\Core.ObjectMapping.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="10.1.1" />
  </ItemGroup>

</Project>
