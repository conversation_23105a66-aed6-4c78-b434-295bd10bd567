using Microsoft.AspNetCore.Razor.TagHelpers;
using Core.AspNetCore.Mvc.UI.Bootstrap.Microsoft.AspNetCore.Razor.TagHelpers;

namespace Core.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Blockquote
{
    public class AbpBlockquoteTagHelperService : AbpTagHelperService<AbpBlockquoteTagHelper>
    {
        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            output.Attributes.AddClass("blockquote");
            output.TagName = "blockquote";
        }
    }
}
