using Microsoft.Extensions.DependencyInjection.Extensions;
using Core.Domain;
using Core.Domain.Repositories.MemoryDb;
using Core.Modularity;
using Core.Uow.MemoryDb;

namespace Core.MemoryDb
{
    [DependsOn(typeof(AbpDddDomainModule))]
    public class AbpMemoryDbModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            context.Services.TryAddTransient(typeof(IMemoryDatabaseProvider<>), typeof(UnitOfWorkMemoryDatabaseProvider<>));
            context.Services.TryAddTransient(typeof(IMemoryDatabaseCollection<>), typeof(MemoryDatabaseCollection<>));
        }
    }
}
