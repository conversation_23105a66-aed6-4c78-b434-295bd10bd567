# Tài liệu <PERSON> thuật: Ký tự động hóa đơn có nguồn tạo là form hoặc excel SignXmlInvoiceFormAndExcelBackgroundWorker

## 1. <PERSON><PERSON> tả Chức năng

### 1.1 Tổng quan
Hàm `DoWorkAsync` là thành phần cốt lõi của Background Worker tự động ký XML cho hóa đơn điện tử được tạo từ Form và Excel. Đây là một service chạy nền (background service) được thiết kế để:

- **Mục đích chính**: Tự động ký số XML cho các hóa đơn điện tử có trạng thái "Chờ ký" (SignStatus = 3)
- **Phạm vi xử lý**: Chỉ xử lý hóa đơn có nguồn tạo từ Form (Source = 1) và Excel (Source = 2)
- **<PERSON>ai trò trong hệ thống**: Đ<PERSON>m bảo các hóa đơn được ký số tự động mà không cần can thiệp thủ công

### 1.2 Kiến trúc hoạt động
- **Loại**: Periodic Background Worker (chạy định kỳ)
- **Chu kỳ mặc định**: 100ms (có thể cấu hình qua `Settings:TimePeriodFormAndExcel`)
- **Cơ chế delay**: Có thể trì hoãn khởi động qua `Settings:TimeDelayStartBackgroundWorker`

## 2. Luồng Xử lý Chi tiết

### 2.1 Bước 1: Kiểm tra điều kiện khởi động
```
1. Đọc cấu hình TimeDelayStartBackgroundWorker
2. Tính toán thời gian cho phép khởi động
3. So sánh với thời gian hiện tại
4. Nếu đủ điều kiện → tiếp tục, ngược lại → bỏ qua chu kỳ này
```

### 2.2 Bước 2: Gọi service ký hóa đơn
```
DoWorkAsync() → SignInvoiceFormAndExcelAuto()
```

### 2.3 Bước 3: Xử lý trong SignInvoiceFormAndExcelAuto()

#### 2.3.1 Kiểm tra cấu hình
- Đọc `Settings:IsEnableAutoSignInvoiceFormAndExcelWorker`
- Nếu = 1: tiếp tục xử lý
- Nếu ≠ 1: bỏ qua

#### 2.3.2 Lấy dữ liệu cần ký
```
GetDataSignFormAndExcel(SignRequest(), SignStatus.ChoKy, [Form, Excel])
```

#### 2.3.3 Nhóm hóa đơn theo mã số thuế
```
invoicesGroup = invoices.GroupBy(x => x.SellerTaxCode)
```

#### 2.3.4 Xử lý ký song song
```
Task.Run(() => SignFromAndExcel(invoicesGroup, sellerFullNameSigned, sellerSignedId))
```

### 2.4 Bước 4: Quá trình ký XML chi tiết

#### 2.4.1 Với mỗi nhóm mã số thuế:
1. Lấy thông tin chứng thư số (Certificate)
2. Xử lý từng hóa đơn trong nhóm
3. Chuyển đổi dữ liệu sang XML
4. Thực hiện ký số XML
5. Lưu file XML đã ký
6. Cập nhật trạng thái trong database

#### 2.4.2 Xử lý lỗi:
- Hóa đơn ký thành công: cập nhật SignStatus = 5 (Đã ký)
- Hóa đơn ký lỗi: cập nhật SignStatus = 1 (Ký lỗi)

## 3. Tương tác với Database

### 3.1 Bảng chính được truy vấn

#### 3.1.1 Bảng Invoice01Header
**Mục đích**: Bảng chứa thông tin header của hóa đơn GTGT
**Các trường quan trọng**:
- `Id`: Khóa chính của hóa đơn
- `SignStatus`: Trạng thái ký (3 = Chờ ký, 5 = Đã ký, 1 = Ký lỗi)
- `ApproveStatus`: Trạng thái duyệt (-1 = Không quy trình duyệt, 2 = Đã duyệt)
- `InvoiceStatus`: Trạng thái hóa đơn (≠ 9 = không phải xóa hủy)
- `Source`: Nguồn tạo hóa đơn (1 = Form, 2 = Excel)
- `InvoiceNo`: Số hóa đơn (phải khác NULL)
- `SellerTaxCode`: Mã số thuế người bán
- `TenantId`: ID tenant
- `SellerSignedTime`: Thời gian ký
- `SellerFullNameSigned`: Tên người ký
- `SellerSignedId`: ID người ký

#### 3.1.2 Bảng Invoice01Detail
**Mục đích**: Chứa chi tiết sản phẩm/dịch vụ của hóa đơn
**Liên kết**: `InvoiceHeaderId` → `Invoice01Header.Id`

#### 3.1.3 Bảng Invoice01Reference
**Mục đích**: Chứa thông tin tham chiếu của hóa đơn
**Liên kết**: `InvoiceHeaderId` → `Invoice01Header.Id`

#### 3.1.4 Bảng Invoice01TaxBreakdown
**Mục đích**: Chứa thông tin phân tích thuế
**Liên kết**: `InvoiceHeaderId` → `Invoice01Header.Id`

#### 3.1.5 Bảng UsbToken
**Mục đích**: Quản lý thông tin USB Token cho ký số
**Điều kiện**: `IsDeleted = 0`
**Liên kết**: `TenantId` với `Invoice01Header.TenantId`

#### 3.1.6 Bảng TaxReport01Header
**Mục đích**: Kiểm tra trạng thái báo cáo thuế
**Điều kiện**: Báo cáo chưa được duyệt (`ApproveStatus ≠ 2`)
**Liên kết**: 
- `TenantId` = `Invoice01Header.TenantId`
- `ReportMonth` = `Invoice01Header.InvoiceDateMonth`
- `ReportYear` = `Invoice01Header.InvoiceDateYear`

#### 3.1.7 Bảng Invoice01Xml
**Mục đích**: Lưu trữ file XML đã ký của hóa đơn
**Các trường quan trọng**:
- `Id`: Khóa chính (từ sequence Invoice01Xml)
- `InvoiceHeaderId`: Liên kết với hóa đơn
- `FileName`: Tên file logic
- `PhysicalFileName`: Tên file vật lý trên storage
- `ContentType`: Loại nội dung (XML)
- `TenantId`: ID tenant
- `CreationTime`: Thời gian tạo
- `InvoiceDate`: Ngày hóa đơn

### 3.2 Câu lệnh SQL chính

#### 3.2.1 Truy vấn lấy dữ liệu hóa đơn cần ký
```sql
WITH usbToken AS (
    SELECT DISTINCT "TenantId" 
    FROM "UsbToken" WHERE "IsDeleted" = 0
),
taxReport01Header AS (
    SELECT "ReportMonth", "ReportYear", "TenantId", "ApproveStatus" 
    FROM "TaxReport01Header" WHERE "IsDeleted" = 0
),
invoice01Header AS (
    SELECT a.* FROM "Invoice01Header" a
    INNER JOIN usbToken b ON a."TenantId" = b."TenantId"
    LEFT JOIN taxReport01Header c ON a."TenantId" = c."TenantId"
        AND c."ReportMonth" = a."InvoiceDateMonth"
        AND c."ReportYear" = a."InvoiceDateYear"
    WHERE a."ApproveStatus" IN (-1, 2) 
        AND a."InvoiceNo" IS NOT NULL
        AND "Source" IN (1, 2)  -- Form và Excel
        AND a."SignStatus" = 3  -- Chờ ký
        AND a."InvoiceStatus" != 9  -- Không phải xóa hủy
        AND (c."TenantId" IS NULL OR c."ApproveStatus" != 2)  -- Báo cáo chưa duyệt
    OFFSET 0 ROWS FETCH NEXT 100 ROWS ONLY
)
```

#### 3.2.2 Cập nhật trạng thái ký thành công
```sql
UPDATE "Invoice01Header" 
SET "SignStatus" = 5,  -- Đã ký
    "SellerSignedTime" = SYSDATE,
    "SellerFullNameSigned" = '{tên_người_ký}',
    "SellerSignedId" = '{id_người_ký}'
WHERE "Id" IN ({danh_sách_id_hóa_đơn})
```

#### 3.2.3 Cập nhật trạng thái ký lỗi
```sql
UPDATE "Invoice01Header" 
SET "SignStatus" = 1,  -- Ký lỗi
    "SellerSignedTime" = SYSDATE,
    "SellerFullNameSigned" = '{tên_người_ký}',
    "SellerSignedId" = '{id_người_ký}'
WHERE "Id" IN ({danh_sách_id_hóa_đơn_lỗi})
```

#### 3.2.4 Thêm mới bản ghi XML
```sql
INSERT INTO "Invoice01Xml"(
    "Id", "ContentType", "FileName", "PhysicalFileName",
    "TenantId", "InvoiceHeaderId", "Length", "Partition",
    "IsActive", "IsDeleted", "CreationTime", "InvoiceDate"
) VALUES (
    {id_từ_sequence}, 'Xml', '{tên_file}', '{tên_file_vật_lý}',
    '{tenant_id}', {invoice_header_id}, '0', '0',
    '0', '0', '{thời_gian_tạo}', '{ngày_hóa_đơn}'
)
```

#### 3.2.5 Cập nhật bản ghi XML đã tồn tại
```sql
UPDATE "Invoice01Xml" 
SET "FileName" = '{tên_file_mới}',
    "PhysicalFileName" = '{tên_file_vật_lý_mới}',
    "CreationTime" = '{thời_gian_cập_nhật}'
WHERE "InvoiceHeaderId" = {invoice_header_id}
```

### 3.3 Sequence được sử dụng
- **Invoice01Xml.NEXTVAL**: Tạo ID cho bản ghi XML mới

## 4. Điều kiện Thực thi

### 4.1 Điều kiện Background Worker
1. **Cấu hình bật**: `Settings:IsEnableAutoSignInvoiceFormAndExcelWorker = 1`
2. **Thời gian delay**: Đã qua thời gian delay khởi động (nếu có)
3. **Chu kỳ chạy**: Theo cấu hình `Settings:TimePeriodFormAndExcel` (mặc định 100ms)

### 4.2 Điều kiện hóa đơn được xử lý
1. **Trạng thái duyệt**: `ApproveStatus IN (-1, 2)` (Không quy trình duyệt hoặc Đã duyệt)
2. **Có số hóa đơn**: `InvoiceNo IS NOT NULL`
3. **Nguồn tạo**: `Source IN (1, 2)` (Form hoặc Excel)
4. **Trạng thái ký**: `SignStatus = 3` (Chờ ký)
5. **Trạng thái hóa đơn**: `InvoiceStatus != 9` (Không phải xóa hủy)
6. **Có USB Token**: Tenant phải có bản ghi trong bảng `UsbToken`
7. **Báo cáo thuế**: Báo cáo thuế tương ứng chưa được duyệt (nếu có)

### 4.3 Giới hạn xử lý
- **Số lượng**: Tối đa 100 hóa đơn mỗi lần chạy (cấu hình `Settings:TakeRows`)
- **Timeout**: Có delay giữa các lần xử lý (`Settings:TaskDelayMilliseconds`)

## 5. Xử lý Lỗi

### 5.1 Các trường hợp lỗi thường gặp

#### 5.1.1 Lỗi chứng thư số
- **Nguyên nhân**: Không tìm thấy certificate hoặc certificate hết hạn
- **Xử lý**: Đánh dấu hóa đơn với SignStatus = 1 (Ký lỗi)
- **Log**: Ghi log chi tiết lỗi certificate

#### 5.1.2 Lỗi HSM (Hardware Security Module)
- **Nguyên nhân**: Kết nối HSM bị gián đoạn hoặc lỗi phần cứng
- **Xử lý**: Đánh dấu hóa đơn với SignStatus = 1 (Ký lỗi)
- **Log**: Ghi log lỗi HSM để troubleshoot

#### 5.1.3 Lỗi chuyển đổi XML
- **Nguyên nhân**: Dữ liệu hóa đơn không hợp lệ hoặc thiếu thông tin bắt buộc
- **Xử lý**: Bỏ qua hóa đơn, ghi log lỗi
- **Log**: Ghi log chi tiết dữ liệu lỗi

#### 5.1.4 Lỗi lưu file
- **Nguyên nhân**: Lỗi kết nối MinIO hoặc hết dung lượng storage
- **Xử lý**: Retry hoặc đánh dấu lỗi
- **Log**: Ghi log lỗi storage

#### 5.1.5 Lỗi database
- **Nguyên nhân**: Lỗi kết nối Oracle hoặc constraint violation
- **Xử lý**: Rollback transaction, ghi log lỗi
- **Log**: Ghi log SQL error chi tiết

### 5.2 Cơ chế xử lý lỗi
```
try {
    // Xử lý ký hóa đơn
} catch (Exception e) {
    Log.Error($"SignInvoiceFormAndExcelAutoSignedError: {e.Message}");
    Log.Error(e, e.Message);
    return false;
}
```

### 5.3 Phân loại lỗi theo mức độ nghiêm trọng
- **Critical**: Lỗi kết nối database, lỗi HSM
- **Warning**: Lỗi certificate, lỗi chuyển đổi XML
- **Info**: Không có hóa đơn cần xử lý

## 6. Monitoring và Troubleshooting

### 6.1 Log quan trọng cần theo dõi

#### 6.1.1 Log thành công
```
[TraceId] Invoice01 - GetDataSignFormAndExcel: {số_lượng} invoice
[TraceId] Invoice01 - Sign invoice Taxcode: {mã_số_thuế} with {số_lượng} invoices
[TraceId] Upload MinIO Completed: {đường_dẫn_file}
[TraceId] INSERT/UPDATE Invoice01Xml Completed
```

#### 6.1.2 Log lỗi
```
SignInvoiceFormAndExcelAutoSignedError: {thông_báo_lỗi}
[TraceId] Invoice01 - FormAndExcelInvoicesBatchSignedError: {thông_báo_lỗi}
```

### 6.2 Metrics cần theo dõi

#### 6.2.1 Performance Metrics
- **Thời gian xử lý**: Từ lúc bắt đầu đến khi hoàn thành
- **Throughput**: Số hóa đơn được ký/phút
- **Success Rate**: Tỷ lệ ký thành công/tổng số hóa đơn

#### 6.2.2 Business Metrics
- **Số hóa đơn chờ ký**: `SELECT COUNT(*) FROM Invoice01Header WHERE SignStatus = 3`
- **Số hóa đơn ký lỗi**: `SELECT COUNT(*) FROM Invoice01Header WHERE SignStatus = 1`
- **Số hóa đơn đã ký**: `SELECT COUNT(*) FROM Invoice01Header WHERE SignStatus = 5`

### 6.3 Câu lệnh SQL để monitoring

#### 6.3.1 Kiểm tra hóa đơn chờ ký
```sql
SELECT 
    h.SellerTaxCode,
    COUNT(*) as PendingCount,
    MIN(h.CreationTime) as OldestPending
FROM Invoice01Header h
INNER JOIN UsbToken u ON h.TenantId = u.TenantId
WHERE h.SignStatus = 3 
    AND h.Source IN (1, 2)
    AND h.InvoiceStatus != 9
    AND u.IsDeleted = 0
GROUP BY h.SellerTaxCode
ORDER BY PendingCount DESC
```

#### 6.3.2 Kiểm tra hóa đơn ký lỗi
```sql
SELECT 
    h.SellerTaxCode,
    COUNT(*) as ErrorCount,
    MAX(h.SellerSignedTime) as LastErrorTime
FROM Invoice01Header h
WHERE h.SignStatus = 1 
    AND h.Source IN (1, 2)
    AND h.SellerSignedTime >= SYSDATE - 1  -- Lỗi trong 24h qua
GROUP BY h.SellerTaxCode
ORDER BY ErrorCount DESC
```

#### 6.3.3 Kiểm tra hiệu suất ký
```sql
SELECT 
    TRUNC(h.SellerSignedTime) as SignDate,
    COUNT(*) as SignedCount,
    COUNT(CASE WHEN h.SignStatus = 5 THEN 1 END) as SuccessCount,
    COUNT(CASE WHEN h.SignStatus = 1 THEN 1 END) as ErrorCount
FROM Invoice01Header h
WHERE h.SellerSignedTime >= SYSDATE - 7  -- 7 ngày qua
    AND h.Source IN (1, 2)
GROUP BY TRUNC(h.SellerSignedTime)
ORDER BY SignDate DESC
```

### 6.4 Hướng dẫn troubleshooting

#### 6.4.1 Khi Background Worker không chạy
1. **Kiểm tra cấu hình**:
   ```sql
   -- Kiểm tra setting enable
   SELECT * FROM Settings WHERE Key = 'IsEnableAutoSignInvoiceFormAndExcelWorker'
   ```

2. **Kiểm tra log application**: Tìm log khởi động service
3. **Kiểm tra resource**: CPU, Memory, Database connection

#### 6.4.2 Khi có nhiều hóa đơn ký lỗi
1. **Kiểm tra certificate**:
   ```sql
   -- Kiểm tra USB Token
   SELECT TenantId, COUNT(*) FROM UsbToken WHERE IsDeleted = 0 GROUP BY TenantId
   ```

2. **Kiểm tra HSM connection**: Xem log HSM error
3. **Kiểm tra dữ liệu hóa đơn**: Validate dữ liệu đầu vào

#### 6.4.3 Khi hiệu suất chậm
1. **Kiểm tra database performance**:
   ```sql
   -- Kiểm tra index trên bảng Invoice01Header
   SELECT * FROM USER_INDEXES WHERE TABLE_NAME = 'Invoice01Header'
   ```

2. **Kiểm tra MinIO storage**: Dung lượng và tốc độ ghi
3. **Điều chỉnh cấu hình**: TakeRows, TimePeriod, TaskDelayMilliseconds

#### 6.4.4 Khi có deadlock database
1. **Kiểm tra session đang chạy**:
   ```sql
   SELECT * FROM V$SESSION WHERE STATUS = 'ACTIVE'
   ```

2. **Kiểm tra lock**: Tìm các session bị block
3. **Tối ưu hóa query**: Sử dụng hint, partition

## 7. Tác động Hiệu năng

### 7.1 Tác động đến Database

#### 7.1.1 Tác động đọc (Read Impact)
- **Bảng chính**: Invoice01Header, Invoice01Detail, Invoice01Reference, Invoice01TaxBreakdown
- **Tần suất**: Mỗi 100ms (hoặc theo cấu hình)
- **Số lượng record**: Tối đa 100 hóa đơn/lần
- **Index sử dụng**: SignStatus, Source, ApproveStatus, InvoiceStatus

#### 7.1.2 Tác động ghi (Write Impact)
- **UPDATE**: Invoice01Header (cập nhật SignStatus, SellerSignedTime, SellerFullNameSigned, SellerSignedId)
- **INSERT**: Invoice01Xml (thêm bản ghi XML mới)
- **Tần suất**: Tùy thuộc số hóa đơn cần ký
- **Transaction**: Mỗi hóa đơn một transaction riêng biệt

#### 7.1.3 Sử dụng Sequence
- **Invoice01Xml.NEXTVAL**: Được gọi cho mỗi hóa đơn mới ký
- **Tác động**: Minimal, Oracle sequence có hiệu suất cao

### 7.2 Tác động đến Storage (MinIO)

#### 7.2.1 Dung lượng
- **File XML**: Trung bình 5-50KB/file tùy thuộc độ phức tạp hóa đơn
- **Tăng trưởng**: Tùy thuộc volume hóa đơn được ký
- **Retention**: Cần policy xóa file cũ

#### 7.2.2 I/O Operations
- **Write operations**: 1 file/hóa đơn được ký thành công
- **Bandwidth**: Tùy thuộc kích thước file và số lượng hóa đơn

### 7.3 Tác động đến HSM

#### 7.3.1 Signing Operations
- **Tần suất**: 1 operation/hóa đơn
- **Latency**: 100-500ms/operation tùy thuộc HSM
- **Concurrent**: Giới hạn bởi số session HSM

#### 7.3.2 Certificate Usage
- **Session management**: Cần quản lý session pool
- **Certificate caching**: Cache certificate để tối ưu hiệu suất

### 7.4 Khuyến nghị Tối ưu hóa

#### 7.4.1 Database Optimization
1. **Index Strategy**:
   ```sql
   -- Index composite cho query chính
   CREATE INDEX IX_Invoice01Header_Sign 
   ON Invoice01Header(SignStatus, Source, ApproveStatus, InvoiceStatus, TenantId)
   
   -- Index cho join với UsbToken
   CREATE INDEX IX_Invoice01Header_TenantId 
   ON Invoice01Header(TenantId)
   ```

2. **Partitioning**: Xem xét partition bảng Invoice01Header theo thời gian
3. **Statistics**: Cập nhật statistics định kỳ cho optimizer

#### 7.4.2 Application Optimization
1. **Batch Processing**: Xử lý theo batch thay vì từng hóa đơn
2. **Connection Pooling**: Sử dụng connection pool hiệu quả
3. **Async Processing**: Tối ưu hóa async/await pattern

#### 7.4.3 Configuration Tuning
```json
{
  "Settings": {
    "TakeRows": 50,  // Giảm từ 100 nếu hiệu suất chậm
    "TimePeriodFormAndExcel": 5000,  // Tăng chu kỳ lên 5s
    "TaskDelayMilliseconds": 1000,  // Thêm delay giữa các batch
    "TimeDelayStartBackgroundWorker": 30  // Delay khởi động 30s
  }
}
```

#### 7.4.4 Monitoring Thresholds
- **Response Time**: < 5s cho 100 hóa đơn
- **Success Rate**: > 95%
- **Error Rate**: < 5%
- **Queue Length**: < 1000 hóa đơn chờ ký

#### 7.4.5 Scaling Strategy
1. **Horizontal Scaling**: Chạy nhiều instance worker
2. **Load Balancing**: Phân tải theo TenantId hoặc SellerTaxCode
3. **Resource Allocation**: Tăng CPU/Memory cho signing operations

---

**Lưu ý**: Tài liệu này cần được cập nhật định kỳ khi có thay đổi về logic xử lý hoặc cấu trúc database.
